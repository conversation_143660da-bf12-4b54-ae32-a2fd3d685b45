import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/core/utils/constants/custom_dialogs.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/models/locations/district_model.dart';
import 'package:logestics/models/locations/from_place_model.dart';
import 'package:logestics/models/locations/station_model.dart';
import 'package:logestics/features/locations/domain/usecases/district_use_case/get_district_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/station_use_case/create_station_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/station_use_case/update_station_use_case.dart';
import 'package:logestics/features/locations/presentation/stations/controller/station_list_controller.dart';
import 'package:uuid/uuid.dart';

class AddStationController extends GetxController {
  final CreateStationUseCase createStationUseCase;
  final UpdateStationUseCase updateStationUseCase;
  final GetDistrictsUseCase getDistrictsUseCase;

  AddStationController({
    required this.createStationUseCase,
    required this.updateStationUseCase,
    required this.getDistrictsUseCase,
  });

  final formKey = GlobalKey<FormState>();

  // Controllers
  final stationNameController = TextEditingController();
  final placeNameController = TextEditingController();
  final kilometersController = TextEditingController();

  // Observables
  final districts = <DistrictModel>[].obs;
  final selectedDistrict = Rxn<DistrictModel>();
  final places = <FromPlaceModel>[].obs;
  final isLoading = false.obs;
  final isEditMode = false.obs;
  final currentStationId = RxString('');

  // Non-editable fields
  final zoneName = RxString('');
  final regionName = RxString('');
  final regionCode = RxString('');

  @override
  void onInit() {
    super.onInit();
    fetchDistricts();
    // Set default "from" place to "FFC Plant" for new stations
    _setDefaultFromPlace();
  }

  /// Set default "from" place to "FFC Plant" for new station entries
  void _setDefaultFromPlace() {
    if (!isEditMode.value) {
      placeNameController.text = 'FFC Plant';
    }
  }

  Future<void> fetchDistricts() async {
    isLoading.value = true;
    try {
      final result = await getDistrictsUseCase.call();
      result.fold(
        (failure) => showErrorDialog(failure),
        (districtList) {
          districts.value = districtList;
        },
      );
    } catch (e) {
      log('Error fetching districts: $e');
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  void initializeWithExistingStation(StationModel station) async {
    isEditMode.value = true;
    currentStationId.value = station.stationId;
    stationNameController.text = station.stationName;
    places.value = List<FromPlaceModel>.from(station.places);

    // Wait for districts to be loaded
    while (districts.isEmpty) {
      await Future.delayed(Duration(milliseconds: 100));
    }

    // Find and select the matching district
    final matchingDistrict = districts.firstWhereOrNull(
      (d) => d.districtId == station.districtId,
    );

    if (matchingDistrict != null) {
      selectedDistrict.value = matchingDistrict;
      zoneName.value = matchingDistrict.zoneName;
      regionName.value = matchingDistrict.regionName;
      regionCode.value = matchingDistrict.regionCode;
    } else {
      log('Warning: Could not find matching district for ID: ${station.districtId}');
    }
  }

  void onDistrictSelected(String? districtName) {
    if (districtName == null) return;

    final district = districts.firstWhereOrNull(
      (d) => d.districtName == districtName,
    );

    if (district != null) {
      selectedDistrict.value = district;
      zoneName.value = district.zoneName;
      regionName.value = district.regionName;
      regionCode.value = district.regionCode;
    }
  }

  void addPlace() {
    if (placeNameController.text.isEmpty || kilometersController.text.isEmpty) {
      (failure) => SnackbarUtils.showError(
          AppStrings.errorS, 'Please fill all place details');
      return;
    }

    final newPlace = FromPlaceModel(
      placeId: const Uuid().v4(),
      fromPlace: placeNameController.text.capitalizeFirstLetter(),
      kilometers: double.parse(kilometersController.text),
    );

    places.add(newPlace);
    placeNameController.clear();
    kilometersController.clear();

    // Reset "from" place to default "FFC Plant" for convenience
    _setDefaultFromPlace();
  }

  void removePlace(int index) {
    places.removeAt(index);
  }

  Future<void> saveStation() async {
    if (!formKey.currentState!.validate()) return;
    if (placeNameController.text.isNotEmpty ||
        kilometersController.text.isNotEmpty) {
      Get.dialog(
        AlertDialog(
          title: Text('Incomplete Place Details'),
          content: Text(
              'Please enter the details and click the + icon to add it to the places list.'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('OK'),
            ),
          ],
        ),
      );
      return;
    }
    if (places.isEmpty) {
      (failure) => SnackbarUtils.showError(
          AppStrings.errorS, 'Please add at least one place');
      return;
    }

    isLoading.value = true;
    try {
      final station = StationModel(
        stationId: currentStationId.value,
        stationName: stationNameController.text.capitalizeFirstLetter(),
        zoneId: selectedDistrict.value!.zoneId,
        zoneName: zoneName.value,
        regionId: selectedDistrict.value!.regionId,
        regionName: regionName.value.capitalizeFirstLetter(),
        regionCode: regionCode.value,
        districtId: selectedDistrict.value!.districtId,
        districtName:
            selectedDistrict.value!.districtName.capitalizeFirstLetter(),
        createdAt: DateTime.now(),
        places: places,
      );

      final result = isEditMode.value
          ? await updateStationUseCase.call(station: station)
          : await createStationUseCase.call(station: station);

      result.fold(
        (failure) => showErrorDialog(failure),
        (success) => _showSuccessSnackbar(success, station),
      );
    } catch (e) {
      showUnexpectedErrorDialog();
    } finally {
      isLoading.value = false;
    }
  }

  void _showSuccessSnackbar(SuccessObj success, StationModel station) {
    try {
      final stationListController = Get.find<StationListController>();

      if (isEditMode.value) {
        stationListController.updateStationInList(station);
      } else {
        stationListController.addStationToList(station);
      }

      clearControllers();
      Navigator.of(Get.context!).pop();

      SnackbarUtils.showSuccess(
        AppStrings.success,
        success.message,
      );
    } catch (e) {
      log('Error handling success: $e');
      showUnexpectedErrorDialog();
    }
  }

  void clearControllers() {
    stationNameController.clear();
    placeNameController.clear();
    kilometersController.clear();
    places.clear();
    selectedDistrict.value = null;
    zoneName.value = '';
    regionName.value = '';
    regionCode.value = '';
    isEditMode.value = false;
    currentStationId.value = '';

    // Set default "from" place after clearing for new station creation
    _setDefaultFromPlace();
  }

  void fetchStations() {
    try {
      final stationListController = Get.find<StationListController>();
      stationListController.fetchStations();
    } catch (e) {
      log('StationListController not found: $e');
    }
  }

  @override
  void onClose() {
    stationNameController.dispose();
    placeNameController.dispose();
    kilometersController.dispose();
    super.onClose();
  }
}
