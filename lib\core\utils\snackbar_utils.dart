import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Enum for snackbar types
enum _SnackbarType { success, error, info, warning }

/// Utility class for standardized snackbars across the application
class SnackbarUtils {
  // Private constructor to prevent instantiation
  SnackbarUtils._();

  // Constants for snackbar styling
  static const Duration _defaultDuration = Duration(seconds: 3);
  static const SnackPosition _defaultPosition = SnackPosition.BOTTOM;

  static const double _borderRadius = 8.0;
  static const EdgeInsets _padding =
      EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0);

  /// Shows a success snackbar with standardized styling
  static void showSuccess(String title, String message) {
    _showSnackbarWithRetry(title, message, _SnackbarType.success);
  }

  /// Core method to show snackbar with retry mechanism
  static void _showSnackbarWithRetry(
      String title, String message, _SnackbarType type,
      {int retryCount = 0}) {
    const maxRetries = 3;

    try {
      // Check if GetX is properly initialized
      if (!_isGetXInitialized()) {
        if (retryCount < maxRetries) {
          // Retry after a short delay
          Future.delayed(Duration(milliseconds: 100 * (retryCount + 1)), () {
            _showSnackbarWithRetry(title, message, type,
                retryCount: retryCount + 1);
          });
          return;
        } else {
          // Max retries reached, log and skip
          log('GetX not initialized after $maxRetries retries, skipping snackbar: $title - $message');
          return;
        }
      }

      // Show the snackbar based on type
      switch (type) {
        case _SnackbarType.success:
          _showSuccessSnackbar(title, message);
          break;
        case _SnackbarType.error:
          _showErrorSnackbar(title, message);
          break;
        case _SnackbarType.info:
          _showInfoSnackbar(title, message);
          break;
        case _SnackbarType.warning:
          _showWarningSnackbar(title, message);
          break;
      }
    } catch (e) {
      log('Error showing snackbar (attempt ${retryCount + 1}): $e');

      if (retryCount < maxRetries) {
        // Retry with simpler approach
        Future.delayed(Duration(milliseconds: 200 * (retryCount + 1)), () {
          _showSimpleSnackbar(title, message, type);
        });
      }
    }
  }

  /// Check if GetX is properly initialized
  static bool _isGetXInitialized() {
    try {
      return Get.context != null &&
          Get.key.currentState != null &&
          Get.key.currentState!.mounted &&
          Get.key.currentState!.context.mounted;
    } catch (e) {
      return false;
    }
  }

  /// Show simple fallback snackbar
  static void _showSimpleSnackbar(
      String title, String message, _SnackbarType type) {
    try {
      Color backgroundColor;
      Color textColor = Colors.white;

      switch (type) {
        case _SnackbarType.success:
          backgroundColor = Colors.green;
          break;
        case _SnackbarType.error:
          backgroundColor = Colors.red;
          break;
        case _SnackbarType.info:
          backgroundColor = Colors.blue;
          break;
        case _SnackbarType.warning:
          backgroundColor = Colors.orange;
          break;
      }

      Get.snackbar(
        title,
        message,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: backgroundColor,
        colorText: textColor,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      log('Failed to show simple snackbar: $e');
    }
  }

  /// Internal method to show styled success snackbar
  static void _showSuccessSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: _defaultPosition,
      backgroundColor: Colors.green.withAlpha(20),
      colorText: Colors.green.shade700,
      duration: _defaultDuration,
      margin: const EdgeInsets.all(8),
      borderRadius: _borderRadius,
      padding: _padding,
      icon: const Icon(Icons.check_circle, color: Colors.green),
      overlayBlur: 0,
      forwardAnimationCurve: Curves.easeOutCirc,
      reverseAnimationCurve: Curves.easeInCirc,
      boxShadows: [
        BoxShadow(
          color: Colors.black.withAlpha(20),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// Shows an error snackbar with standardized styling
  static void showError(String title, String message) {
    _showSnackbarWithRetry(title, message, _SnackbarType.error);
  }

  /// Shows an info snackbar with standardized styling
  static void showInfo(String title, String message) {
    _showSnackbarWithRetry(title, message, _SnackbarType.info);
  }

  /// Shows a warning snackbar with standardized styling
  static void showWarning(String title, String message) {
    _showSnackbarWithRetry(title, message, _SnackbarType.warning);
  }

  /// Internal method to show styled error snackbar
  static void _showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: _defaultPosition,
      backgroundColor: Colors.red.withAlpha(20),
      colorText: Colors.red.shade700,
      duration: _defaultDuration,
      margin: const EdgeInsets.all(8),
      borderRadius: _borderRadius,
      padding: _padding,
      icon: const Icon(Icons.error_outline, color: Colors.red),
      overlayBlur: 0,
      forwardAnimationCurve: Curves.easeOutCirc,
      reverseAnimationCurve: Curves.easeInCirc,
      boxShadows: [
        BoxShadow(
          color: Colors.black.withAlpha(20),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// Internal method to show styled info snackbar
  static void _showInfoSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: _defaultPosition,
      backgroundColor: Colors.blue.withAlpha(20),
      colorText: Colors.blue.shade700,
      duration: _defaultDuration,
      margin: const EdgeInsets.all(8),
      borderRadius: _borderRadius,
      padding: _padding,
      icon: const Icon(Icons.info_outline, color: Colors.blue),
      overlayBlur: 0,
      forwardAnimationCurve: Curves.easeOutCirc,
      reverseAnimationCurve: Curves.easeInCirc,
      boxShadows: [
        BoxShadow(
          color: Colors.black.withAlpha(20),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// Internal method to show styled warning snackbar
  static void _showWarningSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: _defaultPosition,
      backgroundColor: Colors.amber.withAlpha(20),
      colorText: Colors.amber.shade800,
      duration: _defaultDuration,
      margin: const EdgeInsets.all(8),
      borderRadius: _borderRadius,
      padding: _padding,
      icon: const Icon(Icons.warning_amber_outlined, color: Colors.amber),
      overlayBlur: 0,
      forwardAnimationCurve: Curves.easeOutCirc,
      reverseAnimationCurve: Curves.easeInCirc,
      boxShadows: [
        BoxShadow(
          color: Colors.black.withAlpha(20),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }
}
