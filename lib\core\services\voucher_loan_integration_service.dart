import 'dart:developer' as dev;
import 'package:either_dart/either.dart';
import 'package:uuid/uuid.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/models/payment_transaction_model.dart';

import 'package:logestics/firebase_service/finance/loan_firebase_service.dart';
import 'package:logestics/firebase_service/finance/company_firebase_service.dart';
import 'package:logestics/firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import 'package:logestics/core/services/automatic_journal_entry_service.dart';
import 'package:logestics/firebase_service/accounting/journal_entry_firebase_service.dart';
import 'package:logestics/models/finance/journal_entry_model.dart';

/// Service to handle integration between voucher payments and loan system
/// Implements two-stage loan-based payment workflow
class VoucherLoanIntegrationService {
  final LoanFirebaseService _loanFirebaseService;
  final ChartOfAccountsFirebaseService _chartOfAccountsService;
  final AutomaticJournalEntryService _automaticJournalService;
  final JournalEntryFirebaseService _journalEntryService;

  VoucherLoanIntegrationService({
    LoanFirebaseService? loanFirebaseService,
    ChartOfAccountsFirebaseService? chartOfAccountsService,
    AutomaticJournalEntryService? automaticJournalService,
    JournalEntryFirebaseService? journalEntryService,
  })  : _loanFirebaseService = loanFirebaseService ?? LoanFirebaseService(),
        _chartOfAccountsService =
            chartOfAccountsService ?? ChartOfAccountsFirebaseService(),
        _automaticJournalService = automaticJournalService ??
            AutomaticJournalEntryService(ChartOfAccountsFirebaseService()),
        _journalEntryService =
            journalEntryService ?? JournalEntryFirebaseService();

  /// Stage 1: Create loan request from voucher payment
  /// Returns updated PaymentTransactionModel with loan request reference
  Future<Either<FailureObj, PaymentTransactionModel>>
      createLoanRequestFromPayment({
    required PaymentTransactionModel payment,
    required String uid,
    required String createdBy,
  }) async {
    try {
      dev.log(
          '⚠️ PENDING LOAN REQUEST CREATION: Creating loan request from payment: ${payment.id}');
      dev.log(
          '⚠️ PENDING LOAN REQUEST: Payment method = ${payment.method.name}');
      dev.log(
          '⚠️ PENDING LOAN REQUEST: This creates PENDING loan, not active!');
      dev.log(
          '⚠️ PENDING LOAN REQUEST: If this is accountTransfer, something is wrong!');

      // Validate payment has account selection
      if (payment.accountId == null || payment.accountId!.isEmpty) {
        dev.log('❌ Payment validation failed: No account ID provided');
        return Left(FailureObj(
          code: 'validation-error',
          message:
              'Payment must have an account selected for loan-based workflow',
        ));
      }

      // Get the selected account for loan request (use cross-company method for loan-based payments)
      dev.log('🔍 Fetching cross-company account: ${payment.accountId}');
      final selectedAccount = await _chartOfAccountsService
          .getAccountByIdCrossCompany(payment.accountId!);
      if (selectedAccount == null) {
        dev.log(
            '❌ Account fetch failed: Account not found ${payment.accountId}');
        return Left(FailureObj(
          code: 'account-not-found',
          message: 'Selected account not found: ${payment.accountId}',
        ));
      }

      dev.log(
          '✅ Successfully fetched cross-company account: ${selectedAccount.displayName} (Owner: ${selectedAccount.uid})');
      dev.log(
          '🏢 Setting up loan: Current user ($uid) → Account owner (${selectedAccount.uid})');

      // Get proper company names for loan request
      String requestedByName = 'System'; // Default fallback
      String requestedToName = 'Account Owner'; // Default fallback

      try {
        final companyService = CompanyFirebaseService();

        // Get current user's company name (borrower)
        final currentUserResult = await companyService.getUserById(uid);
        currentUserResult.fold(
          (failure) {
            dev.log(
                'Could not get current user company name: ${failure.message}');
          },
          (userModel) {
            requestedByName = userModel.companyName;
            dev.log('Retrieved current user company name: $requestedByName');
          },
        );

        // Get account owner's company name (lender)
        final accountOwnerResult =
            await companyService.getUserById(selectedAccount.uid);
        accountOwnerResult.fold(
          (failure) {
            dev.log(
                'Could not get account owner company name: ${failure.message}');
            // Fallback to account name
            requestedToName = selectedAccount.accountName;
          },
          (userModel) {
            requestedToName = userModel.companyName;
            dev.log('Retrieved account owner company name: $requestedToName');
          },
        );
      } catch (e) {
        dev.log('Error resolving company names for loan request: $e');
      }

      // Create loan request - cross-company loan from account owner to current user
      final loanId = const Uuid().v4();
      final loan = LoanModel(
        id: loanId,
        uid: uid, // Current user (voucher creator)
        requestedBy: uid, // Current user requesting the loan
        requestedByName: requestedByName, // Actual current user company name
        requestedTo: selectedAccount.uid, // Account owner (loan provider)
        requestedToName: requestedToName, // Actual account owner company name
        fromAccountId:
            selectedAccount.id, // Account to debit (loan provider's account)
        toAccountId:
            selectedAccount.id, // For voucher payments, same account reference
        fromAccountName: selectedAccount.displayName,
        toAccountName: selectedAccount.displayName,
        amount: payment.amount,
        dueDate:
            DateTime.now().add(const Duration(days: 30)), // Default 30 days
        status: 'pending',
        requestDate: DateTime.now(),
        notes:
            'Auto-generated loan request for voucher payment #${payment.voucherId} - ${payment.method.name} - Cross-company payment from Company 1 to Company 2 (Account: ${selectedAccount.displayName})',
        voucherPaymentId: payment.id,
      );

      // Save loan request using cross-company method to bypass account ownership validation
      dev.log('💾 Attempting to save cross-company loan request: $loanId');
      final loanResult =
          await _loanFirebaseService.requestCrossCompanyLoan(loan);

      return loanResult.fold(
        (failure) {
          dev.log(
              '❌ Loan request creation failed: ${failure.code} - ${failure.message}');
          return Left(failure);
        },
        (success) {
          dev.log('✅ Created loan request: $loanId for payment: ${payment.id}');
          dev.log(
              '🎯 Loan should appear as: Outgoing for $uid, Incoming for ${selectedAccount.uid}');

          // Return updated payment with loan reference
          final updatedPayment = payment.copyWith(
            loanRequestId: loanId,
            isLoanBased: true,
          );

          return Right(updatedPayment);
        },
      );
    } catch (e) {
      dev.log('❌ Error creating loan request from payment: $e');
      return Left(FailureObj(
        code: 'loan-creation-error',
        message: 'Failed to create loan request: $e',
      ));
    }
  }

  /// Create active loan directly from voucher payment (for "Other" payment types)
  /// This bypasses the pending approval workflow and creates an active loan immediately
  Future<Either<FailureObj, PaymentTransactionModel>>
      createActiveLoanFromPayment({
    required PaymentTransactionModel payment,
    required String uid,
    required String createdBy,
  }) async {
    try {
      dev.log(
          '🚀 ACTIVE LOAN CREATION STARTED: Creating active loan from payment: ${payment.id}');
      dev.log(
          '🚀 ACTIVE LOAN CREATION: Payment method = ${payment.method.name}');
      dev.log('🚀 ACTIVE LOAN CREATION: Payment amount = ${payment.amount}');
      dev.log('🚀 ACTIVE LOAN CREATION: Account ID = ${payment.accountId}');
      dev.log(
          '🚀 ACTIVE LOAN CREATION: This should create an ACTIVE loan, not pending!');

      // Validate payment has account selection
      if (payment.accountId == null || payment.accountId!.isEmpty) {
        dev.log('❌ Payment validation failed: No account ID provided');
        return Left(FailureObj(
          code: 'validation-error',
          message:
              'Payment must have an account selected for active loan workflow',
        ));
      }

      // Get the selected account for loan creation (use cross-company method)
      dev.log('🔍 Fetching cross-company account: ${payment.accountId}');
      final selectedAccount = await _chartOfAccountsService
          .getAccountByIdCrossCompany(payment.accountId!);
      if (selectedAccount == null) {
        dev.log(
            '❌ Account fetch failed: Account not found ${payment.accountId}');
        return Left(FailureObj(
          code: 'account-not-found',
          message: 'Selected account not found: ${payment.accountId}',
        ));
      }

      dev.log(
          '✅ Successfully fetched cross-company account: ${selectedAccount.displayName} (Owner: ${selectedAccount.uid})');
      dev.log(
          '🏢 Setting up active loan: Current user ($uid) → Account owner (${selectedAccount.uid})');

      // Create active loan - cross-company loan from account owner to current user
      final loanId = const Uuid().v4();
      final loan = LoanModel(
        id: loanId,
        uid: uid, // Current user (voucher creator)
        requestedBy: uid, // Current user requesting the loan
        requestedByName: 'System', // Could be enhanced with actual user name
        requestedTo: selectedAccount.uid, // Account owner (loan provider)
        requestedToName:
            'Account Owner', // Could be enhanced with actual company name
        fromAccountId:
            selectedAccount.id, // Account to debit (loan provider's account)
        toAccountId:
            selectedAccount.id, // For voucher payments, same account reference
        fromAccountName: selectedAccount.displayName,
        toAccountName: selectedAccount.displayName,
        amount: payment.amount,
        dueDate:
            DateTime.now().add(const Duration(days: 30)), // Default 30 days
        status: 'approved', // Set as approved immediately
        requestDate: DateTime.now(),
        approvalDate: DateTime.now(), // Set approval date to now
        notes:
            'Auto-generated active loan for voucher payment #${payment.voucherId} - ${payment.method.name} - Cross-company payment from Company 1 to Company 2 (Account: ${selectedAccount.displayName})',
        voucherPaymentId: payment.id,
      );

      // Save active loan using the new direct creation method
      dev.log('💾 Attempting to save active cross-company loan: $loanId');
      final loanResult =
          await _loanFirebaseService.createActiveCrossCompanyLoan(loan);

      return loanResult.fold(
        (failure) {
          dev.log(
              '❌ Active loan creation failed: ${failure.code} - ${failure.message}');
          return Left(failure);
        },
        (success) {
          dev.log('✅ Created active loan: $loanId for payment: ${payment.id}');
          dev.log(
              '🎯 Active loan should appear in: Active Loans for $uid, Loan History for both companies');

          // Return updated payment with loan reference
          final updatedPayment = payment.copyWith(
            loanRequestId: loanId,
            isLoanBased: true,
          );

          return Right(updatedPayment);
        },
      );
    } catch (e) {
      dev.log('❌ Error creating active loan from payment: $e');
      return Left(FailureObj(
        code: 'active-loan-creation-error',
        message: 'Failed to create active loan: $e',
      ));
    }
  }

  /// Stage 1.5: Create auto-posted journal entry for voucher payment
  /// This creates and posts the journal entry immediately since users will approve the loan anyway
  Future<Either<FailureObj, JournalEntryModel>>
      createAutoPostedPaymentJournalEntry({
    required PaymentTransactionModel payment,
    required String uid,
    required String createdBy,
  }) async {
    try {
      dev.log('🔄 Creating pending journal entry for payment: ${payment.id}');
      dev.log(
          '📝 Journal entry details: Method=${payment.method.name}, Amount=${payment.amount}, LoanBased=${payment.isLoanBased}');

      // Generate the standard payment journal entry with loan-based workflow flag
      final journalEntry =
          await _automaticJournalService.generatePaymentJournalEntry(
        payment: payment,
        uid: uid,
        createdBy: createdBy,
        isLoanBasedWorkflow: true, // Explicitly mark as loan-based workflow
      );

      if (journalEntry == null) {
        return Left(FailureObj(
          code: 'journal-generation-error',
          message: 'Failed to generate journal entry for payment',
        ));
      }

      // Modify the journal entry to auto-post for voucher payments
      // Since users will approve the loan anyway, we can post the journal entry immediately
      // Use a unique sourceTransactionId to avoid duplicate detection conflicts
      final autoPostedJournalEntry = journalEntry.copyWith(
        description:
            '${journalEntry.description} - VOUCHER PAYMENT (AUTO-POSTED)',
        status: JournalEntryStatus.posted, // Auto-post for voucher payments
        sourceTransactionType: 'payment_voucher_loan',
        sourceTransactionId: '${payment.id}_voucher_loan', // Make it unique
      );

      // Save the auto-posted journal entry
      dev.log(
          '💾 Saving auto-posted journal entry with ID: ${autoPostedJournalEntry.id}');
      dev.log(
          '💾 Journal entry details: ${autoPostedJournalEntry.description}');
      dev.log(
          '💾 Source transaction ID: ${autoPostedJournalEntry.sourceTransactionId}');
      dev.log(
          '💾 Source transaction type: ${autoPostedJournalEntry.sourceTransactionType}');

      await _journalEntryService.createJournalEntry(autoPostedJournalEntry);

      dev.log(
          '✅ Created auto-posted journal entry: ${autoPostedJournalEntry.entryNumber} (ID: ${autoPostedJournalEntry.id})');
      return Right(autoPostedJournalEntry);
    } catch (e) {
      dev.log('❌ Error creating pending journal entry: $e');
      return Left(FailureObj(
        code: 'journal-creation-error',
        message: 'Failed to create pending journal entry: $e',
      ));
    }
  }

  /// Stage 2: Complete payment journal entry upon loan approval
  /// Note: Uses original payment transaction date, not approval date
  Future<Either<FailureObj, SuccessObj>> completePendingPaymentJournalEntry({
    required String journalEntryId,
    required String loanId,
    required DateTime approvalDate,
    required String uid,
  }) async {
    try {
      dev.log(
          '🔄 Completing pending journal entry: $journalEntryId for loan: $loanId');

      // Get the pending journal entry using cross-company access for voucher payments
      dev.log(
          '🔍 Searching for journal entry with ID (cross-company): $journalEntryId');
      final journalEntry = await _journalEntryService
          .getJournalEntryByIdCrossCompany(journalEntryId);
      if (journalEntry == null) {
        dev.log(
            '❌ Journal entry not found in database (cross-company): $journalEntryId');
        dev.log('🔍 This could indicate:');
        dev.log('   1. Journal entry was not saved during voucher creation');
        dev.log(
            '   2. Journal entry ID mismatch between loan and actual entry');
        dev.log('   3. Journal entry was deleted or corrupted');
        dev.log('   4. Database connectivity issue');

        return Left(FailureObj(
          code: 'journal-not-found',
          message:
              'Pending journal entry not found: $journalEntryId. This indicates the journal entry was not properly created during voucher creation or there is an ID mismatch.',
        ));
      }

      dev.log('✅ Found pending journal entry: ${journalEntry.description}');

      // Update journal entry status to posted (completing the loan-based payment)
      await _journalEntryService.updateJournalEntryStatus(
          journalEntryId, JournalEntryStatus.posted);

      dev.log('✅ Completed journal entry: $journalEntryId');
      return Right(
          SuccessObj(message: 'Payment journal entry completed successfully'));
    } catch (e) {
      dev.log('❌ Error completing journal entry: $e');
      return Left(FailureObj(
        code: 'journal-completion-error',
        message: 'Failed to complete journal entry: $e',
      ));
    }
  }

  /// Rollback loan request and pending journal entry if needed
  Future<Either<FailureObj, SuccessObj>> rollbackLoanBasedPayment({
    required String loanRequestId,
    required String? pendingJournalEntryId,
    required String uid,
  }) async {
    try {
      dev.log('🔄 Rolling back loan-based payment: $loanRequestId');

      // Delete loan request
      // Note: This would need to be implemented in LoanFirebaseService
      // For now, we'll just log the rollback
      dev.log(
          '⚠️ Loan rollback not implemented - manual cleanup required for loan: $loanRequestId');

      // Delete pending journal entry if exists
      if (pendingJournalEntryId != null) {
        // Note: This would need to be implemented in JournalEntryService
        dev.log(
            '⚠️ Journal entry rollback not implemented - manual cleanup required for entry: $pendingJournalEntryId');
      }

      return Right(SuccessObj(
          message: 'Rollback completed (manual cleanup may be required)'));
    } catch (e) {
      dev.log('❌ Error during rollback: $e');
      return Left(FailureObj(
        code: 'rollback-error',
        message: 'Failed to rollback loan-based payment: $e',
      ));
    }
  }

  /// Check if a payment should use loan-based workflow
  /// Currently applies to Cheque and Other payment methods
  bool shouldUseLoanBasedWorkflow(PaymentMethod paymentMethod) {
    return paymentMethod == PaymentMethod.check ||
        paymentMethod == PaymentMethod.accountTransfer;
  }

  /// Update loan request with pending journal entry ID
  Future<Either<FailureObj, SuccessObj>> updateLoanWithPendingJournalEntry({
    required String loanRequestId,
    required String pendingJournalEntryId,
    required String uid,
  }) async {
    try {
      dev.log(
          '🔄 Updating loan $loanRequestId with pending journal entry: $pendingJournalEntryId');

      // Get the existing loan
      final existingLoan =
          await _loanFirebaseService.getLoanById(loanRequestId);
      if (existingLoan == null) {
        return Left(FailureObj(
          code: 'loan-not-found',
          message: 'Loan request not found: $loanRequestId',
        ));
      }

      // Update the loan with the pending journal entry ID
      final updateResult = await _loanFirebaseService.updateLoan(
        loanRequestId,
        {'pendingJournalEntryId': pendingJournalEntryId},
      );

      return updateResult.fold(
        (failure) {
          dev.log(
              '❌ Failed to update loan with pending journal entry: ${failure.message}');
          return Left(failure);
        },
        (success) {
          dev.log(
              '✅ Successfully updated loan $loanRequestId with pending journal entry: $pendingJournalEntryId');
          return Right(SuccessObj(
              message: 'Loan updated with pending journal entry ID'));
        },
      );
    } catch (e) {
      dev.log('❌ Error updating loan with pending journal entry: $e');
      return Left(FailureObj(
        code: 'loan-update-error',
        message: 'Failed to update loan: $e',
      ));
    }
  }

  /// Get loan request by payment transaction
  Future<LoanModel?> getLoanRequestByPayment(String loanRequestId) async {
    try {
      return await _loanFirebaseService.getLoanById(loanRequestId);
    } catch (e) {
      dev.log('❌ Error getting loan request: $e');
      return null;
    }
  }

  /// Create active loan journal entry directly (for "Other" payment types)
  /// This creates a complete journal entry immediately without pending status
  Future<Either<FailureObj, JournalEntryModel>> createActiveLoanJournalEntry({
    required PaymentTransactionModel payment,
    required String uid,
    required String createdBy,
  }) async {
    try {
      dev.log(
          '🔄 Creating active loan journal entry for payment: ${payment.id}');

      // Generate the standard payment journal entry for active loan
      final journalEntry =
          await _automaticJournalService.generatePaymentJournalEntry(
        payment: payment,
        uid: uid,
        createdBy: createdBy,
        isLoanBasedWorkflow:
            true, // This IS loan-based, just active instead of pending
      );

      if (journalEntry == null) {
        return Left(FailureObj(
          code: 'journal-generation-error',
          message: 'Failed to generate journal entry for active loan payment',
        ));
      }

      // Modify the journal entry to indicate it's for an active loan
      final activeLoanJournalEntry = journalEntry.copyWith(
        description: '${journalEntry.description} - ACTIVE LOAN PAYMENT',
        status: JournalEntryStatus.posted, // Mark as posted immediately
        sourceTransactionType: 'payment_active_loan',
      );

      // Save the journal entry using the service directly
      try {
        await _journalEntryService.createJournalEntry(activeLoanJournalEntry);
        dev.log(
            '✅ Created active loan journal entry: ${activeLoanJournalEntry.id} for payment: ${payment.id}');
        return Right(activeLoanJournalEntry);
      } catch (e) {
        dev.log('❌ Failed to save active loan journal entry: $e');
        return Left(FailureObj(
          code: 'journal-save-error',
          message: 'Failed to save active loan journal entry: $e',
        ));
      }
    } catch (e) {
      dev.log('❌ Error creating active loan journal entry: $e');
      return Left(FailureObj(
        code: 'active-loan-journal-error',
        message: 'Failed to create active loan journal entry: $e',
      ));
    }
  }
}
