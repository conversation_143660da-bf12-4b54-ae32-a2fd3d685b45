import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_storage/get_storage.dart';
import '../core/services/transaction_account_mapping_service.dart';
import '../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../firebase_service/finance/loan_firebase_service.dart';
import '../models/finance/chart_of_accounts_model.dart';
import '../models/finance/loan_model.dart';
import '../models/finance/journal_entry_model.dart';

/// Test widget to verify loan account mapping functionality
class LoanAccountMappingTestWidget extends StatefulWidget {
  const LoanAccountMappingTestWidget({super.key});

  @override
  State<LoanAccountMappingTestWidget> createState() =>
      _LoanAccountMappingTestWidgetState();
}

class _LoanAccountMappingTestWidgetState
    extends State<LoanAccountMappingTestWidget> {
  String _testResults = '';
  bool _isRunning = false;
  late final TransactionAccountMappingService _mappingService;
  late final ChartOfAccountsFirebaseService _chartService;

  @override
  void initState() {
    super.initState();
    _chartService = ChartOfAccountsFirebaseService();
    _mappingService = TransactionAccountMappingService(_chartService);
  }

  void _addResult(String result) {
    setState(() {
      _testResults += '$result\n';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Loan Account Mapping Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Loan Account Mapping Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _isRunning ? null : _testLoanAccountMapping,
              icon: const Icon(Icons.account_balance),
              label: const Text('Test Loan Account Mapping'),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _isRunning ? null : _showConfiguredAccounts,
              icon: const Icon(Icons.settings),
              label: const Text('Show Configured Accounts'),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _isRunning ? null : _testFullLoanApprovalWorkflow,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Test Full Loan Approval Workflow'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResults.isEmpty
                        ? 'No test results yet...'
                        : _testResults,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testLoanAccountMapping() async {
    setState(() {
      _isRunning = true;
      _testResults = '';
    });

    try {
      _addResult('🔍 Testing Loan Account Mapping...');

      // Get current user
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _addResult('❌ User not authenticated');
        return;
      }

      _addResult('✅ User authenticated: ${user.uid}');

      // Test the loan account mapping
      _addResult('🔄 Getting loan account mapping...');
      final mapping = await _mappingService.getLoanAccountMapping(user.uid);

      if (mapping == null) {
        _addResult('❌ Failed to get loan account mapping');
        _addResult('⚠️ This means loan approval will fail');
      } else {
        _addResult('✅ Successfully got loan account mapping!');
        _addResult('');
        _addResult('📊 Loan Account Details:');
        _addResult('   🏦 Loan Receivable Account:');
        _addResult(
            '      - Name: ${mapping.loansReceivableAccount.displayName}');
        _addResult('      - ID: ${mapping.loansReceivableAccount.id}');
        _addResult(
            '      - Number: ${mapping.loansReceivableAccount.accountNumber}');
        _addResult(
            '      - Category: ${mapping.loansReceivableAccount.category.name}');
        _addResult(
            '      - Balance: ${mapping.loansReceivableAccount.balance}');
        _addResult('');
        _addResult('   💳 Loan Payable Account:');
        _addResult('      - Name: ${mapping.loansPayableAccount.displayName}');
        _addResult('      - ID: ${mapping.loansPayableAccount.id}');
        _addResult(
            '      - Number: ${mapping.loansPayableAccount.accountNumber}');
        _addResult(
            '      - Category: ${mapping.loansPayableAccount.category.name}');
        _addResult('      - Balance: ${mapping.loansPayableAccount.balance}');
        _addResult('');
        _addResult('   💰 Cash/Asset Account:');
        _addResult('      - Name: ${mapping.cashAccount.displayName}');
        _addResult('      - ID: ${mapping.cashAccount.id}');
        _addResult('      - Number: ${mapping.cashAccount.accountNumber}');
        _addResult('      - Category: ${mapping.cashAccount.category.name}');
        _addResult('      - Balance: ${mapping.cashAccount.balance}');
        _addResult('');
        _addResult('🎉 Loan account mapping is working correctly!');
        _addResult('✅ Loan approval should now create journal entries');
      }
    } catch (e) {
      _addResult('❌ Loan account mapping test failed: $e');
    } finally {
      setState(() => _isRunning = false);
    }
  }

  Future<void> _showConfiguredAccounts() async {
    setState(() {
      _isRunning = true;
      _testResults = '';
    });

    try {
      _addResult('🔍 Checking Configured Loan Accounts...');

      final storage = GetStorage();

      // Check loan receivable account
      final receivableData = storage.read('loan_receivable_account');
      if (receivableData != null) {
        final receivableAccount = ChartOfAccountsModel.fromJson(
            Map<String, dynamic>.from(receivableData));
        _addResult('✅ Loan Receivable Account Configured:');
        _addResult('   - Name: ${receivableAccount.displayName}');
        _addResult('   - ID: ${receivableAccount.id}');
        _addResult('   - Number: ${receivableAccount.accountNumber}');
        _addResult('   - Category: ${receivableAccount.category.name}');
      } else {
        _addResult('❌ No Loan Receivable Account configured');
      }

      _addResult('');

      // Check loan payable account
      final payableData = storage.read('loan_payable_account');
      if (payableData != null) {
        final payableAccount = ChartOfAccountsModel.fromJson(
            Map<String, dynamic>.from(payableData));
        _addResult('✅ Loan Payable Account Configured:');
        _addResult('   - Name: ${payableAccount.displayName}');
        _addResult('   - ID: ${payableAccount.id}');
        _addResult('   - Number: ${payableAccount.accountNumber}');
        _addResult('   - Category: ${payableAccount.category.name}');
      } else {
        _addResult('❌ No Loan Payable Account configured');
      }

      _addResult('');

      // Get all Chart of Accounts
      _addResult('📊 Available Chart of Accounts:');
      final accounts = await _chartService.getAccounts();
      _addResult('   Total accounts: ${accounts.length}');

      final assetAccounts =
          accounts.where((a) => a.category == AccountCategory.assets).toList();
      final liabilityAccounts = accounts
          .where((a) => a.category == AccountCategory.liabilities)
          .toList();

      _addResult('   Asset accounts: ${assetAccounts.length}');
      for (final account in assetAccounts) {
        _addResult('      - ${account.displayName} (${account.accountNumber})');
      }

      _addResult('   Liability accounts: ${liabilityAccounts.length}');
      for (final account in liabilityAccounts) {
        _addResult('      - ${account.displayName} (${account.accountNumber})');
      }
    } catch (e) {
      _addResult('❌ Error checking configured accounts: $e');
    } finally {
      setState(() => _isRunning = false);
    }
  }

  Future<void> _testFullLoanApprovalWorkflow() async {
    setState(() {
      _isRunning = true;
      _testResults = '';
    });

    try {
      _addResult('🔍 Testing Full Loan Approval Workflow...');

      // Get current user
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _addResult('❌ User not authenticated');
        return;
      }

      _addResult('✅ User authenticated: ${user.uid}');
      _addResult(
          '🔍 This test will use the current authenticated user ID consistently');

      // Step 1: Test account mapping
      _addResult('');
      _addResult('📋 Step 1: Testing Account Mapping...');
      _addResult(
          '🔍 Testing account mapping with current user ID: ${user.uid}');
      final mapping = await _mappingService.getLoanAccountMapping(user.uid);

      if (mapping == null) {
        _addResult('❌ Account mapping failed - cannot proceed');
        return;
      }

      _addResult('✅ Account mapping successful');
      _addResult(
          '   - Loan Receivable: ${mapping.loansReceivableAccount.displayName}');
      _addResult(
          '   - Loan Payable: ${mapping.loansPayableAccount.displayName}');
      _addResult('   - Cash/Asset: ${mapping.cashAccount.displayName}');

      // Step 2: Create test loan
      _addResult('');
      _addResult('📋 Step 2: Creating Test Loan...');
      final loanId = 'test-loan-${DateTime.now().millisecondsSinceEpoch}';
      final testLoan = LoanModel(
        id: loanId,
        uid: user.uid,
        requestedBy: user.uid, // Self loan (receiving)
        requestedByName: 'Test Company',
        requestedTo: 'test-company-2',
        requestedToName: 'Test Company 2',
        fromAccountId: mapping.cashAccount.id,
        fromAccountName: mapping.cashAccount.displayName,
        toAccountId: mapping.cashAccount.id,
        toAccountName: mapping.cashAccount.displayName,
        amount: 1000.0,
        dueDate: DateTime.now().add(const Duration(days: 30)),
        status: 'pending',
        requestDate: DateTime.now(),
        notes: 'Test loan for full workflow testing',
      );

      // Import the loan service
      final loanService = LoanFirebaseService();
      final createResult = await loanService.requestLoan(testLoan);

      createResult.fold(
        (failure) {
          _addResult('❌ Failed to create loan request: ${failure.message}');
          return;
        },
        (success) {
          _addResult('✅ Successfully created loan request: $loanId');
        },
      );

      // Step 3: Approve the loan
      _addResult('');
      _addResult('📋 Step 3: Approving Loan...');
      _addResult('🔍 Approving loan with user ID: ${user.uid}');
      _addResult(
          '🔍 Using account: ${mapping.cashAccount.displayName} (${mapping.cashAccount.id})');
      final approveResult =
          await loanService.approveLoanRequest(loanId, mapping.cashAccount.id);

      approveResult.fold(
        (failure) {
          _addResult('❌ Failed to approve loan: ${failure.message}');
          return;
        },
        (success) {
          _addResult('✅ Successfully approved loan: $loanId');
          _addResult(
              '🔍 Loan approval should trigger journal entry creation with user ID: ${user.uid}');
        },
      );

      // Step 4: Wait for journal entries to be created
      _addResult('');
      _addResult('📋 Step 4: Waiting for Journal Entries...');
      await Future.delayed(const Duration(seconds: 3));

      // Step 5: Check for journal entries
      _addResult('');
      _addResult('📋 Step 5: Checking Journal Entries...');

      // Import the journal entry service
      final journalService = JournalEntryFirebaseService();
      final journalEntries = await journalService.getJournalEntriesBySource(
        loanId,
        'loan_disbursement',
      );

      if (journalEntries.isEmpty) {
        // Also check for loan_receipt entries
        final receiptEntries = await journalService.getJournalEntriesBySource(
          loanId,
          'loan_receipt',
        );

        if (receiptEntries.isEmpty) {
          _addResult('❌ No journal entries found for loan: $loanId');
          _addResult('⚠️ Journal entry creation failed');
        } else {
          _addResult(
              '✅ Found ${receiptEntries.length} loan receipt journal entries');
          _showJournalEntryDetails(receiptEntries);
        }
      } else {
        _addResult(
            '✅ Found ${journalEntries.length} loan disbursement journal entries');
        _showJournalEntryDetails(journalEntries);
      }

      // Step 6: Check account balances
      _addResult('');
      _addResult('📋 Step 6: Checking Account Balances...');
      final updatedAccounts = await _chartService.getAccounts();

      for (final account in updatedAccounts) {
        if (account.id == mapping.loansReceivableAccount.id ||
            account.id == mapping.loansPayableAccount.id ||
            account.id == mapping.cashAccount.id) {
          _addResult('💰 ${account.displayName}: ${account.balance}');
        }
      }

      _addResult('');
      _addResult('🎉 Full loan approval workflow test completed!');
    } catch (e) {
      _addResult('❌ Full workflow test failed: $e');
    } finally {
      setState(() => _isRunning = false);
    }
  }

  void _showJournalEntryDetails(List<JournalEntryModel> entries) {
    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      _addResult('');
      _addResult('📝 Journal Entry #${i + 1}:');
      _addResult('   ID: ${entry.id}');
      _addResult('   Number: ${entry.entryNumber}');
      _addResult('   Description: ${entry.description}');
      _addResult('   Type: ${entry.sourceTransactionType}');
      _addResult('   Date: ${entry.entryDate}');
      _addResult('   Status: ${entry.status.name}');
      _addResult('   Total Debits: ${entry.totalDebits}');
      _addResult('   Total Credits: ${entry.totalCredits}');
      _addResult('   Lines: ${entry.lines.length}');

      for (int j = 0; j < entry.lines.length; j++) {
        final line = entry.lines[j];
        _addResult('   - Line #${j + 1}: ${line.accountName}');
        _addResult(
            '     Debit: ${line.debitAmount}, Credit: ${line.creditAmount}');
        _addResult('     Description: ${line.description}');
      }
    }
  }
}
