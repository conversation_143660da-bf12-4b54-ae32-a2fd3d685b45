import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'lib/models/finance/journal_entry_model.dart';
import 'lib/models/finance/chart_of_accounts_model.dart';
import 'lib/core/services/account_type_helper_service.dart';
import 'lib/features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import 'lib/firebase_service/accounting/journal_entry_firebase_service.dart';

/// Targeted fix for loan receivable running balance calculation issues
/// Specifically addresses the problem where loan receivable accounts show negative balances
/// when they should show positive balances after debit entries
void main() {
  runApp(const LoanReceivableRunningBalanceFixApp());
}

class LoanReceivableRunningBalanceFixApp extends StatelessWidget {
  const LoanReceivableRunningBalanceFixApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Loan Receivable Running Balance Fix',
      theme: ThemeData(primarySwatch: Colors.red),
      home: const LoanReceivableRunningBalanceFixScreen(),
    );
  }
}

class LoanReceivableRunningBalanceFixScreen extends StatefulWidget {
  const LoanReceivableRunningBalanceFixScreen({super.key});

  @override
  State<LoanReceivableRunningBalanceFixScreen> createState() =>
      _LoanReceivableRunningBalanceFixScreenState();
}

class _LoanReceivableRunningBalanceFixScreenState
    extends State<LoanReceivableRunningBalanceFixScreen> {
  final TextEditingController _companyUidController = TextEditingController();
  final TextEditingController _accountIdController = TextEditingController();
  bool _isRunning = false;
  final List<String> _logs = [];
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Loan Receivable Running Balance Fix'),
        backgroundColor: Colors.red[700],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Loan Receivable Running Balance Fix',
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.red[800],
                              ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This tool specifically fixes the running balance calculation issue where:\n'
                      '• Loan receivable accounts show negative balances when debited\n'
                      '• Running balances don\'t update correctly in chronological order\n'
                      '• Current balance doesn\'t reflect proper cumulative balance\n\n'
                      'The fix ensures Asset accounts show positive balances when debited (loan approved).',
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _companyUidController,
                      decoration: const InputDecoration(
                        labelText: 'Company UID',
                        hintText: 'Enter the company UID',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _accountIdController,
                      decoration: const InputDecoration(
                        labelText: 'Loan Receivable Account ID (optional)',
                        hintText: 'Leave empty to auto-find',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed:
                              _isRunning ? null : _diagnoseRunningBalanceIssue,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[700],
                          ),
                          child: const Text('1. Diagnose Issue'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _fixRunningBalances,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange[700],
                          ),
                          child: const Text('2. Fix Running Balances'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _verifyFix,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green[700],
                          ),
                          child: const Text('3. Verify Fix'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _isRunning ? null : _runCompleteFix,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[700],
                        minimumSize: const Size(double.infinity, 48),
                      ),
                      child: _isRunning
                          ? const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                ),
                                SizedBox(width: 12),
                                Text('Fixing...'),
                              ],
                            )
                          : const Text(
                              'RUN COMPLETE RUNNING BALANCE FIX',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Fix Log:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _logs.isEmpty
                    ? const Center(
                        child: Text(
                          'No operations run yet. Click a button above to start.',
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(8),
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          final log = _logs[index];
                          Color textColor = Colors.black;
                          if (log.contains('✅')) textColor = Colors.green;
                          if (log.contains('❌')) textColor = Colors.red;
                          if (log.contains('⚠️')) textColor = Colors.orange;
                          if (log.contains('🔍')) textColor = Colors.blue;
                          if (log.contains('🔧')) textColor = Colors.purple;

                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 1),
                            child: Text(
                              log,
                              style: TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 11,
                                color: textColor,
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)} $message');
    });
    dev.log(message);
  }

  String _getCompanyUid() {
    final uid = _companyUidController.text.trim();
    if (uid.isEmpty) {
      _addLog('❌ Please enter a company UID');
      return '';
    }
    return uid;
  }

  Future<void> _diagnoseRunningBalanceIssue() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
    });

    try {
      _addLog(
          '🔍 Diagnosing loan receivable running balance issue for company: $uid');

      // Find loan receivable accounts
      final accountsRepository = Get.find<ChartOfAccountsRepository>();
      final accountsResult = await accountsRepository.getAccounts();

      await accountsResult.fold(
        (failure) async {
          _addLog('❌ Failed to load accounts: ${failure.message}');
        },
        (accounts) async {
          final loanReceivableAccounts = accounts
              .where((account) =>
                  account.accountName.toLowerCase().contains('loan') &&
                  account.accountName.toLowerCase().contains('receivable'))
              .toList();

          if (loanReceivableAccounts.isEmpty) {
            _addLog('⚠️ No loan receivable accounts found by name');
            return;
          }

          _addLog(
              '✅ Found ${loanReceivableAccounts.length} loan receivable accounts');

          for (final account in loanReceivableAccounts) {
            await _diagnoseSpecificAccount(account, uid);
          }
        },
      );
    } catch (e) {
      _addLog('❌ Error during diagnosis: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _diagnoseSpecificAccount(
      ChartOfAccountsModel account, String uid) async {
    _addLog('');
    _addLog('🔍 Diagnosing account: ${account.accountName}');
    _addLog('   Account ID: ${account.id}');
    _addLog('   Account Type: ${account.accountType.displayName}');
    _addLog('   Category: ${account.category.displayName}');
    _addLog('   Current Balance: ${account.balance}');

    // Get journal entries for this account
    try {
      final entriesSnapshot = await _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .where('status', isEqualTo: 'posted')
          .orderBy('createdAt')
          .get();

      final accountEntries = <JournalEntryModel>[];
      for (final doc in entriesSnapshot.docs) {
        try {
          final entry = JournalEntryModel.fromFirestore(doc);
          final hasAccountLine =
              entry.lines.any((line) => line.accountId == account.id);
          if (hasAccountLine) {
            accountEntries.add(entry);
          }
        } catch (e) {
          _addLog('   ⚠️ Error parsing entry ${doc.id}: $e');
        }
      }

      _addLog(
          '   📊 Found ${accountEntries.length} journal entries for this account');

      if (accountEntries.isEmpty) {
        _addLog('   ⚠️ No journal entries found - account balance should be 0');
        return;
      }

      // Analyze running balance progression
      double expectedRunningBalance = 0.0;
      _addLog('   📝 Analyzing running balance progression:');

      for (int i = 0; i < accountEntries.length; i++) {
        final entry = accountEntries[i];
        final accountLines =
            entry.lines.where((line) => line.accountId == account.id).toList();

        for (final line in accountLines) {
          // Calculate expected balance change
          final balanceChange = AccountTypeHelperService.calculateBalanceChange(
            accountType: account.accountType,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
          );

          expectedRunningBalance += balanceChange;

          _addLog('     Entry ${i + 1}: ${entry.entryNumber}');
          _addLog(
              '       Date: ${entry.entryDate.toString().substring(0, 10)}');
          _addLog(
              '       Debit: ${line.debitAmount}, Credit: ${line.creditAmount}');
          _addLog(
              '       Balance Change: ${balanceChange >= 0 ? '+' : ''}$balanceChange');
          _addLog('       Expected Running Balance: $expectedRunningBalance');
          _addLog(
              '       Stored Running Balance: ${line.runningBalance ?? 'N/A'}');

          // Check for issues
          if (line.runningBalance == null) {
            _addLog('       ❌ ISSUE: No running balance stored');
          } else if ((line.runningBalance! - expectedRunningBalance).abs() >
              0.01) {
            _addLog('       ❌ ISSUE: Running balance mismatch');
            _addLog('         Expected: $expectedRunningBalance');
            _addLog('         Stored: ${line.runningBalance}');
          } else {
            _addLog('       ✅ CORRECT: Running balance matches expected value');
          }

          // Check for negative balance on debit entries for Asset accounts
          if (line.debitAmount > 0 &&
              account.category == AccountCategory.assets &&
              (line.runningBalance ?? 0) < 0) {
            _addLog(
                '       ❌ CRITICAL ISSUE: Asset account shows negative balance after debit');
          }
        }
      }

      // Compare final expected balance with stored account balance
      _addLog('   💰 Final balance comparison:');
      _addLog('     Expected final balance: $expectedRunningBalance');
      _addLog('     Stored account balance: ${account.balance}');

      final balanceDifference =
          (account.balance - expectedRunningBalance).abs();
      if (balanceDifference > 0.01) {
        _addLog(
            '     ❌ ISSUE: Account balance doesn\'t match calculated balance');
        _addLog('     Difference: $balanceDifference');
      } else {
        _addLog('     ✅ CORRECT: Account balance matches calculated balance');
      }
    } catch (e) {
      _addLog('   ❌ Error analyzing journal entries: $e');
    }
  }

  Future<void> _fixRunningBalances() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
    });

    try {
      _addLog('🔧 Starting running balance fix for company: $uid');

      // Find loan receivable accounts
      final accountsRepository = Get.find<ChartOfAccountsRepository>();
      final accountsResult = await accountsRepository.getAccounts();

      await accountsResult.fold(
        (failure) async {
          _addLog('❌ Failed to load accounts: ${failure.message}');
        },
        (accounts) async {
          final loanReceivableAccounts = accounts
              .where((account) =>
                  account.accountName.toLowerCase().contains('loan') &&
                  account.accountName.toLowerCase().contains('receivable'))
              .toList();

          for (final account in loanReceivableAccounts) {
            await _fixAccountRunningBalances(account, uid);
          }
        },
      );
    } catch (e) {
      _addLog('❌ Error during running balance fix: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _fixAccountRunningBalances(
      ChartOfAccountsModel account, String uid) async {
    _addLog('');
    _addLog('🔧 Fixing running balances for: ${account.accountName}');

    try {
      // Get all journal entries for this account in chronological order
      final entriesSnapshot = await _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .where('status', isEqualTo: 'posted')
          .orderBy('createdAt')
          .get();

      final accountEntries = <JournalEntryModel>[];
      for (final doc in entriesSnapshot.docs) {
        try {
          final entry = JournalEntryModel.fromFirestore(doc);
          final hasAccountLine =
              entry.lines.any((line) => line.accountId == account.id);
          if (hasAccountLine) {
            accountEntries.add(entry);
          }
        } catch (e) {
          _addLog('   ⚠️ Error parsing entry ${doc.id}: $e');
        }
      }

      if (accountEntries.isEmpty) {
        _addLog('   ⚠️ No journal entries found for this account');
        return;
      }

      // Recalculate running balances chronologically
      double runningBalance = 0.0;
      int entriesFixed = 0;

      for (final entry in accountEntries) {
        final accountLines =
            entry.lines.where((line) => line.accountId == account.id).toList();
        bool entryNeedsUpdate = false;
        final updatedLines = <JournalEntryLineModel>[];

        // Process all lines in the entry
        for (final line in entry.lines) {
          if (line.accountId == account.id) {
            // Calculate correct balance change
            final balanceChange =
                AccountTypeHelperService.calculateBalanceChange(
              accountType: account.accountType,
              debitAmount: line.debitAmount,
              creditAmount: line.creditAmount,
            );

            runningBalance += balanceChange;

            // Check if running balance needs update
            if (line.runningBalance == null ||
                (line.runningBalance! - runningBalance).abs() > 0.01) {
              entryNeedsUpdate = true;
              updatedLines.add(line.copyWith(runningBalance: runningBalance));
              _addLog(
                  '   🔧 Updating running balance: ${line.runningBalance} → $runningBalance');
            } else {
              updatedLines.add(line);
            }
          } else {
            updatedLines.add(line);
          }
        }

        // Update journal entry if needed
        if (entryNeedsUpdate) {
          await _firestore.collection('journal_entries').doc(entry.id).update({
            'lines': updatedLines.map((line) => line.toFirestore()).toList(),
            'updatedAt': Timestamp.fromDate(DateTime.now()),
            'runningBalanceFixNote':
                'Running balances corrected for loan receivable account',
          });

          entriesFixed++;
        }
      }

      // Update account balance to match final running balance
      await _firestore
          .collection('chart_of_accounts')
          .where('id', isEqualTo: account.id)
          .where('uid', isEqualTo: uid)
          .get()
          .then((snapshot) async {
        if (snapshot.docs.isNotEmpty) {
          await snapshot.docs.first.reference.update({
            'balance': runningBalance,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
            'balanceFixNote':
                'Balance corrected during running balance fix - was ${account.balance}, corrected to $runningBalance',
          });

          _addLog(
              '   ✅ Updated account balance: ${account.balance} → $runningBalance');
        }
      });

      _addLog(
          '   ✅ Fixed $entriesFixed journal entries for ${account.accountName}');
      _addLog('   💰 Final running balance: $runningBalance');
    } catch (e) {
      _addLog('   ❌ Error fixing running balances: $e');
    }
  }

  Future<void> _verifyFix() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
    });

    try {
      _addLog('🔍 Verifying fix for company: $uid');

      // Re-run diagnosis to see if issues are resolved
      await _diagnoseRunningBalanceIssue();
    } catch (e) {
      _addLog('❌ Error during verification: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _runCompleteFix() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    _addLog('🚀 Starting complete loan receivable running balance fix...');

    // Step 1: Diagnose the issue
    await _diagnoseRunningBalanceIssue();

    // Step 2: Fix running balances
    await _fixRunningBalances();

    // Step 3: Verify the fix
    await _verifyFix();

    _addLog('');
    _addLog('🎉 Complete loan receivable running balance fix finished!');
    _addLog('📋 Expected results:');
    _addLog(
        '   • Loan receivable accounts show positive balances when debited');
    _addLog('   • Running balances update correctly in chronological order');
    _addLog('   • Current balance reflects proper cumulative balance');
    _addLog('   • All balance calculations follow Asset account principles');
  }

  @override
  void dispose() {
    _companyUidController.dispose();
    _accountIdController.dispose();
    super.dispose();
  }
}
