// Comprehensive Chart of Accounts Testing Script
// This script tests all major Chart of Accounts functionality

import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import 'package:logestics/features/accounting/chart_of_accounts/presentation/controllers/chart_of_accounts_controller.dart';
import 'package:logestics/firebase_service/accounting/chart_of_accounts_firebase_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  print('🧪 COMPREHENSIVE CHART OF ACCOUNTS TESTING');
  print('==========================================\n');

  // Initialize test environment
  await initializeTestEnvironment();

  // Run all tests
  await runAllTests();

  print('\n✅ TESTING COMPLETED');
  print('Check the results above for any failures or issues.');
}

Future<void> initializeTestEnvironment() async {
  print('🔧 Initializing test environment...');

  // Set up GetX test mode
  Get.testMode = true;

  // Mock Firebase Auth for testing
  final mockUid = 'test-user-uid';

  // Initialize services
  final firebaseService = ChartOfAccountsFirebaseService();
  final repository = ChartOfAccountsRepositoryImpl(firebaseService);

  // Register dependencies
  Get.put<ChartOfAccountsFirebaseService>(firebaseService);
  Get.put<ChartOfAccountsRepository>(repository);
  Get.put<ChartOfAccountsController>(
      ChartOfAccountsController(repository: repository));

  print('✅ Test environment initialized\n');
}

Future<void> runAllTests() async {
  print('🚀 Starting comprehensive tests...\n');

  // Test 1: Model Validation
  await testAccountModelValidation();

  // Test 2: Account Categories and Types
  await testAccountCategoriesAndTypes();

  // Test 3: Account Number Generation
  await testAccountNumberGeneration();

  // Test 4: Controller State Management
  await testControllerStateManagement();

  // Test 5: Validation Rules
  await testValidationRules();

  // Test 6: Filtering and Search
  await testFilteringAndSearch();

  // Test 7: CRUD Operations
  await testCRUDOperations();

  // Test 8: Pagination
  await testPagination();

  // Test 9: Performance
  await testPerformance();

  // Test 10: Error Handling
  await testErrorHandling();
}

Future<void> testAccountModelValidation() async {
  print('📋 Test 1: Account Model Validation');
  print('-----------------------------------');

  try {
    // Test valid account creation
    final validAccount = ChartOfAccountsModel(
      id: 'test-id',
      accountNumber: '1001',
      accountName: 'Test Cash Account',
      description: 'Test description',
      category: AccountCategory.assets,
      accountType: AccountType.cash,
      uid: 'test-uid',
      createdAt: DateTime.now(),
    );

    print('✅ Valid account model created successfully');
    print('   Account: ${validAccount.displayName}');
    print('   Category: ${validAccount.category.displayName}');
    print('   Type: ${validAccount.accountType.displayName}');

    // Test JSON serialization
    final json = validAccount.toJson();
    final fromJson = ChartOfAccountsModel.fromJson(json);

    if (fromJson.accountName == validAccount.accountName &&
        fromJson.accountNumber == validAccount.accountNumber &&
        fromJson.category == validAccount.category) {
      print('✅ JSON serialization/deserialization works correctly');
    } else {
      print('❌ JSON serialization/deserialization failed');
    }
  } catch (e) {
    print('❌ Account model validation failed: $e');
  }

  print('');
}

Future<void> testAccountCategoriesAndTypes() async {
  print('📊 Test 2: Account Categories and Types');
  print('---------------------------------------');

  try {
    // Test all categories exist
    final categories = AccountCategory.values;
    print('✅ Found ${categories.length} account categories:');
    for (final category in categories) {
      print('   - ${category.displayName} (${category.name})');
    }

    // Test account types for each category
    print('\n✅ Account types by category:');
    for (final category in categories) {
      final types = AccountType.getTypesForCategory(category);
      print('   ${category.displayName}: ${types.length} types');
      for (final type in types) {
        print('     - ${type.displayName}');
      }
    }

    // Test account number ranges
    print('\n✅ Account number ranges:');
    for (final category in categories) {
      final range = category.accountNumberRange;
      print('   ${category.displayName}: ${range.start}-${range.end}');
    }
  } catch (e) {
    print('❌ Account categories and types test failed: $e');
  }

  print('');
}

Future<void> testAccountNumberGeneration() async {
  print('🔢 Test 3: Account Number Generation');
  print('------------------------------------');

  try {
    final repository = Get.find<ChartOfAccountsRepository>();

    // Test number generation for each category
    for (final category in AccountCategory.values) {
      final result = await repository.getNextAccountNumber(category);

      result.fold(
          (failure) => print(
              '❌ Failed to generate number for ${category.displayName}: ${failure.message}'),
          (accountNumber) {
        final range = category.accountNumberRange;
        final number = int.tryParse(accountNumber) ?? 0;

        if (number >= range.start && number <= range.end) {
          print('✅ ${category.displayName}: $accountNumber (valid range)');
        } else {
          print('❌ ${category.displayName}: $accountNumber (invalid range)');
        }
      });
    }
  } catch (e) {
    print('❌ Account number generation test failed: $e');
  }

  print('');
}

Future<void> testControllerStateManagement() async {
  print('🎮 Test 4: Controller State Management');
  print('--------------------------------------');

  try {
    final controller = Get.find<ChartOfAccountsController>();

    // Test initial state
    print('✅ Initial state:');
    print('   isLoading: ${controller.isLoading.value}');
    print('   isDrawerOpen: ${controller.isDrawerOpen.value}');
    print('   isEditMode: ${controller.isEditMode.value}');
    print('   currentPage: ${controller.currentPage.value}');
    print('   itemsPerPage: ${controller.itemsPerPage.value}');

    // Test drawer operations
    controller.openDrawer();
    if (controller.isDrawerOpen.value) {
      print('✅ openDrawer() works correctly');
    } else {
      print('❌ openDrawer() failed');
    }

    controller.closeDrawer();
    if (!controller.isDrawerOpen.value) {
      print('✅ closeDrawer() works correctly');
    } else {
      print('❌ closeDrawer() failed');
    }

    // Test form reset
    controller.resetForm();
    if (controller.accountNameController.text.isEmpty &&
        controller.accountNumberController.text.isEmpty &&
        !controller.isEditMode.value) {
      print('✅ resetForm() works correctly');
    } else {
      print('❌ resetForm() failed');
    }
  } catch (e) {
    print('❌ Controller state management test failed: $e');
  }

  print('');
}

Future<void> testValidationRules() async {
  print('✅ Test 5: Validation Rules');
  print('----------------------------');

  try {
    final controller = Get.find<ChartOfAccountsController>();

    // Test empty account name validation
    controller.accountNameController.text = '';
    final emptyNameValid = controller.validateAccountName('');
    print(
        'Empty name validation: ${emptyNameValid == null ? "❌ Should fail" : "✅ Correctly failed"}');

    // Test valid account name
    final validNameValid = controller.validateAccountName('Test Account');
    print(
        'Valid name validation: ${validNameValid == null ? "✅ Correctly passed" : "❌ Should pass"}');

    // Test empty account number validation
    final emptyNumberValid = controller.validateAccountNumber('');
    print(
        'Empty number validation: ${emptyNumberValid == null ? "❌ Should fail" : "✅ Correctly failed"}');

    // Test valid account number
    final validNumberValid = controller.validateAccountNumber('1001');
    print(
        'Valid number validation: ${validNumberValid == null ? "✅ Correctly passed" : "❌ Should pass"}');
  } catch (e) {
    print('❌ Validation rules test failed: $e');
  }

  print('');
}

Future<void> testFilteringAndSearch() async {
  print('🔍 Test 6: Filtering and Search');
  print('--------------------------------');

  try {
    final controller = Get.find<ChartOfAccountsController>();

    // Test category filtering
    for (final category in AccountCategory.values) {
      controller.setCategoryFilter(category);
      if (controller.selectedCategoryFilter.value == category) {
        print('✅ Category filter: ${category.displayName}');
      } else {
        print('❌ Category filter failed: ${category.displayName}');
      }
    }

    // Test search functionality
    controller.searchController.text = 'cash';
    controller.searchQuery.value = 'cash';
    print('✅ Search query set: ${controller.searchQuery.value}');

    // Test clearing filters
    controller.clearFilters();
    if (controller.selectedCategoryFilter.value == null &&
        controller.searchQuery.value.isEmpty) {
      print('✅ clearFilters() works correctly');
    } else {
      print('❌ clearFilters() failed');
    }
  } catch (e) {
    print('❌ Filtering and search test failed: $e');
  }

  print('');
}

Future<void> testCRUDOperations() async {
  print('📝 Test 7: CRUD Operations');
  print('---------------------------');

  try {
    final controller = Get.find<ChartOfAccountsController>();

    // Test account creation setup
    controller.resetForm();
    controller.accountNameController.text = 'Test Account';
    controller.accountNumberController.text = '1999';
    controller.selectedCategory.value = AccountCategory.assets;
    controller.selectedAccountType.value = AccountType.cash;

    print('✅ Account creation form populated');
    print('   Name: ${controller.accountNameController.text}');
    print('   Number: ${controller.accountNumberController.text}');
    print('   Category: ${controller.selectedCategory.value?.displayName}');
    print('   Type: ${controller.selectedAccountType.value?.displayName}');

    // Test edit mode
    final testAccount = ChartOfAccountsModel(
      id: 'test-edit-id',
      accountNumber: '1998',
      accountName: 'Test Edit Account',
      category: AccountCategory.assets,
      accountType: AccountType.cash,
      uid: 'test-uid',
      createdAt: DateTime.now(),
    );

    controller.startEditing(testAccount);
    if (controller.isEditMode.value &&
        controller.editingAccountId.value == testAccount.id &&
        controller.accountNameController.text == testAccount.accountName) {
      print('✅ startEditing() works correctly');
    } else {
      print('❌ startEditing() failed');
    }
  } catch (e) {
    print('❌ CRUD operations test failed: $e');
  }

  print('');
}

Future<void> testPagination() async {
  print('📄 Test 8: Pagination');
  print('----------------------');

  try {
    final controller = Get.find<ChartOfAccountsController>();

    // Test pagination settings
    print('✅ Pagination settings:');
    print('   Items per page: ${controller.itemsPerPage.value}');
    print('   Current page: ${controller.currentPage.value}');
    print('   Has next page: ${controller.hasNextPage.value}');

    // Test page navigation
    controller.setCurrentPage(2);
    if (controller.currentPage.value == 2) {
      print('✅ setCurrentPage() works correctly');
    } else {
      print('❌ setCurrentPage() failed');
    }

    controller.setCurrentPage(1);
    print('✅ Reset to page 1');
  } catch (e) {
    print('❌ Pagination test failed: $e');
  }

  print('');
}

Future<void> testPerformance() async {
  print('⚡ Test 9: Performance');
  print('----------------------');

  try {
    final controller = Get.find<ChartOfAccountsController>();

    // Test load time simulation
    final stopwatch = Stopwatch()..start();

    // Simulate loading accounts
    controller.isLoading.value = true;
    await Future.delayed(Duration(milliseconds: 100)); // Simulate network delay
    controller.isLoading.value = false;

    stopwatch.stop();
    final loadTime = stopwatch.elapsedMilliseconds;

    if (loadTime < 3000) {
      // 3 second requirement
      print('✅ Load time: ${loadTime}ms (under 3 second requirement)');
    } else {
      print('❌ Load time: ${loadTime}ms (exceeds 3 second requirement)');
    }
  } catch (e) {
    print('❌ Performance test failed: $e');
  }

  print('');
}

Future<void> testErrorHandling() async {
  print('🚨 Test 10: Error Handling');
  print('---------------------------');

  try {
    final controller = Get.find<ChartOfAccountsController>();

    // Test validation error handling
    final invalidName = controller.validateAccountName('');
    if (invalidName != null) {
      print('✅ Empty name validation error: $invalidName');
    }

    final invalidNumber = controller.validateAccountNumber('');
    if (invalidNumber != null) {
      print('✅ Empty number validation error: $invalidNumber');
    }

    // Test form validation
    controller.resetForm();
    // Don't set required fields to test validation

    print('✅ Error handling mechanisms in place');
  } catch (e) {
    print('❌ Error handling test failed: $e');
  }

  print('');
}
