import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/enums/broker_enums.dart';
import 'package:logestics/firebase_service/voucher/voucher_crud_firebase_service.dart';
import 'package:logestics/models/voucher_model.dart';

/// Service responsible for automatically updating voucher status based on payment completion
/// Monitors payment transactions and updates voucher status to "Completed" when fully paid
class VoucherStatusAutomationService {
  final VoucherCrudFirebaseService _voucherFirebaseService;

  VoucherStatusAutomationService({
    VoucherCrudFirebaseService? voucherFirebaseService,
  }) : _voucherFirebaseService = voucherFirebaseService ?? VoucherCrudFirebaseService();

  /// Check if voucher should be automatically marked as complete based on payment status
  /// This is called after each payment transaction is processed
  Future<bool> checkAndUpdateVoucherStatus({
    required String voucherNumber,
    required String uid,
  }) async {
    try {
      log('🔍 VoucherStatusAutomation: Checking payment completion for voucher: $voucherNumber');

      // Get current voucher data
      final voucherData = await _voucherFirebaseService.getVoucherByNumber(
        uid: uid,
        voucherNumber: voucherNumber,
      );

      if (voucherData == null) {
        log('❌ VoucherStatusAutomation: Voucher not found: $voucherNumber');
        return false;
      }

      // Convert to VoucherModel to use the pendingAmount calculation
      final voucher = VoucherModel.fromJson(voucherData);
      
      log('📊 VoucherStatusAutomation: Voucher $voucherNumber - Total: ${voucher.totalFreight}, Settled: ${voucher.settledFreight}, Pending: ${voucher.pendingAmount}');

      // Check if voucher is already completed
      if (voucher.voucherStatus.toLowerCase() == VoucherStatus.completed.value.toLowerCase()) {
        log('✅ VoucherStatusAutomation: Voucher $voucherNumber already marked as completed');
        return true;
      }

      // Check if pending amount is zero or negative (fully paid)
      if (voucher.pendingAmount <= 0) {
        log('🎉 VoucherStatusAutomation: Voucher $voucherNumber is fully paid! Updating status to completed...');
        
        // Update voucher status to completed
        final success = await _updateVoucherStatusToCompleted(
          voucherNumber: voucherNumber,
          uid: uid,
          voucher: voucher,
        );

        if (success) {
          log('✅ VoucherStatusAutomation: Successfully updated voucher $voucherNumber status to completed');
          return true;
        } else {
          log('❌ VoucherStatusAutomation: Failed to update voucher $voucherNumber status');
          return false;
        }
      } else {
        log('📋 VoucherStatusAutomation: Voucher $voucherNumber still has pending amount: ${voucher.pendingAmount}');
        return true; // No error, just not ready for completion
      }
    } catch (e) {
      log('❌ VoucherStatusAutomation: Error checking voucher status for $voucherNumber: $e');
      return false;
    }
  }

  /// Update voucher status to completed atomically
  Future<bool> _updateVoucherStatusToCompleted({
    required String voucherNumber,
    required String uid,
    required VoucherModel voucher,
  }) async {
    try {
      log('🔄 VoucherStatusAutomation: Updating voucher $voucherNumber status to completed...');

      // Create updated voucher data with completed status
      final updatedVoucherData = voucher.toJson();
      updatedVoucherData['voucherStatus'] = VoucherStatus.completed.value;
      updatedVoucherData['updatedAt'] = DateTime.now().millisecondsSinceEpoch;
      updatedVoucherData['completedAt'] = DateTime.now().millisecondsSinceEpoch; // Track completion time

      // Update voucher in Firebase
      await _voucherFirebaseService.updateVoucherToFirebase(
        uid: uid,
        voucher: updatedVoucherData,
      );

      log('✅ VoucherStatusAutomation: Successfully updated voucher $voucherNumber to completed status');
      return true;
    } catch (e) {
      log('❌ VoucherStatusAutomation: Error updating voucher status: $e');
      return false;
    }
  }

  /// Check multiple vouchers for completion status (batch processing)
  /// Useful for periodic checks or system maintenance
  Future<List<String>> checkMultipleVouchersForCompletion({
    required List<String> voucherNumbers,
    required String uid,
  }) async {
    final completedVouchers = <String>[];

    for (final voucherNumber in voucherNumbers) {
      try {
        final success = await checkAndUpdateVoucherStatus(
          voucherNumber: voucherNumber,
          uid: uid,
        );

        if (success) {
          // Check if voucher was actually completed
          final voucherData = await _voucherFirebaseService.getVoucherByNumber(
            uid: uid,
            voucherNumber: voucherNumber,
          );

          if (voucherData != null) {
            final voucher = VoucherModel.fromJson(voucherData);
            if (voucher.voucherStatus.toLowerCase() == VoucherStatus.completed.value.toLowerCase()) {
              completedVouchers.add(voucherNumber);
            }
          }
        }
      } catch (e) {
        log('❌ VoucherStatusAutomation: Error processing voucher $voucherNumber in batch: $e');
      }
    }

    log('📊 VoucherStatusAutomation: Batch processing completed. ${completedVouchers.length} vouchers marked as completed');
    return completedVouchers;
  }

  /// Get voucher completion statistics
  Future<VoucherCompletionStats> getCompletionStats({required String uid}) async {
    try {
      // This would require additional Firebase queries to get statistics
      // For now, return basic stats structure
      return VoucherCompletionStats(
        totalVouchers: 0,
        completedVouchers: 0,
        pendingVouchers: 0,
        autoCompletedToday: 0,
      );
    } catch (e) {
      log('❌ VoucherStatusAutomation: Error getting completion stats: $e');
      return VoucherCompletionStats(
        totalVouchers: 0,
        completedVouchers: 0,
        pendingVouchers: 0,
        autoCompletedToday: 0,
      );
    }
  }

  /// Validate that a voucher is eligible for automatic completion
  bool _isVoucherEligibleForAutoCompletion(VoucherModel voucher) {
    // Check if voucher is in a valid state for auto-completion
    if (voucher.voucherStatus.toLowerCase() == VoucherStatus.cancelled.value.toLowerCase()) {
      return false; // Don't auto-complete cancelled vouchers
    }

    // Check if total freight is valid
    if (voucher.totalFreight <= 0) {
      return false; // Invalid voucher data
    }

    return true;
  }
}

/// Statistics for voucher completion tracking
class VoucherCompletionStats {
  final int totalVouchers;
  final int completedVouchers;
  final int pendingVouchers;
  final int autoCompletedToday;

  VoucherCompletionStats({
    required this.totalVouchers,
    required this.completedVouchers,
    required this.pendingVouchers,
    required this.autoCompletedToday,
  });

  double get completionRate => totalVouchers > 0 ? (completedVouchers / totalVouchers) * 100 : 0.0;
}
