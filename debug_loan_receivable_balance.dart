import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'lib/models/finance/chart_of_accounts_model.dart';
import 'lib/models/finance/journal_entry_model.dart';
import 'lib/core/services/account_type_helper_service.dart';
import 'lib/features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import 'lib/firebase_service/accounting/journal_entry_firebase_service.dart';

/// Debug utility to investigate loan receivable account balance calculation issues
void main() {
  runApp(const LoanReceivableDebugApp());
}

class LoanReceivableDebugApp extends StatelessWidget {
  const LoanReceivableDebugApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Loan Receivable Balance Debug',
      theme: ThemeData(primarySwatch: Colors.red),
      home: const LoanReceivableDebugScreen(),
    );
  }
}

class LoanReceivableDebugScreen extends StatefulWidget {
  const LoanReceivableDebugScreen({super.key});

  @override
  State<LoanReceivableDebugScreen> createState() =>
      _LoanReceivableDebugScreenState();
}

class _LoanReceivableDebugScreenState extends State<LoanReceivableDebugScreen> {
  final TextEditingController _companyUidController = TextEditingController();
  final TextEditingController _accountIdController = TextEditingController();
  bool _isRunning = false;
  final List<String> _logs = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Loan Receivable Balance Debug'),
        backgroundColor: Colors.red[700],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Loan Receivable Account Balance Investigation',
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.red[800],
                              ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This tool investigates why loan receivable accounts show negative balances '
                      'when they should show positive balances after debit entries. '
                      'Asset accounts should increase with debits according to accounting principles.',
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _companyUidController,
                      decoration: const InputDecoration(
                        labelText: 'Company UID',
                        hintText: 'Enter the company UID',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _accountIdController,
                      decoration: const InputDecoration(
                        labelText: 'Loan Receivable Account ID (optional)',
                        hintText: 'Leave empty to auto-find',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _isRunning ? null : _runDiagnostics,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red[700],
                          ),
                          child: _isRunning
                              ? const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    ),
                                    SizedBox(width: 8),
                                    Text('Running...'),
                                  ],
                                )
                              : const Text('Run Diagnostics'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed:
                              _isRunning ? null : _testBalanceCalculation,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[700],
                          ),
                          child: const Text('Test Balance Logic'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _simulateLoanApproval,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green[700],
                          ),
                          child: const Text('Simulate Loan'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Diagnostic Log:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _logs.isEmpty
                    ? const Center(
                        child: Text(
                          'No diagnostics run yet. Click a button above to start.',
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(8),
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          final log = _logs[index];
                          Color textColor = Colors.black;
                          if (log.contains('✅')) textColor = Colors.green;
                          if (log.contains('❌')) textColor = Colors.red;
                          if (log.contains('⚠️')) textColor = Colors.orange;
                          if (log.contains('🔍')) textColor = Colors.blue;
                          if (log.contains('💰')) textColor = Colors.purple;

                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 1),
                            child: Text(
                              log,
                              style: TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 11,
                                color: textColor,
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)} $message');
    });
    dev.log(message);
  }

  String _getCompanyUid() {
    final uid = _companyUidController.text.trim();
    if (uid.isEmpty) {
      _addLog('❌ Please enter a company UID');
      return '';
    }
    return uid;
  }

  Future<void> _runDiagnostics() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
    });

    try {
      _addLog(
          '🔍 Starting comprehensive loan balance diagnostics for company: $uid');

      // Step 1: Find loan receivable account
      await _findLoanReceivableAccount(uid);

      // Step 2: Analyze account configuration
      await _analyzeAccountConfiguration(uid);

      // Step 3: Check journal entries
      await _checkJournalEntries(uid);

      // Step 4: Verify balance calculation
      await _verifyBalanceCalculation(uid);

      // Step 5: Check manual loan workflow
      await _checkManualLoanWorkflow(uid);

      // Step 6: Check voucher-based loan workflow
      await _checkVoucherLoanWorkflow(uid);

      // Step 7: Check balance synchronization
      await _checkBalanceSynchronization(uid);

      // Step 8: Check running balance updates
      await _checkRunningBalanceUpdates(uid);

      _addLog('✅ Comprehensive diagnostics completed');
    } catch (e) {
      _addLog('❌ Error during diagnostics: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _findLoanReceivableAccount(String uid) async {
    _addLog('🔍 Step 1: Finding loan receivable account...');

    try {
      final accountsRepository = Get.find<ChartOfAccountsRepository>();
      final accountsResult = await accountsRepository.getAccounts();

      accountsResult.fold(
        (failure) {
          _addLog('❌ Failed to load accounts: ${failure.message}');
        },
        (accounts) {
          _addLog('📊 Found ${accounts.length} total accounts');

          // Look for loan receivable accounts
          final loanReceivableAccounts = accounts
              .where((account) =>
                  account.accountName.toLowerCase().contains('loan') &&
                  account.accountName.toLowerCase().contains('receivable'))
              .toList();

          if (loanReceivableAccounts.isEmpty) {
            _addLog('⚠️ No loan receivable accounts found by name');

            // Look for accounts receivable or current assets that might be used
            final possibleAccounts = accounts
                .where((account) =>
                    account.accountType == AccountType.accountsReceivable ||
                    account.accountType == AccountType.currentAssets)
                .toList();

            _addLog(
                '🔍 Found ${possibleAccounts.length} possible asset accounts:');
            for (final account in possibleAccounts) {
              _addLog(
                  '   • ${account.accountName} (${account.accountType.displayName}) - Balance: ${account.balance}');
            }
          } else {
            _addLog(
                '✅ Found ${loanReceivableAccounts.length} loan receivable accounts:');
            for (final account in loanReceivableAccounts) {
              _addLog(
                  '   • ${account.accountName} (${account.accountType.displayName}) - Balance: ${account.balance}');
              _addLog('     Account ID: ${account.id}');
              _addLog('     Account Number: ${account.accountNumber}');
              _addLog('     Category: ${account.category.displayName}');
              _addLog('     Is Active: ${account.isActive}');
            }
          }
        },
      );
    } catch (e) {
      _addLog('❌ Error finding loan receivable account: $e');
    }
  }

  Future<void> _analyzeAccountConfiguration(String uid) async {
    _addLog('');
    _addLog('🔍 Step 2: Analyzing account configuration...');

    // Test the account type helper service logic
    _addLog('📋 Testing AccountTypeHelperService logic:');

    // Test Asset account types
    final assetTypes = [
      AccountType.accountsReceivable,
      AccountType.currentAssets,
      AccountType.cash,
      AccountType.bank,
    ];

    for (final accountType in assetTypes) {
      final isDebitAccount =
          AccountTypeHelperService.isDebitAccount(accountType);
      final behavior =
          AccountTypeHelperService.getAccountBehaviorDescription(accountType);

      _addLog(
          '   • ${accountType.displayName}: isDebitAccount=$isDebitAccount');
      _addLog('     Behavior: $behavior');

      // Test a debit transaction
      final debitChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 40000.0,
        creditAmount: 0.0,
      );

      _addLog(
          '     Debit 40,000: balance change = $debitChange (should be +40,000 for assets)');

      if (debitChange != 40000.0) {
        _addLog(
            '     ❌ INCORRECT: Asset account debit should increase balance by +40,000');
      } else {
        _addLog('     ✅ CORRECT: Asset account debit increases balance');
      }
    }
  }

  Future<void> _checkJournalEntries(String uid) async {
    _addLog('');
    _addLog(
        '🔍 Step 3: Checking journal entries for loan receivable accounts...');

    try {
      final journalService = JournalEntryFirebaseService();
      final entries = await journalService.getJournalEntries();

      _addLog('📊 Found ${entries.length} total journal entries');

      // Look for loan-related entries
      final loanEntries = entries
          .where((entry) =>
              entry.description.toLowerCase().contains('loan') ||
              entry.sourceTransactionType?.contains('loan') == true)
          .toList();

      _addLog('🔍 Found ${loanEntries.length} loan-related journal entries:');

      for (final entry in loanEntries.take(5)) {
        // Show first 5
        _addLog('   📝 Entry: ${entry.entryNumber} - ${entry.description}');
        _addLog('     Date: ${entry.entryDate}');
        _addLog('     Type: ${entry.sourceTransactionType ?? 'N/A'}');
        _addLog('     Status: ${entry.status.name}');
        _addLog('     Total Debits: ${entry.totalDebits}');
        _addLog('     Total Credits: ${entry.totalCredits}');
        _addLog('     Lines:');

        for (final line in entry.lines) {
          _addLog('       • ${line.accountName} (${line.accountId})');
          _addLog(
              '         Debit: ${line.debitAmount}, Credit: ${line.creditAmount}');
          _addLog('         Running Balance: ${line.runningBalance ?? 'N/A'}');
        }
        _addLog('');
      }
    } catch (e) {
      _addLog('❌ Error checking journal entries: $e');
    }
  }

  Future<void> _verifyBalanceCalculation(String uid) async {
    _addLog('🔍 Step 4: Verifying balance calculation logic...');

    // Simulate the exact scenario from the screenshot
    _addLog('💰 Simulating loan approval scenario:');
    _addLog('   Initial balance: 0.00');
    _addLog('   Loan amount (debit): 40,000.00');
    _addLog('   Expected final balance: +40,000.00');

    const accountType =
        AccountType.accountsReceivable; // Typical loan receivable type
    double currentBalance = 0.0;
    double loanAmount = 40000.0;

    // Calculate balance change for debit (loan approval)
    final balanceChange = AccountTypeHelperService.calculateBalanceChange(
      accountType: accountType,
      debitAmount: loanAmount,
      creditAmount: 0.0,
    );

    final newBalance = currentBalance + balanceChange;

    _addLog('   Calculated balance change: $balanceChange');
    _addLog('   New balance: $newBalance');

    if (newBalance == 40000.0) {
      _addLog('   ✅ CORRECT: Balance calculation is working properly');
    } else {
      _addLog('   ❌ INCORRECT: Balance should be +40,000 but got $newBalance');
      _addLog(
          '   🔧 This indicates a problem with the balance calculation logic');
    }

    // Test the reverse scenario (loan repayment)
    _addLog('');
    _addLog('💰 Simulating loan repayment scenario:');
    _addLog('   Current balance: 40,000.00');
    _addLog('   Repayment amount (credit): 40,000.00');
    _addLog('   Expected final balance: 0.00');

    currentBalance = 40000.0;
    final repaymentChange = AccountTypeHelperService.calculateBalanceChange(
      accountType: accountType,
      debitAmount: 0.0,
      creditAmount: loanAmount,
    );

    final repaymentBalance = currentBalance + repaymentChange;

    _addLog('   Calculated balance change: $repaymentChange');
    _addLog('   New balance: $repaymentBalance');

    if (repaymentBalance == 0.0) {
      _addLog('   ✅ CORRECT: Repayment calculation is working properly');
    } else {
      _addLog('   ❌ INCORRECT: Balance should be 0 but got $repaymentBalance');
    }
  }

  Future<void> _testBalanceCalculation() async {
    setState(() {
      _isRunning = true;
    });

    try {
      _addLog('🧪 Testing balance calculation logic for all account types...');

      final testCases = [
        {
          'type': AccountType.accountsReceivable,
          'name': 'Accounts Receivable (Asset)'
        },
        {'type': AccountType.currentAssets, 'name': 'Current Assets (Asset)'},
        {'type': AccountType.cash, 'name': 'Cash (Asset)'},
        {'type': AccountType.loansPayable, 'name': 'Loans Payable (Liability)'},
        {'type': AccountType.salesRevenue, 'name': 'Sales Revenue (Revenue)'},
        {
          'type': AccountType.operatingExpenses,
          'name': 'Operating Expenses (Expense)'
        },
      ];

      for (final testCase in testCases) {
        final accountType = testCase['type'] as AccountType;
        final name = testCase['name'] as String;

        _addLog('');
        _addLog('🧪 Testing: $name');

        // Test debit
        final debitChange = AccountTypeHelperService.calculateBalanceChange(
          accountType: accountType,
          debitAmount: 1000.0,
          creditAmount: 0.0,
        );

        // Test credit
        final creditChange = AccountTypeHelperService.calculateBalanceChange(
          accountType: accountType,
          debitAmount: 0.0,
          creditAmount: 1000.0,
        );

        _addLog('   Debit 1000: balance change = $debitChange');
        _addLog('   Credit 1000: balance change = $creditChange');

        // Verify accounting principles
        final isDebitAccount =
            AccountTypeHelperService.isDebitAccount(accountType);
        if (isDebitAccount) {
          // Asset/Expense: Debit should increase (+), Credit should decrease (-)
          if (debitChange > 0 && creditChange < 0) {
            _addLog('   ✅ CORRECT: Debit account behavior');
          } else {
            _addLog(
                '   ❌ INCORRECT: Debit account should have +debit, -credit');
          }
        } else {
          // Liability/Equity/Revenue: Credit should increase (+), Debit should decrease (-)
          if (creditChange > 0 && debitChange < 0) {
            _addLog('   ✅ CORRECT: Credit account behavior');
          } else {
            _addLog(
                '   ❌ INCORRECT: Credit account should have +credit, -debit');
          }
        }
      }
    } catch (e) {
      _addLog('❌ Error testing balance calculation: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _simulateLoanApproval() async {
    setState(() {
      _isRunning = true;
    });

    try {
      _addLog('🎭 Simulating complete loan approval process...');

      // Simulate the journal entry creation for loan approval
      _addLog('📝 Creating simulated journal entry for loan approval:');
      _addLog('   Loan amount: 40,000.00');
      _addLog('   Loan receivable account: Accounts Receivable (Asset)');
      _addLog('   Source account: Cash (Asset)');

      // Create the journal entry lines as they would be created
      const loanAmount = 40000.0;
      const loanReceivableType = AccountType.accountsReceivable;
      const cashAccountType = AccountType.cash;

      // Line 1: Debit Loan Receivable (Asset increases)
      final receivableChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: loanReceivableType,
        debitAmount: loanAmount,
        creditAmount: 0.0,
      );

      // Line 2: Credit Cash (Asset decreases)
      final cashChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: cashAccountType,
        debitAmount: 0.0,
        creditAmount: loanAmount,
      );

      _addLog('');
      _addLog('📊 Journal Entry Lines:');
      _addLog('   Line 1: Debit Loan Receivable 40,000.00');
      _addLog('     Balance change: $receivableChange');
      _addLog('     Expected: +40,000 (Asset increases with debit)');
      _addLog(
          '     Result: ${receivableChange == 40000.0 ? '✅ CORRECT' : '❌ INCORRECT'}');

      _addLog('   Line 2: Credit Cash 40,000.00');
      _addLog('     Balance change: $cashChange');
      _addLog('     Expected: -40,000 (Asset decreases with credit)');
      _addLog(
          '     Result: ${cashChange == -40000.0 ? '✅ CORRECT' : '❌ INCORRECT'}');

      _addLog('');
      _addLog('⚖️ Double-entry validation:');
      final totalDebits = loanAmount;
      final totalCredits = loanAmount;
      _addLog('   Total debits: $totalDebits');
      _addLog('   Total credits: $totalCredits');
      _addLog('   Balanced: ${totalDebits == totalCredits ? '✅ YES' : '❌ NO'}');

      // Simulate running balance calculation
      _addLog('');
      _addLog('💰 Running balance simulation:');
      double loanReceivableBalance = 0.0;
      double cashBalance = 50000.0; // Assume starting cash balance

      _addLog('   Initial loan receivable balance: $loanReceivableBalance');
      _addLog('   Initial cash balance: $cashBalance');

      loanReceivableBalance += receivableChange;
      cashBalance += cashChange;

      _addLog('   After loan approval:');
      _addLog('     Loan receivable balance: $loanReceivableBalance');
      _addLog('     Cash balance: $cashBalance');

      if (loanReceivableBalance > 0) {
        _addLog('   ✅ CORRECT: Loan receivable shows positive balance');
      } else {
        _addLog('   ❌ INCORRECT: Loan receivable should show positive balance');
      }
    } catch (e) {
      _addLog('❌ Error simulating loan approval: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _checkManualLoanWorkflow(String uid) async {
    _addLog('');
    _addLog('🔍 Step 5: Checking manual loan workflow...');

    try {
      // Check for manual loan journal entries
      final journalService = JournalEntryFirebaseService();
      final entries = await journalService.getJournalEntries();

      final manualLoanEntries = entries
          .where((entry) =>
              entry.sourceTransactionType?.contains('manual_loan') == true ||
              entry.description.toLowerCase().contains('manual loan'))
          .toList();

      _addLog(
          '📊 Found ${manualLoanEntries.length} manual loan journal entries');

      for (final entry in manualLoanEntries.take(3)) {
        _addLog('   📝 ${entry.entryNumber}: ${entry.description}');
        _addLog('     Status: ${entry.status.name}');
        _addLog('     Total Debits: ${entry.totalDebits}');
        _addLog('     Total Credits: ${entry.totalCredits}');

        // Check balance impact
        for (final line in entry.lines) {
          if (line.accountName.toLowerCase().contains('loan') &&
              line.accountName.toLowerCase().contains('receivable')) {
            _addLog('     Loan Receivable Line:');
            _addLog(
                '       Debit: ${line.debitAmount}, Credit: ${line.creditAmount}');
            _addLog('       Running Balance: ${line.runningBalance ?? 'N/A'}');

            if (line.debitAmount > 0 && (line.runningBalance ?? 0) < 0) {
              _addLog(
                  '       ❌ ISSUE: Debit entry but negative running balance');
            } else if (line.debitAmount > 0 && (line.runningBalance ?? 0) > 0) {
              _addLog(
                  '       ✅ CORRECT: Debit entry with positive running balance');
            }
          }
        }
      }
    } catch (e) {
      _addLog('❌ Error checking manual loan workflow: $e');
    }
  }

  Future<void> _checkVoucherLoanWorkflow(String uid) async {
    _addLog('');
    _addLog('🔍 Step 6: Checking voucher-based loan workflow...');

    try {
      // Check for voucher-based loan journal entries
      final journalService = JournalEntryFirebaseService();
      final entries = await journalService.getJournalEntries();

      final voucherLoanEntries = entries
          .where((entry) =>
              entry.sourceTransactionType?.contains('voucher') == true &&
              (entry.description.toLowerCase().contains('loan') ||
                  entry.sourceTransactionType?.contains('loan') == true))
          .toList();

      _addLog(
          '📊 Found ${voucherLoanEntries.length} voucher-based loan journal entries');

      for (final entry in voucherLoanEntries.take(3)) {
        _addLog('   📝 ${entry.entryNumber}: ${entry.description}');
        _addLog('     Source Type: ${entry.sourceTransactionType}');
        _addLog('     Status: ${entry.status.name}');

        // Check for loan receivable lines
        final loanLines = entry.lines
            .where((line) =>
                line.accountName.toLowerCase().contains('loan') &&
                line.accountName.toLowerCase().contains('receivable'))
            .toList();

        if (loanLines.isNotEmpty) {
          _addLog('     Loan Receivable Lines: ${loanLines.length}');
          for (final line in loanLines) {
            _addLog(
                '       Debit: ${line.debitAmount}, Credit: ${line.creditAmount}');
            _addLog('       Running Balance: ${line.runningBalance ?? 'N/A'}');

            if (line.debitAmount > 0 && (line.runningBalance ?? 0) < 0) {
              _addLog(
                  '       ❌ ISSUE: Voucher loan debit but negative running balance');
            }
          }
        }
      }
    } catch (e) {
      _addLog('❌ Error checking voucher loan workflow: $e');
    }
  }

  Future<void> _checkBalanceSynchronization(String uid) async {
    _addLog('');
    _addLog('🔍 Step 7: Checking balance synchronization...');

    try {
      final accountsRepository = Get.find<ChartOfAccountsRepository>();
      final accountsResult = await accountsRepository.getAccounts();

      await accountsResult.fold(
        (failure) async {
          _addLog('❌ Failed to load accounts for synchronization check');
        },
        (accounts) async {
          final loanReceivableAccounts = accounts
              .where((account) =>
                  account.accountName.toLowerCase().contains('loan') &&
                  account.accountName.toLowerCase().contains('receivable'))
              .toList();

          for (final account in loanReceivableAccounts) {
            _addLog('🔍 Checking synchronization for: ${account.accountName}');

            // Get stored balance
            final storedBalance = account.balance;
            _addLog('   Stored balance: $storedBalance');

            // Calculate balance from journal entries
            try {
              final journalService = JournalEntryFirebaseService();
              final calculatedBalance =
                  await journalService.calculateAccountBalance(account.id);
              _addLog('   Calculated balance: $calculatedBalance');

              final difference = (storedBalance - calculatedBalance).abs();
              if (difference > 0.01) {
                _addLog(
                    '   ❌ SYNCHRONIZATION ISSUE: Difference of $difference');
                _addLog(
                    '   🔧 Stored and calculated balances are not synchronized');
              } else {
                _addLog(
                    '   ✅ SYNCHRONIZED: Stored and calculated balances match');
              }

              // Check if both are negative when they should be positive
              if (storedBalance < 0 && calculatedBalance < 0) {
                _addLog(
                    '   ❌ BOTH NEGATIVE: This suggests a systematic calculation error');
              }
            } catch (e) {
              _addLog('   ❌ Error calculating balance: $e');
            }
          }
        },
      );
    } catch (e) {
      _addLog('❌ Error checking balance synchronization: $e');
    }
  }

  Future<void> _checkRunningBalanceUpdates(String uid) async {
    _addLog('');
    _addLog('🔍 Step 8: Checking running balance updates...');

    try {
      final journalService = JournalEntryFirebaseService();
      final entries = await journalService.getJournalEntries();

      // Focus on recent loan-related entries
      final recentLoanEntries = entries
          .where((entry) =>
              entry.description.toLowerCase().contains('loan') &&
              entry.createdAt
                  .isAfter(DateTime.now().subtract(const Duration(days: 30))))
          .toList();

      _addLog(
          '📊 Checking ${recentLoanEntries.length} recent loan entries for running balance issues');

      int entriesWithMissingRunningBalance = 0;
      int entriesWithIncorrectRunningBalance = 0;

      for (final entry in recentLoanEntries.take(5)) {
        _addLog('   📝 Entry: ${entry.entryNumber}');

        for (final line in entry.lines) {
          if (line.accountName.toLowerCase().contains('loan') &&
              line.accountName.toLowerCase().contains('receivable')) {
            if (line.runningBalance == null) {
              entriesWithMissingRunningBalance++;
              _addLog('     ❌ MISSING: No running balance stored');
            } else {
              _addLog('     Running Balance: ${line.runningBalance}');

              // Check if running balance makes sense for the transaction type
              if (line.debitAmount > 0 && line.runningBalance! < 0) {
                entriesWithIncorrectRunningBalance++;
                _addLog(
                    '     ❌ INCORRECT: Debit entry but negative running balance');
              } else if (line.debitAmount > 0 && line.runningBalance! > 0) {
                _addLog(
                    '     ✅ CORRECT: Debit entry with positive running balance');
              }
            }
          }
        }
      }

      _addLog('');
      _addLog('📊 Running Balance Summary:');
      _addLog(
          '   Entries with missing running balance: $entriesWithMissingRunningBalance');
      _addLog(
          '   Entries with incorrect running balance: $entriesWithIncorrectRunningBalance');

      if (entriesWithMissingRunningBalance > 0) {
        _addLog('   🔧 ISSUE: Some entries are missing running balance data');
      }

      if (entriesWithIncorrectRunningBalance > 0) {
        _addLog(
            '   🔧 ISSUE: Some entries have incorrect running balance calculations');
      }
    } catch (e) {
      _addLog('❌ Error checking running balance updates: $e');
    }
  }

  @override
  void dispose() {
    _companyUidController.dispose();
    _accountIdController.dispose();
    super.dispose();
  }
}
