import 'dart:developer' as dev;
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/finance/deposit_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import 'automatic_journal_entry_service.dart';

/// Service for integrating deposit transactions with automatic journal entry generation
/// Following the same pattern as ExpenseJournalIntegrationService
class DepositJournalIntegrationService {
  final AutomaticJournalEntryService _automaticJournalService;
  final GeneralLedgerFirebaseService _generalLedgerService;
  final JournalEntryFirebaseService _journalEntryService;
  final ChartOfAccountsFirebaseService _chartOfAccountsService;

  DepositJournalIntegrationService(
    this._automaticJournalService,
    this._generalLedgerService,
    this._journalEntryService,
    this._chartOfAccountsService,
  );

  /// Get current user's display name for audit trail
  String get _currentUserName =>
      FirebaseAuth.instance.currentUser?.displayName ??
      FirebaseAuth.instance.currentUser?.email ??
      'System User';

  /// Generate and create journal entry for a deposit transaction
  Future<bool> processDepositTransaction(
      DepositModel deposit, String uid) async {
    try {
      dev.log(
          'Processing deposit transaction for journal entry: ${deposit.amount}');

      // Validate deposit for journal entry generation
      final validation = await validateDepositForJournalEntry(deposit);
      if (!validation.isValid) {
        dev.log('Deposit validation failed: ${validation.issuesText}');
        throw Exception('Deposit validation failed: ${validation.issuesText}');
      }

      // Generate journal entry for the deposit
      final journalEntry =
          await _automaticJournalService.generateDepositJournalEntry(
        deposit: deposit,
        uid: uid,
        createdBy: _currentUserName, // Use actual authenticated user
      );

      if (journalEntry == null) {
        final errorMsg =
            'No journal entry generated for deposit: ${deposit.id}. This indicates a configuration issue with Chart of Accounts.';
        dev.log(errorMsg);
        throw Exception(errorMsg);
      }

      // Create the journal entry using the proper service
      // Note: JournalEntryFirebaseService automatically handles account balance updates
      // and ledger entries when creating journal entries, so we don't need to do it manually
      try {
        await _journalEntryService.createJournalEntry(journalEntry);
        dev.log(
            '✅ Successfully created journal entry for deposit: ${deposit.id}');
      } catch (journalError) {
        dev.log(
            '❌ [JOURNAL CREATE] Error creating journal entry: $journalError');
        dev.log(
            '❌ [JOURNAL CREATE] Failed entry details - Ref: ${journalEntry.referenceNumber}, Source: ${journalEntry.sourceTransactionId}');

        // Re-throw the specific journal error for better error handling upstream
        throw Exception('Failed to create journal entry: $journalError');
      }

      dev.log('✅ Successfully processed deposit transaction: ${deposit.id}');
      return true;
    } catch (e) {
      dev.log('❌ Error processing deposit transaction: $e');
      rethrow; // Re-throw to allow proper error handling upstream
    }
  }

  /// Batch process multiple deposit transactions
  Future<BatchProcessResult> batchProcessDepositTransactions(
    List<DepositModel> deposits,
    String uid,
  ) async {
    int successCount = 0;
    int failureCount = 0;
    final List<String> failedDepositIds = [];

    dev.log('Batch processing ${deposits.length} deposit transactions');

    for (final deposit in deposits) {
      final success = await processDepositTransaction(deposit, uid);
      if (success) {
        successCount++;
      } else {
        failureCount++;
        failedDepositIds.add(deposit.id);
      }
    }

    dev.log(
        'Batch processing completed: $successCount successful, $failureCount failed');

    return BatchProcessResult(
      totalProcessed: deposits.length,
      successCount: successCount,
      failureCount: failureCount,
      failedTransactionIds: failedDepositIds,
    );
  }

  /// Validate deposit transaction for journal entry generation
  Future<DepositValidationResult> validateDepositForJournalEntry(
    DepositModel deposit,
  ) async {
    try {
      final errors = <String>[];
      final warnings = <String>[];

      // Check if deposit uses Chart of Accounts
      if (!deposit.usesChartOfAccounts) {
        errors.add(
            'Deposit must use Chart of Accounts (source and destination accounts required)');
        return DepositValidationResult(
          isValid: false,
          issues: errors,
        );
      }

      // Validate source account exists and is accessible
      final sourceAccount = await _chartOfAccountsService
          .getAccountById(deposit.sourceAccountId!);
      if (sourceAccount == null) {
        errors.add('Source account not found: ${deposit.sourceAccountId}');
      } else if (!sourceAccount.isActive) {
        errors.add('Source account is inactive: ${sourceAccount.accountName}');
      }

      // Validate destination account exists and is accessible
      final destinationAccount = await _chartOfAccountsService
          .getAccountById(deposit.destinationAccountId!);
      if (destinationAccount == null) {
        errors.add(
            'Destination account not found: ${deposit.destinationAccountId}');
      } else if (!destinationAccount.isActive) {
        errors.add(
            'Destination account is inactive: ${destinationAccount.accountName}');
      }

      // Validate amount
      if (deposit.amount <= 0) {
        errors.add('Deposit amount must be greater than zero');
      }

      // Add warnings for account type recommendations
      if (sourceAccount != null && destinationAccount != null) {
        _addAccountTypeWarnings(sourceAccount, destinationAccount, warnings);
      }

      return DepositValidationResult(
        isValid: errors.isEmpty,
        issues: errors,
      );
    } catch (e) {
      dev.log('Error validating deposit for journal entry: $e');
      return DepositValidationResult(
        isValid: false,
        issues: ['Validation error: $e'],
      );
    }
  }

  /// Add account type warnings for deposit transactions
  void _addAccountTypeWarnings(
    ChartOfAccountsModel sourceAccount,
    ChartOfAccountsModel destinationAccount,
    List<String> warnings,
  ) {
    // Typical deposit flow: Revenue/Liability account increases, Asset account (cash/bank) increases
    if (sourceAccount.category != AccountCategory.revenue &&
        sourceAccount.category != AccountCategory.liabilities) {
      warnings.add(
          'Source account "${sourceAccount.accountName}" is not a Revenue or Liability account. '
          'Typical deposit transactions decrease Revenue or Liability accounts.');
    }

    if (destinationAccount.category != AccountCategory.assets) {
      warnings.add(
          'Destination account "${destinationAccount.accountName}" is not an Asset account. '
          'Typical deposit transactions increase Asset accounts (Cash, Bank, etc.)');
    }
  }

  /// Get journal entries associated with a deposit
  Future<List<JournalEntryModel>> getJournalEntriesForDeposit(
    String depositId,
    String uid,
  ) async {
    try {
      final allEntries =
          await _generalLedgerService.getJournalEntries(uid, null, null);

      return allEntries.fold(
        (failure) {
          dev.log('Failed to fetch journal entries: ${failure.message}');
          return <JournalEntryModel>[];
        },
        (entries) {
          return entries
              .where((entry) =>
                  entry.sourceTransactionId == depositId &&
                  entry.sourceTransactionType == 'deposit' &&
                  entry.uid == uid)
              .toList();
        },
      );
    } catch (e) {
      dev.log('Error fetching journal entries for deposit: $e');
      return <JournalEntryModel>[];
    }
  }

  /// Check if a deposit already has associated journal entries
  Future<bool> hasExistingJournalEntries(String depositId, String uid) async {
    final entries = await getJournalEntriesForDeposit(depositId, uid);
    return entries.isNotEmpty;
  }

  /// Reverse journal entries for a deposit (when deposit is deleted/modified)
  Future<bool> reverseDepositJournalEntries(
      String depositId, String uid) async {
    try {
      final entries = await getJournalEntriesForDeposit(depositId, uid);

      if (entries.isEmpty) {
        dev.log('No journal entries found for deposit: $depositId');
        return true; // Nothing to reverse
      }

      bool allReversed = true;
      for (final entry in entries) {
        final result = await _generalLedgerService.reverseJournalEntry(
          entry.id,
          'Deposit transaction reversed',
          _currentUserName, // Use actual authenticated user
        );

        result.fold(
          (failure) {
            dev.log(
                'Failed to reverse journal entry ${entry.id}: ${failure.message}');
            allReversed = false;
          },
          (success) {
            dev.log('Successfully reversed journal entry: ${entry.id}');
          },
        );
      }

      return allReversed;
    } catch (e) {
      dev.log('Error reversing deposit journal entries: $e');
      return false;
    }
  }
}

/// Result class for batch processing operations
class BatchProcessResult {
  final int totalProcessed;
  final int successCount;
  final int failureCount;
  final List<String> failedTransactionIds;

  BatchProcessResult({
    required this.totalProcessed,
    required this.successCount,
    required this.failureCount,
    required this.failedTransactionIds,
  });

  double get successRate =>
      totalProcessed > 0 ? successCount / totalProcessed : 0.0;
  bool get hasFailures => failureCount > 0;
  bool get allSuccessful => failureCount == 0;
}

/// Result class for deposit validation
class DepositValidationResult {
  final bool isValid;
  final List<String> issues;

  DepositValidationResult({
    required this.isValid,
    required this.issues,
  });

  String get issuesText => issues.join(', ');
}
