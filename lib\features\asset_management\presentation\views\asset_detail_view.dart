import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/features/asset_management/presentation/controllers/asset_detail_controller.dart';
import 'package:logestics/features/asset_management/presentation/controllers/audit_trail_controller.dart';
import 'package:logestics/features/asset_management/presentation/widgets/maintenance_history_widget.dart';
import 'package:logestics/features/asset_management/presentation/views/audit_trail_view.dart';
import 'package:logestics/repositories/asset/asset_audit_repository.dart';
import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:logestics/models/asset/asset_model.dart';

class AssetDetailView extends StatefulWidget {
  final String assetId;
  final AssetModel? asset; // Optional pre-loaded asset

  const AssetDetailView({
    super.key,
    required this.assetId,
    this.asset,
  });

  @override
  State<AssetDetailView> createState() => _AssetDetailViewState();
}

class _AssetDetailViewState extends State<AssetDetailView>
    with SingleTickerProviderStateMixin {
  late AssetDetailController controller;
  late ColorNotifier notifier;
  late TabController tabController;

  @override
  void initState() {
    super.initState();
    controller = Get.put(AssetDetailController());
    tabController = TabController(length: 4, vsync: this); // Updated to 4 tabs

    // Initialize with asset data if provided
    if (widget.asset != null) {
      controller.asset.value = widget.asset;
    }

    controller.initializeWithAssetId(widget.assetId);

    // Initialize audit trail controller with repository
    final auditRepository = Get.find<AssetAuditRepository>();
    Get.put(AuditTrailController(auditRepository));
  }

  @override
  void dispose() {
    tabController.dispose();
    Get.delete<AssetDetailController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of<ColorNotifier>(context, listen: true);
    final size = MediaQuery.of(context).size;
    final isDesktop = size.width > 768;

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      appBar: AppBar(
        backgroundColor: notifier.getBgColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: notifier.text),
          onPressed: () => Get.back(),
        ),
        title: Obx(() => Text(
              controller.asset.value?.name ?? 'Asset Details',
              style: TextStyle(
                color: notifier.text,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            )),
        actions: [
          Obx(() => controller.asset.value != null
              ? PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, color: notifier.text),
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        controller.editAsset();
                        break;
                      case 'maintenance':
                        controller.addMaintenance();
                        break;
                      case 'refresh':
                        controller.refreshData();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20),
                          SizedBox(width: 8),
                          Text('Edit Asset'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'maintenance',
                      child: Row(
                        children: [
                          Icon(Icons.build, size: 20),
                          SizedBox(width: 8),
                          Text('Add Maintenance'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'refresh',
                      child: Row(
                        children: [
                          Icon(Icons.refresh, size: 20),
                          SizedBox(width: 8),
                          Text('Refresh'),
                        ],
                      ),
                    ),
                  ],
                )
              : const SizedBox()),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.error.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  controller.error.value,
                  style: TextStyle(color: notifier.text, fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: controller.refreshData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (controller.asset.value == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
                const SizedBox(height: 16),
                Text(
                  'Asset not found',
                  style: TextStyle(color: notifier.text, fontSize: 16),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            _buildAssetHeader(isDesktop),
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: tabController,
                children: [
                  _buildOverviewTab(isDesktop),
                  _buildMaintenanceTab(),
                  _buildFinancialTab(isDesktop),
                  _buildAuditTrailTab(),
                ],
              ),
            ),
          ],
        );
      }),
      floatingActionButton: Obx(() => controller.asset.value != null
          ? FloatingActionButton.extended(
              onPressed: controller.addMaintenance,
              backgroundColor: const Color(0xFF0165FC),
              icon: const Icon(Icons.build, color: Colors.white),
              label: const Text(
                'Add Maintenance',
                style: TextStyle(color: Colors.white),
              ),
            )
          : const SizedBox()),
    );
  }

  Widget _buildAssetHeader(bool isDesktop) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: isDesktop
          ? Row(
              children: [
                _buildAssetIcon(),
                const SizedBox(width: 20),
                Expanded(child: _buildAssetInfo()),
                const SizedBox(width: 20),
                _buildQuickStats(),
              ],
            )
          : Column(
              children: [
                Row(
                  children: [
                    _buildAssetIcon(),
                    const SizedBox(width: 16),
                    Expanded(child: _buildAssetInfo()),
                  ],
                ),
                const SizedBox(height: 16),
                _buildQuickStats(),
              ],
            ),
    );
  }

  Widget _buildAssetIcon() {
    final asset = controller.asset.value!;
    IconData iconData;

    switch (asset.type) {
      case 'Vehicle':
        iconData = Icons.directions_car;
        break;
      case 'Machinery':
        iconData = Icons.precision_manufacturing;
        break;
      case 'Tools':
        iconData = Icons.build;
        break;
      case 'Equipment':
        iconData = Icons.devices;
        break;
      default:
        iconData = Icons.inventory_2;
    }

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: controller.getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        iconData,
        size: 30,
        color: controller.getStatusColor(),
      ),
    );
  }

  Widget _buildAssetInfo() {
    final asset = controller.asset.value!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          asset.name,
          style: TextStyle(
            color: notifier.text,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${asset.brand} ${asset.model}',
          style: TextStyle(
            color: notifier.text.withOpacity(0.7),
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: controller.getStatusColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            asset.status,
            style: TextStyle(
              color: controller.getStatusColor(),
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStats() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildStatItem(
          'Current Value',
          'PKR ${controller.formatCurrency(controller.asset.value!.currentValue)}',
          Icons.attach_money,
          Colors.green,
        ),
        _buildStatItem(
          'Depreciation',
          '${controller.getDepreciationPercentage().toStringAsFixed(1)}%',
          Icons.trending_down,
          Colors.orange,
        ),
        _buildStatItem(
          'Last Maintenance',
          controller.getLastMaintenanceDate(),
          Icons.build,
          Colors.blue,
        ),
      ],
    );
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: notifier.text,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: notifier.text.withOpacity(0.6),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: tabController,
        labelColor: const Color(0xFF0165FC),
        unselectedLabelColor: notifier.text.withOpacity(0.6),
        indicatorColor: const Color(0xFF0165FC),
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Maintenance'),
          Tab(text: 'Financial'),
          Tab(text: 'Audit Trail'),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(bool isDesktop) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: isDesktop
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(flex: 2, child: _buildBasicInfoCard()),
                const SizedBox(width: 16),
                Expanded(flex: 2, child: _buildOperationalInfoCard()),
                const SizedBox(width: 16),
                Expanded(flex: 1, child: _buildDepreciationCard()),
              ],
            )
          : Column(
              children: [
                _buildBasicInfoCard(),
                const SizedBox(height: 16),
                _buildOperationalInfoCard(),
                const SizedBox(height: 16),
                _buildDepreciationCard(),
              ],
            ),
    );
  }

  Widget _buildBasicInfoCard() {
    final asset = controller.asset.value!;

    return Card(
      color: notifier.getBgColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Asset Name', asset.name),
            _buildInfoRow('Type', asset.type),
            _buildInfoRow('Brand', asset.brand),
            _buildInfoRow('Model', asset.model),
            _buildInfoRow('Registration No.', asset.registrationNumber),
            _buildInfoRow('Serial Number', asset.serialNumber),
            _buildInfoRow('Purchase Date', _formatDate(asset.purchaseDate)),
            _buildInfoRow('Vendor', asset.vendor),
            _buildInfoRow('Supplier', asset.supplier),
            if (asset.notes.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Notes',
                style: TextStyle(
                  color: notifier.text,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                asset.notes,
                style: TextStyle(
                  color: notifier.text.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOperationalInfoCard() {
    final asset = controller.asset.value!;

    return Card(
      color: notifier.getBgColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Operational Information',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Location', asset.location),
            _buildInfoRow('Department', asset.department),
            _buildInfoRow('Status', asset.status),
            _buildInfoRow('Created', _formatDate(asset.createdAt)),
            _buildInfoRow('Last Updated', _formatDate(asset.updatedAt)),
            const SizedBox(height: 16),
            if (asset.attachmentUrls.isNotEmpty) ...[
              Text(
                'Attachments',
                style: TextStyle(
                  color: notifier.text,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: asset.attachmentUrls.map((url) {
                  final fileName = url.split('/').last.split('?').first;
                  return Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF0165FC).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.attach_file,
                            size: 16, color: Color(0xFF0165FC)),
                        const SizedBox(width: 4),
                        Text(
                          fileName.length > 20
                              ? '${fileName.substring(0, 20)}...'
                              : fileName,
                          style: const TextStyle(
                            color: Color(0xFF0165FC),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDepreciationCard() {
    final asset = controller.asset.value!;
    final depreciationPercentage = controller.getDepreciationPercentage();

    return Card(
      color: notifier.getBgColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Depreciation',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Method', asset.depreciationMethod),
            _buildInfoRow('Useful Life', '${asset.estimatedUsefulLife} years'),
            _buildInfoRow('Purchase Cost',
                'PKR ${controller.formatCurrency(asset.purchaseCost)}'),
            _buildInfoRow('Current Value',
                'PKR ${controller.formatCurrency(asset.currentValue)}'),
            const SizedBox(height: 16),
            Text(
              'Depreciation Progress',
              style: TextStyle(
                color: notifier.text,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: depreciationPercentage / 100,
              backgroundColor: Colors.grey.withValues(alpha: 0.3),
              valueColor: AlwaysStoppedAnimation<Color>(
                depreciationPercentage > 80
                    ? Colors.red
                    : depreciationPercentage > 50
                        ? Colors.orange
                        : Colors.green,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${depreciationPercentage.toStringAsFixed(1)}% depreciated',
              style: TextStyle(
                color: notifier.text.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceTab() {
    return Obx(() => controller.isLoadingMaintenance.value
        ? const Center(child: CircularProgressIndicator())
        : controller.maintenanceRecords.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.build_outlined, size: 64, color: Colors.grey),
                    const SizedBox(height: 16),
                    Text(
                      'No maintenance records found',
                      style: TextStyle(color: notifier.text, fontSize: 16),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: controller.addMaintenance,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0165FC),
                      ),
                      child: const Text(
                        'Add First Maintenance',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              )
            : MaintenanceHistoryWidget(
                assetId: controller.assetId,
                assetName: controller.asset.value?.name ?? '',
              ));
  }

  Widget _buildFinancialTab(bool isDesktop) {
    final asset = controller.asset.value!;
    final totalMaintenanceCost = controller.getTotalMaintenanceCost();
    final totalCost = asset.purchaseCost + totalMaintenanceCost;
    final maintenanceTrend = controller.getMaintenanceTrend();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: isDesktop
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    child: _buildFinancialSummaryCard(
                        asset, totalMaintenanceCost, totalCost)),
                const SizedBox(width: 16),
                Expanded(
                    child: _buildMaintenanceCostCard(
                        totalMaintenanceCost, maintenanceTrend)),
              ],
            )
          : Column(
              children: [
                _buildFinancialSummaryCard(
                    asset, totalMaintenanceCost, totalCost),
                const SizedBox(height: 16),
                _buildMaintenanceCostCard(
                    totalMaintenanceCost, maintenanceTrend),
              ],
            ),
    );
  }

  Widget _buildFinancialSummaryCard(
      AssetModel asset, double totalMaintenanceCost, double totalCost) {
    return Card(
      color: notifier.getBgColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Financial Summary',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Purchase Cost',
                'PKR ${controller.formatCurrency(asset.purchaseCost)}'),
            _buildInfoRow('Current Value',
                'PKR ${controller.formatCurrency(asset.currentValue)}'),
            _buildInfoRow('Total Maintenance',
                'PKR ${controller.formatCurrency(totalMaintenanceCost)}'),
            _buildInfoRow('Total Investment',
                'PKR ${controller.formatCurrency(totalCost)}'),
            const Divider(),
            _buildInfoRow('Depreciation Amount',
                'PKR ${controller.formatCurrency(asset.purchaseCost - asset.currentValue)}'),
            _buildInfoRow('Depreciation %',
                '${controller.getDepreciationPercentage().toStringAsFixed(1)}%'),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceCostCard(
      double totalMaintenanceCost, double maintenanceTrend) {
    return Card(
      color: notifier.getBgColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Maintenance Analysis',
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Total Maintenance Cost',
                'PKR ${controller.formatCurrency(totalMaintenanceCost)}'),
            _buildInfoRow('Average per Record',
                'PKR ${controller.formatCurrency(controller.maintenanceRecords.isEmpty ? 0 : totalMaintenanceCost / controller.maintenanceRecords.length)}'),
            _buildInfoRow(
                'Total Records', '${controller.maintenanceRecords.length}'),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  'Cost Trend (6 months): ',
                  style: TextStyle(
                    color: notifier.text,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Icon(
                  maintenanceTrend > 0
                      ? Icons.trending_up
                      : maintenanceTrend < 0
                          ? Icons.trending_down
                          : Icons.trending_flat,
                  color: maintenanceTrend > 0
                      ? Colors.red
                      : maintenanceTrend < 0
                          ? Colors.green
                          : Colors.grey,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  '${maintenanceTrend.abs().toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: maintenanceTrend > 0
                        ? Colors.red
                        : maintenanceTrend < 0
                            ? Colors.green
                            : Colors.grey,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                color: notifier.text.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                color: notifier.text,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuditTrailTab() {
    return AuditTrailView(assetId: widget.assetId);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
