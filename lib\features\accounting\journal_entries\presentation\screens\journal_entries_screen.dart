import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../models/finance/journal_entry_model.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../../../../core/services/account_type_helper_service.dart';
import '../controllers/journal_entry_controller.dart';
import '../widgets/journal_entry_card.dart';
import '../widgets/create_journal_entry_dialog.dart';
// DEBUG IMPORT - COMMENTED OUT FOR PRODUCTION
// import '../widgets/date_handling_test_widget.dart';

class JournalEntriesScreen extends StatelessWidget {
  const JournalEntriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    log('🔍 [UI DEBUG] JournalEntriesScreen build() called');
    final controller = Get.find<JournalEntryController>();
    log('🔍 [UI DEBUG] Controller found: ${controller.runtimeType}');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Journal Entries'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // DEBUG BUTTON - COMMENTED OUT FOR PRODUCTION
          // IconButton(
          //   icon: const Icon(Icons.bug_report),
          //   onPressed: () {
          //     log('🔍 [UI DEBUG] Test button pressed');
          //     controller.testDebugLogs();
          //   },
          // ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.loadJournalEntries(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Summary Cards
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            child: Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    context,
                    'Total Entries',
                    controller.journalEntries.length.toString(),
                    Icons.receipt_long,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(() {
                    final draftCount = controller.journalEntries
                        .where(
                            (entry) => entry.status == JournalEntryStatus.draft)
                        .length;
                    return _buildSummaryCard(
                      context,
                      'Draft Entries',
                      draftCount.toString(),
                      Icons.edit_note,
                      Colors.orange,
                    );
                  }),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(() {
                    final postedCount = controller.journalEntries
                        .where((entry) =>
                            entry.status == JournalEntryStatus.posted)
                        .length;
                    return _buildSummaryCard(
                      context,
                      'Posted Entries',
                      postedCount.toString(),
                      Icons.check_circle,
                      Colors.green,
                    );
                  }),
                ),
              ],
            ),
          ),

          // Debug Information Panel (for troubleshooting)
          Obx(() => Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  border:
                      Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.bug_report, color: Colors.orange, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Debug Info (Remove in Production)',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange[800],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Entries: ${controller.journalEntries.length} | '
                      'Loading: ${controller.isLoading.value} | '
                      'Repository: ${controller.repository.runtimeType}',
                      style: TextStyle(fontSize: 11, color: Colors.grey[700]),
                    ),
                    if (controller.journalEntries.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Latest: ${controller.journalEntries.first.entryNumber} - ${controller.journalEntries.first.description}',
                        style: TextStyle(fontSize: 11, color: Colors.grey[700]),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    // Check for voucher 45545414
                    Builder(builder: (context) {
                      final voucher45545414Entries = controller.journalEntries
                          .where((entry) =>
                              entry.referenceNumber == '45545414' ||
                              entry.description.contains('45545414') ||
                              entry.sourceTransactionId == '45545414')
                          .toList();
                      if (voucher45545414Entries.isNotEmpty) {
                        return Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            '✅ Found ${voucher45545414Entries.length} entries for voucher 45545414',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.green[700],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      } else {
                        return Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            '❌ No entries found for voucher 45545414',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.red[700],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      }
                    }),
                    // Color coding explanation
                    const SizedBox(height: 4),
                    Text(
                      '🎨 Colors: Assets/Expenses (Debit=GREEN, Credit=RED) | Liabilities/Equity/Revenue (Debit=RED, Credit=GREEN)',
                      style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              )),

          // DEBUG WIDGET - COMMENTED OUT FOR PRODUCTION
          // Date Handling Test Widget (Debug)
          // const SizedBox(
          //   height: 300,
          //   child: DateHandlingTestWidget(),
          // ),

          // Journal Entries List
          Expanded(
            child: Obx(() {
              // Debug logging for UI state
              log('🔍 [UI DEBUG] Obx rebuilding - isLoading: ${controller.isLoading.value}');
              log('🔍 [UI DEBUG] Obx rebuilding - entries count: ${controller.journalEntries.length}');

              if (controller.isLoading.value) {
                log('🔍 [UI DEBUG] Showing loading indicator');
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (controller.journalEntries.isEmpty) {
                log('🔍 [UI DEBUG] Showing empty state');
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No journal entries found',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Create your first journal entry to get started',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[500],
                            ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: () => _showCreateJournalEntryDialog(context),
                        icon: const Icon(Icons.add),
                        label: const Text('Create Journal Entry'),
                      ),
                    ],
                  ),
                );
              }

              log('🔍 [UI DEBUG] Showing list view with ${controller.journalEntries.length} entries');
              return RefreshIndicator(
                onRefresh: controller.loadJournalEntries,
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: controller.journalEntries.length,
                  itemBuilder: (context, index) {
                    final entry = controller.journalEntries[index];
                    log('🔍 [UI DEBUG] Building item $index: ${entry.entryNumber}');
                    return JournalEntryCard(
                      journalEntry: entry,
                      onTap: () => _showJournalEntryDetails(context, entry),
                      onPost: entry.status == JournalEntryStatus.draft
                          ? () => _confirmPostEntry(context, entry)
                          : null,
                      onDelete: entry.status == JournalEntryStatus.draft
                          ? () => _confirmDeleteEntry(context, entry)
                          : null,
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showCreateJournalEntryDialog(context),
        icon: const Icon(Icons.add),
        label: const Text('New Entry'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateJournalEntryDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CreateJournalEntryDialog(),
    );
  }

  void _showJournalEntryDetails(BuildContext context, JournalEntryModel entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Journal Entry ${entry.entryNumber}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Date', entry.entryDate.toString().split(' ')[0]),
              _buildDetailRow('Description', entry.description),
              _buildDetailRow('Status', entry.status.displayName),
              _buildDetailRow('Type', entry.entryType.displayName),
              if (entry.referenceNumber != null)
                _buildDetailRow('Reference', entry.referenceNumber!),
              const SizedBox(height: 16),
              Text(
                'Journal Lines',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              ...entry.lines.map((line) => Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${line.accountNumber} - ${line.accountName}',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          Text(line.description),
                          const SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Debit: \$${line.debitAmount.toStringAsFixed(2)}',
                                style: TextStyle(
                                  color: line.debitAmount > 0
                                      ? _getAmountColor(line, isDebit: true)
                                      : Colors.grey,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                'Credit: \$${line.creditAmount.toStringAsFixed(2)}',
                                style: TextStyle(
                                  color: line.creditAmount > 0
                                      ? _getAmountColor(line, isDebit: false)
                                      : Colors.grey,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  )),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Debits: \$${entry.totalDebits.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700], // Neutral color for totals
                    ),
                  ),
                  Text(
                    'Total Credits: \$${entry.totalCredits.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700], // Neutral color for totals
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _confirmPostEntry(BuildContext context, JournalEntryModel entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Post Journal Entry'),
        content: Text(
          'Are you sure you want to post journal entry ${entry.entryNumber}? '
          'Posted entries cannot be modified.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Get.find<JournalEntryController>().postJournalEntry(entry.id);
            },
            child: const Text('Post'),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteEntry(BuildContext context, JournalEntryModel entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Journal Entry'),
        content: Text(
          'Are you sure you want to delete journal entry ${entry.entryNumber}? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Get.find<JournalEntryController>().deleteJournalEntry(entry.id);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Get the appropriate color for debit/credit amounts based on Chart of Accounts principles
  Color _getAmountColor(JournalEntryLineModel line, {required bool isDebit}) {
    // Determine account category from account number
    // Account numbers follow the pattern: 1000-1999 (Assets), 2000-2999 (Liabilities), etc.
    final accountCategory = _getAccountCategoryFromNumber(line.accountNumber);

    if (accountCategory == null) {
      // Fallback to grey if we can't determine the account category
      return Colors.grey[600]!;
    }

    // Use the AccountTypeHelperService to get the correct color
    return AccountTypeHelperService.getBalanceChangeColorByCategory(
      accountCategory: accountCategory,
      debitAmount: isDebit ? line.debitAmount : 0.0,
      creditAmount: isDebit ? 0.0 : line.creditAmount,
    );
  }

  /// Determine account category from account number
  AccountCategory? _getAccountCategoryFromNumber(String accountNumber) {
    if (accountNumber.isEmpty) return null;

    try {
      final number = int.tryParse(accountNumber);
      if (number == null) return null;

      // Determine category based on account number ranges
      if (number >= 1000 && number <= 1999) {
        return AccountCategory.assets;
      } else if (number >= 2000 && number <= 2999) {
        return AccountCategory.liabilities;
      } else if (number >= 3000 && number <= 3999) {
        return AccountCategory.equity;
      } else if (number >= 4000 && number <= 4999) {
        return AccountCategory.revenue;
      } else if (number >= 5000 && number <= 5999) {
        return AccountCategory.expenses;
      }
    } catch (e) {
      log('Error parsing account number: $accountNumber - $e');
    }

    return null;
  }
}
