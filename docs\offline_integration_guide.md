# Offline Integration Guide

## Overview

This guide provides step-by-step instructions for integrating the offline-first architecture into your logistics application.

## Prerequisites

1. **Dependencies Added**: Ensure all required dependencies are in `pubspec.yaml`
2. **Hive Initialization**: Hive is initialized in `AppDependencyInjection`
3. **Service Bindings**: Offline services are registered in `AppBindings`

## Integration Steps

### Step 1: Initialize Offline Services

The offline services are automatically initialized when the app starts through the dependency injection system:

```dart
// In AppBindings._registerOfflineServices()
Get.put(ConnectivityService(), permanent: true);
Get.put(OfflineStateService(), permanent: true);
Get.put(SyncService(), permanent: true);
Get.put(OfflineVoucherService(), permanent: true);
Get.put(OfflineJournalService(), permanent: true);
```

### Step 2: Update Existing Controllers

#### Voucher Controllers

Replace the existing voucher repository with the offline-capable version:

```dart
// In your voucher controller
class VoucherController extends GetxController {
  late final OfflineVoucherRepository _repository;
  
  @override
  void onInit() {
    super.onInit();
    _repository = OfflineVoucherRepository(
      onlineRepository: Get.find<VoucherRepository>(),
      connectivityService: Get.find<ConnectivityService>(),
      offlineVoucherService: Get.find<OfflineVoucherService>(),
    );
  }
  
  Future<void> createVoucher(Map<String, dynamic> voucherData) async {
    final result = await _repository.createVoucher(
      uid: currentUserId,
      voucher: voucherData,
    );
    
    result.fold(
      (failure) => showError(failure.message),
      (success) => showSuccess(success.message),
    );
  }
}
```

### Step 3: Add UI Components

#### Add Offline Status Indicator

```dart
// In your main app bar or header
AppBar(
  title: Text('Logistics App'),
  actions: [
    OfflineStatusIndicator(
      showDetails: true,
      onTap: () => _showOfflineDetails(),
    ),
  ],
)
```

#### Add Offline Mode Banner

```dart
// In your main scaffold
Scaffold(
  body: Column(
    children: [
      OfflineModeBanner(),
      Expanded(
        child: YourMainContent(),
      ),
    ],
  ),
)
```

#### Add Sync Progress Indicator

```dart
// In your main content area
Column(
  children: [
    SyncProgressIndicator(),
    Expanded(
      child: YourContent(),
    ),
  ],
)
```

### Step 4: Handle Offline Voucher Lists

```dart
// Create a screen to show offline vouchers
class OfflineVouchersScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Offline Vouchers'),
        actions: [
          IconButton(
            icon: Icon(Icons.sync),
            onPressed: () => _syncOfflineVouchers(),
          ),
        ],
      ),
      body: OfflineVoucherList(
        showSyncedVouchers: false,
        onRefresh: () => _refreshVouchers(),
      ),
    );
  }
}
```

### Step 5: Cache Chart of Accounts

Ensure Chart of Accounts are cached for offline use:

```dart
// In your chart of accounts controller
class ChartOfAccountsController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    _cacheAccountsForOfflineUse();
  }
  
  Future<void> _cacheAccountsForOfflineUse() async {
    final accounts = await _getChartOfAccounts();
    final offlineJournalService = Get.find<OfflineJournalService>();
    await offlineJournalService.cacheChartOfAccounts(accounts);
  }
}
```

## Configuration Options

### Connectivity Monitoring

```dart
// Configure connectivity check intervals
class ConnectivityService extends GetxController {
  static const Duration _connectionTestInterval = Duration(seconds: 30);
  static const Duration _connectionTestTimeout = Duration(seconds: 10);
}
```

### Sync Settings

```dart
// Configure sync behavior
class SyncService extends GetxController {
  static const int maxRetryAttempts = 5;
  static const Duration baseSyncDelay = Duration(seconds: 30);
  static const Duration maxSyncDelay = Duration(minutes: 30);
  static const int batchSize = 10;
}
```

### Offline Storage

```dart
// Configure offline storage retention
class OfflineStateService extends GetxController {
  // Automatically clean up synced data older than 30 days
  static const Duration dataRetentionPeriod = Duration(days: 30);
}
```

## Error Handling

### Network Errors

```dart
// Handle network-related errors gracefully
Future<void> handleNetworkError(Exception error) async {
  if (error is SocketException) {
    // Network unavailable - operation will be queued for sync
    showMessage('Operation saved offline. Will sync when online.');
  } else if (error is TimeoutException) {
    // Request timeout - retry offline
    showMessage('Request timed out. Saving offline.');
  }
}
```

### Sync Conflicts

```dart
// Handle sync conflicts
void handleSyncConflict(ConflictData conflict) {
  showDialog(
    context: context,
    builder: (context) => ConflictResolutionDialog(
      conflict: conflict,
      onResolved: (resolution) => _applySyncResolution(resolution),
    ),
  );
}
```

## Testing Integration

### Unit Tests

```dart
// Test offline voucher creation
testWidgets('should create voucher offline when no connectivity', (tester) async {
  // Arrange
  final mockConnectivity = MockConnectivityService();
  when(mockConnectivity.isOnline).thenReturn(false);
  
  // Act
  final result = await offlineVoucherRepository.createVoucher(
    uid: 'test-uid',
    voucher: testVoucherData,
  );
  
  // Assert
  expect(result.isRight(), true);
  verify(mockOfflineVoucherService.createVoucherOffline(any, any)).called(1);
});
```

### Integration Tests

```dart
// Test end-to-end offline flow
testWidgets('should handle complete offline-to-online flow', (tester) async {
  // 1. Create voucher offline
  // 2. Verify local storage
  // 3. Simulate connectivity restoration
  // 4. Verify sync completion
  // 5. Verify data consistency
});
```

## Performance Considerations

### Memory Management

- Offline data is stored in Hive boxes with automatic cleanup
- Synced operations are removed from local storage
- Balance snapshots are retained for audit purposes

### Storage Optimization

- Use compression for large datasets
- Implement data pagination for large offline collections
- Regular cleanup of old sync metadata

### Sync Optimization

- Batch operations to reduce server load
- Use exponential backoff for failed operations
- Prioritize critical operations (vouchers, journal entries)

## Monitoring and Analytics

### Offline Usage Metrics

```dart
// Track offline usage patterns
class OfflineAnalytics {
  static void trackOfflineOperation(String operationType) {
    // Log offline operation for analytics
  }
  
  static void trackSyncPerformance(Duration syncTime, int operationCount) {
    // Log sync performance metrics
  }
}
```

### Health Checks

```dart
// Monitor offline system health
class OfflineHealthCheck {
  static Future<Map<String, dynamic>> getHealthStatus() async {
    return {
      'connectivity': connectivityService.status,
      'pendingOperations': offlineStateService.pendingOperations,
      'lastSyncTime': offlineStateService.lastSyncTime,
      'storageUsage': await _getStorageUsage(),
    };
  }
}
```

## Troubleshooting

### Common Issues

1. **Sync Failures**: Check network connectivity and server availability
2. **Storage Full**: Implement storage cleanup and user notifications
3. **Data Conflicts**: Provide clear conflict resolution interfaces
4. **Performance Issues**: Monitor sync batch sizes and frequency

### Debug Tools

```dart
// Enable debug logging for offline operations
class OfflineDebug {
  static void enableVerboseLogging() {
    // Enable detailed logging for all offline operations
  }
  
  static Future<void> exportDebugData() async {
    // Export offline data for debugging
  }
}
```

## Migration Strategy

### Gradual Rollout

1. **Phase 1**: Enable offline for voucher creation only
2. **Phase 2**: Add journal entries and balance updates
3. **Phase 3**: Extend to all financial operations
4. **Phase 4**: Add advanced conflict resolution

### Rollback Plan

- Keep existing online-only code paths
- Feature flags for offline functionality
- Ability to disable offline mode per user/company

## Security Considerations

### Data Encryption

- Encrypt sensitive data in local storage
- Use secure key management for encryption keys
- Implement data integrity checks

### Access Control

- Maintain user authentication state offline
- Implement offline permission checks
- Secure sync communication channels

This integration guide ensures a smooth transition to offline-first architecture while maintaining data integrity and user experience.
