Stack trace:
Frame         Function      Args
0007FFFF9DC0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9DC0, 0007FFFF8CC0) msys-2.0.dll+0x1FE8E
0007FFFF9DC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA098) msys-2.0.dll+0x67F9
0007FFFF9DC0  000210046832 (000210286019, 0007FFFF9C78, 0007FFFF9DC0, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DC0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DC0  000210068E24 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0A0  00021006A225 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB81C60000 ntdll.dll
7FFB80610000 KERNEL32.DLL
7FFB7F470000 KERNELBASE.dll
7FFB81860000 USER32.dll
7FFB7F9C0000 win32u.dll
7FFB80730000 GDI32.dll
7FFB7F1B0000 gdi32full.dll
7FFB7F070000 msvcp_win.dll
7FFB7F870000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB80FC0000 advapi32.dll
7FFB7FF00000 msvcrt.dll
7FFB800D0000 sechost.dll
7FFB7FFB0000 RPCRT4.dll
7FFB7E280000 CRYPTBASE.DLL
7FFB7EFD0000 bcryptPrimitives.dll
7FFB7FA80000 IMM32.DLL
