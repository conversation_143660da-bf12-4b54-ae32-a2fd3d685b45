import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/voucher_model.dart';

void main() {
  group('Broker Account Validation Tests', () {
    // These tests focus on the voucher model structure and validation logic
    // without requiring service dependencies

    test(
        'should allow voucher with broker fees when brokerAccountId is null (using default mappings)',
        () {
      // Create a voucher with broker fees but null account ID
      final voucher = _createTestVoucher(
        brokerFees: 5000.0,
        brokerAccountId:
            null, // This should be allowed - will use default mappings
      );

      // The validation should pass because null account ID is acceptable
      // when using default account mappings
      expect(voucher.brokerFees, equals(5000.0));
      expect(voucher.brokerAccountId, isNull);

      // This represents the scenario that was previously failing
      // but should now pass with our fix
    });

    test(
        'should reject voucher with broker fees when brokerAccountId is empty string',
        () {
      // Create a voucher with broker fees and empty account ID
      final voucher = _createTestVoucher(
        brokerFees: 5000.0,
        brokerAccountId: '', // This should fail - empty string is invalid
      );

      // The validation should fail because empty string is not acceptable
      expect(voucher.brokerFees, equals(5000.0));
      expect(voucher.brokerAccountId, equals(''));

      // This represents a true validation error that should be caught
    });

    test('should allow voucher with broker fees when brokerAccountId is valid',
        () {
      // Create a voucher with broker fees and valid account ID
      final voucher = _createTestVoucher(
        brokerFees: 5000.0,
        brokerAccountId: 'valid-account-id',
      );

      // The validation should pass
      expect(voucher.brokerFees, equals(5000.0));
      expect(voucher.brokerAccountId, equals('valid-account-id'));
    });

    test('should allow voucher without broker fees regardless of account ID',
        () {
      // Create a voucher without broker fees
      final voucher = _createTestVoucher(
        brokerFees: 0.0,
        brokerAccountId: null, // Should not matter when no fees
      );

      // The validation should pass
      expect(voucher.brokerFees, equals(0.0));
      expect(voucher.brokerAccountId, isNull);
    });

    test('should handle both Own and Outsource broker types with fees', () {
      // Test Own broker type
      final ownBrokerVoucher = _createTestVoucher(
        brokerFees: 3000.0,
        brokerAccountId: 'own-broker-account',
        brokerType: 'Own',
      );

      expect(ownBrokerVoucher.brokerType, equals('Own'));
      expect(ownBrokerVoucher.brokerFees, equals(3000.0));

      // Test Outsource broker type
      final outsourceBrokerVoucher = _createTestVoucher(
        brokerFees: 5000.0,
        brokerAccountId: 'outsource-broker-account',
        brokerType: 'Outsource',
      );

      expect(outsourceBrokerVoucher.brokerType, equals('Outsource'));
      expect(outsourceBrokerVoucher.brokerFees, equals(5000.0));
    });
  });
}

/// Helper function to create test voucher with specific parameters
VoucherModel _createTestVoucher({
  required double brokerFees,
  required String? brokerAccountId,
  String brokerType = 'Outsource',
  double munshianaFees = 0.0,
  String? munshianaAccountId,
}) {
  return VoucherModel(
    voucherNumber: 'TEST-001',
    voucherStatus: 'Active',
    departureDate: '2024-01-15',
    driverName: 'Test Driver',
    invoiceTasNumberList: ['TAS-001'],
    invoiceBiltyNumberList: ['BILTY-001'],
    weightInTons: 25,
    productName: 'Test Product',
    totalNumberOfBags: 500,
    brokerType: brokerType,
    brokerName: 'Test Broker',
    brokerFees: brokerFees,
    munshianaFees: munshianaFees,
    brokerAccount: 'Test Broker Account', // Legacy field
    munshianaAccount: 'Test Munshiana Account', // Legacy field
    driverPhoneNumber: '**********',
    truckNumber: 'TRK-001',
    conveyNoteNumber: 'CN-001',
    totalFreight: 50000.0,
    companyFreight: 50000.0,
    calculatedProfit: 5000.0,
    calculatedTax: 2300.0,
    calculatedFreightTax: 7500.0,
    // Chart of Accounts fields
    brokerAccountId: brokerAccountId,
    munshianaAccountId: munshianaAccountId,
    // Payment transactions
    paymentTransactions: [],
  );
}
