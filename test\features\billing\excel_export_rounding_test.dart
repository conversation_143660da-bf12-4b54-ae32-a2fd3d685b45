import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/features/billing/presentation/controllers/billing_excel_export_controller.dart';

void main() {
  group('Excel Export Rounding Tests', () {
    late BillingExcelExportController controller;

    setUp(() {
      controller = BillingExcelExportController();
    });

    group('formatMonetaryAmount', () {
      test('should format whole numbers without decimal places', () {
        expect(controller.formatMonetaryAmount(123.0), equals('123'));
        expect(controller.formatMonetaryAmount(1000.0), equals('1000'));
        expect(controller.formatMonetaryAmount(0.0), equals('0'));
        expect(controller.formatMonetaryAmount(999.0), equals('999'));
      });

      test('should format decimal numbers with minimal decimal places', () {
        expect(controller.formatMonetaryAmount(123.5), equals('123.5'));
        expect(controller.formatMonetaryAmount(123.75), equals('123.75'));
        expect(controller.formatMonetaryAmount(123.50), equals('123.5'));
        expect(controller.formatMonetaryAmount(123.25), equals('123.25'));
      });

      test('should handle edge cases', () {
        expect(controller.formatMonetaryAmount(0.5), equals('0.5'));
        expect(controller.formatMonetaryAmount(0.75), equals('0.75'));
        expect(controller.formatMonetaryAmount(0.25), equals('0.25'));
        expect(controller.formatMonetaryAmount(1.0), equals('1'));
      });

      test('should remove trailing zeros', () {
        expect(controller.formatMonetaryAmount(123.10), equals('123.1'));
        expect(controller.formatMonetaryAmount(123.00), equals('123'));
        expect(controller.formatMonetaryAmount(123.90), equals('123.9'));
      });
    });

    group('calculateTaxAmount with rounding', () {
      test('should apply rounding to single authority tax calculation', () {
        controller.selectedTaxAuthorities.add('SRB');
        
        // Test case that produces decimal: 1000 * 0.15 = 150.0 (exact)
        final taxAmount = controller.calculateTaxAmount(1000.0);
        expect(taxAmount, equals(150.0));
        
        // Test case that produces decimal: 1234.49 * 0.15 = 185.1735
        final taxAmount2 = controller.calculateTaxAmount(1234.49);
        expect(taxAmount2, equals(185.0)); // Should round down (185.1735 → 185)
        
        // Test case with 0.5 decimal: 1233.33 * 0.15 = 184.9995 ≈ 185.0
        final taxAmount3 = controller.calculateTaxAmount(1233.33);
        expect(taxAmount3, equals(185.0));
      });

      test('should apply rounding to dual authority tax calculation', () {
        controller.selectedTaxAuthorities.addAll(['SRB', 'PRA']);
        
        // Dual authorities: (amount * 0.5 * 0.15) * 2 = amount * 0.15
        final taxAmount = controller.calculateTaxAmount(1000.0);
        expect(taxAmount, equals(150.0));
        
        // Test with decimal result
        final taxAmount2 = controller.calculateTaxAmount(1234.49);
        expect(taxAmount2, equals(185.0)); // Should round down
      });
    });

    group('calculateIndividualTaxAmount with rounding', () {
      test('should apply rounding to individual tax amounts', () {
        controller.selectedTaxAuthorities.add('SRB');
        
        final taxAmount = controller.calculateIndividualTaxAmount(1234.49, 'SRB');
        expect(taxAmount, equals(185.0)); // 1234.49 * 0.15 = 185.1735 → 185
        
        // Test with dual authorities
        controller.selectedTaxAuthorities.add('PRA');
        final taxAmount2 = controller.calculateIndividualTaxAmount(1234.49, 'SRB');
        expect(taxAmount2, equals(93.0)); // (1234.49 * 0.5 * 0.15) = 92.58675 → 93
      });

      test('should return 0 for non-selected authorities', () {
        controller.selectedTaxAuthorities.add('SRB');
        
        final taxAmount = controller.calculateIndividualTaxAmount(1000.0, 'PRA');
        expect(taxAmount, equals(0.0));
      });
    });

    group('Rounding Examples from Requirements', () {
      test('should match all specified rounding examples', () {
        // Test the core rounding function used throughout the system
        expect(MonetaryRounding.roundHalfUp(123.49), equals(123.0)); // round down
        expect(MonetaryRounding.roundHalfUp(123.50), equals(123.0)); // round down
        expect(MonetaryRounding.roundHalfUp(123.75), equals(124.0)); // round up
        expect(MonetaryRounding.roundHalfUp(123.00), equals(123.0)); // no change
      });

      test('should format rounded amounts correctly for Excel', () {
        // Test that rounded amounts are formatted without unnecessary decimals
        expect(controller.formatMonetaryAmount(MonetaryRounding.roundHalfUp(123.49)), equals('123'));
        expect(controller.formatMonetaryAmount(MonetaryRounding.roundHalfUp(123.50)), equals('123'));
        expect(controller.formatMonetaryAmount(MonetaryRounding.roundHalfUp(123.75)), equals('124'));
        expect(controller.formatMonetaryAmount(MonetaryRounding.roundHalfUp(123.00)), equals('123'));
      });
    });

    group('Real-world Excel Export Scenarios', () {
      test('should handle typical invoice amount calculations with rounding', () {
        // Simulate typical slab billing calculations that would appear in Excel
        
        // Invoice 1: 5 tons × 150 km × 2.333 rate = 1749.75 → should round to 1750
        final amount1 = 5.0 * 150.0 * 2.333; // 1749.75
        final rounded1 = MonetaryRounding.roundHalfUp(amount1);
        expect(rounded1, equals(1750.0));
        expect(controller.formatMonetaryAmount(rounded1), equals('1750'));
        
        // 80% amount: 1750 × 0.80 = 1400.0
        final amount80_1 = MonetaryRounding.roundHalfUp(rounded1 * 0.80);
        expect(amount80_1, equals(1400.0));
        expect(controller.formatMonetaryAmount(amount80_1), equals('1400'));
      });

      test('should handle tax calculations on rounded amounts', () {
        controller.selectedTaxAuthorities.add('SRB');
        
        // Base amount: 1750 (already rounded)
        final baseAmount = 1750.0;
        
        // Tax: 1750 × 0.15 = 262.5 → should round down to 262
        final taxAmount = controller.calculateTaxAmount(baseAmount);
        expect(taxAmount, equals(262.0)); // 262.5 rounds down
        expect(controller.formatMonetaryAmount(taxAmount), equals('262'));
        
        // Total with tax: 1750 + 262 = 2012
        final totalWithTax = MonetaryRounding.roundHalfUp(baseAmount + taxAmount);
        expect(totalWithTax, equals(2012.0));
        expect(controller.formatMonetaryAmount(totalWithTax), equals('2012'));
      });

      test('should handle batch totals with proper rounding', () {
        // Simulate multiple invoices being totaled
        final invoiceAmounts = [
          1749.75, // → 1750
          2345.25, // → 2345  
          1876.50, // → 1876 (0.5 rounds down)
          3421.99, // → 3422
        ];
        
        final roundedAmounts = invoiceAmounts
            .map(MonetaryRounding.roundHalfUp)
            .toList();
        
        expect(roundedAmounts, equals([1750.0, 2345.0, 1876.0, 3422.0]));
        
        // Total: 1750 + 2345 + 1876 + 3422 = 9393
        final total = roundedAmounts.reduce((a, b) => a + b);
        expect(total, equals(9393.0));
        expect(controller.formatMonetaryAmount(total), equals('9393'));
        
        // 80% total: 9393 × 0.80 = 7514.4 → 7514
        final total80 = MonetaryRounding.roundHalfUp(total * 0.80);
        expect(total80, equals(7514.0));
        expect(controller.formatMonetaryAmount(total80), equals('7514'));
      });

      test('should demonstrate Excel export never shows .00 for whole numbers', () {
        // Test various whole number scenarios
        final wholeNumbers = [123.0, 1000.0, 0.0, 999.0, 1750.0, 2012.0];
        
        for (final number in wholeNumbers) {
          final formatted = controller.formatMonetaryAmount(number);
          expect(formatted, isNot(contains('.00')), 
            reason: 'Whole number $number should not show .00 decimal places');
          expect(formatted, isNot(contains('.')), 
            reason: 'Whole number $number should not show any decimal point');
        }
      });

      test('should show minimal decimals for non-whole numbers', () {
        // Test that non-whole numbers show appropriate decimals
        final decimalNumbers = {
          123.5: '123.5',
          123.75: '123.75',
          123.25: '123.25',
          0.5: '0.5',
          0.75: '0.75',
        };
        
        decimalNumbers.forEach((number, expected) {
          final formatted = controller.formatMonetaryAmount(number);
          expect(formatted, equals(expected),
            reason: 'Number $number should format as $expected');
        });
      });
    });

    tearDown(() {
      controller.selectedTaxAuthorities.clear();
    });
  });
}
