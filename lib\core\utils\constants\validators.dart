String? validateInvoiceNumber(String? value) {
  if (value == null || value.isEmpty) {
    return 'Invoice number is required.';
  }
  if (!RegExp(r'^\d+$').hasMatch(value)) {
    return 'Invoice number must be numeric.';
  }
  return null;
}

String? validateInvoiceStatus(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Must select.';
  }
  return null;
}

String? validateDeliveryMode(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Must select.';
  }
  return null;
}

String? validateRegion(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Must select.';
  }
  return null;
}

String? validateOrderNumber(String? value) {
  if (value == null || value.isEmpty) {
    return 'Order number is required.';
  }
  if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
    return 'Order number must be alphanumeric.';
  }
  return null;
}

// Validator for order date to check if it follows DD/MM/YYYY format
String? orderDateValidator(String? value) {
  final dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
  if (value == null || value.isEmpty) {
    return 'Please enter the order date';
  }

  // Check if the input matches the DD/MM/YYYY pattern
  if (!dateRegex.hasMatch(value)) {
    return 'Invalid date format. Please use DD/MM/YYYY';
  }

  // Further validate the date (e.g., check if the day, month, and year are correct)
  List<String> parts = value.split('/');
  int day = int.parse(parts[0]);
  int month = int.parse(parts[1]);
  int year = int.parse(parts[2]);

  if (month < 1 || month > 12) {
    return 'Invalid month';
  }

  if (day < 1 || day > 31) {
    return 'Invalid day';
  }

  // Handle months with less than 31 days
  if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
    return 'This month has only 30 days';
  }

  // Handle February
  if (month == 2) {
    bool isLeapYear = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
    if (day > (isLeapYear ? 29 : 28)) {
      return 'February has only ${isLeapYear ? 29 : 28} days in this year';
    }
  }

  return null; // No error, valid date
}

String? validateProductName(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Product name is required.';
  }
  return null;
}

String? validateNumberOfBags(String? value) {
  if (value == null || value.isEmpty) {
    return 'Number of bags is required.';
  }
  if (int.tryParse(value) == null || int.parse(value) <= 0) {
    return 'Number of bags must be a positive integer.';
  }
  return null;
}

String? validateConsignorName(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Consignor name is required.';
  }
  return null;
}

String? validateConsignorPickUpAddress(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Consignor destination is required.';
  }
  return null;
}

String? validateGstFbrTrackingNumber(String? value) {
  if (value == null || value.isEmpty) {
    return 'GST/FBR tracking number is required.';
  }
  if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
    return 'GST/FBR tracking number must be alphanumeric.';
  }
  return null;
}

String? validateCustomerName(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Customer name is required.';
  }
  return null;
}

String? validateCustomerCnic(String? value) {
  if (value == null || value.isEmpty) {
    return 'Customer CNIC is required.';
  }
  // if (!RegExp(r'^\d{15}$').hasMatch(value)) {
  //   return 'Customer CNIC must be 15 digits.';
  // }
  return null;
}

String? validateTruckNumber(String? value) {
  if (value == null || value.isEmpty) {
    return 'Truck/Wagon number is required';
  }
  // Allow any format - no format restrictions
  return null;
}

String? validateTasNumber(String? value) {
  if (value == null || value.isEmpty) {
    return 'TAS number is required.';
  }
  if (!RegExp(r'^\d+$').hasMatch(value)) {
    return 'TAS number must be numeric.';
  }
  return null;
}

String? validateConveyNoteNumber(String? value) {
  if (value == null || value.isEmpty) {
    return 'Convey note number is required.';
  }
  if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
    return 'Convey note number must be alphanumeric.';
  }
  return null;
}

String? validateDestinationAddress(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Destination address is required.';
  }
  return null;
}

String? validateWeightPerBag(String? value) {
  if (value == null || value.isEmpty) {
    return 'Weight per bag is required.';
  }
  if (double.tryParse(value) == null || double.parse(value) <= 0) {
    return 'Weight must be a positive number.';
  }
  return null;
}

String? validateDistanceInKilometers(String? value) {
  if (value == null || value.isEmpty) {
    return 'Distance in kilometers is required.';
  }
  if (double.tryParse(value) == null || double.parse(value) <= 0) {
    return 'Distance must be a positive number.';
  }
  return null;
}

String? validateCarrierName(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Carrier name is required.';
  }
  return null;
}

String? validateShipmentDate(String? value) {
  final dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
  if (value == null || value.isEmpty) {
    return 'Please enter the shipment date.';
  }

  if (!dateRegex.hasMatch(value)) {
    return 'Invalid date format. Use DD/MM/YYYY.';
  }

  try {
    List<String> parts = value.split('/');
    int day = int.parse(parts[0]);
    int month = int.parse(parts[1]);
    int year = int.parse(parts[2]);

    if (month < 1 || month > 12) {
      return 'Invalid month.';
    }

    if (day < 1 || day > 31) {
      return 'Invalid day.';
    }

    // Handle months with less than 31 days
    if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
      return 'This month has only 30 days.';
    }

    // Handle February
    if (month == 2) {
      bool isLeapYear = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
      if (day > (isLeapYear ? 29 : 28)) {
        return 'February has only ${isLeapYear ? 29 : 28} days this year.';
      }
    }

    // Check if shipment date is earlier than order date
    if (dateRegex.hasMatch(value)) {
      List<String> orderParts = value.split('/');
      DateTime order = DateTime(
        int.parse(orderParts[2]),
        int.parse(orderParts[1]),
        int.parse(orderParts[0]),
      );
      DateTime shipment = DateTime(year, month, day);

      if (shipment.isBefore(order)) {
        return 'Shipment date cannot be earlier than order date.';
      }
    }
  } catch (e) {
    return 'Invalid date.';
  }

  return null; // Valid date
}

String? validateOrderDate(String? value) {
  final dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
  if (value == null || value.isEmpty) {
    return 'Please enter the order date.';
  }

  if (!dateRegex.hasMatch(value)) {
    return 'Invalid date format. Use DD/MM/YYYY.';
  }

  try {
    List<String> parts = value.split('/');
    int day = int.parse(parts[0]);
    int month = int.parse(parts[1]);
    int year = int.parse(parts[2]);

    if (month < 1 || month > 12) {
      return 'Invalid month.';
    }

    if (day < 1 || day > 31) {
      return 'Invalid day.';
    }

    // Handle months with less than 31 days
    if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
      return 'This month has only 30 days.';
    }

    // Handle February
    if (month == 2) {
      bool isLeapYear = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
      if (day > (isLeapYear ? 29 : 28)) {
        return 'February has only ${isLeapYear ? 29 : 28} days this year.';
      }
    }
  } catch (e) {
    return 'Invalid date.';
  }

  return null; // Valid date
}
