import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/payment_transaction_model.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/models/voucher_model.dart';

/// Test to verify payment-based truck freight accounting
/// This test ensures truck freight is recorded only when payments are made
void main() {
  group('Payment-Based Truck Freight Accounting Tests', () {
    // Mock Chart of Accounts for testing
    late ChartOfAccountsModel bankAccount;
    late ChartOfAccountsModel truckFreightAccount;
    late VoucherModel testVoucher;

    setUpAll(() {
      // Create mock bank account (Asset)
      bankAccount = ChartOfAccountsModel(
        id: 'bank_001',
        accountName: 'Main Bank Account',
        accountNumber: '1100',
        category: AccountCategory.assets,
        accountType: AccountType.bank,
        description: 'Primary bank account',
        isActive: true,
        balance: 500000.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );

      // Create mock truck freight account (Liability)
      truckFreightAccount = ChartOfAccountsModel(
        id: 'truck_freight_001',
        accountName: 'Truck Freight Payable',
        accountNumber: '2100',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        description: 'Truck freight expenses payable',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );

      // Create test voucher
      testVoucher = VoucherModel(
        voucherNumber: 'V-TEST-001',
        voucherStatus: 'Active',
        departureDate: DateTime.now().toIso8601String(),
        driverName: 'Test Driver',
        invoiceTasNumberList: ['TAS-001'],
        invoiceBiltyNumberList: ['BILTY-001'],
        weightInTons: 25,
        productName: 'Test Product',
        totalNumberOfBags: 500,
        brokerType: 'Outsource',
        brokerName: 'Test Customer',
        brokerFees: 5000.0,
        munshianaFees: 3000.0,
        brokerAccount: 'Test Broker Account',
        munshianaAccount: 'Test Munshiana Account',
        driverPhoneNumber: '**********',
        truckNumber: 'TRK-001',
        conveyNoteNumber: 'CN-001',
        totalFreight: 100000.0, // Total truck freight amount
        truckFreightAccountId:
            truckFreightAccount.id, // Configured truck freight account
        paymentTransactions: [], // No payments initially
      );
    });

    test('should validate truck freight account configuration', () {
      // Assert: Truck freight account should be properly configured
      expect(truckFreightAccount.category, equals(AccountCategory.liabilities));
      expect(truckFreightAccount.accountType,
          equals(AccountType.currentLiabilities));
      expect(truckFreightAccount.isActive, isTrue);
      expect(truckFreightAccount.balance, equals(0.0)); // Should start at zero

      // Verify voucher has truck freight account configured
      expect(testVoucher.truckFreightAccountId, equals(truckFreightAccount.id));
      expect(testVoucher.totalFreight, equals(100000.0));
    });

    test('should create payment transaction with Chart of Accounts reference',
        () {
      // Arrange & Act: Create payment transaction for truck freight
      final payment = PaymentTransactionModel(
        id: 'payment_001',
        voucherId: testVoucher.voucherNumber,
        method: PaymentMethod.check,
        status: PaymentStatus.paid,
        amount: 50000.0, // Partial payment
        pendingAmount: 50000.0, // Remaining amount
        transactionDate: DateTime.now(),
        accountId: bankAccount.id, // Chart of Accounts reference
        accountName: bankAccount.accountName,
        checkNumber: 'CHK-001',
        bankName: bankAccount.accountName,
        notes: 'Partial truck freight payment',
      );

      // Assert: Payment should reference Chart of Accounts
      expect(payment.accountId, equals(bankAccount.id));
      expect(payment.accountName, equals(bankAccount.accountName));
      expect(payment.voucherId, equals(testVoucher.voucherNumber));
      expect(payment.amount, equals(50000.0));
      expect(payment.pendingAmount, equals(50000.0));
    });

    test('should validate payment-based accounting logic', () {
      // Arrange: Create multiple payments for the same voucher
      final payments = [
        PaymentTransactionModel(
          id: 'payment_002',
          voucherId: testVoucher.voucherNumber,
          method: PaymentMethod.check,
          status: PaymentStatus.paid,
          amount: 30000.0,
          pendingAmount: 70000.0,
          transactionDate: DateTime.now(),
          accountId: bankAccount.id,
          accountName: bankAccount.accountName,
          checkNumber: 'CHK-002',
        ),
        PaymentTransactionModel(
          id: 'payment_003',
          voucherId: testVoucher.voucherNumber,
          method: PaymentMethod.accountTransfer,
          status: PaymentStatus.paid,
          amount: 40000.0,
          pendingAmount: 30000.0,
          transactionDate: DateTime.now().add(const Duration(days: 1)),
          accountId: bankAccount.id,
          accountName: bankAccount.accountName,
        ),
        PaymentTransactionModel(
          id: 'payment_004',
          voucherId: testVoucher.voucherNumber,
          method: PaymentMethod.check,
          status: PaymentStatus.paid,
          amount: 30000.0,
          pendingAmount: 0.0, // Final payment
          transactionDate: DateTime.now().add(const Duration(days: 2)),
          accountId: bankAccount.id,
          accountName: bankAccount.accountName,
          checkNumber: 'CHK-003',
        ),
      ];

      // Act: Calculate total payments
      final totalPayments =
          payments.fold<double>(0, (sum, payment) => sum + payment.amount);

      // Assert: Payments should match voucher total freight
      expect(totalPayments, equals(100000.0));
      expect(payments, hasLength(3));

      // Verify each payment has proper Chart of Accounts reference
      for (final payment in payments) {
        expect(payment.accountId, equals(bankAccount.id));
        expect(payment.voucherId, equals(testVoucher.voucherNumber));
        expect(payment.amount, greaterThan(0));
      }

      // Verify payment progression (pending amounts decrease)
      expect(payments[0].pendingAmount, equals(70000.0));
      expect(payments[1].pendingAmount, equals(30000.0));
      expect(payments[2].pendingAmount, equals(0.0)); // Fully paid
    });

    test('should validate double-entry accounting for truck freight payments',
        () {
      // Arrange: Create a truck freight payment
      final payment = PaymentTransactionModel(
        id: 'payment_005',
        voucherId: testVoucher.voucherNumber,
        method: PaymentMethod.check,
        status: PaymentStatus.paid,
        amount: 25000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: bankAccount.id,
        accountName: bankAccount.accountName,
        checkNumber: 'CHK-004',
      );

      // Simulate journal entry creation logic
      // 1. Credit Bank Account (Asset decreases)
      final bankCreditAmount = payment.amount;

      // 2. Debit Truck Freight Account (Liability increases)
      final truckFreightDebitAmount = payment.amount;

      // Assert: Double-entry accounting should balance
      expect(bankCreditAmount, equals(truckFreightDebitAmount));
      expect(bankCreditAmount, equals(25000.0));

      // Verify account types are correct for the transaction
      expect(bankAccount.category, equals(AccountCategory.assets));
      expect(truckFreightAccount.category, equals(AccountCategory.liabilities));
    });

    test('should handle payment serialization with truck freight context', () {
      // Arrange: Create payment with truck freight context
      final payment = PaymentTransactionModel(
        id: 'payment_006',
        voucherId: testVoucher.voucherNumber,
        method: PaymentMethod.accountTransfer,
        status: PaymentStatus.partial,
        amount: 75000.0,
        pendingAmount: 25000.0,
        transactionDate: DateTime.now(),
        accountId: bankAccount.id,
        accountName: bankAccount.accountName,
        notes: 'Truck freight payment - partial',
      );

      // Act: Convert to map and back
      final paymentMap = payment.toMap();
      final reconstructedPayment = PaymentTransactionModel.fromMap(paymentMap);

      // Assert: Data should be preserved through serialization
      expect(reconstructedPayment.id, equals(payment.id));
      expect(reconstructedPayment.voucherId, equals(payment.voucherId));
      expect(reconstructedPayment.method, equals(payment.method));
      expect(reconstructedPayment.status, equals(payment.status));
      expect(reconstructedPayment.amount, equals(payment.amount));
      expect(reconstructedPayment.pendingAmount, equals(payment.pendingAmount));
      expect(reconstructedPayment.accountId, equals(payment.accountId));
      expect(reconstructedPayment.accountName, equals(payment.accountName));
      expect(reconstructedPayment.notes, contains('Truck freight'));
    });

    test(
        'should validate voucher without payments has no truck freight entries',
        () {
      // Arrange: Voucher without any payments
      final voucherWithoutPayments = VoucherModel(
        voucherNumber: 'V-TEST-002',
        voucherStatus: 'Active',
        departureDate: DateTime.now().toIso8601String(),
        driverName: 'Test Driver 2',
        invoiceTasNumberList: ['TAS-002'],
        invoiceBiltyNumberList: ['BILTY-002'],
        weightInTons: 20,
        productName: 'Test Product 2',
        totalNumberOfBags: 400,
        brokerType: 'Outsource',
        brokerName: 'Test Customer 2',
        brokerFees: 4000.0,
        munshianaFees: 2000.0,
        brokerAccount: 'Test Broker Account 2',
        munshianaAccount: 'Test Munshiana Account 2',
        driverPhoneNumber: '**********',
        truckNumber: 'TRK-002',
        conveyNoteNumber: 'CN-002',
        totalFreight: 80000.0,
        truckFreightAccountId: truckFreightAccount.id,
        paymentTransactions: [], // No payments
      );

      // Assert: Voucher should have truck freight amount but no payments
      expect(voucherWithoutPayments.totalFreight, equals(80000.0));
      expect(voucherWithoutPayments.paymentTransactions, isEmpty);
      expect(voucherWithoutPayments.truckFreightAccountId,
          equals(truckFreightAccount.id));

      // This voucher should NOT create any truck freight journal entries
      // until payments are actually made
    });

    test('should validate payment method compatibility with truck freight', () {
      // Arrange: Test different payment methods
      final paymentMethods = [
        PaymentMethod.check,
        PaymentMethod.accountTransfer,
        PaymentMethod.fuelCard,
      ];

      for (final method in paymentMethods) {
        // Act: Create payment with different methods
        final payment = PaymentTransactionModel(
          id: 'payment_${method.name}',
          voucherId: testVoucher.voucherNumber,
          method: method,
          status: PaymentStatus.paid,
          amount: 10000.0,
          pendingAmount: 0.0,
          transactionDate: DateTime.now(),
          accountId: bankAccount.id,
          accountName: bankAccount.accountName,
        );

        // Assert: All payment methods should work with truck freight accounting
        expect(payment.method, equals(method));
        expect(payment.accountId, equals(bankAccount.id));
        expect(payment.voucherId, equals(testVoucher.voucherNumber));
        expect(payment.amount, equals(10000.0));
      }
    });

    test('should validate truck freight account balance progression', () {
      // Arrange: Simulate progressive payments and balance updates
      double truckFreightBalance = 0.0;
      final payments = [
        {'amount': 20000.0, 'description': 'First payment'},
        {'amount': 35000.0, 'description': 'Second payment'},
        {'amount': 45000.0, 'description': 'Final payment'},
      ];

      // Act & Assert: Simulate balance updates for each payment
      for (final paymentData in payments) {
        final amount = paymentData['amount'] as double;

        // Truck freight liability increases with each payment (debit increases liability)
        truckFreightBalance += amount;

        // Verify balance progression
        expect(truckFreightBalance, greaterThan(0));
        expect(amount, greaterThan(0));
      }

      // Final balance should equal total payments
      expect(truckFreightBalance, equals(100000.0));
    });
  });
}
