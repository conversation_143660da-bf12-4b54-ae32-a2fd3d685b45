import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import 'package:logestics/features/billing/presentation/controllers/billing_excel_export_controller.dart';

class BillingExcelExportDialog extends StatelessWidget {
  BillingExcelExportDialog({super.key});

  // Using late initialization to avoid constructor issues
  late final BillingExcelExportController controller;

  String formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd/MM/yyyy').format(date);
  }

  @override
  Widget build(BuildContext context) {
    // Initialize controller here to avoid constructor issues
    controller = Get.find<BillingExcelExportController>();
    notifier = Provider.of(context, listen: true);

    return Dialog(
      backgroundColor: notifier.getcardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 900,
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Generate Billing Excel Report',
                    style: AppTextStyles.titleStyle.copyWith(
                      color: notifier.text,
                      fontSize: 20,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: Icon(Icons.close, color: notifier.text),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Content
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Date Range Section
                      _buildDateRangeSection(notifier),

                      const SizedBox(height: 24),

                      // Slab Selection Section
                      _buildSlabSelectionSection(notifier),

                      const SizedBox(height: 24),

                      // Tax Authority Selection Section
                      _buildTaxAuthoritySection(notifier),

                      const SizedBox(height: 24),

                      // Custom Bill Name Section
                      _buildCustomBillNameSection(notifier),

                      const SizedBox(height: 24),

                      // Invoice Preview
                      _buildInvoicePreview(notifier),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Action Buttons
              _buildActionButtons(notifier),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeSection(notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Date Range',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            // Start Date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Start Date',
                    style: TextStyle(
                      color: notifier.text.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => InkWell(
                        onTap: () => _selectStartDate(Get.context!),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: notifier.text.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(8),
                            color: notifier.getBgColor,
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today,
                                  color: notifier.text.withValues(alpha: 0.6),
                                  size: 16),
                              const SizedBox(width: 8),
                              Text(
                                controller.startDate.value != null
                                    ? DateFormat('dd/MM/yyyy')
                                        .format(controller.startDate.value!)
                                    : 'Select Date',
                                style: TextStyle(color: notifier.text),
                              ),
                            ],
                          ),
                        ),
                      )),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // End Date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'End Date',
                    style: TextStyle(
                      color: notifier.text.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => InkWell(
                        onTap: () => _selectEndDate(Get.context!),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: notifier.text.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(8),
                            color: notifier.getBgColor,
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today,
                                  color: notifier.text.withValues(alpha: 0.6),
                                  size: 16),
                              const SizedBox(width: 8),
                              Text(
                                controller.endDate.value != null
                                    ? DateFormat('dd/MM/yyyy')
                                        .format(controller.endDate.value!)
                                    : 'Select Date',
                                style: TextStyle(color: notifier.text),
                              ),
                            ],
                          ),
                        ),
                      )),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Date range indicator
        Obx(() {
          if (controller.startDate.value != null &&
              controller.endDate.value != null) {
            final dateFormat = DateFormat('dd/MM/yyyy');
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Text(
                '${dateFormat.format(controller.startDate.value!)} - ${dateFormat.format(controller.endDate.value!)}',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  Widget _buildSlabSelectionSection(notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Slab',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),
        Obx(() {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(color: notifier.text.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: controller.isLoadingSlabs.value
                ? const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('Loading slabs...'),
                      ],
                    ),
                  )
                : controller.availableSlabs.isEmpty
                    ? Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.warning_amber_outlined,
                              color: Colors.orange.shade600,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'No active slabs found',
                              style: TextStyle(
                                color: Colors.orange.shade600,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      )
                    : DropdownButton<String>(
                        value: controller.selectedSlab.value != null
                            ? controller.getSlabDisplayName(
                                controller.selectedSlab.value!)
                            : null,
                        hint: Text(
                          'Select Slab (Required)',
                          style: TextStyle(
                              color: notifier.text.withValues(alpha: 0.6)),
                        ),
                        isExpanded: true,
                        underline: const SizedBox.shrink(),
                        items: controller.availableSlabs.map((slab) {
                          final displayName =
                              controller.getSlabDisplayName(slab);
                          return DropdownMenuItem<String>(
                            value: displayName,
                            child: Text(
                              '$displayName (${DateFormat('dd/MM/yyyy').format(slab.startDate)} - ${DateFormat('dd/MM/yyyy').format(slab.expiryDate)})',
                              style: TextStyle(color: notifier.text),
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          controller.onSlabSelected(value);
                        },
                      ),
          );
        }),
      ],
    );
  }

  Widget _buildTaxAuthoritySection(notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tax Authorities (Optional)',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Select up to 2 tax authorities for 15% sales tax calculation',
          style: TextStyle(
            color: notifier.text.withValues(alpha: 0.7),
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 16),

        // Tax authority checkboxes
        Obx(() => Wrap(
              spacing: 16,
              runSpacing: 8,
              children: controller.availableTaxAuthorities.map((authority) {
                final isSelected = controller.isTaxAuthoritySelected(authority);
                return InkWell(
                  onTap: () => controller.toggleTaxAuthority(authority),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Colors.blue.withValues(alpha: 0.1)
                          : notifier.getBgColor,
                      border: Border.all(
                        color: isSelected
                            ? Colors.blue
                            : notifier.text.withValues(alpha: 0.3),
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isSelected
                              ? Icons.check_box
                              : Icons.check_box_outline_blank,
                          color: isSelected
                              ? Colors.blue
                              : notifier.text.withValues(alpha: 0.6),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          authority,
                          style: TextStyle(
                            color: isSelected ? Colors.blue : notifier.text,
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            )),

        // Error message
        Obx(() => controller.taxAuthorityError.value.isNotEmpty
            ? Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  controller.taxAuthorityError.value,
                  style: TextStyle(
                    color: Colors.red.shade600,
                    fontSize: 12,
                  ),
                ),
              )
            : const SizedBox.shrink()),

        // Selected authorities info
        Obx(() => controller.selectedTaxAuthorities.isNotEmpty
            ? Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                    border:
                        Border.all(color: Colors.green.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.green.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        controller.selectedTaxAuthorities.length == 1
                            ? '15% tax will be applied to ${controller.selectedTaxAuthorities.first}'
                            : '15% tax will be split equally between ${controller.selectedTaxAuthorities.join(' and ')}',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : const SizedBox.shrink()),
      ],
    );
  }

  Widget _buildCustomBillNameSection(notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Custom Bill Name (Optional)',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          'Enter a custom name for this bill. If left empty, the auto-generated bill number will be used.',
          style: TextStyle(
            color: notifier.text.withValues(alpha: 0.7),
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        MyTextFormField(
          titleText: null,
          labelText: 'Custom Bill Name',
          hintText: 'e.g., January 2025 Billing, Customer ABC Bills, etc.',
          controller: controller.customBillNameController,
        ),
      ],
    );
  }

  Widget _buildInvoicePreview(notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Invoice Preview',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 16,
              ),
            ),
            // Invoice count indicator
            Obx(() => Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFFe85542).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0xFFe85542).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    '${controller.filteredInvoices.length} Pending Billing Invoices',
                    style: TextStyle(
                      color: const Color(0xFFe85542),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )),
          ],
        ),

        const SizedBox(height: 16),

        // Preview table will be added in the next part
        Obx(() {
          if (controller.filteredInvoices.isEmpty) {
            return Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: notifier.text.withValues(alpha: 0.2)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.receipt_long_outlined,
                      size: 48,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No invoices found for selected date range',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Please select a date range to preview billing data',
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          // Preview table
          return Container(
            height: 300,
            decoration: BoxDecoration(
              border: Border.all(color: notifier.text.withValues(alpha: 0.2)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: SingleChildScrollView(
              child: Table(
                border: TableBorder.all(
                  color: notifier.isDark
                      ? Colors.grey.shade700
                      : Colors.grey.shade300,
                  width: 1,
                ),
                columnWidths: const {
                  0: FlexColumnWidth(0.8), // SN.
                  1: FlexColumnWidth(1.2), // Lifting Date (formerly Order Date)
                  2: FlexColumnWidth(1.2), // Truck No
                  3: FlexColumnWidth(1.2), // Bilty No
                  4: FlexColumnWidth(1.5), // Convey Note Number
                  5: FlexColumnWidth(1.3), // Product Name - new column
                  6: FlexColumnWidth(1.3), // Product TAS NO
                  7: FlexColumnWidth(1.3), // Destination
                  8: FlexColumnWidth(1.0), // No of Bags
                  9: FlexColumnWidth(1.0), // Weight
                  10: FlexColumnWidth(0.8), // KM
                  11: FlexColumnWidth(1.2), // District
                },
                children: [
                  // Table header
                  TableRow(
                    decoration: BoxDecoration(
                      color: notifier.getHoverColor,
                    ),
                    children: [
                      DataTableHeaderCell(
                        text: 'SN.',
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: 'Lifting Date', // Changed from 'Order Date'
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: 'Truck No',
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: 'Bilty No',
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: 'Convey Note Number',
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: 'Product Name', // New column added
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: 'Product TAS NO',
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: 'Destination',
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: 'No of Bags',
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text:
                            'Weight', // Already correct (formerly Total Weight (Tons))
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: 'KM',
                        textColor: notifier.text,
                      ),
                      DataTableHeaderCell(
                        text: 'District',
                        textColor: notifier.text,
                      ),
                    ],
                  ),
                  // Table rows
                  ...controller.filteredInvoices
                      .take(10)
                      .toList()
                      .asMap()
                      .entries
                      .map((entry) {
                    final index = entry.key;
                    final invoice = entry.value;
                    return TableRow(
                      children: [
                        DataTableCell(
                          text: (index + 1).toString(), // SN.
                        ),
                        DataTableCell(
                          text: formatDate(invoice
                              .orderDate), // Lifting Date (formerly Order Date)
                        ),
                        DataTableCell(
                          text: invoice.truckNumber, // Truck No
                        ),
                        DataTableCell(
                          text: invoice.biltyNumber, // Bilty No
                        ),
                        DataTableCell(
                          text: invoice.conveyNoteNumber, // Convey Note Number
                        ),
                        DataTableCell(
                          text:
                              invoice.productName, // Product Name - new column
                        ),
                        DataTableCell(
                          text: invoice.tasNumber, // Product TAS NO
                        ),
                        DataTableCell(
                          text: invoice.stationName, // Destination
                        ),
                        DataTableCell(
                          text: invoice.numberOfBags.toString(), // No of Bags
                        ),
                        DataTableCell(
                          text: invoice.weightPerBag.toString(), // Weight
                        ),
                        DataTableCell(
                          text: invoice.distanceInKilometers.toString(), // KM
                        ),
                        DataTableCell(
                          text: invoice.districtName, // District
                        ),
                      ],
                    );
                  }),
                  // Show "and X more..." if there are more than 10 invoices
                  if (controller.filteredInvoices.length > 10)
                    TableRow(
                      children: [
                        TableCell(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            child: Center(
                              child: Text(
                                '... and ${controller.filteredInvoices.length - 10} more invoices',
                                style: TextStyle(
                                  color: notifier.text.withValues(alpha: 0.6),
                                  fontSize: 12,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          ),
                        ),
                        // Empty cells for other columns (11 more cells for 12 total)
                        const TableCell(child: SizedBox()),
                        const TableCell(child: SizedBox()),
                        const TableCell(child: SizedBox()),
                        const TableCell(child: SizedBox()),
                        const TableCell(child: SizedBox()),
                        const TableCell(child: SizedBox()),
                        const TableCell(child: SizedBox()),
                        const TableCell(child: SizedBox()),
                        const TableCell(child: SizedBox()),
                        const TableCell(child: SizedBox()),
                        const TableCell(
                            child: SizedBox()), // Added for Product Name column
                      ],
                    ),
                ],
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildActionButtons(notifier) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Cancel Button
        OutlinedButton(
          onPressed: () => Get.back(),
          child: const Text('Cancel'),
        ),

        const SizedBox(width: 12),

        // Save Bill Button
        Obx(() => ElevatedButton.icon(
              onPressed: controller.isSavingBill.value ||
                      controller.isLoading.value ||
                      controller.filteredInvoices.isEmpty ||
                      controller.selectedSlab.value == null
                  ? null
                  : () {
                      controller.saveBill();
                    },
              icon: controller.isSavingBill.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.save),
              label: Text(controller.isSavingBill.value
                  ? 'Saving Bill...'
                  : 'Save Bill'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            )),

        const SizedBox(width: 12),

        // Generate Excel Button
        Obx(() => ElevatedButton.icon(
              onPressed: controller.isLoading.value ||
                      controller.filteredInvoices.isEmpty ||
                      controller.selectedSlab.value == null
                  ? null
                  : () {
                      controller.generateExcel();
                    },
              icon: controller.isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.file_download),
              label: Text(controller.isLoading.value
                  ? 'Generating...'
                  : 'Generate Excel'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            )),
      ],
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ??
          DateTime.now().subtract(const Duration(days: 30)),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      controller.setDateRange(
          picked, controller.endDate.value ?? DateTime.now());
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? DateTime.now(),
      firstDate: controller.startDate.value ?? DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      controller.setDateRange(
          controller.startDate.value ??
              DateTime.now().subtract(const Duration(days: 30)),
          picked);
    }
  }
}
