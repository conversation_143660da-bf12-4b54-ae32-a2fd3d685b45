import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'lib/core/services/loan_receivable_balance_correction_service.dart';
import 'lib/features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';

/// Simple utility to run the loan receivable balance fix
/// This addresses the specific issue where loan receivable accounts show negative balances
/// when they should show positive balances after debit entries
void main() {
  runApp(const LoanReceivableBalanceFixApp());
}

class LoanReceivableBalanceFixApp extends StatelessWidget {
  const LoanReceivableBalanceFixApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Loan Receivable Balance Fix',
      theme: ThemeData(primarySwatch: Colors.green),
      home: const LoanReceivableBalanceFixScreen(),
    );
  }
}

class LoanReceivableBalanceFixScreen extends StatefulWidget {
  const LoanReceivableBalanceFixScreen({super.key});

  @override
  State<LoanReceivableBalanceFixScreen> createState() => _LoanReceivableBalanceFixScreenState();
}

class _LoanReceivableBalanceFixScreenState extends State<LoanReceivableBalanceFixScreen> {
  final TextEditingController _companyUidController = TextEditingController();
  bool _isRunning = false;
  String _currentOperation = '';
  final List<String> _logs = [];

  // Service
  late LoanReceivableBalanceCorrectionService _correctionService;

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  void _initializeService() {
    // In a real app, this would be injected via GetX or other DI
    final accountsRepository = Get.find<ChartOfAccountsRepository>();
    
    _correctionService = LoanReceivableBalanceCorrectionService(
      accountsRepository: accountsRepository,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Loan Receivable Balance Fix'),
        backgroundColor: Colors.green[700],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Loan Receivable Balance Fix Utility',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[800],
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This utility fixes the critical issue where loan receivable accounts show '
                      'negative balances when they should show positive balances after debit entries.\n\n'
                      'The fix ensures that:\n'
                      '• Asset accounts (loan receivable) show positive balances when debited\n'
                      '• Running balances update correctly in chronological order\n'
                      '• Current balance reflects proper cumulative balance\n'
                      '• All balance calculations follow proper accounting principles',
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _companyUidController,
                      decoration: const InputDecoration(
                        labelText: 'Company UID',
                        hintText: 'Enter the company UID to fix',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _isRunning ? null : _validateCurrentState,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[700],
                          ),
                          child: const Text('1. Validate Current State'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _runBalanceFix,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange[700],
                          ),
                          child: const Text('2. Fix Balances'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _validateAfterFix,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green[700],
                          ),
                          child: const Text('3. Validate Fix'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isRunning ? null : _runCompleteFix,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[700],
                        minimumSize: const Size(double.infinity, 56),
                      ),
                      child: _isRunning 
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 3,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Text(
                                  _currentOperation.isEmpty ? 'Processing...' : _currentOperation,
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ],
                            )
                          : const Text(
                              'RUN COMPLETE LOAN RECEIVABLE BALANCE FIX',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Fix Progress Log:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _logs.isEmpty
                    ? const Center(
                        child: Text(
                          'No operations run yet. Click a button above to start fixing loan receivable balance issues.',
                          style: TextStyle(color: Colors.grey),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(8),
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          final log = _logs[index];
                          Color textColor = Colors.black;
                          if (log.contains('✅')) textColor = Colors.green;
                          if (log.contains('❌')) textColor = Colors.red;
                          if (log.contains('⚠️')) textColor = Colors.orange;
                          if (log.contains('🔧')) textColor = Colors.blue;
                          if (log.contains('📊')) textColor = Colors.purple;
                          if (log.contains('🔍')) textColor = Colors.teal;
                          
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 1),
                            child: Text(
                              log,
                              style: TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 11,
                                color: textColor,
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)} $message');
    });
    dev.log(message);
  }

  String _getCompanyUid() {
    final uid = _companyUidController.text.trim();
    if (uid.isEmpty) {
      _addLog('❌ Please enter a company UID');
      return '';
    }
    return uid;
  }

  Future<void> _validateCurrentState() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
      _currentOperation = 'Validating current state...';
    });

    try {
      _addLog('🔍 Validating current loan receivable balance state for company: $uid');
      
      final validationResult = await _correctionService.validateFix(uid);
      
      _addLog('📊 Validation Results:');
      _addLog('   Total accounts checked: ${validationResult.totalAccounts}');
      _addLog('   Valid accounts: ${validationResult.validAccounts}');
      _addLog('   Invalid accounts: ${validationResult.invalidAccounts}');
      
      if (validationResult.success) {
        _addLog('✅ All loan receivable accounts have correct balances');
      } else {
        _addLog('❌ Found ${validationResult.invalidAccounts} accounts with incorrect balances');
        for (final issue in validationResult.issues) {
          _addLog('   • $issue');
        }
      }
      
    } catch (e) {
      _addLog('❌ Error during validation: $e');
    } finally {
      setState(() {
        _isRunning = false;
        _currentOperation = '';
      });
    }
  }

  Future<void> _runBalanceFix() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
      _currentOperation = 'Fixing loan receivable balances...';
    });

    try {
      _addLog('🔧 Starting loan receivable balance fix for company: $uid');
      
      final fixResult = await _correctionService.fixLoanReceivableBalances(uid);
      
      if (fixResult.success) {
        _addLog('✅ Loan receivable balance fix completed successfully');
        _addLog('📊 Fix Results:');
        _addLog('   Accounts found: ${fixResult.accountsFound}');
        _addLog('   Accounts fixed: ${fixResult.accountsFixed}');
        _addLog('   Journal entries fixed: ${fixResult.journalEntriesFixed}');
        
        _addLog('');
        _addLog('📋 Account Details:');
        for (final detail in fixResult.details) {
          if (detail.fixed) {
            _addLog('   ✅ ${detail.accountName}: ${detail.originalBalance} → ${detail.newBalance}');
            _addLog('     Journal entries fixed: ${detail.entriesFixed}');
          } else {
            _addLog('   ✓ ${detail.accountName}: Already correct (${detail.originalBalance})');
          }
        }
        
      } else {
        _addLog('❌ Loan receivable balance fix failed: ${fixResult.errorMessage}');
        
        if (fixResult.errors.isNotEmpty) {
          _addLog('');
          _addLog('📋 Detailed Errors:');
          for (final error in fixResult.errors) {
            _addLog('   • $error');
          }
        }
      }
      
    } catch (e) {
      _addLog('❌ Error during balance fix: $e');
    } finally {
      setState(() {
        _isRunning = false;
        _currentOperation = '';
      });
    }
  }

  Future<void> _validateAfterFix() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
      _currentOperation = 'Validating after fix...';
    });

    try {
      _addLog('🔍 Validating loan receivable balances after fix for company: $uid');
      
      final validationResult = await _correctionService.validateFix(uid);
      
      _addLog('📊 Post-Fix Validation Results:');
      _addLog('   Total accounts checked: ${validationResult.totalAccounts}');
      _addLog('   Valid accounts: ${validationResult.validAccounts}');
      _addLog('   Invalid accounts: ${validationResult.invalidAccounts}');
      
      if (validationResult.success) {
        _addLog('✅ SUCCESS: All loan receivable accounts now have correct balances!');
        _addLog('🎉 The fix has resolved all balance calculation issues');
      } else {
        _addLog('❌ Some issues remain after fix:');
        for (final issue in validationResult.issues) {
          _addLog('   • $issue');
        }
      }
      
    } catch (e) {
      _addLog('❌ Error during post-fix validation: $e');
    } finally {
      setState(() {
        _isRunning = false;
        _currentOperation = '';
      });
    }
  }

  Future<void> _runCompleteFix() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    _addLog('🚀 Starting complete loan receivable balance fix process for company: $uid');
    _addLog('📋 This will validate, fix, and re-validate loan receivable account balances...');
    
    // Step 1: Validate current state
    setState(() {
      _currentOperation = 'Step 1: Validating current state...';
    });
    await _validateCurrentState();
    
    // Step 2: Run the fix
    setState(() {
      _currentOperation = 'Step 2: Fixing balances...';
    });
    await _runBalanceFix();
    
    // Step 3: Validate after fix
    setState(() {
      _currentOperation = 'Step 3: Validating fix...';
    });
    await _validateAfterFix();
    
    setState(() {
      _currentOperation = '';
    });
    
    _addLog('');
    _addLog('🎉 COMPLETE LOAN RECEIVABLE BALANCE FIX PROCESS FINISHED!');
    _addLog('');
    _addLog('💡 EXPECTED RESULTS:');
    _addLog('   • Loan receivable accounts now show POSITIVE balances when loans are approved');
    _addLog('   • Running balances update correctly in chronological order');
    _addLog('   • Current balance reflects proper cumulative balance');
    _addLog('   • All balance calculations follow Asset account principles');
    _addLog('');
    _addLog('🔍 VERIFICATION STEPS:');
    _addLog('   1. Check loan receivable account balance - should be positive after loan approval');
    _addLog('   2. Verify running balances in transaction history are correct');
    _addLog('   3. Confirm balance calculations follow Asset account principles');
    _addLog('   4. Test new loan approvals to ensure they work correctly');
  }

  @override
  void dispose() {
    _companyUidController.dispose();
    super.dispose();
  }
}
