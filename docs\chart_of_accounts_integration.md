# Chart of Accounts Integration for Expense and Deposit Features

## Overview

This document describes the complete integration of the Chart of Accounts system with the expense and deposit features in the logistics application. The integration follows standard accounting principles and provides automatic journal entry generation.

## Architecture

### Two-Account System

Both expense and deposit transactions now use a two-account system:

#### Expenses
- **Source Account**: Asset account (Cash, Bank) where money decreases from
- **Destination Account**: Expense account (Fuel, Office Supplies) where money increases to

#### Deposits  
- **Source Account**: Revenue/Liability account (Customer, Sales Revenue) where money decreases from
- **Destination Account**: Asset account (Cash, Bank) where money increases to

### Accounting Principles

The system follows standard double-entry bookkeeping:

- **Assets & Expenses**: Increase with debits, decrease with credits
- **Revenue & Liabilities**: Increase with credits, decrease with debits
- **Balanced Entries**: Total debits always equal total credits

## Key Components

### Models

#### ExpenseModel
```dart
class ExpenseModel {
  // Legacy fields (backward compatibility)
  final String accountId;
  final String accountName;
  
  // Chart of Accounts fields
  final String? sourceAccountId;
  final String? sourceAccountName;
  final String? destinationAccountId;
  final String? destinationAccountName;
  
  // Helper methods
  bool get usesChartOfAccounts;
  bool get usesLegacyAccount;
  String get accountSetupDescription;
}
```

#### DepositModel
Similar structure to ExpenseModel with the same Chart of Accounts fields.

### Services

#### ExpenseJournalIntegrationService
- Validates expense transactions for Chart of Accounts compliance
- Generates journal entries with proper debit/credit logic
- Updates account balances automatically
- Creates ledger entries for audit trail

#### DepositJournalIntegrationService
- Similar functionality for deposit transactions
- Handles Revenue/Liability to Asset account flows

#### Accounting Hook Services
- **ExpenseAccountingHookService**: Automatically triggered on expense creation
- **DepositAccountingHookService**: Automatically triggered on deposit creation

### Migration

#### ChartOfAccountsMigrationService
Provides utilities to migrate legacy records:

```dart
// Migrate expenses (dry run first)
final result = await migrationService.migrateExpenses(
  uid: userId,
  dryRun: true,
);

// Actual migration
final result = await migrationService.migrateExpenses(
  uid: userId,
  dryRun: false,
);
```

## Usage Examples

### Creating an Expense

1. **Select Source Account**: Choose a Cash or Bank account
2. **Select Destination Account**: Choose an Expense account (Fuel, Office Supplies, etc.)
3. **Enter Amount**: System validates sufficient balance
4. **Save**: Automatic journal entry created

**Journal Entry Generated**:
```
Date: 2024-01-15
Description: Expense: Fuel - ABC Gas Station

Account                 Debit    Credit
Fuel Expense           $100.00
    Cash Account                 $100.00
```

### Creating a Deposit

1. **Select Source Account**: Choose a Revenue or Customer account
2. **Select Destination Account**: Choose a Cash or Bank account
3. **Enter Amount**: No balance validation needed (external source)
4. **Save**: Automatic journal entry created

**Journal Entry Generated**:
```
Date: 2024-01-15
Description: Deposit from: Customer ABC - Service Revenue

Account                 Debit    Credit
Cash Account           $500.00
    Service Revenue              $500.00
```

## Validation Rules

### Expense Validation
- Source account must be Asset category (Cash, Bank)
- Destination account must be Expense category
- Source account must have sufficient balance
- Both accounts must be active

### Deposit Validation
- Source account must be Revenue or Liability category
- Destination account must be Asset category (Cash, Bank)
- Both accounts must be active

## Error Handling

### Common Validation Errors
- "Please select a source account"
- "Source account should be an Asset account (Cash, Bank, etc.)"
- "Insufficient balance in [Account Name]"
- "Selected account is inactive"

### System Errors
- Journal entry generation failures are logged
- Account balance update failures trigger rollbacks
- Atomic transactions ensure data consistency

## Backward Compatibility

### Legacy Records
- Existing expenses/deposits continue to work
- Legacy account balance updates still function
- Migration utility available for upgrading records

### Gradual Migration
- Users can choose Chart of Accounts or legacy system
- No forced migration required
- Data integrity maintained throughout

## Best Practices

### Account Setup
1. Create proper Chart of Accounts structure
2. Set up Asset accounts (Cash, Bank)
3. Set up Expense accounts by category
4. Set up Revenue accounts for deposits

### Transaction Flow
1. Always validate account selections
2. Check account balances before expenses
3. Use descriptive transaction descriptions
4. Review journal entries for accuracy

### Maintenance
1. Regularly review account balances
2. Monitor journal entry generation
3. Use migration utility for legacy records
4. Keep Chart of Accounts organized

## Troubleshooting

### Journal Entries Not Created
- Check if accounts use Chart of Accounts system
- Verify account categories are correct
- Ensure accounts are active
- Check service logs for errors

### Balance Discrepancies
- Verify journal entries were created
- Check account balance calculations
- Review ledger entries
- Ensure atomic transactions completed

### UI Issues
- Verify Chart of Accounts repository is injected
- Check dropdown filters for account categories
- Ensure proper validation messages display

## Technical Implementation

### Service Dependencies
```dart
ExpenseJournalIntegrationService(
  automaticJournalService,
  generalLedgerService,
  journalEntryService,
  chartOfAccountsService,
  mappingService,
  accountLedgerService,
)
```

### Controller Integration
```dart
ExpenseController(
  expenseRepository,
  accountRepository,
  payeeRepository,
  categoryRepository,
  chartOfAccountsRepository, // New dependency
)
```

### Repository Updates
```dart
// Expense creation with Chart of Accounts
if (expense.usesChartOfAccounts) {
  await _firebaseService.createExpense(expense);
  await _hookService.onExpenseCreated(expense);
} else {
  // Legacy account balance update
  await _updateLegacyAccountBalance(expense);
  await _firebaseService.createExpense(expense);
}
```

## Future Enhancements

### Planned Features
- Bulk migration tools
- Advanced validation rules
- Custom account mapping
- Enhanced reporting integration

### Performance Optimizations
- Batch journal entry creation
- Optimized balance calculations
- Cached account lookups
- Improved error handling

## Support

For issues or questions regarding the Chart of Accounts integration:

1. Check this documentation first
2. Review service logs for errors
3. Use migration utilities for legacy data
4. Test with dry-run migrations before actual migration

The integration provides a robust, scalable foundation for professional accounting practices while maintaining backward compatibility with existing data.
