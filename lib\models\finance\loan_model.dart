import 'package:cloud_firestore/cloud_firestore.dart';

class LoanModel {
  final String id;
  final String uid; // User ID for ownership tracking
  final String requestedBy; // User/Company ID requesting the loan
  final String requestedByName; // User/Company name requesting the loan
  final String requestedTo; // User/Company ID to request from
  final String requestedToName; // User/Company name to request from
  final String fromAccountId; // <PERSON><PERSON>'s account ID
  final String toAccountId; // <PERSON><PERSON><PERSON>'s account ID
  final String fromAccountName; // <PERSON><PERSON>'s account name
  final String toAccountName; // <PERSON><PERSON><PERSON>'s account name
  final double amount;
  final DateTime dueDate;
  final String status; // "pending", "approved", "rejected", "repaid"
  final DateTime requestDate;
  final DateTime? approvalDate;
  final DateTime? repaymentDate;
  final String? notes;
  final String? rejectionReason;

  // For voucher payment integration
  final String? voucherPaymentId; // Reference to associated voucher payment
  final String?
      pendingJournalEntryId; // Reference to pending journal entry awaiting completion

  LoanModel({
    required this.id,
    required this.uid,
    required this.requestedBy,
    required this.requestedByName,
    required this.requestedTo,
    required this.requestedToName,
    required this.fromAccountId,
    required this.toAccountId,
    required this.fromAccountName,
    required this.toAccountName,
    required this.amount,
    required this.dueDate,
    required this.status,
    required this.requestDate,
    this.approvalDate,
    this.repaymentDate,
    this.notes,
    this.rejectionReason,
    this.voucherPaymentId,
    this.pendingJournalEntryId,
  });

  // Helper method to parse DateTime from various formats
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) {
      throw ArgumentError('DateTime value cannot be null');
    }

    if (value is DateTime) {
      return value;
    }

    if (value is Timestamp) {
      return value.toDate();
    }

    if (value is String) {
      return DateTime.parse(value);
    }

    throw ArgumentError('Invalid DateTime format: ${value.runtimeType}');
  }

  // Factory constructor from Firestore document
  factory LoanModel.fromJson(Map<String, dynamic> json) {
    return LoanModel(
      id: json['id'] ?? '',
      uid: json['uid'] ?? '', // Handle existing loans without uid
      requestedBy: json['requestedBy'] ?? '',
      requestedByName: json['requestedByName'] ?? '',
      requestedTo: json['requestedTo'] ?? '',
      requestedToName: json['requestedToName'] ?? '',
      fromAccountId: json['fromAccountId'] ?? '',
      toAccountId: json['toAccountId'] ?? '',
      fromAccountName: json['fromAccountName'] ?? '',
      toAccountName: json['toAccountName'] ?? '',
      amount: (json['amount'] as num).toDouble(),
      dueDate: _parseDateTime(json['dueDate']),
      status: json['status'] ?? 'pending',
      requestDate: _parseDateTime(json['requestDate']),
      approvalDate: json['approvalDate'] != null
          ? _parseDateTime(json['approvalDate'])
          : null,
      repaymentDate: json['repaymentDate'] != null
          ? _parseDateTime(json['repaymentDate'])
          : null,
      notes: json['notes'],
      rejectionReason: json['rejectionReason'],
      voucherPaymentId: json['voucherPaymentId'],
      pendingJournalEntryId: json['pendingJournalEntryId'],
    );
  }

  // Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'id': id,
      'uid': uid,
      'requestedBy': requestedBy,
      'requestedByName': requestedByName,
      'requestedTo': requestedTo,
      'requestedToName': requestedToName,
      'fromAccountId': fromAccountId,
      'toAccountId': toAccountId,
      'fromAccountName': fromAccountName,
      'toAccountName': toAccountName,
      'amount': amount,
      'dueDate': dueDate,
      'status': status,
      'requestDate': requestDate,
    };

    if (approvalDate != null) data['approvalDate'] = approvalDate;
    if (repaymentDate != null) data['repaymentDate'] = repaymentDate;
    if (notes != null) data['notes'] = notes;
    if (rejectionReason != null) data['rejectionReason'] = rejectionReason;
    if (voucherPaymentId != null) data['voucherPaymentId'] = voucherPaymentId;
    if (pendingJournalEntryId != null) {
      data['pendingJournalEntryId'] = pendingJournalEntryId;
    }

    return data;
  }

  // Create a copy with updated fields
  LoanModel copyWith({
    String? id,
    String? uid,
    String? requestedBy,
    String? requestedByName,
    String? requestedTo,
    String? requestedToName,
    String? fromAccountId,
    String? toAccountId,
    String? fromAccountName,
    String? toAccountName,
    double? amount,
    DateTime? dueDate,
    String? status,
    DateTime? requestDate,
    DateTime? approvalDate,
    DateTime? repaymentDate,
    String? notes,
    String? rejectionReason,
    String? voucherPaymentId,
    String? pendingJournalEntryId,
  }) {
    return LoanModel(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      requestedBy: requestedBy ?? this.requestedBy,
      requestedByName: requestedByName ?? this.requestedByName,
      requestedTo: requestedTo ?? this.requestedTo,
      requestedToName: requestedToName ?? this.requestedToName,
      fromAccountId: fromAccountId ?? this.fromAccountId,
      toAccountId: toAccountId ?? this.toAccountId,
      fromAccountName: fromAccountName ?? this.fromAccountName,
      toAccountName: toAccountName ?? this.toAccountName,
      amount: amount ?? this.amount,
      dueDate: dueDate ?? this.dueDate,
      status: status ?? this.status,
      requestDate: requestDate ?? this.requestDate,
      approvalDate: approvalDate ?? this.approvalDate,
      repaymentDate: repaymentDate ?? this.repaymentDate,
      notes: notes ?? this.notes,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      voucherPaymentId: voucherPaymentId ?? this.voucherPaymentId,
      pendingJournalEntryId:
          pendingJournalEntryId ?? this.pendingJournalEntryId,
    );
  }
}
