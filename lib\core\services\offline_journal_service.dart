import 'dart:async';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:dartz/dartz.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/offline/sync_queue_entry.dart';
import '../../models/offline/offline_transaction_state.dart';
import '../../core/shared_services/failure_obj.dart';
import '../../core/shared_services/success_obj.dart';
import 'connectivity_service.dart';
import 'offline_state_service.dart';

/// Service for managing offline journal entries and Chart of Accounts operations
class OfflineJournalService extends GetxController {
  static OfflineJournalService get instance =>
      Get.find<OfflineJournalService>();

  // Hive boxes for local storage
  Box<Map>? _localJournalEntriesBox;
  Box<Map>? _localChartAccountsBox;
  Box<Map>? _localBalancesBox;
  Box<Map>? _balanceSnapshotsBox;

  // Services
  late final ConnectivityService _connectivityService;
  late final OfflineStateService _offlineStateService;

  // Reactive state
  final RxList<JournalEntryModel> _localJournalEntries =
      <JournalEntryModel>[].obs;
  final RxList<ChartOfAccountsModel> _localChartAccounts =
      <ChartOfAccountsModel>[].obs;
  final RxMap<String, double> _localBalances = <String, double>{}.obs;
  final RxBool _isInitialized = false.obs;

  // Public getters
  List<JournalEntryModel> get localJournalEntries => _localJournalEntries;
  List<ChartOfAccountsModel> get localChartAccounts => _localChartAccounts;
  Map<String, double> get localBalances => _localBalances;
  bool get isInitialized => _isInitialized.value;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _initializeOfflineStorage();
  }

  @override
  void onClose() {
    _localJournalEntriesBox?.close();
    _localChartAccountsBox?.close();
    _localBalancesBox?.close();
    _balanceSnapshotsBox?.close();
    super.onClose();
  }

  /// Initialize required services
  void _initializeServices() {
    _connectivityService = Get.find<ConnectivityService>();
    _offlineStateService = Get.find<OfflineStateService>();
  }

  /// Initialize offline storage
  Future<void> _initializeOfflineStorage() async {
    try {
      _localJournalEntriesBox =
          await Hive.openBox<Map>('local_journal_entries');
      _localChartAccountsBox = await Hive.openBox<Map>('local_chart_accounts');
      _localBalancesBox = await Hive.openBox<Map>('local_balances');
      _balanceSnapshotsBox = await Hive.openBox<Map>('balance_snapshots');

      await _loadLocalData();
      _isInitialized.value = true;

      log('OfflineJournalService: Initialized successfully');
    } catch (e) {
      log('OfflineJournalService: Failed to initialize: $e');
    }
  }

  /// Load local data from storage
  Future<void> _loadLocalData() async {
    try {
      await _loadLocalJournalEntries();
      await _loadLocalChartAccounts();
      await _loadLocalBalances();

      log('OfflineJournalService: Loaded local data successfully');
    } catch (e) {
      log('OfflineJournalService: Failed to load local data: $e');
    }
  }

  /// Load local journal entries
  Future<void> _loadLocalJournalEntries() async {
    try {
      final entries = <JournalEntryModel>[];

      final values = _localJournalEntriesBox?.values ?? <Map>[];
      for (final entry in values) {
        try {
          final journalEntry =
              JournalEntryModel.fromJson(Map<String, dynamic>.from(entry));
          entries.add(journalEntry);
        } catch (e) {
          log('OfflineJournalService: Failed to load journal entry: $e');
        }
      }

      _localJournalEntries.value = entries;
      log('OfflineJournalService: Loaded ${entries.length} local journal entries');
    } catch (e) {
      log('OfflineJournalService: Failed to load local journal entries: $e');
    }
  }

  /// Load local chart of accounts
  Future<void> _loadLocalChartAccounts() async {
    try {
      final accounts = <ChartOfAccountsModel>[];

      final values = _localChartAccountsBox?.values ?? <Map>[];
      for (final entry in values) {
        try {
          final account =
              ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(entry));
          accounts.add(account);
        } catch (e) {
          log('OfflineJournalService: Failed to load chart account: $e');
        }
      }

      _localChartAccounts.value = accounts;
      log('OfflineJournalService: Loaded ${accounts.length} local chart accounts');
    } catch (e) {
      log('OfflineJournalService: Failed to load local chart accounts: $e');
    }
  }

  /// Load local balances
  Future<void> _loadLocalBalances() async {
    try {
      final balances = <String, double>{};

      final values = _localBalancesBox?.values ?? <Map>[];
      for (final entry in values) {
        try {
          final data = Map<String, dynamic>.from(entry);
          final accountId = data['accountId'] as String;
          final balance = (data['balance'] as num).toDouble();
          balances[accountId] = balance;
        } catch (e) {
          log('OfflineJournalService: Failed to load balance entry: $e');
        }
      }

      _localBalances.value = balances;
      log('OfflineJournalService: Loaded ${balances.length} local balances');
    } catch (e) {
      log('OfflineJournalService: Failed to load local balances: $e');
    }
  }

  /// Create journal entry offline
  Future<Either<FailureObj, SuccessObj>> createJournalEntryOffline({
    required JournalEntryModel journalEntry,
    required String uid,
  }) async {
    try {
      log('OfflineJournalService: Creating journal entry offline: ${journalEntry.entryNumber}');

      // Validate journal entry
      final validation = await _validateJournalEntry(journalEntry);
      if (!validation.isValid) {
        return Left(FailureObj(
          code: 'validation-failed',
          message: validation.errors.join(', '),
        ));
      }

      // Save journal entry locally
      await _saveJournalEntryLocally(journalEntry);

      // Update local balances
      await _updateLocalBalances(journalEntry);

      // Create balance snapshot
      await _createBalanceSnapshot(journalEntry);

      // Add to sync queue
      await _addJournalEntryToSyncQueue(journalEntry, uid);

      // Update reactive list
      _localJournalEntries.add(journalEntry);

      log('OfflineJournalService: Journal entry created offline successfully');
      return Right(
          SuccessObj(message: 'Journal entry created offline successfully'));
    } catch (e) {
      log('OfflineJournalService: Failed to create journal entry offline: $e');
      return Left(FailureObj(
        code: 'offline-journal-creation-failed',
        message: 'Failed to create journal entry offline: $e',
      ));
    }
  }

  /// Validate journal entry
  Future<ValidationResult> _validateJournalEntry(
      JournalEntryModel journalEntry) async {
    final errors = <String>[];

    try {
      // Check if entry has lines
      if (journalEntry.lines.isEmpty) {
        errors.add('Journal entry must have at least one line');
      }

      // Validate each line
      for (final line in journalEntry.lines) {
        // Check if account exists in local cache
        final account = _localChartAccounts.firstWhereOrNull(
          (acc) => acc.id == line.accountId,
        );

        if (account == null) {
          errors.add('Account not found in local cache: ${line.accountId}');
        }

        // Check amounts
        if (line.debitAmount < 0 || line.creditAmount < 0) {
          errors.add('Debit and credit amounts must be non-negative');
        }

        if (line.debitAmount > 0 && line.creditAmount > 0) {
          errors.add('A line cannot have both debit and credit amounts');
        }

        if (line.debitAmount == 0 && line.creditAmount == 0) {
          errors.add('A line must have either debit or credit amount');
        }
      }

      // Check if entry is balanced (for non-automatic entries)
      if (journalEntry.entryType != JournalEntryType.automatic) {
        final totalDebits = journalEntry.lines
            .map((line) => line.debitAmount)
            .fold(0.0, (sum, amount) => sum + amount);

        final totalCredits = journalEntry.lines
            .map((line) => line.creditAmount)
            .fold(0.0, (sum, amount) => sum + amount);

        if ((totalDebits - totalCredits).abs() > 0.01) {
          errors.add(
              'Journal entry is not balanced: Debits=$totalDebits, Credits=$totalCredits');
        }
      }
    } catch (e) {
      errors.add('Validation error: $e');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Save journal entry locally
  Future<void> _saveJournalEntryLocally(JournalEntryModel journalEntry) async {
    try {
      final entryData = journalEntry.toJson();
      entryData['isLocal'] = true;
      entryData['localCreatedAt'] = DateTime.now().toIso8601String();

      await _localJournalEntriesBox?.put(journalEntry.id, entryData);
      log('OfflineJournalService: Saved journal entry locally: ${journalEntry.entryNumber}');
    } catch (e) {
      log('OfflineJournalService: Failed to save journal entry locally: $e');
      rethrow;
    }
  }

  /// Update local balances based on journal entry
  Future<void> _updateLocalBalances(JournalEntryModel journalEntry) async {
    try {
      for (final line in journalEntry.lines) {
        final account = _localChartAccounts.firstWhereOrNull(
          (acc) => acc.id == line.accountId,
        );

        if (account != null) {
          final currentBalance =
              _localBalances[line.accountId] ?? account.balance;
          final balanceChange = _calculateBalanceChange(
              account, line.debitAmount, line.creditAmount);
          final newBalance = currentBalance + balanceChange;

          // Update local balance
          _localBalances[line.accountId] = newBalance;

          // Save to storage
          await _localBalancesBox?.put(line.accountId, {
            'accountId': line.accountId,
            'balance': newBalance,
            'lastUpdated': DateTime.now().toIso8601String(),
            'journalEntryId': journalEntry.id,
          });

          log('OfflineJournalService: Updated local balance for ${line.accountId}: $currentBalance -> $newBalance');
        }
      }
    } catch (e) {
      log('OfflineJournalService: Failed to update local balances: $e');
      rethrow;
    }
  }

  /// Calculate balance change based on account type and transaction amounts
  double _calculateBalanceChange(
      ChartOfAccountsModel account, double debitAmount, double creditAmount) {
    // Assets and Expenses increase with debits, decrease with credits
    // Liabilities, Equity, and Revenue decrease with debits, increase with credits

    switch (account.category) {
      case AccountCategory.assets:
      case AccountCategory.expenses:
        return debitAmount - creditAmount;
      case AccountCategory.liabilities:
      case AccountCategory.equity:
      case AccountCategory.revenue:
        return creditAmount - debitAmount;
    }
  }

  /// Create balance snapshot for audit trail
  Future<void> _createBalanceSnapshot(JournalEntryModel journalEntry) async {
    try {
      final snapshot = {
        'id': '${journalEntry.id}_snapshot',
        'journalEntryId': journalEntry.id,
        'entryNumber': journalEntry.entryNumber,
        'timestamp': DateTime.now().toIso8601String(),
        'balances': Map<String, double>.from(_localBalances),
      };

      await _balanceSnapshotsBox?.put(snapshot['id'], snapshot);
      log('OfflineJournalService: Created balance snapshot for ${journalEntry.entryNumber}');
    } catch (e) {
      log('OfflineJournalService: Failed to create balance snapshot: $e');
    }
  }

  /// Add journal entry to sync queue
  Future<void> _addJournalEntryToSyncQueue(
      JournalEntryModel journalEntry, String uid) async {
    try {
      final syncEntry = SyncQueueEntry.journalEntryCreate(
        journalData: journalEntry.toJson(),
        userId: uid,
        companyId: uid,
      );

      await _offlineStateService.addToSyncQueue(
        operationType: syncEntry.operationType.value,
        data: syncEntry.data,
        dependencies: syncEntry.dependencies,
      );

      log('OfflineJournalService: Added journal entry to sync queue: ${journalEntry.entryNumber}');
    } catch (e) {
      log('OfflineJournalService: Failed to add journal entry to sync queue: $e');
    }
  }

  /// Cache chart of accounts for offline use
  Future<void> cacheChartOfAccounts(List<ChartOfAccountsModel> accounts) async {
    try {
      // Clear existing cache
      await _localChartAccountsBox?.clear();

      // Cache new accounts
      for (final account in accounts) {
        await _localChartAccountsBox?.put(account.id, account.toJson());
      }

      // Update reactive list
      _localChartAccounts.value = accounts;

      // Initialize balances if not already present
      for (final account in accounts) {
        if (!_localBalances.containsKey(account.id)) {
          _localBalances[account.id] = account.balance;
          await _localBalancesBox?.put(account.id, {
            'accountId': account.id,
            'balance': account.balance,
            'lastUpdated': DateTime.now().toIso8601String(),
            'source': 'initial_cache',
          });
        }
      }

      log('OfflineJournalService: Cached ${accounts.length} chart of accounts');
    } catch (e) {
      log('OfflineJournalService: Failed to cache chart of accounts: $e');
    }
  }

  /// Get account balance (local or cached)
  double getAccountBalance(String accountId) {
    return _localBalances[accountId] ?? 0.0;
  }

  /// Get account by ID
  ChartOfAccountsModel? getAccount(String accountId) {
    return _localChartAccounts.firstWhereOrNull((acc) => acc.id == accountId);
  }

  /// Get accounts by category
  List<ChartOfAccountsModel> getAccountsByCategory(AccountCategory category) {
    return _localChartAccounts
        .where((acc) => acc.category == category)
        .toList();
  }

  /// Get pending journal entries for sync
  List<JournalEntryModel> getPendingJournalEntries() {
    // In a real implementation, this would check sync status
    return _localJournalEntries
        .where((entry) => entry.status == JournalEntryStatus.draft)
        .toList();
  }

  /// Clear synced journal entries
  Future<void> clearSyncedJournalEntries() async {
    try {
      final syncedEntries = _localJournalEntries
          .where((entry) => entry.status == JournalEntryStatus.posted)
          .toList();

      for (final entry in syncedEntries) {
        await _localJournalEntriesBox?.delete(entry.id);
        _localJournalEntries.remove(entry);
      }

      log('OfflineJournalService: Cleared ${syncedEntries.length} synced journal entries');
    } catch (e) {
      log('OfflineJournalService: Failed to clear synced journal entries: $e');
    }
  }

  /// Get balance history for an account
  List<Map<String, dynamic>> getBalanceHistory(String accountId) {
    try {
      final history = <Map<String, dynamic>>[];

      final values = _balanceSnapshotsBox?.values ?? <Map>[];
      for (final snapshot in values) {
        final data = Map<String, dynamic>.from(snapshot);
        final balances = Map<String, double>.from(data['balances'] ?? {});

        if (balances.containsKey(accountId)) {
          history.add({
            'timestamp': data['timestamp'],
            'balance': balances[accountId],
            'journalEntryId': data['journalEntryId'],
            'entryNumber': data['entryNumber'],
          });
        }
      }

      // Sort by timestamp
      history.sort((a, b) => a['timestamp'].compareTo(b['timestamp']));

      return history;
    } catch (e) {
      log('OfflineJournalService: Failed to get balance history: $e');
      return [];
    }
  }
}

/// Validation result class
class ValidationResult {
  final bool isValid;
  final List<String> errors;

  ValidationResult({
    required this.isValid,
    required this.errors,
  });
}
