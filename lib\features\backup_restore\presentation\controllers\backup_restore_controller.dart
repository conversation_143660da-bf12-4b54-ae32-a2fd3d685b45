import 'dart:convert';
import 'dart:developer';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/firebase_service/backup_restore_firebase_service.dart';
import 'package:logestics/features/user/presentation/controllers/user_controller.dart';

// Conditional import for web
import 'package:web/web.dart' as web;
import 'dart:js_interop';

class BackupRestoreController extends GetxController {
  final BackupRestoreFirebaseService _backupService =
      BackupRestoreFirebaseService();

  // Use lazy initialization for UserController to avoid dependency issues
  UserController get _userController {
    try {
      return Get.find<UserController>();
    } catch (e) {
      log('UserController not found, creating new instance: $e');
      return Get.put(UserController());
    }
  }

  // Observable states
  final RxBool isCreatingBackup = false.obs;
  final RxBool isRestoringBackup = false.obs;
  final RxString backupProgress = ''.obs;
  final RxString restoreProgress = ''.obs;
  final RxString selectedFileName = ''.obs;
  final RxBool hasSelectedFile = false.obs;
  final RxDouble backupProgressPercent = 0.0.obs;
  final RxDouble restoreProgressPercent = 0.0.obs;
  final RxInt totalCollections = 0.obs;
  final RxInt processedCollections = 0.obs;

  // Enhanced progress tracking
  final RxString currentOperation = ''.obs;
  final RxInt totalDocuments = 0.obs;
  final RxInt processedDocuments = 0.obs;
  final RxList<String> completedCollections = <String>[].obs;
  final RxList<String> failedCollections = <String>[].obs;
  final RxMap<String, int> collectionDocumentCounts = <String, int>{}.obs;
  final RxString backupVersion = ''.obs;
  final RxString backupCompatibility = ''.obs;

  // File data
  Uint8List? _selectedFileBytes;
  Map<String, dynamic>? _parsedBackupData;

  @override
  void onInit() {
    super.onInit();
    log('BackupRestoreController initialized');
  }

  /// Create a backup of all company data
  Future<void> createBackup() async {
    try {
      isCreatingBackup.value = true;
      backupProgress.value = 'Initializing comprehensive backup...';
      backupProgressPercent.value = 0.0;
      processedCollections.value = 0;

      // Reset enhanced tracking
      currentOperation.value = 'Initializing';
      totalDocuments.value = 0;
      processedDocuments.value = 0;
      completedCollections.clear();
      failedCollections.clear();
      collectionDocumentCounts.clear();

      log('Starting backup creation');

      // Validate user controller and authentication
      final userController = _userController;
      final companyName = userController.userName;
      log('Backup for company: $companyName');

      if (companyName.isEmpty || companyName == 'User') {
        throw Exception(
            'Company name not available. Please ensure you are logged in.');
      }

      final result = await _backupService.createBackup(
        onProgress: (progress) {
          log('Backup progress: $progress');
          backupProgress.value = progress;
          currentOperation.value = progress;

          // Extract progress information from the progress string
          if (progress.contains('(') && progress.contains('%)')) {
            final percentMatch = RegExp(r'\((\d+)%\)').firstMatch(progress);
            if (percentMatch != null) {
              final percent =
                  double.tryParse(percentMatch.group(1) ?? '0') ?? 0.0;
              backupProgressPercent.value = percent;
              log('Progress percentage: $percent%');
            }
          }

          // Track collection completion
          if (progress.startsWith('Backed up ')) {
            final collectionMatch =
                RegExp(r'Backed up (\w+)').firstMatch(progress);
            if (collectionMatch != null) {
              final collectionName = collectionMatch.group(1) ?? '';
              if (!completedCollections.contains(collectionName)) {
                completedCollections.add(collectionName);
              }
              processedCollections.value = completedCollections.length;
              log('Completed collections: ${completedCollections.length}');
            }
          }

          // Track backing up operations
          if (progress.startsWith('Backing up ')) {
            final collectionMatch =
                RegExp(r'Backing up (\w+)').firstMatch(progress);
            if (collectionMatch != null) {
              final collectionName = collectionMatch.group(1) ?? '';
              currentOperation.value = 'Backing up $collectionName...';
            }
          }

          // Track counter operations
          if (progress.contains('counter')) {
            currentOperation.value = 'Backing up system counters...';
          }
        },
      );

      result.fold(
        (failure) {
          log('Backup creation failed: ${failure.message}');
          _showErrorSnackbar('Backup Failed', failure.message);
        },
        (backupData) async {
          log('Backup creation successful');
          backupProgress.value = 'Preparing download...';
          currentOperation.value = 'Finalizing backup file...';

          // Extract metadata for display
          final metadata = backupData['metadata'] as Map<String, dynamic>?;
          if (metadata != null) {
            totalDocuments.value = metadata['totalDocuments'] ?? 0;
            backupVersion.value = metadata['version'] ?? 'Unknown';

            final compatibility =
                metadata['compatibility'] as Map<String, dynamic>?;
            if (compatibility != null) {
              final supportsCrossPlatform =
                  compatibility['supportsCrossPlatform'] ?? false;
              backupCompatibility.value = supportsCrossPlatform
                  ? 'Cross-platform compatible'
                  : 'Platform specific';
            }

            // Extract collection summary
            final collectionSummary =
                metadata['collectionSummary'] as Map<String, dynamic>?;
            if (collectionSummary != null) {
              collectionDocumentCounts.clear();
              for (String collection in collectionSummary.keys) {
                final summary =
                    collectionSummary[collection] as Map<String, dynamic>;
                collectionDocumentCounts[collection] =
                    summary['documentCount'] ?? 0;
              }
            }
          }

          await _downloadBackupFile(backupData);

          backupProgress.value = 'Backup completed successfully!';
          currentOperation.value =
              'Backup complete - ${totalDocuments.value} documents backed up';
          _showSuccessSnackbar('Backup Created',
              'Successfully backed up ${totalDocuments.value} documents from ${completedCollections.length} collections');
        },
      );
    } catch (e) {
      log('Error during backup creation: $e');
      _showErrorSnackbar('Backup Error', 'An unexpected error occurred: $e');
    } finally {
      isCreatingBackup.value = false;
      // Clear progress after a delay
      Future.delayed(const Duration(seconds: 3), () {
        backupProgress.value = '';
      });
    }
  }

  /// Download backup file to user's device
  Future<void> _downloadBackupFile(Map<String, dynamic> backupData) async {
    try {
      final companyName = _userController.userName;
      log('Generating backup file for company: $companyName');

      final filename = _backupService.generateBackupFilename(companyName);
      log('Generated filename: $filename');

      final jsonString = _backupService.backupToJson(backupData);
      log('JSON data size: ${jsonString.length} characters');

      if (kIsWeb) {
        // Web download
        log('Starting web download...');
        final bytes = utf8.encode(jsonString);
        log('Encoded bytes size: ${bytes.length}');

        final blob = web.Blob(
            [bytes.toJS].toJS, web.BlobPropertyBag(type: 'application/json'));
        final url = web.URL.createObjectURL(blob);
        log('Created blob URL: $url');

        final anchor = web.HTMLAnchorElement()
          ..href = url
          ..download = filename
          ..style.display = 'none';

        // Add to DOM, click, then remove
        web.document.body?.appendChild(anchor);
        anchor.click();
        anchor.remove();

        web.URL.revokeObjectURL(url);

        log('Backup file downloaded successfully: $filename');
      } else {
        // Mobile/Desktop - would need platform-specific implementation
        log('Mobile/Desktop download not implemented yet');
        throw UnimplementedError('Mobile/Desktop download not implemented');
      }
    } catch (e) {
      log('Error downloading backup file: $e');
      _showErrorSnackbar(
          'Download Failed', 'Failed to download backup file: $e');
      rethrow;
    }
  }

  /// Pick a backup file for restoration
  Future<void> pickBackupFile() async {
    try {
      log('Opening file picker for backup file');

      // Show user feedback that file picker is opening
      _showInfoSnackbar('File Picker', 'Opening file picker...');

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        withData: true,
        allowMultiple: false,
      );

      log('File picker result: ${result != null ? 'Files found: ${result.files.length}' : 'No result'}');

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        log('Selected file: ${file.name}, size: ${file.size} bytes');

        if (file.bytes == null) {
          log('File bytes are null');
          _showErrorSnackbar('File Error', 'Could not read the selected file');
          return;
        }

        selectedFileName.value = file.name;
        _selectedFileBytes = file.bytes!;
        hasSelectedFile.value = true;

        log('File data loaded successfully: ${_selectedFileBytes!.length} bytes');

        // Parse and validate the backup file
        await _validateSelectedFile();

        log('Backup file selected and validated: ${file.name}');
        _showSuccessSnackbar('File Selected',
            'Backup file "${file.name}" selected successfully');
      } else {
        log('No file selected by user');
        _showInfoSnackbar('File Selection', 'No file was selected');
      }
    } catch (e) {
      log('Error picking backup file: $e');
      _showErrorSnackbar('File Picker Error', 'Failed to select file: $e');
    }
  }

  /// Validate the selected backup file
  Future<void> _validateSelectedFile() async {
    try {
      if (_selectedFileBytes == null) return;

      final jsonString = utf8.decode(_selectedFileBytes!);
      final parseResult = _backupService.parseBackupJson(jsonString);

      parseResult.fold(
        (failure) {
          _showErrorSnackbar('Invalid File', failure.message);
          _clearSelectedFile();
        },
        (backupData) {
          _parsedBackupData = backupData;

          // Extract and display comprehensive metadata
          final metadata = backupData['metadata'] as Map<String, dynamic>;
          final createdAt = DateTime.parse(metadata['createdAt'] as String);
          final totalDocs = metadata['totalDocuments'] ?? 0;
          final totalCols = metadata['totalCollections'] ?? 0;

          // Update observable values for UI display
          backupVersion.value = metadata['version'] ?? 'Unknown';
          totalDocuments.value = totalDocs;
          totalCollections.value = totalCols;

          // Extract compatibility info
          final compatibility =
              metadata['compatibility'] as Map<String, dynamic>?;
          if (compatibility != null) {
            final supportsCrossPlatform =
                compatibility['supportsCrossPlatform'] ?? false;
            backupCompatibility.value = supportsCrossPlatform
                ? 'Cross-platform compatible'
                : 'Platform specific';
          }

          // Extract collection summary
          final collectionSummary =
              metadata['collectionSummary'] as Map<String, dynamic>?;
          if (collectionSummary != null) {
            collectionDocumentCounts.clear();
            for (String collection in collectionSummary.keys) {
              final summary =
                  collectionSummary[collection] as Map<String, dynamic>;
              collectionDocumentCounts[collection] =
                  summary['documentCount'] ?? 0;
            }
          }

          _showInfoSnackbar('File Validated',
              'Backup v${backupVersion.value} created on ${_formatDate(createdAt)}\n$totalDocs documents across $totalCols collections\n${backupCompatibility.value}');
        },
      );
    } catch (e) {
      log('Error validating backup file: $e');
      _showErrorSnackbar(
          'Validation Error', 'Failed to validate backup file: $e');
      _clearSelectedFile();
    }
  }

  /// Clear selected file
  void _clearSelectedFile() {
    selectedFileName.value = '';
    hasSelectedFile.value = false;
    _selectedFileBytes = null;
    _parsedBackupData = null;

    // Clear enhanced tracking
    backupVersion.value = '';
    backupCompatibility.value = '';
    totalDocuments.value = 0;
    totalCollections.value = 0;
    collectionDocumentCounts.clear();
  }

  /// Restore data from selected backup file
  Future<void> restoreFromBackup() async {
    try {
      if (_parsedBackupData == null) {
        _showErrorSnackbar('No File', 'Please select a backup file first');
        return;
      }

      // Show confirmation dialog
      final confirmed = await _showRestoreConfirmationDialog();
      if (!confirmed) return;

      isRestoringBackup.value = true;
      restoreProgress.value = 'Initializing restore...';

      log('Starting restore operation');

      final result = await _backupService.restoreFromBackup(
        backupData: _parsedBackupData!,
        onProgress: (progress) {
          restoreProgress.value = progress;
        },
        overwriteExisting: true,
      );

      result.fold(
        (failure) {
          log('Restore operation failed: ${failure.message}');
          _showErrorSnackbar('Restore Failed', failure.message);
        },
        (message) {
          log('Restore operation successful: $message');
          restoreProgress.value = 'Restore completed successfully!';
          _showSuccessSnackbar('Restore Complete', message);
          _clearSelectedFile();
        },
      );
    } catch (e) {
      log('Error during restore operation: $e');
      _showErrorSnackbar('Restore Error', 'An unexpected error occurred: $e');
    } finally {
      isRestoringBackup.value = false;
      // Clear progress after a delay
      Future.delayed(const Duration(seconds: 3), () {
        restoreProgress.value = '';
      });
    }
  }

  /// Show restore confirmation dialog
  Future<bool> _showRestoreConfirmationDialog() async {
    return await Get.dialog<bool>(
          AlertDialog(
            title: const Text('Confirm Data Restore'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '⚠️ WARNING: This action will permanently replace all your current data with the backup data.',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                SizedBox(height: 16),
                Text(
                    'This action cannot be undone. Please ensure you have a recent backup of your current data before proceeding.'),
                SizedBox(height: 16),
                Text('Do you want to continue with the restore operation?'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Get.back(result: true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Restore Data'),
              ),
            ],
          ),
          barrierDismissible: false,
        ) ??
        false;
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Show success snackbar
  void _showSuccessSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: const Color(0xFF4CAF50),
      colorText: const Color(0xFFFFFFFF),
      duration: const Duration(seconds: 4),
    );
  }

  /// Show error snackbar
  void _showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: const Color(0xFFF44336),
      colorText: const Color(0xFFFFFFFF),
      duration: const Duration(seconds: 5),
    );
  }

  /// Show info snackbar
  void _showInfoSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: const Color(0xFF2196F3),
      colorText: const Color(0xFFFFFFFF),
      duration: const Duration(seconds: 4),
    );
  }
}
