import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/features/finance/brokers/presentation/controllers/broker_detail_controller.dart';
import 'package:logestics/features/finance/brokers/presentation/views/broker_payment_dialog.dart';
import 'package:logestics/main.dart';
import 'package:logestics/models/finance/broker_model.dart';
import 'package:provider/provider.dart';

class BrokerDetailView extends StatelessWidget {
  final BrokerModel broker;

  const BrokerDetailView({super.key, required this.broker});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<BrokerDetailController>();
    notifier = Provider.of(context, listen: true);

    // Initialize controller with broker data
    controller.initializeBroker(broker);

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      body: Column(
        children: [
          _buildHeader(controller),
          _buildTabB<PERSON>(controller),
          Expanded(
            child: Tab<PERSON><PERSON><PERSON><PERSON><PERSON>(
              controller: controller.tabController,
              children: [
                _buildOverviewTab(controller),
                _buildTransactionsTab(controller),
                _buildPaymentsTab(controller),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BrokerDetailController controller) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: notifier.getcardColor,
        border: Border(
          bottom: BorderSide(color: notifier.getfillborder),
        ),
      ),
      child: Row(
        children: [
          // Broker info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  broker.name,
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 24,
                  ),
                ),
                const SizedBox(height: 8),
                if (broker.phoneNumber?.isNotEmpty == true)
                  Text(
                    'Phone: ${broker.phoneNumber}',
                    style: AppTextStyles.subtitleStyle.copyWith(
                      color: notifier.text.withOpacity(0.7),
                    ),
                  ),
                if (broker.email?.isNotEmpty == true)
                  Text(
                    'Email: ${broker.email}',
                    style: AppTextStyles.subtitleStyle.copyWith(
                      color: notifier.text.withOpacity(0.7),
                    ),
                  ),
              ],
            ),
          ),

          // Balance summary
          Obx(() {
            final summary = controller.financialSummary.value;
            final balance = summary['balance']?.toDouble() ?? 0.0;

            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: notifier.getHoverColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: notifier.getfillborder),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Current Balance',
                    style: AppTextStyles.subtitleStyle.copyWith(
                      color: notifier.text.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    controller.getBalanceText(balance),
                    style: AppTextStyles.titleStyle.copyWith(
                      color: controller.getBalanceColor(balance),
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            );
          }),

          const SizedBox(width: 16),

          // Action buttons
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () => _showPaymentDialog(controller),
                icon: const Icon(Icons.payment),
                label: const Text('Record Payment'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: () => controller.refreshData(),
                icon: const Icon(Icons.refresh),
                tooltip: 'Refresh',
              ),
              IconButton(
                onPressed: () => Get.back(),
                icon: const Icon(Icons.close),
                tooltip: 'Close',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BrokerDetailController controller) {
    return Container(
      decoration: BoxDecoration(
        color: notifier.getcardColor,
        border: Border(
          bottom: BorderSide(color: notifier.getfillborder),
        ),
      ),
      child: TabBar(
        controller: controller.tabController,
        labelColor: notifier.text,
        unselectedLabelColor: notifier.text.withOpacity(0.6),
        indicatorColor: Colors.blue,
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Transactions'),
          Tab(text: 'Payments'),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(BrokerDetailController controller) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        final summary = controller.financialSummary.value;

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Financial summary cards
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      'Total Fees',
                      controller.formatCurrency(
                          summary['totalFees']?.toDouble() ?? 0.0),
                      Icons.add_circle_outline,
                      Colors.red,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSummaryCard(
                      'Total Payments',
                      controller.formatCurrency(
                          summary['totalPayments']?.toDouble() ?? 0.0),
                      Icons.remove_circle_outline,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSummaryCard(
                      'Outstanding Balance',
                      controller.formatCurrency(
                          summary['balance']?.toDouble() ?? 0.0),
                      Icons.account_balance,
                      controller.getBalanceColor(
                          summary['balance']?.toDouble() ?? 0.0),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Transaction counts
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      'Fee Transactions',
                      '${summary['feeTransactionCount'] ?? 0}',
                      Icons.receipt_long,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSummaryCard(
                      'Payment Transactions',
                      '${summary['paymentTransactionCount'] ?? 0}',
                      Icons.payment,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSummaryCard(
                      'Total Transactions',
                      '${summary['totalTransactionCount'] ?? 0}',
                      Icons.list_alt,
                      Colors.purple,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Broker details
              Card(
                color: notifier.getcardColor,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Broker Information',
                        style: AppTextStyles.titleStyle.copyWith(
                          color: notifier.text,
                          fontSize: 18,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildInfoRow('Name', broker.name),
                      if (broker.description?.isNotEmpty == true)
                        _buildInfoRow('Description', broker.description!),
                      if (broker.phoneNumber?.isNotEmpty == true)
                        _buildInfoRow('Phone', broker.phoneNumber!),
                      if (broker.email?.isNotEmpty == true)
                        _buildInfoRow('Email', broker.email!),
                      if (broker.address?.isNotEmpty == true)
                        _buildInfoRow('Address', broker.address!),
                      _buildInfoRow(
                          'Created', controller.formatDate(broker.createdAt)),
                      if (summary['lastTransactionDate'] != null)
                        _buildInfoRow(
                            'Last Transaction',
                            controller.formatDate(DateTime.parse(
                                summary['lastTransactionDate']))),
                      if (summary['lastPaymentDate'] != null)
                        _buildInfoRow(
                            'Last Payment',
                            controller.formatDate(
                                DateTime.parse(summary['lastPaymentDate']))),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      color: notifier.getcardColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppTextStyles.subtitleStyle.copyWith(
                    color: notifier.text.withOpacity(0.7),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: AppTextStyles.titleStyle.copyWith(
                color: color,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: AppTextStyles.subtitleStyle.copyWith(
                color: notifier.text.withOpacity(0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.subtitleStyle.copyWith(
                color: notifier.text,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsTab(BrokerDetailController controller) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Obx(() {
        if (controller.isLoadingTransactions.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.transactions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  size: 64,
                  color: notifier.text.withOpacity(0.3),
                ),
                const SizedBox(height: 16),
                Text(
                  'No transactions found',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text.withOpacity(0.5),
                  ),
                ),
              ],
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Transaction History',
              style: AppTextStyles.titleStyle.copyWith(
                color: notifier.text,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                color: notifier.getcardColor,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: _buildTransactionsTable(controller),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildPaymentsTab(BrokerDetailController controller) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Obx(() {
        if (controller.isLoadingPayments.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Payment History',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 18,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showPaymentDialog(controller),
                  icon: const Icon(Icons.add),
                  label: const Text('Record Payment'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                color: notifier.getcardColor,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: controller.payments.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.payment_outlined,
                                size: 64,
                                color: notifier.text.withOpacity(0.3),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No payments recorded',
                                style: AppTextStyles.titleStyle.copyWith(
                                  color: notifier.text.withOpacity(0.5),
                                ),
                              ),
                            ],
                          ),
                        )
                      : _buildPaymentsTable(controller),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildTransactionsTable(BrokerDetailController controller) {
    return SingleChildScrollView(
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(0.8), // Date
          1: FlexColumnWidth(1.0), // Type
          2: FlexColumnWidth(1.5), // Description
          3: FlexColumnWidth(0.8), // Amount
          4: FlexColumnWidth(1.0), // Reference
        },
        border: TableBorder(
          horizontalInside: BorderSide(color: notifier.getfillborder),
        ),
        children: [
          // Header
          TableRow(
            decoration: BoxDecoration(color: notifier.getHoverColor),
            children: [
              DataTableCell(
                text: 'Date',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
              DataTableCell(
                text: 'Type',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
              DataTableCell(
                text: 'Description',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
              DataTableCell(
                text: 'Amount',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
              DataTableCell(
                text: 'Reference',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          // Data rows
          ...controller.transactions.map((transaction) {
            return TableRow(
              children: [
                DataTableCell(
                  text: controller.formatDate(transaction.transactionDate),
                  style: AppTextStyles.invoiceDataStyle.copyWith(
                    color: notifier.text,
                  ),
                ),
                TableCell(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Icon(
                          controller.getTransactionTypeIcon(transaction.type),
                          size: 16,
                          color: controller
                              .getTransactionTypeColor(transaction.type),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          transaction.type.name.toUpperCase(),
                          style: AppTextStyles.invoiceDataStyle.copyWith(
                            color: controller
                                .getTransactionTypeColor(transaction.type),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                DataTableCell(
                  text: transaction.description,
                  style: AppTextStyles.invoiceDataStyle.copyWith(
                    color: notifier.text,
                  ),
                ),
                DataTableCell(
                  text: controller.formatCurrency(transaction.amount),
                  style: AppTextStyles.invoiceDataStyle.copyWith(
                    color: controller.getTransactionTypeColor(transaction.type),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                DataTableCell(
                  text:
                      transaction.voucherNumber ?? transaction.paymentId ?? '-',
                  style: AppTextStyles.invoiceDataStyle.copyWith(
                    color: notifier.text.withOpacity(0.7),
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPaymentsTable(BrokerDetailController controller) {
    return SingleChildScrollView(
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(0.8), // Date
          1: FlexColumnWidth(0.8), // Method
          2: FlexColumnWidth(0.8), // Amount
          3: FlexColumnWidth(1.0), // Account
          4: FlexColumnWidth(1.2), // Notes
          5: FlexColumnWidth(0.8), // Reference
        },
        border: TableBorder(
          horizontalInside: BorderSide(color: notifier.getfillborder),
        ),
        children: [
          // Header
          TableRow(
            decoration: BoxDecoration(color: notifier.getHoverColor),
            children: [
              DataTableCell(
                text: 'Date',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
              DataTableCell(
                text: 'Method',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
              DataTableCell(
                text: 'Amount',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
              DataTableCell(
                text: 'Account',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
              DataTableCell(
                text: 'Notes',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
              DataTableCell(
                text: 'Reference',
                style: AppTextStyles.invoiceDataStyle.copyWith(
                  color: notifier.text,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          // Data rows
          ...controller.payments.map((payment) {
            return TableRow(
              children: [
                DataTableCell(
                  text: controller.formatDate(payment.paymentDate),
                  style: AppTextStyles.invoiceDataStyle.copyWith(
                    color: notifier.text,
                  ),
                ),
                DataTableCell(
                  text: payment.method.name.toUpperCase(),
                  style: AppTextStyles.invoiceDataStyle.copyWith(
                    color: notifier.text,
                  ),
                ),
                DataTableCell(
                  text: controller.formatCurrency(payment.amount),
                  style: AppTextStyles.invoiceDataStyle.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                DataTableCell(
                  text: payment.accountName ?? '-',
                  style: AppTextStyles.invoiceDataStyle.copyWith(
                    color: notifier.text,
                  ),
                ),
                DataTableCell(
                  text: payment.notes ?? '-',
                  style: AppTextStyles.invoiceDataStyle.copyWith(
                    color: notifier.text.withOpacity(0.7),
                  ),
                ),
                DataTableCell(
                  text: payment.referenceNumber ?? payment.id.substring(0, 8),
                  style: AppTextStyles.invoiceDataStyle.copyWith(
                    color: notifier.text.withOpacity(0.7),
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }

  void _showPaymentDialog(BrokerDetailController controller) {
    showDialog(
      context: Get.context!,
      builder: (context) => BrokerPaymentDialog(
        broker: broker,
        controller: controller,
      ),
    );
  }
}
