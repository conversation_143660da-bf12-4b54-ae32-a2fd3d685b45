import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/payment_transaction_model.dart';
import 'package:logestics/models/finance/loan_model.dart';

void main() {
  group('Other Payment Pending Loan Request Tests', () {
    test('should identify Other payment type correctly', () {
      // Arrange: Create payment with accountTransfer method (Other payment type)
      final otherPayment = PaymentTransactionModel(
        id: 'payment_001',
        voucherId: 'V-TEST-001',
        method: PaymentMethod.accountTransfer, // This is "Other" payment type
        status: PaymentStatus.paid,
        amount: 50000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: 'cross-company-account-id',
        accountName: 'Company 2 Bank Account',
        notes: 'Cross-company payment test',
      );

      // Act & Assert: Verify payment method is accountTransfer
      expect(otherPayment.method, equals(PaymentMethod.accountTransfer));
      expect(otherPayment.method == PaymentMethod.accountTransfer, isTrue);
    });

    test('should distinguish Other payments from Check payments', () {
      // Arrange: Create both payment types
      final otherPayment = PaymentTransactionModel(
        id: 'payment_002',
        voucherId: 'V-TEST-002',
        method: PaymentMethod.accountTransfer, // Other payment
        status: PaymentStatus.paid,
        amount: 30000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: 'account-id-1',
        accountName: 'Account 1',
      );

      final checkPayment = PaymentTransactionModel(
        id: 'payment_003',
        voucherId: 'V-TEST-002',
        method: PaymentMethod.check, // Check payment
        status: PaymentStatus.paid,
        amount: 20000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: 'account-id-2',
        accountName: 'Account 2',
        checkNumber: 'CHK-001',
      );

      // Act & Assert: Verify different payment types
      expect(otherPayment.method == PaymentMethod.accountTransfer, isTrue);
      expect(checkPayment.method == PaymentMethod.check, isTrue);
      expect(otherPayment.method != checkPayment.method, isTrue);
    });

    test('should create pending loan request with correct status', () {
      // Arrange: Create loan model for pending loan request
      final pendingLoan = LoanModel(
        id: 'loan_001',
        uid: 'company-1-uid',
        requestedBy: 'company-1-uid',
        requestedByName: 'Company 1',
        requestedTo: 'company-2-uid',
        requestedToName: 'Company 2',
        fromAccountId: 'company-2-account-id',
        toAccountId: 'company-2-account-id',
        fromAccountName: 'Company 2 Bank Account',
        toAccountName: 'Company 2 Bank Account',
        amount: 50000.0,
        dueDate: DateTime.now().add(const Duration(days: 30)),
        status: 'pending', // Pending loan request status
        requestDate: DateTime.now(),
        approvalDate: null, // Not approved yet
        notes: 'Auto-generated loan request for voucher payment',
        voucherPaymentId: 'payment_001',
      );

      // Act & Assert: Verify loan is pending
      expect(pendingLoan.status, equals('pending'));
      expect(pendingLoan.approvalDate, isNull);
      expect(pendingLoan.voucherPaymentId, equals('payment_001'));
    });

    test('should handle loan workflow routing logic', () {
      // Arrange: Create different payment methods
      final paymentMethods = [
        PaymentMethod.cash,
        PaymentMethod.check,
        PaymentMethod.accountTransfer,
        PaymentMethod.fuelCard,
      ];

      // Act & Assert: Test workflow routing logic
      for (final method in paymentMethods) {
        final shouldUseLoanWorkflow = method == PaymentMethod.check ||
            method == PaymentMethod.accountTransfer;
        final isOtherPaymentType = method == PaymentMethod.accountTransfer;

        if (method == PaymentMethod.accountTransfer) {
          // Other payment should use loan workflow and be treated as pending loan request
          expect(shouldUseLoanWorkflow, isTrue);
          expect(isOtherPaymentType, isTrue);
        } else if (method == PaymentMethod.check) {
          // Check payment should use loan workflow but be treated as pending loan
          expect(shouldUseLoanWorkflow, isTrue);
          expect(isOtherPaymentType, isFalse);
        } else {
          // Cash and fuel card should not use loan workflow
          expect(shouldUseLoanWorkflow, isFalse);
          expect(isOtherPaymentType, isFalse);
        }
      }
    });

    test('should validate pending loan request creation parameters', () {
      // Arrange: Create payment for pending loan request creation
      final payment = PaymentTransactionModel(
        id: 'payment_004',
        voucherId: 'V-TEST-004',
        method: PaymentMethod.accountTransfer,
        status: PaymentStatus.paid,
        amount: 75000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: 'valid-account-id',
        accountName: 'Valid Account',
        notes: 'Pending loan request test payment',
      );

      // Act & Assert: Verify payment has required fields for pending loan request creation
      expect(payment.accountId, isNotNull);
      expect(payment.accountId!.isNotEmpty, isTrue);
      expect(payment.amount, greaterThan(0));
      expect(payment.method, equals(PaymentMethod.accountTransfer));
    });

    test('should handle cross-company loan description format', () {
      // Arrange: Create loan with proper description
      final expectedDescription =
          'Auto-generated loan request for voucher payment #V-TEST-005 - accountTransfer - Cross-company payment from Company 1 to Company 2 (Account: Test Account)';

      final loan = LoanModel(
        id: 'loan_002',
        uid: 'company-1-uid',
        requestedBy: 'company-1-uid',
        requestedByName: 'Company 1',
        requestedTo: 'company-2-uid',
        requestedToName: 'Company 2',
        fromAccountId: 'test-account-id',
        toAccountId: 'test-account-id',
        fromAccountName: 'Test Account',
        toAccountName: 'Test Account',
        amount: 25000.0,
        dueDate: DateTime.now().add(const Duration(days: 30)),
        status: 'approved',
        requestDate: DateTime.now(),
        approvalDate: DateTime.now(),
        notes: expectedDescription,
        voucherPaymentId: 'payment_005',
      );

      // Act & Assert: Verify description format
      expect(loan.notes, contains('Auto-generated loan request'));
      expect(loan.notes, contains('voucher payment #V-TEST-005'));
      expect(loan.notes, contains('accountTransfer'));
      expect(loan.notes, contains('Cross-company payment'));
      expect(loan.notes, contains('Test Account'));
    });
  });
}
