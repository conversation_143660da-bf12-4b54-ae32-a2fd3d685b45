import '../voucher_model.dart';
import 'offline_transaction_state.dart';

/// Extended voucher model for offline operations
class OfflineVoucherModel extends VoucherModel {
  final String offlineId;
  final OfflineTransactionState syncState;
  final DateTime localCreatedAt;
  final List<String> localJournalEntryIds;
  final Map<String, double> localBalanceChanges;
  final bool hasConflicts;
  final String? conflictDetails;
  final Map<String, dynamic> syncMetadata;
  final int syncAttempts;
  final DateTime? lastSyncAttempt;
  
  OfflineVoucherModel({
    required this.offlineId,
    this.syncState = OfflineTransactionState.pending,
    DateTime? localCreatedAt,
    this.localJournalEntryIds = const [],
    this.localBalanceChanges = const {},
    this.hasConflicts = false,
    this.conflictDetails,
    this.syncMetadata = const {},
    this.syncAttempts = 0,
    this.lastSyncAttempt,
    // All VoucherModel parameters
    required super.voucherStatus,
    required super.departureDate,
    required super.driverName,
    required super.invoiceTasNumberList,
    required super.invoiceBiltyNumberList,
    required super.weightInTons,
    required super.voucherNumber,
    required super.productName,
    required super.totalNumberOfBags,
    required super.brokerType,
    required super.brokerName,
    required super.brokerFees,
    required super.munshianaFees,
    required super.brokerAccount,
    required super.munshianaAccount,
    required super.driverPhoneNumber,
    required super.truckNumber,
    required super.conveyNoteNumber,
    required super.totalFreight,
    super.companyFreight = 0.0,
    super.settledFreight = 0.0,
    super.paymentTransactions = const [],
    super.dieselLiters,
    super.dieselCompany,
    super.chequeAmount,
    super.bankName,
    super.chequeNumber,
    super.belongsToDate,
    super.createdAt,
    super.brokerList = const [],
    super.calculatedProfit = 0.0,
    super.calculatedTax = 0.0,
    super.calculatedFreightTax = 0.0,
    super.brokerAccountId,
    super.munshianaAccountId,
    super.salesTaxAccountId,
    super.freightTaxAccountId,
    super.profitAccountId,
    super.truckFreightAccountId,
    super.taxAccountName,
    super.freightTaxAccountName,
    super.profitAccountName,
    super.companyFreightAccountId,
    super.companyFreightAccountName,
    super.brokerCompanyId,
    super.brokerCompanyName,
    super.selectedTaxAuthorities = const [],
  }) : localCreatedAt = localCreatedAt ?? DateTime.now();
  
  /// Create from regular VoucherModel
  factory OfflineVoucherModel.fromVoucherModel(
    VoucherModel voucher, {
    String? offlineId,
    OfflineTransactionState syncState = OfflineTransactionState.pending,
  }) {
    return OfflineVoucherModel(
      offlineId: offlineId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      syncState: syncState,
      voucherStatus: voucher.voucherStatus,
      departureDate: voucher.departureDate,
      driverName: voucher.driverName,
      invoiceTasNumberList: voucher.invoiceTasNumberList,
      invoiceBiltyNumberList: voucher.invoiceBiltyNumberList,
      weightInTons: voucher.weightInTons,
      voucherNumber: voucher.voucherNumber,
      productName: voucher.productName,
      totalNumberOfBags: voucher.totalNumberOfBags,
      brokerType: voucher.brokerType,
      brokerName: voucher.brokerName,
      brokerFees: voucher.brokerFees,
      munshianaFees: voucher.munshianaFees,
      brokerAccount: voucher.brokerAccount,
      munshianaAccount: voucher.munshianaAccount,
      driverPhoneNumber: voucher.driverPhoneNumber,
      truckNumber: voucher.truckNumber,
      conveyNoteNumber: voucher.conveyNoteNumber,
      totalFreight: voucher.totalFreight,
      companyFreight: voucher.companyFreight,
      settledFreight: voucher.settledFreight,
      paymentTransactions: voucher.paymentTransactions,
      dieselLiters: voucher.dieselLiters,
      dieselCompany: voucher.dieselCompany,
      chequeAmount: voucher.chequeAmount,
      bankName: voucher.bankName,
      chequeNumber: voucher.chequeNumber,
      belongsToDate: voucher.belongsToDate,
      createdAt: voucher.createdAt,
      brokerList: voucher.brokerList,
      calculatedProfit: voucher.calculatedProfit,
      calculatedTax: voucher.calculatedTax,
      calculatedFreightTax: voucher.calculatedFreightTax,
      brokerAccountId: voucher.brokerAccountId,
      munshianaAccountId: voucher.munshianaAccountId,
      salesTaxAccountId: voucher.salesTaxAccountId,
      freightTaxAccountId: voucher.freightTaxAccountId,
      profitAccountId: voucher.profitAccountId,
      truckFreightAccountId: voucher.truckFreightAccountId,
      taxAccountName: voucher.taxAccountName,
      freightTaxAccountName: voucher.freightTaxAccountName,
      profitAccountName: voucher.profitAccountName,
      companyFreightAccountId: voucher.companyFreightAccountId,
      companyFreightAccountName: voucher.companyFreightAccountName,
      brokerCompanyId: voucher.brokerCompanyId,
      brokerCompanyName: voucher.brokerCompanyName,
      selectedTaxAuthorities: voucher.selectedTaxAuthorities,
    );
  }
  
  /// Convert to regular VoucherModel for sync
  VoucherModel toVoucherModel() {
    return VoucherModel(
      voucherStatus: voucherStatus,
      departureDate: departureDate,
      driverName: driverName,
      invoiceTasNumberList: invoiceTasNumberList,
      invoiceBiltyNumberList: invoiceBiltyNumberList,
      weightInTons: weightInTons,
      voucherNumber: voucherNumber,
      productName: productName,
      totalNumberOfBags: totalNumberOfBags,
      brokerType: brokerType,
      brokerName: brokerName,
      brokerFees: brokerFees,
      munshianaFees: munshianaFees,
      brokerAccount: brokerAccount,
      munshianaAccount: munshianaAccount,
      driverPhoneNumber: driverPhoneNumber,
      truckNumber: truckNumber,
      conveyNoteNumber: conveyNoteNumber,
      totalFreight: totalFreight,
      companyFreight: companyFreight,
      settledFreight: settledFreight,
      paymentTransactions: paymentTransactions,
      dieselLiters: dieselLiters,
      dieselCompany: dieselCompany,
      chequeAmount: chequeAmount,
      bankName: bankName,
      chequeNumber: chequeNumber,
      belongsToDate: belongsToDate,
      createdAt: createdAt,
      brokerList: brokerList,
      calculatedProfit: calculatedProfit,
      calculatedTax: calculatedTax,
      calculatedFreightTax: calculatedFreightTax,
      brokerAccountId: brokerAccountId,
      munshianaAccountId: munshianaAccountId,
      salesTaxAccountId: salesTaxAccountId,
      freightTaxAccountId: freightTaxAccountId,
      profitAccountId: profitAccountId,
      truckFreightAccountId: truckFreightAccountId,
      taxAccountName: taxAccountName,
      freightTaxAccountName: freightTaxAccountName,
      profitAccountName: profitAccountName,
      companyFreightAccountId: companyFreightAccountId,
      companyFreightAccountName: companyFreightAccountName,
      brokerCompanyId: brokerCompanyId,
      brokerCompanyName: brokerCompanyName,
      selectedTaxAuthorities: selectedTaxAuthorities,
    );
  }
  
  /// Create from JSON
  factory OfflineVoucherModel.fromJson(Map<String, dynamic> json) {
    final voucherModel = VoucherModel.fromJson(json);
    return OfflineVoucherModel(
      offlineId: json['offlineId'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      syncState: OfflineTransactionState.fromString(json['syncState'] ?? 'pending'),
      localCreatedAt: json['localCreatedAt'] != null 
          ? DateTime.parse(json['localCreatedAt'])
          : DateTime.now(),
      localJournalEntryIds: List<String>.from(json['localJournalEntryIds'] ?? []),
      localBalanceChanges: Map<String, double>.from(json['localBalanceChanges'] ?? {}),
      hasConflicts: json['hasConflicts'] ?? false,
      conflictDetails: json['conflictDetails'],
      syncMetadata: Map<String, dynamic>.from(json['syncMetadata'] ?? {}),
      syncAttempts: json['syncAttempts'] ?? 0,
      lastSyncAttempt: json['lastSyncAttempt'] != null 
          ? DateTime.parse(json['lastSyncAttempt'])
          : null,
      // VoucherModel fields
      voucherStatus: voucherModel.voucherStatus,
      departureDate: voucherModel.departureDate,
      driverName: voucherModel.driverName,
      invoiceTasNumberList: voucherModel.invoiceTasNumberList,
      invoiceBiltyNumberList: voucherModel.invoiceBiltyNumberList,
      weightInTons: voucherModel.weightInTons,
      voucherNumber: voucherModel.voucherNumber,
      productName: voucherModel.productName,
      totalNumberOfBags: voucherModel.totalNumberOfBags,
      brokerType: voucherModel.brokerType,
      brokerName: voucherModel.brokerName,
      brokerFees: voucherModel.brokerFees,
      munshianaFees: voucherModel.munshianaFees,
      brokerAccount: voucherModel.brokerAccount,
      munshianaAccount: voucherModel.munshianaAccount,
      driverPhoneNumber: voucherModel.driverPhoneNumber,
      truckNumber: voucherModel.truckNumber,
      conveyNoteNumber: voucherModel.conveyNoteNumber,
      totalFreight: voucherModel.totalFreight,
      companyFreight: voucherModel.companyFreight,
      settledFreight: voucherModel.settledFreight,
      paymentTransactions: voucherModel.paymentTransactions,
      dieselLiters: voucherModel.dieselLiters,
      dieselCompany: voucherModel.dieselCompany,
      chequeAmount: voucherModel.chequeAmount,
      bankName: voucherModel.bankName,
      chequeNumber: voucherModel.chequeNumber,
      belongsToDate: voucherModel.belongsToDate,
      createdAt: voucherModel.createdAt,
      brokerList: voucherModel.brokerList,
      calculatedProfit: voucherModel.calculatedProfit,
      calculatedTax: voucherModel.calculatedTax,
      calculatedFreightTax: voucherModel.calculatedFreightTax,
      brokerAccountId: voucherModel.brokerAccountId,
      munshianaAccountId: voucherModel.munshianaAccountId,
      salesTaxAccountId: voucherModel.salesTaxAccountId,
      freightTaxAccountId: voucherModel.freightTaxAccountId,
      profitAccountId: voucherModel.profitAccountId,
      truckFreightAccountId: voucherModel.truckFreightAccountId,
      taxAccountName: voucherModel.taxAccountName,
      freightTaxAccountName: voucherModel.freightTaxAccountName,
      profitAccountName: voucherModel.profitAccountName,
      companyFreightAccountId: voucherModel.companyFreightAccountId,
      companyFreightAccountName: voucherModel.companyFreightAccountName,
      brokerCompanyId: voucherModel.brokerCompanyId,
      brokerCompanyName: voucherModel.brokerCompanyName,
      selectedTaxAuthorities: voucherModel.selectedTaxAuthorities,
    );
  }
  
  /// Convert to JSON
  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'offlineId': offlineId,
      'syncState': syncState.value,
      'localCreatedAt': localCreatedAt.toIso8601String(),
      'localJournalEntryIds': localJournalEntryIds,
      'localBalanceChanges': localBalanceChanges,
      'hasConflicts': hasConflicts,
      'conflictDetails': conflictDetails,
      'syncMetadata': syncMetadata,
      'syncAttempts': syncAttempts,
      'lastSyncAttempt': lastSyncAttempt?.toIso8601String(),
    });
    return json;
  }
  
  /// Create a copy with updated offline fields
  OfflineVoucherModel copyWithOffline({
    OfflineTransactionState? syncState,
    List<String>? localJournalEntryIds,
    Map<String, double>? localBalanceChanges,
    bool? hasConflicts,
    String? conflictDetails,
    Map<String, dynamic>? syncMetadata,
    int? syncAttempts,
    DateTime? lastSyncAttempt,
  }) {
    return OfflineVoucherModel(
      offlineId: offlineId,
      syncState: syncState ?? this.syncState,
      localCreatedAt: localCreatedAt,
      localJournalEntryIds: localJournalEntryIds ?? this.localJournalEntryIds,
      localBalanceChanges: localBalanceChanges ?? this.localBalanceChanges,
      hasConflicts: hasConflicts ?? this.hasConflicts,
      conflictDetails: conflictDetails ?? this.conflictDetails,
      syncMetadata: syncMetadata ?? this.syncMetadata,
      syncAttempts: syncAttempts ?? this.syncAttempts,
      lastSyncAttempt: lastSyncAttempt ?? this.lastSyncAttempt,
      // VoucherModel fields remain the same
      voucherStatus: voucherStatus,
      departureDate: departureDate,
      driverName: driverName,
      invoiceTasNumberList: invoiceTasNumberList,
      invoiceBiltyNumberList: invoiceBiltyNumberList,
      weightInTons: weightInTons,
      voucherNumber: voucherNumber,
      productName: productName,
      totalNumberOfBags: totalNumberOfBags,
      brokerType: brokerType,
      brokerName: brokerName,
      brokerFees: brokerFees,
      munshianaFees: munshianaFees,
      brokerAccount: brokerAccount,
      munshianaAccount: munshianaAccount,
      driverPhoneNumber: driverPhoneNumber,
      truckNumber: truckNumber,
      conveyNoteNumber: conveyNoteNumber,
      totalFreight: totalFreight,
      companyFreight: companyFreight,
      settledFreight: settledFreight,
      paymentTransactions: paymentTransactions,
      dieselLiters: dieselLiters,
      dieselCompany: dieselCompany,
      chequeAmount: chequeAmount,
      bankName: bankName,
      chequeNumber: chequeNumber,
      belongsToDate: belongsToDate,
      createdAt: createdAt,
      brokerList: brokerList,
      calculatedProfit: calculatedProfit,
      calculatedTax: calculatedTax,
      calculatedFreightTax: calculatedFreightTax,
      brokerAccountId: brokerAccountId,
      munshianaAccountId: munshianaAccountId,
      salesTaxAccountId: salesTaxAccountId,
      freightTaxAccountId: freightTaxAccountId,
      profitAccountId: profitAccountId,
      truckFreightAccountId: truckFreightAccountId,
      taxAccountName: taxAccountName,
      freightTaxAccountName: freightTaxAccountName,
      profitAccountName: profitAccountName,
      companyFreightAccountId: companyFreightAccountId,
      companyFreightAccountName: companyFreightAccountName,
      brokerCompanyId: brokerCompanyId,
      brokerCompanyName: brokerCompanyName,
      selectedTaxAuthorities: selectedTaxAuthorities,
    );
  }
  
  /// Check if voucher is ready for sync
  bool get isReadyForSync => syncState == OfflineTransactionState.pending;
  
  /// Check if voucher is synced
  bool get isSynced => syncState == OfflineTransactionState.synced;
  
  /// Check if voucher has sync conflicts
  bool get hasSyncConflicts => syncState == OfflineTransactionState.conflict;
  
  /// Check if voucher sync failed
  bool get syncFailed => syncState == OfflineTransactionState.failed;
  
  /// Get sync status description
  String get syncStatusDescription {
    switch (syncState) {
      case OfflineTransactionState.pending:
        return 'Waiting to sync';
      case OfflineTransactionState.syncing:
        return 'Syncing...';
      case OfflineTransactionState.synced:
        return 'Synced successfully';
      case OfflineTransactionState.conflict:
        return 'Sync conflict - needs resolution';
      case OfflineTransactionState.failed:
        return 'Sync failed - will retry';
      case OfflineTransactionState.interrupted:
        return 'Sync interrupted - will retry';
    }
  }
}
