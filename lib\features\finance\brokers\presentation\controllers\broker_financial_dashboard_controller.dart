import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/services/broker_financial_service.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/brokers/repositories/broker_repository.dart';
import 'package:logestics/models/finance/broker_model.dart';

class BrokerFinancialDashboardController extends GetxController {
  final BrokerRepository brokerRepository;
  final BrokerFinancialService brokerFinancialService;

  BrokerFinancialDashboardController({
    required this.brokerRepository,
    required this.brokerFinancialService,
  });

  // Observable data
  final RxList<BrokerModel> brokers = <BrokerModel>[].obs;
  final RxMap<String, Map<String, dynamic>> brokerFinancialSummaries =
      <String, Map<String, dynamic>>{}.obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingSummaries = false.obs;

  // Filter and search
  final searchController = TextEditingController();
  final RxString searchQuery = ''.obs;
  final RxString sortBy =
      'name'.obs; // name, balance, totalFees, lastTransaction
  final RxBool sortAscending = true.obs;
  final RxString filterBy = 'all'.obs; // all, withBalance, withoutBalance

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(() {
      searchQuery.value = searchController.text;
    });
    loadBrokers();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// Load all brokers
  Future<void> loadBrokers() async {
    isLoading.value = true;
    try {
      final result = await brokerRepository.getBrokers();
      result.fold(
        (failure) {
          log('Failed to load brokers: ${failure.message}');
          SnackbarUtils.showError('Error', 'Failed to load brokers');
        },
        (brokerList) {
          brokers.value = brokerList;
          _loadFinancialSummaries();
        },
      );
    } catch (e) {
      log('Error loading brokers: $e');
      SnackbarUtils.showError('Error', 'Failed to load brokers');
    } finally {
      isLoading.value = false;
    }
  }

  /// Load financial summaries for all brokers
  Future<void> _loadFinancialSummaries() async {
    if (brokers.isEmpty) return;

    isLoadingSummaries.value = true;
    try {
      final summaries = <String, Map<String, dynamic>>{};

      for (final broker in brokers) {
        final result =
            await brokerFinancialService.getBrokerFinancialSummary(broker.id);
        result.fold(
          (failure) {
            log('Failed to load financial summary for broker ${broker.name}: ${failure.message}');
            // Set empty summary for failed loads
            summaries[broker.id] = {
              'totalFees': 0.0,
              'totalPayments': 0.0,
              'balance': 0.0,
              'feeTransactionCount': 0,
              'paymentTransactionCount': 0,
              'totalTransactionCount': 0,
              'lastTransactionDate': null,
              'lastPaymentDate': null,
            };
          },
          (summary) {
            summaries[broker.id] = summary;
          },
        );
      }

      brokerFinancialSummaries.value = summaries;
    } catch (e) {
      log('Error loading financial summaries: $e');
    } finally {
      isLoadingSummaries.value = false;
    }
  }

  /// Get filtered and sorted brokers
  List<BrokerModel> get filteredBrokers {
    var filtered = brokers.where((broker) {
      // Search filter
      if (searchQuery.value.isNotEmpty) {
        final query = searchQuery.value.toLowerCase();
        if (!broker.name.toLowerCase().contains(query) &&
            !(broker.phoneNumber?.toLowerCase().contains(query) ?? false) &&
            !(broker.email?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // Balance filter
      if (filterBy.value != 'all') {
        final summary = brokerFinancialSummaries[broker.id];
        final balance = summary?['balance']?.toDouble() ?? 0.0;

        switch (filterBy.value) {
          case 'withBalance':
            if (balance <= 0) return false;
            break;
          case 'withoutBalance':
            if (balance > 0) return false;
            break;
        }
      }

      return true;
    }).toList();

    // Sort
    filtered.sort((a, b) {
      int comparison = 0;

      switch (sortBy.value) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'balance':
          final balanceA =
              brokerFinancialSummaries[a.id]?['balance']?.toDouble() ?? 0.0;
          final balanceB =
              brokerFinancialSummaries[b.id]?['balance']?.toDouble() ?? 0.0;
          comparison = balanceA.compareTo(balanceB);
          break;
        case 'totalFees':
          final feesA =
              brokerFinancialSummaries[a.id]?['totalFees']?.toDouble() ?? 0.0;
          final feesB =
              brokerFinancialSummaries[b.id]?['totalFees']?.toDouble() ?? 0.0;
          comparison = feesA.compareTo(feesB);
          break;
        case 'lastTransaction':
          final dateA = brokerFinancialSummaries[a.id]?['lastTransactionDate'];
          final dateB = brokerFinancialSummaries[b.id]?['lastTransactionDate'];

          if (dateA == null && dateB == null) {
            comparison = 0;
          } else if (dateA == null) {
            comparison = 1;
          } else if (dateB == null) {
            comparison = -1;
          } else {
            comparison = DateTime.parse(dateA).compareTo(DateTime.parse(dateB));
          }
          break;
      }

      return sortAscending.value ? comparison : -comparison;
    });

    return filtered;
  }

  /// Get financial summary for a broker
  Map<String, dynamic> getBrokerFinancialSummary(String brokerId) {
    return brokerFinancialSummaries[brokerId] ??
        {
          'totalFees': 0.0,
          'totalPayments': 0.0,
          'balance': 0.0,
          'feeTransactionCount': 0,
          'paymentTransactionCount': 0,
          'totalTransactionCount': 0,
          'lastTransactionDate': null,
          'lastPaymentDate': null,
        };
  }

  /// Get total outstanding balance across all brokers
  double get totalOutstandingBalance {
    double total = 0.0;
    for (final summary in brokerFinancialSummaries.values) {
      final balance = summary['balance']?.toDouble() ?? 0.0;
      if (balance > 0) {
        total += balance;
      }
    }
    return total;
  }

  /// Get count of brokers with outstanding balances
  int get brokersWithBalanceCount {
    int count = 0;
    for (final summary in brokerFinancialSummaries.values) {
      final balance = summary['balance']?.toDouble() ?? 0.0;
      if (balance > 0) {
        count++;
      }
    }
    return count;
  }

  /// Get total fees across all brokers
  double get totalFeesAllBrokers {
    double total = 0.0;
    for (final summary in brokerFinancialSummaries.values) {
      total += summary['totalFees']?.toDouble() ?? 0.0;
    }
    return total;
  }

  /// Get total payments across all brokers
  double get totalPaymentsAllBrokers {
    double total = 0.0;
    for (final summary in brokerFinancialSummaries.values) {
      total += summary['totalPayments']?.toDouble() ?? 0.0;
    }
    return total;
  }

  /// Refresh all data
  Future<void> refreshData() async {
    await loadBrokers();
  }

  /// Set sort criteria
  void setSortBy(String sortField) {
    if (sortBy.value == sortField) {
      sortAscending.value = !sortAscending.value;
    } else {
      sortBy.value = sortField;
      sortAscending.value = true;
    }
  }

  /// Set filter criteria
  void setFilterBy(String filter) {
    filterBy.value = filter;
  }

  /// Clear search
  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
  }

  /// Format currency
  String formatCurrency(double amount) {
    return 'PKR ${amount.toStringAsFixed(2)}';
  }

  /// Format date
  String formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Get balance color
  Color getBalanceColor(double balance) {
    if (balance > 0) {
      return Colors.red; // Money owed to broker
    } else if (balance < 0) {
      return Colors.green; // Overpaid
    } else {
      return Colors.grey; // Balanced
    }
  }

  /// Get balance text
  String getBalanceText(double balance) {
    if (balance > 0) {
      return 'Owed: ${formatCurrency(balance)}';
    } else if (balance < 0) {
      return 'Overpaid: ${formatCurrency(-balance)}';
    } else {
      return 'Balanced';
    }
  }
}
