import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import '../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../models/finance/chart_of_accounts_model.dart';

/// Simple script to check Chart of Accounts data
class CheckChartOfAccounts {
  static Future<void> runCheck() async {
    log('🔍 ========== CHART OF ACCOUNTS CHECK START ==========');
    
    try {
      // Check authentication
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        log('❌ No authenticated user found');
        return;
      }
      
      final uid = user.uid;
      log('👤 Checking Chart of Accounts for user: $uid');
      
      final chartService = ChartOfAccountsFirebaseService();
      
      // Try to get accounts with timeout
      log('🔍 Attempting to fetch Chart of Accounts...');
      
      // First, try to get a small subset to test connectivity
      try {
        final accounts = await chartService.getAllAccounts();
        log('📊 Total accounts found: ${accounts.length}');
        
        if (accounts.isEmpty) {
          log('⚠️ No Chart of Accounts found for this user');
          log('💡 This might be why the diagnostic is hanging');
          log('🔧 Recommendation: Create some Chart of Accounts first');
        } else {
          log('✅ Chart of Accounts data exists');
          
          // Check for required account types
          final requiredTypes = [
            AccountType.operatingExpenses,
            AccountType.salesRevenue,
            AccountType.cash,
          ];
          
          for (final accountType in requiredTypes) {
            try {
              final typeAccounts = await chartService.getAccountsByType(accountType);
              log('📋 ${accountType.name} accounts: ${typeAccounts.length}');
              
              for (final account in typeAccounts.take(3)) {
                log('  - ${account.accountNumber}: ${account.accountName}');
              }
            } catch (e) {
              log('❌ Error getting ${accountType.name} accounts: $e');
            }
          }
          
          // Check for specific required accounts
          final requiredAccounts = [
            {'type': AccountType.operatingExpenses, 'name': 'Broker Fees'},
            {'type': AccountType.operatingExpenses, 'name': 'Munshiana'},
            {'type': AccountType.salesRevenue, 'name': 'Freight Revenue'},
            {'type': AccountType.cash, 'name': 'Cash'},
          ];
          
          log('🔍 Checking for required voucher accounts...');
          for (final accountInfo in requiredAccounts) {
            final accountType = accountInfo['type'] as AccountType;
            final accountName = accountInfo['name'] as String;
            
            try {
              final typeAccounts = await chartService.getAccountsByType(accountType);
              final accountExists = typeAccounts.any((account) => 
                account.accountName.toLowerCase().contains(accountName.toLowerCase()));
              
              if (accountExists) {
                final account = typeAccounts.firstWhere((account) => 
                  account.accountName.toLowerCase().contains(accountName.toLowerCase()));
                log('✅ Found required account: $accountName (${account.id})');
              } else {
                log('❌ Missing required account: $accountName');
              }
            } catch (e) {
              log('❌ Error checking for $accountName: $e');
            }
          }
        }
        
      } catch (e) {
        log('❌ Error fetching Chart of Accounts: $e');
        log('🔍 This explains why the diagnostic is hanging');
        
        if (e.toString().contains('permission')) {
          log('🔒 Permission issue detected');
          log('💡 Check Firebase security rules for chartOfAccounts collection');
        } else if (e.toString().contains('timeout')) {
          log('⏱️ Timeout issue detected');
          log('💡 Try reducing query size or check network connectivity');
        } else {
          log('🔍 Unknown error - check Firebase console for more details');
        }
      }
      
    } catch (e) {
      log('❌ Chart of Accounts check failed: $e');
      log('📋 Stack trace: ${StackTrace.current}');
    }
    
    log('🔍 ========== CHART OF ACCOUNTS CHECK END ==========');
  }
}
