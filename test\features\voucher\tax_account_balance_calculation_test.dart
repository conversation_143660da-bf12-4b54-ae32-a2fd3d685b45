import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/models/finance/account_model.dart';

void main() {
  group('Tax Account Balance Calculation Tests', () {
    const voucherNumber = '001';
    const referenceNumber = 'V-001';

    // Helper function to create a test freight tax account
    AccountModel createFreightTaxAccount({double initialBalance = 10000.0}) {
      return AccountModel(
        id: 'freight_tax_account_1',
        name: '15% Freight Tax Account',
        initialBalance: initialBalance,
        availableBalance: initialBalance,
        accountNumber: 'FTA-001',
        branchCode: 'BR001',
        branchAddress: 'Main Branch',
        createdAt: DateTime.now(),
        uid: 'user1',
      );
    }

    // Helper function to create tax authority deposit entries
    List<DepositModel> createTaxAuthorityDeposits({
      required String accountId,
      required String accountName,
      required double companyFreight,
      bool isSplit = false,
    }) {
      final totalTaxAmount = companyFreight * 0.15; // 15% tax

      if (isSplit) {
        // Split between two authorities (7.5% each)
        final splitAmount = totalTaxAmount / 2;
        return [
          DepositModel(
            id: 'tax_dep_1',
            accountId: accountId,
            accountName: accountName,
            amount: splitAmount,
            createdAt: DateTime.now(),
            categoryId: 'tax_authority_split',
            categoryName: 'Tax Authority Payment (Split)',
            payerId: '',
            payerName: 'SRB (Sindh Revenue Board)',
            referenceNumber: referenceNumber,
            notes:
                'Tax payment to SRB (Sindh Revenue Board) - Voucher #$voucherNumber (Split payment)',
          ),
          DepositModel(
            id: 'tax_dep_2',
            accountId: accountId,
            accountName: accountName,
            amount: splitAmount,
            createdAt: DateTime.now(),
            categoryId: 'tax_authority_split',
            categoryName: 'Tax Authority Payment (Split)',
            payerId: '',
            payerName: 'PRA (Punjab Revenue Authority)',
            referenceNumber: referenceNumber,
            notes:
                'Tax payment to PRA (Punjab Revenue Authority) - Voucher #$voucherNumber (Split payment)',
          ),
        ];
      } else {
        // Single authority gets full 15%
        return [
          DepositModel(
            id: 'tax_dep_1',
            accountId: accountId,
            accountName: accountName,
            amount: totalTaxAmount,
            createdAt: DateTime.now(),
            categoryId: 'tax_authority_single',
            categoryName: 'Tax Authority Payment',
            payerId: '',
            payerName: 'SRB (Sindh Revenue Board)',
            referenceNumber: referenceNumber,
            notes:
                'Tax payment to SRB (Sindh Revenue Board) - Voucher #$voucherNumber',
          ),
        ];
      }
    }

    // Helper function to simulate account balance changes
    double simulateDepositCreation(
        double currentBalance, double depositAmount) {
      return currentBalance + depositAmount; // Deposits increase balance
    }

    double simulateDepositDeletion(
        double currentBalance, double depositAmount) {
      return currentBalance -
          depositAmount; // Deposit deletion decreases balance
    }

    test('should calculate correct balance for single tax authority payment',
        () {
      final account = createFreightTaxAccount(initialBalance: 10000.0);
      const companyFreight = 45000.0;
      final expectedTaxAmount = companyFreight * 0.15; // 6750.0

      // Create tax authority deposits
      final taxDeposits = createTaxAuthorityDeposits(
        accountId: account.id,
        accountName: account.name,
        companyFreight: companyFreight,
        isSplit: false,
      );

      // Verify single deposit created
      expect(taxDeposits.length, 1);
      expect(taxDeposits[0].amount, expectedTaxAmount);
      expect(taxDeposits[0].categoryId, 'tax_authority_single');

      // Simulate voucher creation (deposit creation)
      var currentBalance = account.availableBalance;
      for (final deposit in taxDeposits) {
        currentBalance =
            simulateDepositCreation(currentBalance, deposit.amount);
      }

      // Verify balance after voucher creation
      expect(currentBalance, 10000.0 + 6750.0); // 16750.0

      // Simulate voucher deletion (deposit deletion)
      for (final deposit in taxDeposits) {
        currentBalance =
            simulateDepositDeletion(currentBalance, deposit.amount);
      }

      // Verify balance returns to original after voucher deletion
      expect(currentBalance, 10000.0); // Should return to original balance
      expect(currentBalance >= 0, true); // Should not be negative
    });

    test('should calculate correct balance for split tax authority payments',
        () {
      final account = createFreightTaxAccount(initialBalance: 10000.0);
      const companyFreight = 45000.0;
      final expectedTaxAmount = companyFreight * 0.15; // 6750.0
      final expectedSplitAmount = expectedTaxAmount / 2; // 3375.0 each

      // Create split tax authority deposits
      final taxDeposits = createTaxAuthorityDeposits(
        accountId: account.id,
        accountName: account.name,
        companyFreight: companyFreight,
        isSplit: true,
      );

      // Verify split deposits created
      expect(taxDeposits.length, 2);
      expect(taxDeposits[0].amount, expectedSplitAmount);
      expect(taxDeposits[1].amount, expectedSplitAmount);
      expect(taxDeposits[0].categoryId, 'tax_authority_split');
      expect(taxDeposits[1].categoryId, 'tax_authority_split');

      // Verify total amount is correct
      final totalAmount =
          taxDeposits.fold(0.0, (sum, deposit) => sum + deposit.amount);
      expect(totalAmount, expectedTaxAmount);

      // Simulate voucher creation (deposit creation)
      var currentBalance = account.availableBalance;
      for (final deposit in taxDeposits) {
        currentBalance =
            simulateDepositCreation(currentBalance, deposit.amount);
      }

      // Verify balance after voucher creation
      expect(currentBalance, 10000.0 + 6750.0); // 16750.0

      // Simulate voucher deletion (deposit deletion)
      for (final deposit in taxDeposits) {
        currentBalance =
            simulateDepositDeletion(currentBalance, deposit.amount);
      }

      // Verify balance returns to original after voucher deletion
      expect(currentBalance, 10000.0); // Should return to original balance
      expect(currentBalance >= 0, true); // Should not be negative
    });

    test('should handle multiple vouchers correctly', () {
      final account = createFreightTaxAccount(initialBalance: 10000.0);

      // Create multiple vouchers with different tax amounts
      final voucher1Deposits = createTaxAuthorityDeposits(
        accountId: account.id,
        accountName: account.name,
        companyFreight: 30000.0, // 4500.0 tax
        isSplit: false,
      );

      final voucher2Deposits = createTaxAuthorityDeposits(
        accountId: account.id,
        accountName: account.name,
        companyFreight: 20000.0, // 3000.0 tax
        isSplit: true,
      );

      var currentBalance = account.availableBalance;

      // Create voucher 1
      for (final deposit in voucher1Deposits) {
        currentBalance =
            simulateDepositCreation(currentBalance, deposit.amount);
      }
      expect(currentBalance, 14500.0); // 10000 + 4500

      // Create voucher 2
      for (final deposit in voucher2Deposits) {
        currentBalance =
            simulateDepositCreation(currentBalance, deposit.amount);
      }
      expect(currentBalance, 17500.0); // 14500 + 3000

      // Delete voucher 1
      for (final deposit in voucher1Deposits) {
        currentBalance =
            simulateDepositDeletion(currentBalance, deposit.amount);
      }
      expect(currentBalance, 13000.0); // 17500 - 4500

      // Delete voucher 2
      for (final deposit in voucher2Deposits) {
        currentBalance =
            simulateDepositDeletion(currentBalance, deposit.amount);
      }
      expect(currentBalance, 10000.0); // Back to original
      expect(currentBalance >= 0, true); // Should not be negative
    });

    test('should identify potential negative balance scenarios', () {
      // Test scenario that might cause negative balance
      final account = createFreightTaxAccount(
          initialBalance: 5000.0); // Lower initial balance
      const companyFreight = 45000.0;
      final expectedTaxAmount = companyFreight * 0.15; // 6750.0

      final taxDeposits = createTaxAuthorityDeposits(
        accountId: account.id,
        accountName: account.name,
        companyFreight: companyFreight,
        isSplit: false,
      );

      var currentBalance = account.availableBalance;

      // Create voucher (deposit creation)
      for (final deposit in taxDeposits) {
        currentBalance =
            simulateDepositCreation(currentBalance, deposit.amount);
      }
      expect(currentBalance, 11750.0); // 5000 + 6750

      // Delete voucher (deposit deletion)
      for (final deposit in taxDeposits) {
        currentBalance =
            simulateDepositDeletion(currentBalance, deposit.amount);
      }

      // Should return to original balance, not negative
      expect(currentBalance, 5000.0);
      expect(currentBalance >= 0, true);
    });

    test('should verify deposit cleanup finds correct tax entries', () {
      final account = createFreightTaxAccount();
      const companyFreight = 45000.0;

      // Create mixed deposits (tax and non-tax)
      final allDeposits = [
        // Tax authority deposits
        ...createTaxAuthorityDeposits(
          accountId: account.id,
          accountName: account.name,
          companyFreight: companyFreight,
          isSplit: true,
        ),
        // Non-tax deposit
        DepositModel(
          id: 'other_dep_1',
          accountId: account.id,
          accountName: account.name,
          amount: 1000.0,
          createdAt: DateTime.now(),
          categoryId: 'other',
          categoryName: 'Other',
          payerId: '',
          payerName: 'Other Payer',
          referenceNumber: 'V-OTHER-001',
          notes: 'Other deposit',
        ),
      ];

      // Filter deposits by reference number (simulating cleanup logic)
      final voucherDeposits = allDeposits
          .where((deposit) => deposit.referenceNumber == referenceNumber)
          .toList();

      // Should find only tax authority deposits for this voucher
      expect(voucherDeposits.length, 2); // Two split tax deposits
      expect(
          voucherDeposits
              .every((d) => d.categoryId.startsWith('tax_authority')),
          true);

      // Calculate total tax amount to be removed
      final totalTaxAmount =
          voucherDeposits.fold(0.0, (sum, deposit) => sum + deposit.amount);
      expect(totalTaxAmount, companyFreight * 0.15);
    });

    test('should verify category ID filtering works correctly', () {
      final account = createFreightTaxAccount();

      // Create deposits with different category IDs
      final testDeposits = [
        DepositModel(
          id: 'dep1',
          accountId: account.id,
          accountName: account.name,
          amount: 3375.0,
          createdAt: DateTime.now(),
          categoryId: 'tax_authority_single',
          categoryName: 'Tax Authority Payment',
          payerId: '',
          payerName: 'SRB',
          referenceNumber: referenceNumber,
          notes: 'Single tax payment',
        ),
        DepositModel(
          id: 'dep2',
          accountId: account.id,
          accountName: account.name,
          amount: 1687.5,
          createdAt: DateTime.now(),
          categoryId: 'tax_authority_split',
          categoryName: 'Tax Authority Payment (Split)',
          payerId: '',
          payerName: 'PRA',
          referenceNumber: referenceNumber,
          notes: 'Split tax payment',
        ),
        DepositModel(
          id: 'dep3',
          accountId: account.id,
          accountName: account.name,
          amount: 5000.0,
          createdAt: DateTime.now(),
          categoryId: 'broker_fees',
          categoryName: 'Broker Fees',
          payerId: '',
          payerName: 'Broker',
          referenceNumber: referenceNumber,
          notes: 'Broker fee deposit',
        ),
      ];

      // Filter by tax authority categories
      final taxDeposits = testDeposits
          .where((deposit) =>
              deposit.categoryId == 'tax_authority_single' ||
              deposit.categoryId == 'tax_authority_split')
          .toList();

      expect(taxDeposits.length, 2);
      expect(taxDeposits[0].categoryId, 'tax_authority_single');
      expect(taxDeposits[1].categoryId, 'tax_authority_split');

      // Verify non-tax deposits are not included
      final nonTaxDeposits = testDeposits
          .where((deposit) => !deposit.categoryId.startsWith('tax_authority'))
          .toList();

      expect(nonTaxDeposits.length, 1);
      expect(nonTaxDeposits[0].categoryId, 'broker_fees');
    });

    test('should simulate exact voucher deletion scenario with debugging', () {
      final account = createFreightTaxAccount(initialBalance: 10000.0);
      const companyFreight = 45000.0;
      final expectedTaxAmount = companyFreight * 0.15; // 6750.0

      // Step 1: Create voucher with tax authority payments
      final taxDeposits = createTaxAuthorityDeposits(
        accountId: account.id,
        accountName: account.name,
        companyFreight: companyFreight,
        isSplit: true, // Split payment: 3375.0 each to two authorities
      );

      // Verify tax deposits created correctly
      expect(taxDeposits.length, 2);
      expect(taxDeposits[0].amount, 3375.0);
      expect(taxDeposits[1].amount, 3375.0);

      // Step 2: Simulate account balance after voucher creation
      var currentBalance = account.availableBalance;
      final balanceHistory = <String, double>{};
      balanceHistory['Initial'] = currentBalance;

      for (int i = 0; i < taxDeposits.length; i++) {
        final deposit = taxDeposits[i];
        currentBalance =
            simulateDepositCreation(currentBalance, deposit.amount);
        balanceHistory['After deposit ${i + 1} (${deposit.amount})'] =
            currentBalance;
      }

      // Verify balance after voucher creation
      expect(currentBalance, 16750.0); // 10000 + 6750
      balanceHistory['After voucher creation'] = currentBalance;

      // Step 3: Simulate voucher deletion cleanup
      for (int i = 0; i < taxDeposits.length; i++) {
        final deposit = taxDeposits[i];
        currentBalance =
            simulateDepositDeletion(currentBalance, deposit.amount);
        balanceHistory['After deleting deposit ${i + 1} (${deposit.amount})'] =
            currentBalance;
      }

      // Verify final balance
      expect(currentBalance, 10000.0); // Should return to original
      balanceHistory['After voucher deletion'] = currentBalance;

      // Debug: Verify balance history (print statements removed for production)
      // Balance history is tracked in balanceHistory map for debugging if needed

      // Verify no negative balance
      expect(currentBalance >= 0, true,
          reason: 'Balance should not be negative');

      // Verify balance returned to original
      expect(currentBalance, account.initialBalance,
          reason: 'Balance should return to original after voucher deletion');
    });

    test('should identify potential double-deletion scenarios', () {
      final account = createFreightTaxAccount(initialBalance: 10000.0);
      const companyFreight = 45000.0;

      final taxDeposits = createTaxAuthorityDeposits(
        accountId: account.id,
        accountName: account.name,
        companyFreight: companyFreight,
        isSplit: false, // Single payment: 6750.0
      );

      var currentBalance = account.availableBalance;

      // Create voucher
      for (final deposit in taxDeposits) {
        currentBalance =
            simulateDepositCreation(currentBalance, deposit.amount);
      }
      expect(currentBalance, 16750.0);

      // Delete voucher once (correct)
      for (final deposit in taxDeposits) {
        currentBalance =
            simulateDepositDeletion(currentBalance, deposit.amount);
      }
      expect(currentBalance, 10000.0);

      // Simulate accidental double deletion (this would cause negative balance)
      for (final deposit in taxDeposits) {
        currentBalance =
            simulateDepositDeletion(currentBalance, deposit.amount);
      }

      // This would result in negative balance
      expect(currentBalance, 3250.0); // 10000 - 6750 = 3250 (negative scenario)

      // This demonstrates what happens if deletion is called twice
      // Double deletion result would be: $currentBalance
      // This would be the issue if deletion is called multiple times
    });

    test('should verify reference number matching logic', () {
      final account = createFreightTaxAccount();

      // Create deposits with different reference numbers
      final allDeposits = [
        // Voucher V-001 deposits
        DepositModel(
          id: 'dep1',
          accountId: account.id,
          accountName: account.name,
          amount: 3375.0,
          createdAt: DateTime.now(),
          categoryId: 'tax_authority_single',
          categoryName: 'Tax Authority Payment',
          payerId: '',
          payerName: 'SRB',
          referenceNumber: 'V-001', // Target voucher
          notes: 'Tax payment for V-001',
        ),
        // Voucher V-002 deposits
        DepositModel(
          id: 'dep2',
          accountId: account.id,
          accountName: account.name,
          amount: 2250.0,
          createdAt: DateTime.now(),
          categoryId: 'tax_authority_single',
          categoryName: 'Tax Authority Payment',
          payerId: '',
          payerName: 'PRA',
          referenceNumber: 'V-002', // Different voucher
          notes: 'Tax payment for V-002',
        ),
        // Non-voucher deposit
        DepositModel(
          id: 'dep3',
          accountId: account.id,
          accountName: account.name,
          amount: 1000.0,
          createdAt: DateTime.now(),
          categoryId: 'other',
          categoryName: 'Other',
          payerId: '',
          payerName: 'Other',
          referenceNumber: 'OTHER-001', // Non-voucher reference
          notes: 'Other deposit',
        ),
      ];

      // Simulate cleanup for voucher V-001
      final targetReferenceNumber = 'V-001';
      final voucherDeposits = allDeposits
          .where((deposit) => deposit.referenceNumber == targetReferenceNumber)
          .toList();

      // Should find only deposits for V-001
      expect(voucherDeposits.length, 1);
      expect(voucherDeposits[0].referenceNumber, 'V-001');
      expect(voucherDeposits[0].amount, 3375.0);

      // Verify other deposits are not affected
      final otherDeposits = allDeposits
          .where((deposit) => deposit.referenceNumber != targetReferenceNumber)
          .toList();

      expect(otherDeposits.length, 2);
      expect(otherDeposits.any((d) => d.referenceNumber == 'V-002'), true);
      expect(otherDeposits.any((d) => d.referenceNumber == 'OTHER-001'), true);
    });

    test('should reproduce the exact bug scenario: -3375 balance', () {
      // Reproduce the exact scenario from the bug report
      final account =
          createFreightTaxAccount(initialBalance: 0.0); // Start with 0 balance
      const companyFreight = 45000.0;
      final expectedTaxAmount = companyFreight * 0.15; // 6750.0

      // === REPRODUCING BUG SCENARIO ===
      // Initial account balance: ${account.availableBalance}
      // Company freight: $companyFreight
      // Expected 15% tax: $expectedTaxAmount

      // Create split tax authority payments (3375.0 each)
      final taxDeposits = createTaxAuthorityDeposits(
        accountId: account.id,
        accountName: account.name,
        companyFreight: companyFreight,
        isSplit: true,
      );

      var currentBalance = account.availableBalance;
      // Tax deposits created: ${taxDeposits.length}

      // Simulate voucher creation
      for (int i = 0; i < taxDeposits.length; i++) {
        final deposit = taxDeposits[i];
        // Creating deposit ${i + 1}: ${deposit.amount}
        currentBalance =
            simulateDepositCreation(currentBalance, deposit.amount);
        // Balance after deposit ${i + 1}: $currentBalance
      }

      // Balance after voucher creation: $currentBalance
      expect(currentBalance, 6750.0); // Should be 0 + 6750

      // Now simulate the bug: what if the account somehow has a different balance?
      // Let's say the account balance was incorrectly set to 3375.0 instead of 6750.0
      currentBalance = 3375.0; // Simulate incorrect balance
      // Simulating incorrect balance state: $currentBalance

      // Now delete the voucher
      for (int i = 0; i < taxDeposits.length; i++) {
        final deposit = taxDeposits[i];
        // Deleting deposit ${i + 1}: ${deposit.amount}
        currentBalance =
            simulateDepositDeletion(currentBalance, deposit.amount);
        // Balance after deleting deposit ${i + 1}: $currentBalance
      }

      // Final balance after voucher deletion: $currentBalance

      // This would result in -3375.0 if the balance was incorrectly 3375.0 before deletion
      expect(currentBalance, -3375.0);
      // BUG REPRODUCED: Balance is negative: $currentBalance
    });

    test('should identify the root cause of negative balance', () {
      // Test different scenarios that could cause negative balance
      final scenarios = [
        {
          'name': 'Scenario 1: Double deletion',
          'initialBalance': 10000.0,
          'taxAmount': 6750.0,
          'deletionCount': 2, // Delete twice
          'expectedFinal': 3250.0, // 10000 + 6750 - 6750 - 6750
        },
        {
          'name': 'Scenario 2: Deletion without creation (missing deposit)',
          'initialBalance': 3375.0, // Half of tax amount
          'taxAmount': 6750.0,
          'deletionCount': 1,
          'skipCreation':
              true, // Skip deposit creation to simulate missing deposit
          'expectedFinal':
              -3375.0, // 3375 - 6750 = -3375 (deletion without creation)
        },
        {
          'name': 'Scenario 3: Missing deposit creation',
          'initialBalance': 10000.0,
          'taxAmount': 6750.0,
          'deletionCount': 1,
          'skipCreation': true,
          'expectedFinal': 3250.0, // 10000 - 6750 (deletion without creation)
        },
      ];

      for (final scenario in scenarios) {
        // === ${scenario['name']} ===

        var balance = scenario['initialBalance'] as double;
        final taxAmount = scenario['taxAmount'] as double;
        final deletionCount = scenario['deletionCount'] as int;
        final skipCreation = scenario['skipCreation'] as bool? ?? false;
        final expectedFinal = scenario['expectedFinal'] as double;

        // Initial balance: $balance

        // Create deposit (unless skipped)
        if (!skipCreation) {
          balance = simulateDepositCreation(balance, taxAmount);
          // After deposit creation: $balance
        }

        // Delete deposit (possibly multiple times)
        for (int i = 0; i < deletionCount; i++) {
          balance = simulateDepositDeletion(balance, taxAmount);
          // After deletion ${i + 1}: $balance
        }

        // Final balance: $balance
        // Expected: $expectedFinal
        expect(balance, expectedFinal);

        // if (balance < 0) {
        //   NEGATIVE BALANCE DETECTED: $balance
        // }
      }
    });

    test('should verify the correct tax account workflow', () {
      // This test shows how the tax account should work correctly
      final account = createFreightTaxAccount(initialBalance: 10000.0);
      const companyFreight = 45000.0;
      final taxAmount = companyFreight * 0.15; // 6750.0

      // === CORRECT WORKFLOW ===
      // Initial balance: ${account.availableBalance}

      var currentBalance = account.availableBalance;

      // Step 1: Create voucher with tax authority payments
      // Step 1: Creating voucher with tax payments
      currentBalance = simulateDepositCreation(currentBalance, taxAmount);
      // Balance after tax deposit: $currentBalance
      expect(currentBalance, 16750.0); // 10000 + 6750

      // Step 2: Delete voucher and its tax authority payments
      // Step 2: Deleting voucher and tax payments
      currentBalance = simulateDepositDeletion(currentBalance, taxAmount);
      // Balance after tax deletion: $currentBalance
      expect(currentBalance, 10000.0); // Back to original

      // Verify no negative balance
      expect(currentBalance >= 0, true);
      // SUCCESS: Balance returned to original without going negative
    });
  });
}
