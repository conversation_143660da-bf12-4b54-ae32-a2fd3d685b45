import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import '../../../../../core/services/voucher_integration_test_service.dart';
import '../../../../../core/utils/app_constants/styles/app_text_styles.dart';
import '../../../../../features/home/<USER>/theme.dart';

/// Widget for running and displaying voucher integration test results
class VoucherIntegrationTestWidget extends StatefulWidget {
  const VoucherIntegrationTestWidget({super.key});

  @override
  State<VoucherIntegrationTestWidget> createState() =>
      _VoucherIntegrationTestWidgetState();
}

class _VoucherIntegrationTestWidgetState
    extends State<VoucherIntegrationTestWidget> {
  final VoucherIntegrationTestService _testService =
      VoucherIntegrationTestService();
  IntegrationTestResult? _testResult;
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(notifier),
            const SizedBox(height: 16),
            _buildTestControls(notifier),
            if (_testResult != null) ...[
              const SizedBox(height: 24),
              _buildTestResults(notifier),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ColorNotifier notifier) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.integration_instructions,
            color: Colors.blue[700],
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Voucher Integration Test',
                style: AppTextStyles.titleLargeStyle.copyWith(
                  color: notifier.text,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Test the complete voucher-to-journal entry integration workflow',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTestControls(ColorNotifier notifier) {
    return Row(
      children: [
        ElevatedButton.icon(
          onPressed: _isRunning ? null : _runIntegrationTest,
          icon: _isRunning
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.play_arrow),
          label: Text(_isRunning ? 'Running Test...' : 'Run Integration Test'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[700],
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 16),
        if (_testResult != null)
          OutlinedButton.icon(
            onPressed: _isRunning ? null : _cleanupTestData,
            icon: const Icon(Icons.cleaning_services),
            label: const Text('Cleanup Test Data'),
          ),
      ],
    );
  }

  Widget _buildTestResults(ColorNotifier notifier) {
    if (_testResult == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildResultSummary(notifier),
        const SizedBox(height: 16),
        _buildTestSteps(notifier),
        if (_testResult!.journalEntries != null) ...[
          const SizedBox(height: 16),
          _buildJournalEntriesSection(notifier),
        ],
        if (_testResult!.accountBalances != null) ...[
          const SizedBox(height: 16),
          _buildAccountBalancesSection(notifier),
        ],
        if (_testResult!.ledgerEntries != null) ...[
          const SizedBox(height: 16),
          _buildLedgerEntriesSection(notifier),
        ],
      ],
    );
  }

  Widget _buildResultSummary(ColorNotifier notifier) {
    final isSuccess = _testResult!.isSuccessful;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSuccess ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSuccess ? Colors.green[200]! : Colors.red[200]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isSuccess ? Icons.check_circle : Icons.error,
            color: isSuccess ? Colors.green[700] : Colors.red[700],
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _testResult!.summary,
                  style: AppTextStyles.headingMedium.copyWith(
                    color: isSuccess ? Colors.green[700] : Colors.red[700],
                  ),
                ),
                if (_testResult!.testVoucher != null)
                  Text(
                    'Test Voucher: ${_testResult!.testVoucher!.voucherNumber}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestSteps(ColorNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Test Steps',
          style: AppTextStyles.headingMedium.copyWith(
            color: notifier.text,
          ),
        ),
        const SizedBox(height: 8),
        ...(_testResult!.steps.map((step) => _buildTestStep(step, notifier))),
      ],
    );
  }

  Widget _buildTestStep(TestStep step, ColorNotifier notifier) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            step.isSuccessful ? Icons.check_circle : Icons.cancel,
            color: step.isSuccessful ? Colors.green[600] : Colors.red[600],
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              step.description,
              style: AppTextStyles.bodyMedium.copyWith(
                color: notifier.text,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildJournalEntriesSection(ColorNotifier notifier) {
    return _buildDataSection(
      title: 'Journal Entries',
      icon: Icons.receipt_long,
      data: _testResult!.journalEntries!,
      notifier: notifier,
    );
  }

  Widget _buildAccountBalancesSection(ColorNotifier notifier) {
    return _buildDataSection(
      title: 'Account Balances',
      icon: Icons.account_balance,
      data: _testResult!.accountBalances!,
      notifier: notifier,
    );
  }

  Widget _buildLedgerEntriesSection(ColorNotifier notifier) {
    return _buildDataSection(
      title: 'Ledger Entries',
      icon: Icons.account_balance_wallet,
      data: _testResult!.ledgerEntries!,
      notifier: notifier,
    );
  }

  Widget _buildDataSection({
    required String title,
    required IconData icon,
    required List<Map<String, dynamic>> data,
    required ColorNotifier notifier,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Colors.blue[700], size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: AppTextStyles.headingMedium.copyWith(
                color: notifier.text,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            children: data
                .map((item) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        item.toString(),
                        style: AppTextStyles.bodySmall.copyWith(
                          fontFamily: 'monospace',
                          color: Colors.grey[700],
                        ),
                      ),
                    ))
                .toList(),
          ),
        ),
      ],
    );
  }

  Future<void> _runIntegrationTest() async {
    setState(() {
      _isRunning = true;
      _testResult = null;
    });

    try {
      final result = await _testService.runComprehensiveTest();
      setState(() {
        _testResult = result;
      });

      // Show result snackbar
      Get.snackbar(
        result.isSuccessful ? 'Test Passed' : 'Test Failed',
        result.summary,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor:
            result.isSuccessful ? Colors.green[100] : Colors.red[100],
        colorText: result.isSuccessful ? Colors.green[800] : Colors.red[800],
      );
    } catch (e) {
      Get.snackbar(
        'Test Error',
        'Failed to run integration test: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[100],
        colorText: Colors.red[800],
      );
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _cleanupTestData() async {
    if (_testResult == null) return;

    try {
      await _testService.cleanupTestData(_testResult!);
      Get.snackbar(
        'Cleanup Complete',
        'Test data has been cleaned up',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue[100],
        colorText: Colors.blue[800],
      );
    } catch (e) {
      Get.snackbar(
        'Cleanup Error',
        'Failed to cleanup test data: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[100],
        colorText: Colors.red[800],
      );
    }
  }
}
