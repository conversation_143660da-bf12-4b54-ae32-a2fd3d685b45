import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/services/voucher_accounting_hook_service.dart';
import 'package:logestics/models/voucher_model.dart';

/// Debug service to test voucher-to-journal entry integration
class VoucherIntegrationDebugService {
  static final VoucherAccountingHookService _hookService =
      VoucherAccountingHookService();

  /// Test the voucher accounting hook with a sample voucher
  static Future<void> testVoucherIntegration() async {
    try {
      final uid = FirebaseAuth.instance.currentUser?.uid ?? '';
      if (uid.isEmpty) {
        log('❌ No authenticated user found');
        return;
      }

      log('🔍 Testing voucher-to-journal entry integration...');

      // Create a test voucher with Chart of Accounts data
      final testVoucherData = {
        'voucherNumber': 'TEST-${DateTime.now().millisecondsSinceEpoch}',
        'voucherStatus': 'Active',
        'driverName': 'Test Driver',
        'departureDate': DateTime.now().toIso8601String(),
        'productName': 'Test Product',
        'totalNumberOfBags': 100,
        'brokerType': 'outsource',
        'brokerName': 'Test Broker',
        'selectedBroker': 'test-broker-id',
        'brokerFees': 5000.0,
        'munshianaFees': 2000.0,
        'brokerAccount': '',
        'munshianaAccount': '',
        'driverPhoneNumber': '**********',
        'truckNumber': 'ABC-123',
        'conveyNoteNumber': 'CN-123',
        'totalFreight': 50000.0,
        'companyFreight': 45000.0,
        'settledFreight': 45000.0,
        'weightInTons': 10,
        'invoiceTasNumberList': ['TAS-001'],
        'invoiceBiltyNumberList': ['BILTY-001'],
        'paymentTransactions': [],
        'belongsToDate': DateTime.now().millisecondsSinceEpoch,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
        'uid': uid,

        // Chart of Accounts fields
        'brokerAccountId': 'test-broker-account-id',
        'munshianaAccountId': 'test-munshiana-account-id',
        'salesTaxAccountId': 'test-sales-tax-account-id',
        'freightTaxAccountId': 'test-freight-tax-account-id',
        'profitAccountId': 'test-profit-account-id',
        'nlcAccountId': 'test-nlc-account-id',
        'truckFreightAccountId': 'test-truck-freight-account-id',

        // Tax amounts
        'salesTaxAmount': 750.0, // 15% of 5000
        'freightTaxAmount': 2070.0, // 4.6% of 45000
        'netProfitAmount': 37180.0, // Calculated net profit
      };

      log('📝 Test voucher data: ${testVoucherData['voucherNumber']}');

      // Test VoucherModel.fromJson conversion
      try {
        final voucher = VoucherModel.fromJson(testVoucherData);
        log('✅ VoucherModel.fromJson successful for voucher: ${voucher.voucherNumber}');
      } catch (e) {
        log('❌ VoucherModel.fromJson failed: $e');
        return;
      }

      // Test hook service call
      log('🔗 Calling voucher accounting hook...');
      await _hookService.onVoucherCreated(testVoucherData, uid);

      log('✅ Voucher integration test completed');
    } catch (e) {
      log('❌ Voucher integration test failed: $e');
    }
  }

  /// Test with actual voucher data from the UI
  static Future<void> testWithActualVoucher(
      Map<String, dynamic> voucherData) async {
    try {
      final uid = FirebaseAuth.instance.currentUser?.uid ?? '';
      if (uid.isEmpty) {
        log('❌ No authenticated user found');
        return;
      }

      log('🔍 Testing actual voucher integration...');
      log('📝 Voucher number: ${voucherData['voucherNumber']}');

      // Log voucher data structure
      log('📊 Voucher data keys: ${voucherData.keys.toList()}');

      // Check for Chart of Accounts fields
      final coaFields = [
        'brokerAccountId',
        'munshianaAccountId',
        'salesTaxAccountId',
        'freightTaxAccountId',
        'profitAccountId',
        'nlcAccountId',
        'truckFreightAccountId'
      ];

      log('🏦 Chart of Accounts fields present:');
      for (final field in coaFields) {
        final value = voucherData[field];
        log('  $field: ${value ?? 'NOT SET'}');
      }

      // Test VoucherModel conversion
      try {
        final voucher = VoucherModel.fromJson(voucherData);
        log('✅ VoucherModel conversion successful for voucher: ${voucher.voucherNumber}');

        // Call hook service
        log('🔗 Calling voucher accounting hook...');
        await _hookService.onVoucherCreated(voucherData, uid);

        log('✅ Actual voucher integration test completed');
      } catch (e) {
        log('❌ VoucherModel conversion failed: $e');
      }
    } catch (e) {
      log('❌ Actual voucher integration test failed: $e');
    }
  }

  /// Check if journal entries exist for a voucher
  static Future<void> checkJournalEntries(String voucherNumber) async {
    try {
      final uid = FirebaseAuth.instance.currentUser?.uid ?? '';
      if (uid.isEmpty) {
        log('❌ No authenticated user found');
        return;
      }

      log('🔍 Checking journal entries for voucher: $voucherNumber');

      // This would require access to the journal entry service
      // For now, just log that we're checking
      log('📋 Journal entry check would be performed here');
    } catch (e) {
      log('❌ Journal entry check failed: $e');
    }
  }
}
