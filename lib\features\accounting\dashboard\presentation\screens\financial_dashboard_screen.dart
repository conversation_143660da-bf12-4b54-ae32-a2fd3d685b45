import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/financial_dashboard_controller.dart';
import '../widgets/financial_summary_widget.dart';
import '../widgets/account_balance_widget.dart';
import '../widgets/recent_transactions_widget.dart';
import '../widgets/financial_reports_widget.dart';
import '../widgets/cash_flow_chart_widget.dart';
import '../widgets/expense_breakdown_widget.dart';

/// Main Financial Dashboard screen showing comprehensive financial insights
class FinancialDashboardScreen extends StatelessWidget {
  const FinancialDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(FinancialDashboardController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('Financial Dashboard'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshDashboard,
            tooltip: 'Refresh Dashboard',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showDashboardSettings(context, controller),
            tooltip: 'Dashboard Settings',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading Financial Dashboard...'),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refreshDashboard,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: LayoutBuilder(
              builder: (context, constraints) {
                if (constraints.maxWidth > 1200) {
                  return _buildDesktopLayout(controller);
                } else if (constraints.maxWidth > 800) {
                  return _buildTabletLayout(controller);
                } else {
                  return _buildMobileLayout(controller);
                }
              },
            ),
          ),
        );
      }),
    );
  }

  /// Desktop layout with 3-column grid
  Widget _buildDesktopLayout(FinancialDashboardController controller) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column - Summary and Balances
        Expanded(
          flex: 1,
          child: Column(
            children: [
              const FinancialSummaryWidget(),
              const SizedBox(height: 20),
              const AccountBalanceWidget(),
              const SizedBox(height: 20),
              const FinancialReportsWidget(),
            ],
          ),
        ),
        const SizedBox(width: 20),

        // Middle column - Charts and Analytics
        Expanded(
          flex: 2,
          child: Column(
            children: [
              const CashFlowChartWidget(),
              const SizedBox(height: 20),
              const ExpenseBreakdownWidget(),
            ],
          ),
        ),
        const SizedBox(width: 20),

        // Right column - Recent Activity
        Expanded(
          flex: 1,
          child: const RecentTransactionsWidget(),
        ),
      ],
    );
  }

  /// Tablet layout with 2-column grid
  Widget _buildTabletLayout(FinancialDashboardController controller) {
    return Column(
      children: [
        // Top row - Summary cards
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Expanded(child: FinancialSummaryWidget()),
            const SizedBox(width: 20),
            const Expanded(child: AccountBalanceWidget()),
          ],
        ),
        const SizedBox(height: 20),

        // Middle row - Charts
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Expanded(child: CashFlowChartWidget()),
            const SizedBox(width: 20),
            const Expanded(child: ExpenseBreakdownWidget()),
          ],
        ),
        const SizedBox(height: 20),

        // Bottom row - Reports and Transactions
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Expanded(child: FinancialReportsWidget()),
            const SizedBox(width: 20),
            const Expanded(child: RecentTransactionsWidget()),
          ],
        ),
      ],
    );
  }

  /// Mobile layout with single column
  Widget _buildMobileLayout(FinancialDashboardController controller) {
    return Column(
      children: [
        const FinancialSummaryWidget(),
        const SizedBox(height: 20),
        const AccountBalanceWidget(),
        const SizedBox(height: 20),
        const CashFlowChartWidget(),
        const SizedBox(height: 20),
        const ExpenseBreakdownWidget(),
        const SizedBox(height: 20),
        const FinancialReportsWidget(),
        const SizedBox(height: 20),
        const RecentTransactionsWidget(),
      ],
    );
  }

  /// Show dashboard settings dialog
  void _showDashboardSettings(
      BuildContext context, FinancialDashboardController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Dashboard Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Obx(() => SwitchListTile(
                  title: const Text('Auto Refresh'),
                  subtitle:
                      const Text('Automatically refresh data every 5 minutes'),
                  value: controller.autoRefresh.value,
                  onChanged: controller.toggleAutoRefresh,
                )),
            Obx(() => SwitchListTile(
                  title: const Text('Show Inactive Accounts'),
                  subtitle: const Text(
                      'Include inactive accounts in balance calculations'),
                  value: controller.showInactiveAccounts.value,
                  onChanged: controller.toggleShowInactiveAccounts,
                )),
            const Divider(),
            ListTile(
              title: const Text('Date Range'),
              subtitle: Obx(() => Text(controller.dateRangeText)),
              trailing: const Icon(Icons.calendar_today),
              onTap: () => _showDateRangePicker(context, controller),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show date range picker
  void _showDateRangePicker(
      BuildContext context, FinancialDashboardController controller) {
    showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: controller.startDate.value,
        end: controller.endDate.value,
      ),
    ).then((range) {
      if (range != null) {
        controller.updateDateRange(range.start, range.end);
        if (context.mounted) {
          Navigator.of(context).pop(); // Close settings dialog
        }
      }
    });
  }
}
