import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';
import '../models/finance/account_model.dart';
import '../models/finance/account_transaction_model.dart';
import '../models/finance/deposit_model.dart';
import '../models/finance/payer_model.dart';
import '../models/finance/deposit_category_model.dart';
import '../models/finance/expense_model.dart';
import '../models/finance/payee_model.dart';
import '../models/finance/expense_category_model.dart';
import '../models/user_model.dart';
import '../models/invoice_model.dart';
import '../models/finance/bill_model.dart';
import '../core/utils/constants/constants.dart';

class PDFGenerationService {
  static final PDFGenerationService _instance =
      PDFGenerationService._internal();
  factory PDFGenerationService() => _instance;
  PDFGenerationService._internal();

  /// Generate Account Statement PDF
  Future<Uint8List> generateAccountStatementPDF({
    required AccountModel account,
    required List<AccountTransactionModel> transactions,
    required UserModel currentUser,
    required DateTime startDate,
    required DateTime endDate,
    String? companyName,
    String? companyAddress,
    String? customTitle,
    Uint8List? logoBytes,
  }) async {
    try {
      log('Generating PDF for account: ${account.name}');

      final pdf = pw.Document();
      final dateFormat = DateFormat('dd/MM/yyyy');
      final currencyFormat =
          NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2);

      // Calculate totals
      final totalCredits = transactions
          .where((t) => t.amount > 0)
          .fold(0.0, (sum, t) => sum + t.amount);

      final totalDebits = transactions
          .where((t) => t.amount < 0)
          .fold(0.0, (sum, t) => sum + t.amount.abs());

      final closingBalance =
          account.initialBalance + totalCredits - totalDebits;

      // Sort transactions by date
      final sortedTransactions =
          List<AccountTransactionModel>.from(transactions)
            ..sort((a, b) => a.transactionDate.compareTo(b.transactionDate));

      // Calculate running balance for each transaction
      double runningBalance = account.initialBalance;
      final transactionsWithBalance = <Map<String, dynamic>>[];

      for (final transaction in sortedTransactions) {
        runningBalance += transaction.amount;
        transactionsWithBalance.add({
          'transaction': transaction,
          'runningBalance': runningBalance,
        });
      }

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (context) => _buildHeader(
            currentUser: currentUser,
            account: account,
            startDate: startDate,
            endDate: endDate,
            companyName: companyName,
            companyAddress: companyAddress,
            customTitle: customTitle,
            logoBytes: logoBytes,
          ),
          footer: (context) => _buildFooter(context),
          build: (context) => [
            // Account Summary Section
            _buildAccountSummary(
              account: account,
              totalCredits: totalCredits,
              totalDebits: totalDebits,
              closingBalance: closingBalance,
              currencyFormat: currencyFormat,
            ),

            pw.SizedBox(height: 20),

            // Transactions Table
            _buildTransactionsTable(
              transactionsWithBalance: transactionsWithBalance,
              dateFormat: dateFormat,
              currencyFormat: currencyFormat,
            ),

            pw.SizedBox(height: 20),

            // Summary Footer
            _buildSummaryFooter(
              totalCredits: totalCredits,
              totalDebits: totalDebits,
              closingBalance: closingBalance,
              currencyFormat: currencyFormat,
            ),
          ],
        ),
      );

      final pdfBytes = await pdf.save();
      log('PDF generated successfully. Size: ${pdfBytes.length} bytes');
      return pdfBytes;
    } catch (e) {
      log('Error generating PDF: $e');
      rethrow;
    }
  }

  /// Generate Deposits Report PDF
  Future<Uint8List> generateDepositsReportPDF({
    required List<DepositModel> deposits,
    required UserModel currentUser,
    DateTime? startDate,
    DateTime? endDate,
    List<AccountModel>? selectedAccounts,
    List<PayerModel>? selectedPayers,
    List<DepositCategoryModel>? selectedCategories,
    double? minAmount,
    double? maxAmount,
    String? companyName,
    String? companyAddress,
    String? customTitle,
    Uint8List? logoBytes,
  }) async {
    try {
      log('Generating Deposits Report PDF with ${deposits.length} deposits');

      final pdf = pw.Document();
      final dateFormat = DateFormat('dd/MM/yyyy');
      final currencyFormat =
          NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2);

      // Calculate totals
      final totalAmount =
          deposits.fold(0.0, (sum, deposit) => sum + deposit.amount);
      final totalCount = deposits.length;

      // Group by account
      final accountGroups = <String, List<DepositModel>>{};
      for (final deposit in deposits) {
        accountGroups.putIfAbsent(deposit.accountName, () => []).add(deposit);
      }

      // Group by category
      final categoryGroups = <String, List<DepositModel>>{};
      for (final deposit in deposits) {
        categoryGroups.putIfAbsent(deposit.categoryName, () => []).add(deposit);
      }

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              // Header
              _buildDepositsHeader(
                currentUser: currentUser,
                startDate: startDate,
                endDate: endDate,
                companyName: companyName,
                companyAddress: companyAddress,
                customTitle: customTitle,
                logoBytes: logoBytes,
              ),

              pw.SizedBox(height: 20),

              // Filter Summary
              _buildDepositsFilterSummary(
                selectedAccounts: selectedAccounts,
                selectedPayers: selectedPayers,
                selectedCategories: selectedCategories,
                startDate: startDate,
                endDate: endDate,
                minAmount: minAmount,
                maxAmount: maxAmount,
              ),

              pw.SizedBox(height: 20),

              // Summary Section
              _buildDepositsSummary(
                totalAmount: totalAmount,
                totalCount: totalCount,
                accountGroups: accountGroups,
                categoryGroups: categoryGroups,
                currencyFormat: currencyFormat,
              ),

              pw.SizedBox(height: 20),

              // Deposits Table
              _buildDepositsTable(
                deposits: deposits,
                dateFormat: dateFormat,
                currencyFormat: currencyFormat,
              ),
            ];
          },
          footer: (pw.Context context) {
            return _buildFooter(context);
          },
        ),
      );

      final pdfBytes = await pdf.save();
      log('Deposits PDF generated successfully. Size: ${pdfBytes.length} bytes');
      return pdfBytes;
    } catch (e) {
      log('Error generating Deposits PDF: $e');
      rethrow;
    }
  }

  /// Generate Expenses Report PDF
  Future<Uint8List> generateExpensesReportPDF({
    required List<ExpenseModel> expenses,
    required UserModel currentUser,
    DateTime? startDate,
    DateTime? endDate,
    List<AccountModel>? selectedAccounts,
    List<PayeeModel>? selectedPayees,
    List<ExpenseCategoryModel>? selectedCategories,
    double? minAmount,
    double? maxAmount,
    String? customTitle,
    String? companyName,
    String? companyAddress,
    Uint8List? logoBytes,
  }) async {
    try {
      log('Generating Expenses PDF with ${expenses.length} expenses');

      final pdf = pw.Document();
      final dateFormat = DateFormat('dd/MM/yyyy');
      final currencyFormat =
          NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2);

      // Calculate totals
      final totalExpenses = expenses.length;
      final totalAmount =
          expenses.fold(0.0, (sum, expense) => sum + expense.amount);

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (context) {
            return _buildExpensesHeader(
              context,
              currentUser,
              customTitle,
              companyName,
              companyAddress,
              logoBytes,
            );
          },
          build: (context) {
            return [
              // Filter Summary
              _buildExpensesFilterSummary(
                startDate,
                endDate,
                selectedAccounts,
                selectedPayees,
                selectedCategories,
                minAmount,
                maxAmount,
                dateFormat,
                currencyFormat,
              ),

              pw.SizedBox(height: 20),

              // Summary Section
              _buildExpensesSummary(
                totalExpenses,
                totalAmount,
                currencyFormat,
              ),

              pw.SizedBox(height: 20),

              // Expenses Table
              _buildExpensesTable(expenses, dateFormat, currencyFormat),
            ];
          },
          footer: (context) {
            return _buildFooter(context);
          },
        ),
      );

      final pdfBytes = await pdf.save();
      log('Expenses PDF generated successfully. Size: ${pdfBytes.length} bytes');
      return pdfBytes;
    } catch (e) {
      log('Error generating Expenses PDF: $e');
      rethrow;
    }
  }

  /// Build PDF Header
  pw.Widget _buildHeader({
    required UserModel currentUser,
    required AccountModel account,
    required DateTime startDate,
    required DateTime endDate,
    String? companyName,
    String? companyAddress,
    String? customTitle,
    Uint8List? logoBytes,
  }) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(width: 2, color: PdfColors.grey300),
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Logo and Company Info Row
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              // Logo section (if provided)
              if (logoBytes != null)
                pw.Container(
                  width: 80,
                  height: 80,
                  child: pw.Image(pw.MemoryImage(logoBytes)),
                ),

              // Company Info
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Text(
                      companyName ?? currentUser.companyName,
                      style: pw.TextStyle(
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.blue800,
                      ),
                    ),
                    if (companyAddress != null) ...[
                      pw.SizedBox(height: 4),
                      pw.Text(
                        companyAddress,
                        style: const pw.TextStyle(
                            fontSize: 10, color: PdfColors.grey600),
                      ),
                    ],
                    pw.SizedBox(height: 4),
                    pw.Text(
                      'Email: ${currentUser.email}',
                      style: const pw.TextStyle(
                          fontSize: 10, color: PdfColors.grey600),
                    ),
                    if (currentUser.phoneNumber.isNotEmpty) ...[
                      pw.Text(
                        'Phone: ${currentUser.phoneNumber}',
                        style: const pw.TextStyle(
                            fontSize: 10, color: PdfColors.grey600),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          pw.SizedBox(height: 20),

          // Report Title
          pw.Center(
            child: pw.Text(
              customTitle ?? 'Account Statement Report',
              style: pw.TextStyle(
                fontSize: 20,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue900,
              ),
            ),
          ),

          pw.SizedBox(height: 15),

          // Account and Date Info
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Account: ${account.name}',
                    style: pw.TextStyle(
                        fontSize: 12, fontWeight: pw.FontWeight.bold),
                  ),
                  pw.Text(
                    'Account Number: ${account.accountNumber}',
                    style: const pw.TextStyle(
                        fontSize: 10, color: PdfColors.grey600),
                  ),
                  if (account.branchCode.isNotEmpty)
                    pw.Text(
                      'Branch: ${account.branchCode}',
                      style: const pw.TextStyle(
                          fontSize: 10, color: PdfColors.grey600),
                    ),
                ],
              ),
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  pw.Text(
                    'Period: ${dateFormat.format(startDate)} - ${dateFormat.format(endDate)}',
                    style: pw.TextStyle(
                        fontSize: 12, fontWeight: pw.FontWeight.bold),
                  ),
                  pw.Text(
                    'Generated: ${dateFormat.format(DateTime.now())}',
                    style: const pw.TextStyle(
                        fontSize: 10, color: PdfColors.grey600),
                  ),
                  pw.Text(
                    'Generated by: ${currentUser.companyName}',
                    style: const pw.TextStyle(
                        fontSize: 10, color: PdfColors.grey600),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build PDF Footer
  pw.Widget _buildFooter(pw.Context context) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 20),
      child: pw.Text(
        'Page ${context.pageNumber} of ${context.pagesCount}',
        style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
      ),
    );
  }

  /// Build Account Summary Section
  pw.Widget _buildAccountSummary({
    required AccountModel account,
    required double totalCredits,
    required double totalDebits,
    required double closingBalance,
    required NumberFormat currencyFormat,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            'Account Summary',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
            children: [
              _buildSummaryItem(
                'Opening Balance',
                currencyFormat.format(account.initialBalance),
                PdfColors.blue600,
              ),
              _buildSummaryItem(
                'Total Credits',
                currencyFormat.format(totalCredits),
                PdfColors.green600,
              ),
              _buildSummaryItem(
                'Total Debits',
                currencyFormat.format(totalDebits),
                PdfColors.red600,
              ),
              _buildSummaryItem(
                'Closing Balance',
                currencyFormat.format(closingBalance),
                closingBalance >= 0 ? PdfColors.green700 : PdfColors.red700,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build Summary Item
  pw.Widget _buildSummaryItem(String label, String value, PdfColor color) {
    return pw.Column(
      children: [
        pw.Text(
          label,
          style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
        ),
        pw.SizedBox(height: 4),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// Build Transactions Table
  pw.Widget _buildTransactionsTable({
    required List<Map<String, dynamic>> transactionsWithBalance,
    required DateFormat dateFormat,
    required NumberFormat currencyFormat,
  }) {
    if (transactionsWithBalance.isEmpty) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(20),
        child: pw.Center(
          child: pw.Text(
            'No transactions found for the selected period.',
            style: const pw.TextStyle(fontSize: 12, color: PdfColors.grey600),
          ),
        ),
      );
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Transaction Details',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FixedColumnWidth(60), // Date
            1: const pw.FlexColumnWidth(2.5), // Description
            2: const pw.FlexColumnWidth(1.5), // Payee/Payer
            3: const pw.FixedColumnWidth(70), // Debit
            4: const pw.FixedColumnWidth(70), // Credit
            5: const pw.FixedColumnWidth(80), // Balance
          },
          children: [
            // Header Row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.blue50),
              children: [
                _buildTableHeader('Date'),
                _buildTableHeader('Description'),
                _buildTableHeader('Payee/Payer'),
                _buildTableHeader('Debit'),
                _buildTableHeader('Credit'),
                _buildTableHeader('Balance'),
              ],
            ),
            // Data Rows
            ...transactionsWithBalance.asMap().entries.map((entry) {
              final index = entry.key;
              final data = entry.value;
              final transaction =
                  data['transaction'] as AccountTransactionModel;
              final runningBalance = data['runningBalance'] as double;
              final isEvenRow = index % 2 == 0;

              // Determine payee/payer based on transaction type
              final isCredit = transaction.amount > 0;
              String payeePayerInfo = '';

              if (isCredit && (transaction.payerName?.isNotEmpty ?? false)) {
                payeePayerInfo = transaction.payerName!;
              } else if (!isCredit &&
                  (transaction.payeeName?.isNotEmpty ?? false)) {
                payeePayerInfo = transaction.payeeName!;
              } else if (transaction.voucherNumber?.isNotEmpty ?? false) {
                payeePayerInfo = 'V: ${transaction.voucherNumber}';
              } else {
                payeePayerInfo = '-';
              }

              return pw.TableRow(
                decoration: pw.BoxDecoration(
                  color: isEvenRow ? PdfColors.white : PdfColors.grey50,
                ),
                children: [
                  _buildTableCell(
                      dateFormat.format(transaction.transactionDate)),
                  _buildTableCell(
                    transaction.description,
                    alignment: pw.Alignment.centerLeft,
                  ),
                  _buildTableCell(
                    payeePayerInfo,
                    alignment: pw.Alignment.centerLeft,
                  ),
                  _buildTableCell(
                    transaction.amount < 0
                        ? currencyFormat.format(transaction.amount.abs())
                        : '-',
                    color: transaction.amount < 0 ? PdfColors.red600 : null,
                  ),
                  _buildTableCell(
                    transaction.amount > 0
                        ? currencyFormat.format(transaction.amount)
                        : '-',
                    color: transaction.amount > 0 ? PdfColors.green600 : null,
                  ),
                  _buildTableCell(
                    currencyFormat.format(runningBalance),
                    color: runningBalance >= 0
                        ? PdfColors.blue600
                        : PdfColors.red600,
                  ),
                ],
              );
            }),
          ],
        ),
      ],
    );
  }

  /// Build Table Header Cell
  pw.Widget _buildTableHeader(String text) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 10,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.blue800,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// Build Table Cell
  pw.Widget _buildTableCell(
    String text, {
    pw.Alignment alignment = pw.Alignment.center,
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 9,
          color: color ?? PdfColors.black,
        ),
        textAlign: alignment == pw.Alignment.centerLeft
            ? pw.TextAlign.left
            : pw.TextAlign.center,
      ),
    );
  }

  /// Build Summary Footer
  pw.Widget _buildSummaryFooter({
    required double totalCredits,
    required double totalDebits,
    required double closingBalance,
    required NumberFormat currencyFormat,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
        children: [
          pw.Column(
            children: [
              pw.Text(
                'Total Credits',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.green700,
                ),
              ),
              pw.Text(
                currencyFormat.format(totalCredits),
                style:
                    const pw.TextStyle(fontSize: 14, color: PdfColors.green700),
              ),
            ],
          ),
          pw.Column(
            children: [
              pw.Text(
                'Total Debits',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.red700,
                ),
              ),
              pw.Text(
                currencyFormat.format(totalDebits),
                style:
                    const pw.TextStyle(fontSize: 14, color: PdfColors.red700),
              ),
            ],
          ),
          pw.Column(
            children: [
              pw.Text(
                'Closing Balance',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
              pw.Text(
                currencyFormat.format(closingBalance),
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: closingBalance >= 0
                      ? PdfColors.green700
                      : PdfColors.red700,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Preview PDF in browser with web-specific handling
  Future<void> previewPDF(Uint8List pdfBytes, String fileName) async {
    try {
      if (kIsWeb) {
        // Web-specific PDF preview
        await _previewPDFWeb(pdfBytes, fileName);
      } else {
        // Mobile/Desktop PDF preview
        await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdfBytes,
          name: fileName,
        );
      }
    } catch (e) {
      log('Error previewing PDF: $e');
      // Fallback for web if printing fails
      if (kIsWeb) {
        await _downloadPDFWeb(pdfBytes, fileName);
      } else {
        rethrow;
      }
    }
  }

  /// Share PDF with web-specific handling
  Future<void> sharePDF(Uint8List pdfBytes, String fileName) async {
    try {
      if (kIsWeb) {
        // Web doesn't support native sharing, so download instead
        await _downloadPDFWeb(pdfBytes, fileName);
      } else {
        // Mobile/Desktop PDF sharing
        await Printing.sharePdf(
          bytes: pdfBytes,
          filename: fileName,
        );
      }
    } catch (e) {
      log('Error sharing PDF: $e');
      // Fallback to download for web
      if (kIsWeb) {
        await _downloadPDFWeb(pdfBytes, fileName);
      } else {
        rethrow;
      }
    }
  }

  /// Web-specific PDF preview using printing package
  Future<void> _previewPDFWeb(Uint8List pdfBytes, String fileName) async {
    try {
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfBytes,
        name: fileName,
        format: PdfPageFormat.a4,
      );
    } catch (e) {
      log('Web PDF preview failed, falling back to download: $e');
      await _downloadPDFWeb(pdfBytes, fileName);
    }
  }

  /// Web-specific PDF download fallback
  Future<void> _downloadPDFWeb(Uint8List pdfBytes, String fileName) async {
    try {
      if (kIsWeb) {
        // Use the printing package's web download functionality
        await Printing.sharePdf(
          bytes: pdfBytes,
          filename: fileName,
        );
        log('PDF downloaded successfully on web: $fileName');
      }
    } catch (e) {
      log('Error downloading PDF on web: $e');
      // If all else fails, just log the error
      // The printing package should handle web downloads automatically
      rethrow;
    }
  }

  /// Build Deposits Header
  pw.Widget _buildDepositsHeader({
    required UserModel currentUser,
    DateTime? startDate,
    DateTime? endDate,
    String? companyName,
    String? companyAddress,
    String? customTitle,
    Uint8List? logoBytes,
  }) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(width: 2, color: PdfColors.grey300),
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    companyName ?? currentUser.companyName,
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.blue800,
                    ),
                  ),
                  if (companyAddress != null) ...[
                    pw.SizedBox(height: 4),
                    pw.Text(
                      companyAddress,
                      style: const pw.TextStyle(
                        fontSize: 12,
                        color: PdfColors.grey600,
                      ),
                    ),
                  ],
                ],
              ),
              if (logoBytes != null)
                pw.Container(
                  width: 80,
                  height: 80,
                  child: pw.Image(pw.MemoryImage(logoBytes)),
                ),
            ],
          ),
          pw.SizedBox(height: 20),
          pw.Center(
            child: pw.Text(
              customTitle ?? 'Deposits Report',
              style: pw.TextStyle(
                fontSize: 20,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.grey800,
              ),
            ),
          ),
          pw.SizedBox(height: 10),
          if (startDate != null && endDate != null)
            pw.Center(
              child: pw.Text(
                'Period: ${dateFormat.format(startDate)} to ${dateFormat.format(endDate)}',
                style: const pw.TextStyle(
                  fontSize: 12,
                  color: PdfColors.grey600,
                ),
              ),
            ),
          pw.SizedBox(height: 10),
          pw.Center(
            child: pw.Text(
              'Generated on: ${dateFormat.format(DateTime.now())} at ${DateFormat('HH:mm').format(DateTime.now())}',
              style: const pw.TextStyle(
                fontSize: 10,
                color: PdfColors.grey500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build Deposits Filter Summary
  pw.Widget _buildDepositsFilterSummary({
    List<AccountModel>? selectedAccounts,
    List<PayerModel>? selectedPayers,
    List<DepositCategoryModel>? selectedCategories,
    DateTime? startDate,
    DateTime? endDate,
    double? minAmount,
    double? maxAmount,
  }) {
    final hasFilters = (selectedAccounts?.isNotEmpty ?? false) ||
        (selectedPayers?.isNotEmpty ?? false) ||
        (selectedCategories?.isNotEmpty ?? false) ||
        startDate != null ||
        endDate != null ||
        minAmount != null ||
        maxAmount != null;

    if (!hasFilters) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(12),
        decoration: pw.BoxDecoration(
          color: PdfColors.grey100,
          borderRadius: pw.BorderRadius.circular(8),
        ),
        child: pw.Text(
          'Filters: All deposits included (no filters applied)',
          style: const pw.TextStyle(fontSize: 12, color: PdfColors.grey600),
        ),
      );
    }

    final currencyFormat =
        NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2);
    final dateFormat = DateFormat('dd/MM/yyyy');

    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Applied Filters:',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey800,
            ),
          ),
          pw.SizedBox(height: 8),
          if (selectedAccounts?.isNotEmpty ?? false)
            pw.Text(
              'Accounts: ${selectedAccounts!.map((a) => a.name).join(', ')}',
              style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
            ),
          if (selectedPayers?.isNotEmpty ?? false)
            pw.Text(
              'Payers: ${selectedPayers!.map((p) => p.name).join(', ')}',
              style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
            ),
          if (selectedCategories?.isNotEmpty ?? false)
            pw.Text(
              'Categories: ${selectedCategories!.map((c) => c.name).join(', ')}',
              style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
            ),
          if (startDate != null && endDate != null)
            pw.Text(
              'Date Range: ${dateFormat.format(startDate)} to ${dateFormat.format(endDate)}',
              style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
            ),
          if (minAmount != null || maxAmount != null)
            pw.Text(
              'Amount Range: ${minAmount != null ? currencyFormat.format(minAmount) : 'No minimum'} to ${maxAmount != null ? currencyFormat.format(maxAmount) : 'No maximum'}',
              style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
            ),
        ],
      ),
    );
  }

  /// Build Deposits Summary
  pw.Widget _buildDepositsSummary({
    required double totalAmount,
    required int totalCount,
    required Map<String, List<DepositModel>> accountGroups,
    required Map<String, List<DepositModel>> categoryGroups,
    required NumberFormat currencyFormat,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Summary',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 12),

          // Overall totals
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'Total Deposits:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                '$totalCount transactions',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 4),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'Total Amount:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                currencyFormat.format(totalAmount),
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.green700,
                ),
              ),
            ],
          ),

          pw.SizedBox(height: 16),

          // Account breakdown
          if (accountGroups.isNotEmpty) ...[
            pw.Text(
              'Breakdown by Account:',
              style: pw.TextStyle(
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.grey700,
              ),
            ),
            pw.SizedBox(height: 8),
            ...accountGroups.entries.map((entry) {
              final accountTotal =
                  entry.value.fold(0.0, (sum, d) => sum + d.amount);
              return pw.Padding(
                padding: const pw.EdgeInsets.only(bottom: 4),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Expanded(
                      child: pw.Text(
                        '${entry.key}: ${entry.value.length} deposits',
                        style: const pw.TextStyle(fontSize: 10),
                      ),
                    ),
                    pw.Text(
                      currencyFormat.format(accountTotal),
                      style: const pw.TextStyle(fontSize: 10),
                    ),
                  ],
                ),
              );
            }),
            pw.SizedBox(height: 12),
          ],

          // Category breakdown
          if (categoryGroups.isNotEmpty) ...[
            pw.Text(
              'Breakdown by Category:',
              style: pw.TextStyle(
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.grey700,
              ),
            ),
            pw.SizedBox(height: 8),
            ...categoryGroups.entries.map((entry) {
              final categoryTotal =
                  entry.value.fold(0.0, (sum, d) => sum + d.amount);
              return pw.Padding(
                padding: const pw.EdgeInsets.only(bottom: 4),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Expanded(
                      child: pw.Text(
                        '${entry.key}: ${entry.value.length} deposits',
                        style: const pw.TextStyle(fontSize: 10),
                      ),
                    ),
                    pw.Text(
                      currencyFormat.format(categoryTotal),
                      style: const pw.TextStyle(fontSize: 10),
                    ),
                  ],
                ),
              );
            }),
          ],
        ],
      ),
    );
  }

  /// Build Deposits Table
  pw.Widget _buildDepositsTable({
    required List<DepositModel> deposits,
    required DateFormat dateFormat,
    required NumberFormat currencyFormat,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Deposit Details',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.grey800,
          ),
        ),
        pw.SizedBox(height: 12),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(1.5), // Date
            1: const pw.FlexColumnWidth(2.5), // Account
            2: const pw.FlexColumnWidth(2.0), // Payer
            3: const pw.FlexColumnWidth(1.8), // Amount
            4: const pw.FlexColumnWidth(2.0), // Category
            5: const pw.FlexColumnWidth(2.5), // Reference/Notes
          },
          children: [
            // Header row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildDepositsTableCell('Date', isHeader: true),
                _buildDepositsTableCell('Account', isHeader: true),
                _buildDepositsTableCell('Payer', isHeader: true),
                _buildDepositsTableCell('Amount', isHeader: true),
                _buildDepositsTableCell('Category', isHeader: true),
                _buildDepositsTableCell('Reference/Notes', isHeader: true),
              ],
            ),

            // Data rows
            ...deposits.map((deposit) => pw.TableRow(
                  children: [
                    _buildDepositsTableCell(
                        dateFormat.format(deposit.createdAt)),
                    _buildDepositsTableCell(deposit.accountName),
                    _buildDepositsTableCell(deposit.payerName),
                    _buildDepositsTableCell(
                      currencyFormat.format(deposit.amount),
                      textAlign: pw.TextAlign.right,
                    ),
                    _buildDepositsTableCell(deposit.categoryName),
                    _buildDepositsTableCell(
                      deposit.referenceNumber.isNotEmpty
                          ? '${deposit.referenceNumber}${deposit.notes.isNotEmpty ? '\n${deposit.notes}' : ''}'
                          : deposit.notes,
                    ),
                  ],
                )),
          ],
        ),
      ],
    );
  }

  /// Build deposits table cell
  pw.Widget _buildDepositsTableCell(
    String text, {
    bool isHeader = false,
    pw.TextAlign textAlign = pw.TextAlign.left,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: isHeader ? PdfColors.grey800 : PdfColors.grey700,
        ),
        textAlign: textAlign,
      ),
    );
  }

  /// Build Expenses Header
  pw.Widget _buildExpensesHeader(
    pw.Context context,
    UserModel currentUser,
    String? customTitle,
    String? companyName,
    String? companyAddress,
    Uint8List? logoBytes,
  ) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Logo (if provided)
          if (logoBytes != null) ...[
            pw.Container(
              width: 60,
              height: 60,
              child: pw.Image(pw.MemoryImage(logoBytes)),
            ),
            pw.SizedBox(width: 20),
          ],

          // Title and Date
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  customTitle ?? 'Expenses Report',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue800,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  'Generated on: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
                  style: const pw.TextStyle(
                    fontSize: 12,
                    color: PdfColors.grey600,
                  ),
                ),
              ],
            ),
          ),

          // Company Info
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  companyName ?? currentUser.companyName,
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue800,
                  ),
                ),
                if (companyAddress != null) ...[
                  pw.SizedBox(height: 4),
                  pw.Text(
                    companyAddress,
                    style: const pw.TextStyle(
                        fontSize: 10, color: PdfColors.grey600),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build Expenses Filter Summary
  pw.Widget _buildExpensesFilterSummary(
    DateTime? startDate,
    DateTime? endDate,
    List<AccountModel>? selectedAccounts,
    List<PayeeModel>? selectedPayees,
    List<ExpenseCategoryModel>? selectedCategories,
    double? minAmount,
    double? maxAmount,
    DateFormat dateFormat,
    NumberFormat currencyFormat,
  ) {
    final filters = <String>[];

    // Date range
    if (startDate != null && endDate != null) {
      filters.add(
          'Date: ${dateFormat.format(startDate)} to ${dateFormat.format(endDate)}');
    }

    // Accounts
    if (selectedAccounts != null && selectedAccounts.isNotEmpty) {
      filters
          .add('Accounts: ${selectedAccounts.map((a) => a.name).join(', ')}');
    }

    // Payees
    if (selectedPayees != null && selectedPayees.isNotEmpty) {
      filters.add('Payees: ${selectedPayees.map((p) => p.name).join(', ')}');
    }

    // Categories
    if (selectedCategories != null && selectedCategories.isNotEmpty) {
      filters.add(
          'Categories: ${selectedCategories.map((c) => c.name).join(', ')}');
    }

    // Amount range
    if (minAmount != null || maxAmount != null) {
      String amountFilter = 'Amount: ';
      if (minAmount != null && maxAmount != null) {
        amountFilter +=
            '${currencyFormat.format(minAmount)} to ${currencyFormat.format(maxAmount)}';
      } else if (minAmount != null) {
        amountFilter += 'Min ${currencyFormat.format(minAmount)}';
      } else if (maxAmount != null) {
        amountFilter += 'Max ${currencyFormat.format(maxAmount)}';
      }
      filters.add(amountFilter);
    }

    if (filters.isEmpty) {
      filters.add('No filters applied - All expenses');
    }

    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Applied Filters:',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey800,
            ),
          ),
          pw.SizedBox(height: 4),
          ...filters.map((filter) => pw.Padding(
                padding: const pw.EdgeInsets.only(bottom: 2),
                child: pw.Text(
                  '• $filter',
                  style: const pw.TextStyle(
                    fontSize: 10,
                    color: PdfColors.grey700,
                  ),
                ),
              )),
        ],
      ),
    );
  }

  /// Build Expenses Summary
  pw.Widget _buildExpensesSummary(
    int totalExpenses,
    double totalAmount,
    NumberFormat currencyFormat,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
        children: [
          pw.Column(
            children: [
              pw.Text(
                'Total Expenses',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                totalExpenses.toString(),
                style: pw.TextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue900,
                ),
              ),
            ],
          ),
          pw.Container(
            width: 1,
            height: 40,
            color: PdfColors.blue200,
          ),
          pw.Column(
            children: [
              pw.Text(
                'Total Amount',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                currencyFormat.format(totalAmount),
                style: pw.TextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.red700,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build Expenses Table
  pw.Widget _buildExpensesTable(
    List<ExpenseModel> expenses,
    DateFormat dateFormat,
    NumberFormat currencyFormat,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FixedColumnWidth(80), // Date
        1: const pw.FlexColumnWidth(2), // Title
        2: const pw.FlexColumnWidth(1.5), // Account
        3: const pw.FlexColumnWidth(1.5), // Payee
        4: const pw.FlexColumnWidth(1.5), // Category
        5: const pw.FixedColumnWidth(80), // Amount
        6: const pw.FlexColumnWidth(1), // Reference
      },
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildDepositsTableCell('Date', isHeader: true),
            _buildDepositsTableCell('Title', isHeader: true),
            _buildDepositsTableCell('Account', isHeader: true),
            _buildDepositsTableCell('Payee', isHeader: true),
            _buildDepositsTableCell('Category', isHeader: true),
            _buildDepositsTableCell('Amount',
                isHeader: true, textAlign: pw.TextAlign.right),
            _buildDepositsTableCell('Reference', isHeader: true),
          ],
        ),
        // Data rows
        ...expenses.map((expense) {
          return pw.TableRow(
            children: [
              _buildDepositsTableCell(dateFormat.format(expense.createdAt)),
              _buildDepositsTableCell(
                  expense.title.isNotEmpty ? expense.title : 'N/A'),
              _buildDepositsTableCell(
                  expense.accountName.isNotEmpty ? expense.accountName : 'N/A'),
              _buildDepositsTableCell(
                  expense.payeeName.isNotEmpty ? expense.payeeName : 'N/A'),
              _buildDepositsTableCell(expense.categoryName.isNotEmpty
                  ? expense.categoryName
                  : 'N/A'),
              _buildDepositsTableCell(
                currencyFormat.format(expense.amount),
                textAlign: pw.TextAlign.right,
              ),
              _buildDepositsTableCell(expense.referenceNumber.isNotEmpty
                  ? expense.referenceNumber
                  : 'N/A'),
            ],
          );
        }),
      ],
    );
  }

  /// Generate Bill PDF matching Excel template format
  Future<Uint8List> generateBillPDF({
    required BillModel bill,
    required List<InvoiceModel> invoices,
    required UserModel currentUser,
    String? companyName,
    String? companyAddress,
    String? customTitle,
    Uint8List? logoBytes,
    List<String> selectedTaxAuthorities = const [],
  }) async {
    try {
      log('Generating Bill PDF for bill: ${bill.billNumber}');

      final pdf = pw.Document();
      final dateFormat = DateFormat('dd/MM/yyyy');
      final currencyFormat =
          NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2);

      // Calculate totals
      double totalTons = 0.0;
      double total100Amount = 0.0;
      double total80Amount = 0.0;

      // Calculate amounts for each invoice
      final invoiceData = <Map<String, dynamic>>[];
      for (int i = 0; i < invoices.length; i++) {
        final invoice = invoices[i];

        // Calculate total weight in tons
        final totalWeightTons =
            (invoice.numberOfBags * invoice.weightPerBag) / 1000;

        // Format weight with dynamic decimal places
        final totalWeightFormatted = totalWeightTons % 1 == 0
            ? totalWeightTons.toInt().toString()
            : totalWeightTons
                .toStringAsFixed(2)
                .replaceAll(RegExp(r'\.?0+$'), '');

        // For now, use a default rate calculation - this should be replaced with actual slab calculation
        final hmtRate = 150.0; // Default rate
        final amount100 =
            MonetaryRounding.roundHalfUp(totalWeightTons * hmtRate);
        final amount80 = MonetaryRounding.roundHalfUp(amount100 * 0.80);
        final netAmount = amount100;

        // Add to totals
        totalTons += totalWeightTons;
        total100Amount += amount100;
        total80Amount += amount80;

        invoiceData.add({
          'sn': (i + 1).toString(),
          'liftingDate': invoice.orderDate != null
              ? dateFormat.format(invoice.orderDate!)
              : '',
          'truckNo': invoice.truckNumber,
          'biltyNo': invoice.biltyNumber,
          'conveyNoteNo': invoice.conveyNoteNumber,
          'productName': invoice.productName,
          'tasNo': invoice.tasNumber,
          'destination': invoice.stationName,
          'numberOfBags': invoice.numberOfBags.toString(),
          'weight': totalWeightFormatted,
          'km': invoice.distanceInKilometers.toString(),
          'district': invoice.districtName,
          'hmtRate': hmtRate.toStringAsFixed(2),
          'amount100': amount100.toStringAsFixed(2),
          'amount80': amount80.toStringAsFixed(2),
          'netAmount': netAmount.toStringAsFixed(2),
        });
      }

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          // 3.5 inches top margin = 252 points (72 points per inch)
          margin: const pw.EdgeInsets.only(
            top: 252, // 3.5 inches
            left: 32,
            right: 32,
            bottom: 32,
          ),
          header: (context) => _buildBillHeader(
            context,
            bill,
            invoices.length,
            currentUser,
            customTitle,
            companyName,
            companyAddress,
            logoBytes,
          ),
          build: (context) => [
            _buildBillTable(invoiceData),
            pw.SizedBox(height: 20),
            _buildBillSummary(
              totalTons,
              total100Amount,
              total80Amount,
              selectedTaxAuthorities,
              currencyFormat,
            ),
          ],
          footer: (context) => _buildFooter(context),
        ),
      );

      return await pdf.save();
    } catch (e) {
      log('Error generating Bill PDF: $e');
      rethrow;
    }
  }

  /// Build bill header with invoice count and bill number
  pw.Widget _buildBillHeader(
    pw.Context context,
    BillModel bill,
    int invoiceCount,
    UserModel currentUser,
    String? customTitle,
    String? companyName,
    String? companyAddress,
    Uint8List? logoBytes,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Company info and logo section
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  companyName ?? 'Company Name',
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                if (companyAddress != null)
                  pw.Text(
                    companyAddress,
                    style: const pw.TextStyle(fontSize: 12),
                  ),
              ],
            ),
            if (logoBytes != null)
              pw.Image(
                pw.MemoryImage(logoBytes),
                width: 60,
                height: 60,
              ),
          ],
        ),
        pw.SizedBox(height: 20),

        // Bill number and invoice count
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'BILL # ${bill.billNumber}',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
            pw.Text(
              'Total Invoices: $invoiceCount',
              style: pw.TextStyle(
                fontSize: 14,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 10),
      ],
    );
  }

  /// Build bill table matching Excel template format
  pw.Widget _buildBillTable(List<Map<String, dynamic>> invoiceData) {
    return pw.Table(
      border: pw.TableBorder.all(
        color: PdfColors.black,
        width: 1,
      ),
      columnWidths: {
        0: const pw.FlexColumnWidth(0.8), // SN.
        1: const pw.FlexColumnWidth(1.2), // Lifting Date
        2: const pw.FlexColumnWidth(1.2), // Truck No
        3: const pw.FlexColumnWidth(1.2), // Bilty No
        4: const pw.FlexColumnWidth(1.5), // Convey Note Number
        5: const pw.FlexColumnWidth(1.3), // Product Name
        6: const pw.FlexColumnWidth(1.3), // Product TAS NO
        7: const pw.FlexColumnWidth(1.3), // Destination
        8: const pw.FlexColumnWidth(1.0), // No of Bags
        9: const pw.FlexColumnWidth(1.0), // Weight
        10: const pw.FlexColumnWidth(0.8), // KM
        11: const pw.FlexColumnWidth(1.2), // District
        12: const pw.FlexColumnWidth(1.5), // HMT Rates
        13: const pw.FlexColumnWidth(1.2), // 100% Amount
        14: const pw.FlexColumnWidth(1.2), // 80% Amount
        15: const pw.FlexColumnWidth(1.2), // Net Amount
      },
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(
            color: PdfColors.grey200,
          ),
          children: [
            _buildBillTableCell('SN.', isHeader: true),
            _buildBillTableCell('Lifting\ndate', isHeader: true),
            _buildBillTableCell('Truck\nNo', isHeader: true),
            _buildBillTableCell('Bilty\nNo', isHeader: true),
            _buildBillTableCell('Convey\nNote No', isHeader: true),
            _buildBillTableCell('Product', isHeader: true),
            _buildBillTableCell('TAS\nNO', isHeader: true),
            _buildBillTableCell('Destination', isHeader: true),
            _buildBillTableCell('No of\nBags', isHeader: true),
            _buildBillTableCell('Weight', isHeader: true),
            _buildBillTableCell('KM', isHeader: true),
            _buildBillTableCell('District', isHeader: true),
            _buildBillTableCell('HMT\nRate/Non Fuel\nInc WHT', isHeader: true),
            _buildBillTableCell('100%\nAmount', isHeader: true),
            _buildBillTableCell('80%\nAmount', isHeader: true),
            _buildBillTableCell('Net\nAmount', isHeader: true),
          ],
        ),
        // Data rows
        ...invoiceData.map((invoice) => pw.TableRow(
              children: [
                _buildBillTableCell(invoice['sn']),
                _buildBillTableCell(invoice['liftingDate']),
                _buildBillTableCell(invoice['truckNo']),
                _buildBillTableCell(invoice['biltyNo']),
                _buildBillTableCell(invoice['conveyNoteNo']),
                _buildBillTableCell(invoice['productName']),
                _buildBillTableCell(invoice['tasNo']),
                _buildBillTableCell(invoice['destination']),
                _buildBillTableCell(invoice['numberOfBags']),
                _buildBillTableCell(invoice['weight']),
                _buildBillTableCell(invoice['km']),
                _buildBillTableCell(invoice['district']),
                _buildBillTableCell(invoice['hmtRate']),
                _buildBillTableCell(invoice['amount100']),
                _buildBillTableCell(invoice['amount80']),
                _buildBillTableCell(invoice['netAmount']),
              ],
            )),
      ],
    );
  }

  /// Build table cell with consistent styling for bill tables
  pw.Widget _buildBillTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// Build bill summary section matching Excel template
  pw.Widget _buildBillSummary(
    double totalTons,
    double total100Amount,
    double total80Amount,
    List<String> selectedTaxAuthorities,
    NumberFormat currencyFormat,
  ) {
    final roundedTotalTons = MonetaryRounding.roundHalfUp(totalTons);
    final roundedTotal100Amount = MonetaryRounding.roundHalfUp(total100Amount);
    final roundedTotal80Amount = MonetaryRounding.roundHalfUp(total80Amount);

    final summaryRows = <pw.TableRow>[];

    // Base summary row
    summaryRows.add(
      pw.TableRow(
        children: [
          pw.Container(), // Empty cells for alignment
          pw.Container(),
          pw.Container(),
          pw.Container(),
          pw.Container(),
          pw.Container(),
          pw.Container(),
          pw.Container(),
          pw.Container(),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'Tons',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              roundedTotalTons.toStringAsFixed(2),
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'Bill Amount Exclusive of GST (100%)',
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Container(),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              roundedTotal100Amount.toStringAsFixed(2),
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              roundedTotal80Amount.toStringAsFixed(2),
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              roundedTotal100Amount.toStringAsFixed(2),
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              textAlign: pw.TextAlign.center,
            ),
          ),
        ],
      ),
    );

    // Add tax authority rows if any are selected
    if (selectedTaxAuthorities.isNotEmpty) {
      for (String authority in selectedTaxAuthorities) {
        final taxAmount = MonetaryRounding.roundHalfUp(
            roundedTotal100Amount * 0.15); // 15% tax
        summaryRows.add(
          pw.TableRow(
            children: [
              ...List.generate(11, (index) => pw.Container()), // Empty cells
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  '$authority Sales Tax (15%)',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  textAlign: pw.TextAlign.center,
                ),
              ),
              pw.Container(),
              pw.Container(),
              pw.Container(),
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  taxAmount.toStringAsFixed(2),
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ],
          ),
        );
      }

      // Add total amount with tax row
      final totalTaxAmount = MonetaryRounding.roundHalfUp(
          roundedTotal100Amount * 0.15 * selectedTaxAuthorities.length);
      final totalWithTax =
          MonetaryRounding.roundHalfUp(roundedTotal100Amount + totalTaxAmount);

      summaryRows.add(
        pw.TableRow(
          children: [
            ...List.generate(11, (index) => pw.Container()), // Empty cells
            pw.Container(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'TOTAL BILL AMOUNT INCLUDING GST',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Container(),
            pw.Container(),
            pw.Container(),
            pw.Container(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                totalWithTax.toStringAsFixed(2),
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }

    return pw.Table(
      border: pw.TableBorder.all(
        color: PdfColors.black,
        width: 1,
      ),
      columnWidths: {
        0: const pw.FlexColumnWidth(0.8),
        1: const pw.FlexColumnWidth(1.2),
        2: const pw.FlexColumnWidth(1.2),
        3: const pw.FlexColumnWidth(1.2),
        4: const pw.FlexColumnWidth(1.5),
        5: const pw.FlexColumnWidth(1.3),
        6: const pw.FlexColumnWidth(1.3),
        7: const pw.FlexColumnWidth(1.3),
        8: const pw.FlexColumnWidth(1.0),
        9: const pw.FlexColumnWidth(1.0),
        10: const pw.FlexColumnWidth(0.8),
        11: const pw.FlexColumnWidth(1.2),
        12: const pw.FlexColumnWidth(1.5),
        13: const pw.FlexColumnWidth(1.2),
        14: const pw.FlexColumnWidth(1.2),
        15: const pw.FlexColumnWidth(1.2),
      },
      children: summaryRows,
    );
  }
}
