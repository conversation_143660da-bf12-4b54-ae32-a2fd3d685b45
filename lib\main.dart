import 'dart:developer';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/core/router/app_routes.dart';
import 'package:logestics/core/router/route_guard.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import 'package:logestics/firebase_options.dart';
import 'package:provider/provider.dart';

import 'features/home/<USER>/theme.dart';
import 'core/utils/widgets/scrolling.dart';

late ColorNotifier notifier;
void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    await AppDependencyInjection().init();

    runApp(const MyApp());
  } catch (e) {
    log('Error during initialization: $e');

    // Run app with error handling
    runApp(MaterialApp(
      title: 'Logistics App - Initialization Error',
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Initialization Error'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'App Initialization Failed',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Error: $e',
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // Restart the app
                  main();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    ));
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => ColorNotifier(),
        ),
      ],
      child: Builder(builder: (context) {
        var notifier = Provider.of<ColorNotifier>(context, listen: true);
        return GetMaterialApp(
          initialRoute: AppRoutes.root,
          initialBinding: AppBindings(),
          theme: ThemeData(
            appBarTheme: AppBarTheme(
              scrolledUnderElevation: 0,
              elevation: 0,
              iconTheme: IconThemeData(color: notifier.text),
              backgroundColor: AppColors.primary,
            ),
            scaffoldBackgroundColor: AppColors.surface,
            dialogTheme: DialogThemeData(
              backgroundColor: AppColors.surface,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            primaryColor: AppColors.primary,
            fontFamily: "Outfit",
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            inputDecorationTheme: InputDecorationTheme(
              filled: true,
              fillColor: AppColors.surface,
              border: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.onSurfaceVariant),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.onSurfaceVariant),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.primary),
              ),
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.onPrimary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            cardTheme: CardThemeData(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          // darkTheme: AppTheme.darkTheme(MediaQuery.of(context).devicePixelRatio),
          // themeMode: ThemeMode.system,
          getPages: AppRoutes.getPages,
          unknownRoute: GetPage(
            name: '/not-found',
            page: () => const UnknownRoutePage(),
          ),
          defaultTransition: Transition.fade,
          debugShowCheckedModeBanner: false,
          scrollBehavior: MyCustomScrollerBehavior(),
          builder: (context, child) {
            return child ?? SizedBox();
          },
        );
      }),
    );
  }
}
