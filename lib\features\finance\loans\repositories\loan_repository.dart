import 'package:either_dart/either.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/firebase_service/finance/loan_firebase_service.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/core/services/loan_accounting_hook_service.dart';
import 'package:logestics/core/services/automatic_journal_entry_service.dart';
import 'package:logestics/firebase_service/accounting/journal_entry_firebase_service.dart';
import 'package:logestics/firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import 'dart:developer' as dev;

abstract class LoanRepository {
  // Request a loan
  Future<Either<FailureObj, SuccessObj>> requestLoan(LoanModel loan);

  // Get all loan requests made to current user
  Future<Either<FailureObj, List<LoanModel>>> getIncomingLoanRequests();

  // Get all loan requests made by current user
  Future<Either<FailureObj, List<LoanModel>>> getOutgoingLoanRequests();

  // Approve a loan request
  Future<Either<FailureObj, SuccessObj>> approveLoanRequest(
      String loanId, String fromAccountId);

  // Reject a loan request
  Future<Either<FailureObj, SuccessObj>> rejectLoanRequest(
      String loanId, String reason);

  // Repay a loan
  Future<Either<FailureObj, SuccessObj>> repayLoan(
      String loanId, String fromAccountId);

  // Get all active loans (approved but not repaid)
  Future<Either<FailureObj, List<LoanModel>>> getActiveLoans();

  // Get loan history (all loans)
  Future<Either<FailureObj, List<LoanModel>>> getLoanHistory();

  // Real-time streams
  Stream<List<LoanModel>> listenToIncomingLoanRequests();
  Stream<List<LoanModel>> listenToOutgoingLoanRequests();
  Stream<List<LoanModel>> listenToActiveLoans();
  Stream<List<LoanModel>> listenToLoanHistory();
}

class LoanRepositoryImpl implements LoanRepository {
  final LoanFirebaseService _loanFirebaseService;
  final LoanAccountingHookService _hookService;
  late final AutomaticJournalEntryService _automaticJournalService;
  late final JournalEntryFirebaseService _journalEntryService;

  LoanRepositoryImpl(this._loanFirebaseService)
      : _hookService = LoanAccountingHookService() {
    // Initialize services for manual loan request journal entries
    _automaticJournalService =
        AutomaticJournalEntryService(ChartOfAccountsFirebaseService());
    _journalEntryService = JournalEntryFirebaseService();
  }

  @override
  Future<Either<FailureObj, SuccessObj>> requestLoan(LoanModel loan) async {
    // Create the loan request first
    final result = await _loanFirebaseService.requestLoan(loan);

    // If loan creation was successful and this is a manual loan request (not voucher-based),
    // create journal entries immediately
    return result.fold(
      (failure) => Left(failure),
      (success) async {
        // Check if this is a manual loan request (no voucherPaymentId)
        final isManualLoanRequest =
            loan.voucherPaymentId == null || loan.voucherPaymentId!.isEmpty;

        if (isManualLoanRequest) {
          // Create journal entries for manual loan request
          await _createManualLoanRequestJournalEntries(loan);
        }

        return Right(success);
      },
    );
  }

  @override
  Future<Either<FailureObj, List<LoanModel>>> getIncomingLoanRequests() {
    return _loanFirebaseService.getIncomingLoanRequests();
  }

  @override
  Future<Either<FailureObj, List<LoanModel>>> getOutgoingLoanRequests() {
    return _loanFirebaseService.getOutgoingLoanRequests();
  }

  @override
  Future<Either<FailureObj, SuccessObj>> approveLoanRequest(
      String loanId, String fromAccountId) async {
    final result =
        await _loanFirebaseService.approveLoanRequest(loanId, fromAccountId);

    // If approval was successful, trigger accounting hook
    result.fold(
      (failure) {
        // Do nothing on failure
      },
      (success) async {
        // Get the updated loan and trigger accounting hook
        final loan = await _loanFirebaseService.getLoanById(loanId);
        if (loan != null) {
          await _hookService.onLoanApproved(loan);
        }
      },
    );

    return result;
  }

  @override
  Future<Either<FailureObj, SuccessObj>> rejectLoanRequest(
      String loanId, String reason) async {
    // Get the loan before rejection to check if it had journal entries
    final loan = await _loanFirebaseService.getLoanById(loanId);

    final result = await _loanFirebaseService.rejectLoanRequest(loanId, reason);

    // If rejection was successful and loan existed, trigger cancellation hook
    result.fold(
      (failure) {
        // Do nothing on failure
      },
      (success) async {
        if (loan != null) {
          await _hookService.onLoanCancelled(loan);
        }
      },
    );

    return result;
  }

  @override
  Future<Either<FailureObj, SuccessObj>> repayLoan(
      String loanId, String fromAccountId) async {
    final result = await _loanFirebaseService.repayLoan(loanId, fromAccountId);

    // If repayment was successful, trigger accounting hook
    result.fold(
      (failure) {
        // Do nothing on failure
      },
      (success) async {
        // Get the updated loan and trigger accounting hook
        final loan = await _loanFirebaseService.getLoanById(loanId);
        if (loan != null) {
          await _hookService.onLoanRepaid(loan);
        }
      },
    );

    return result;
  }

  @override
  Future<Either<FailureObj, List<LoanModel>>> getActiveLoans() {
    return _loanFirebaseService.getActiveLoans();
  }

  @override
  Future<Either<FailureObj, List<LoanModel>>> getLoanHistory() {
    return _loanFirebaseService.getLoanHistory();
  }

  @override
  Stream<List<LoanModel>> listenToIncomingLoanRequests() {
    return _loanFirebaseService.listenToIncomingLoanRequests();
  }

  @override
  Stream<List<LoanModel>> listenToOutgoingLoanRequests() {
    return _loanFirebaseService.listenToOutgoingLoanRequests();
  }

  @override
  Stream<List<LoanModel>> listenToActiveLoans() {
    return _loanFirebaseService.listenToActiveLoans();
  }

  @override
  Stream<List<LoanModel>> listenToLoanHistory() {
    return _loanFirebaseService.listenToLoanHistory();
  }

  /// Create journal entries for manual loan requests
  /// This creates immediate journal entries with debit to receiving account and credit to loan payable
  Future<void> _createManualLoanRequestJournalEntries(LoanModel loan) async {
    try {
      dev.log(
          '🔄 Creating journal entries for manual loan request: ${loan.id}');
      dev.log(
          '📝 Loan details: Amount=${loan.amount}, To Account=${loan.toAccountName} (${loan.toAccountId})');

      // Generate journal entry for manual loan request
      // This creates a "loan receipt" entry from the borrower's perspective
      final journalEntries =
          await _automaticJournalService.generateLoanJournalEntries(
        loan: loan,
        transactionType:
            'disbursement', // Use disbursement to create proper debit/credit entries
        uid: loan.uid,
        createdBy: 'system',
      );

      if (journalEntries.isEmpty) {
        dev.log(
            '⚠️ No journal entries generated for manual loan request: ${loan.id}');
        return;
      }

      // Create each journal entry
      for (final journalEntry in journalEntries) {
        try {
          // Update the journal entry to reflect it's for a manual loan request
          final manualLoanEntry = journalEntry.copyWith(
            description: 'Manual loan request - ${journalEntry.description}',
            sourceTransactionType: 'manual_loan_request',
            entryDate: loan.requestDate, // Use loan request date
          );

          await _journalEntryService.createJournalEntry(manualLoanEntry);
          dev.log(
              '✅ Created journal entry for manual loan request: ${manualLoanEntry.id}');
        } catch (e) {
          dev.log(
              '❌ Failed to create journal entry for manual loan request: $e');
        }
      }

      dev.log(
          '✅ Completed journal entry creation for manual loan request: ${loan.id}');
    } catch (e) {
      dev.log(
          '❌ Error creating journal entries for manual loan request ${loan.id}: $e');
      // Don't throw the error - we don't want to fail the loan creation if journal entries fail
    }
  }
}
