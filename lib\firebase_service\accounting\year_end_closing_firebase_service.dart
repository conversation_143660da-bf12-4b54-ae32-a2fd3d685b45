import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/year_end_closing_model.dart';
import 'package:logestics/models/finance/fiscal_period_model.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/models/finance/journal_entry_model.dart';

/// Firebase service for year-end closing operations
class YearEndClosingFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String get _uid => _auth.currentUser?.uid ?? '';

  /// Start year-end closing process
  Future<YearEndClosingModel> startYearEndClosing({
    required FiscalYearModel fiscalYear,
    required String startedBy,
    String? notes,
  }) async {
    log('Starting year-end closing for fiscal year: ${fiscalYear.yearName}');

    try {
      // Create year-end closing record
      final closingRef =
          _firestore.collection(AppCollection.yearEndClosingsCollection).doc();

      final closing = YearEndClosingModel(
        id: closingRef.id,
        fiscalYearId: fiscalYear.id,
        fiscalYearName: fiscalYear.yearName,
        fiscalYearEndDate: fiscalYear.endDate,
        status: YearEndClosingStatus.inProgress,
        startedAt: DateTime.now(),
        startedBy: startedBy,
        closingJournalEntryIds: [],
        totalRevenuesClosed: 0.0,
        totalExpensesClosed: 0.0,
        netIncomeTransferred: 0.0,
        notes: notes,
        createdAt: DateTime.now(),
        uid: _uid,
      );

      await closingRef.set(closing.toJson());
      log('Year-end closing record created: ${closing.id}');

      return closing;
    } catch (e) {
      log('Error starting year-end closing: $e');
      rethrow;
    }
  }

  /// Get revenue accounts with balances for closing
  Future<List<ChartOfAccountsModel>> getRevenueAccountsForClosing() async {
    log('Fetching revenue accounts for year-end closing');

    try {
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('category', isEqualTo: AccountCategory.revenue.name)
          .where('isActive', isEqualTo: true)
          .get();

      final accounts = snapshot.docs
          .map((doc) => ChartOfAccountsModel.fromJson(doc.data()))
          .where((account) =>
              account.balance != 0.0) // Only accounts with balances
          .toList();

      log('Found ${accounts.length} revenue accounts with balances');
      return accounts;
    } catch (e) {
      log('Error fetching revenue accounts: $e');
      rethrow;
    }
  }

  /// Get expense accounts with balances for closing
  Future<List<ChartOfAccountsModel>> getExpenseAccountsForClosing() async {
    log('Fetching expense accounts for year-end closing');

    try {
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('category', isEqualTo: AccountCategory.expenses.name)
          .where('isActive', isEqualTo: true)
          .get();

      final accounts = snapshot.docs
          .map((doc) => ChartOfAccountsModel.fromJson(doc.data()))
          .where((account) =>
              account.balance != 0.0) // Only accounts with balances
          .toList();

      log('Found ${accounts.length} expense accounts with balances');
      return accounts;
    } catch (e) {
      log('Error fetching expense accounts: $e');
      rethrow;
    }
  }

  /// Get or create retained earnings account
  Future<ChartOfAccountsModel> getOrCreateRetainedEarningsAccount() async {
    log('Getting or creating retained earnings account');

    try {
      // First, try to find existing retained earnings account
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('accountType', isEqualTo: AccountType.retainedEarnings.name)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        final account =
            ChartOfAccountsModel.fromJson(snapshot.docs.first.data());
        log('Found existing retained earnings account: ${account.accountName}');
        return account;
      }

      // Create new retained earnings account if not found
      log('Creating new retained earnings account');
      final accountRef =
          _firestore.collection(AppCollection.chartOfAccountsCollection).doc();

      final retainedEarningsAccount = ChartOfAccountsModel(
        id: accountRef.id,
        accountNumber: '3100',
        accountName: 'Retained Earnings',
        description: 'Accumulated profits retained in the business',
        category: AccountCategory.equity,
        accountType: AccountType.retainedEarnings,
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        uid: _uid,
      );

      await accountRef.set(retainedEarningsAccount.toJson());
      log('Created retained earnings account: ${retainedEarningsAccount.id}');

      return retainedEarningsAccount;
    } catch (e) {
      log('Error getting/creating retained earnings account: $e');
      rethrow;
    }
  }

  /// Create closing journal entry for revenue accounts
  Future<JournalEntryModel> createRevenueClosingEntry({
    required List<ChartOfAccountsModel> revenueAccounts,
    required String createdBy,
    required String closingId,
  }) async {
    log('Creating revenue closing journal entry');

    try {
      final entryRef =
          _firestore.collection(AppCollection.journalEntriesCollection).doc();

      // Calculate total revenues
      final totalRevenues = revenueAccounts.fold<double>(
        0.0,
        (total, account) => total + account.balance,
      );

      // Create journal entry lines
      final lines = <JournalEntryLineModel>[];

      // Debit each revenue account to close it
      for (final account in revenueAccounts) {
        lines.add(JournalEntryLineModel(
          id: '',
          journalEntryId: entryRef.id,
          accountId: account.id,
          accountNumber: account.accountNumber,
          accountName: account.accountName,
          description: 'Year-end closing - ${account.accountName}',
          debitAmount: account.balance,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ));
      }

      // Credit Income Summary account (temporary account for closing)
      lines.add(JournalEntryLineModel(
        id: '',
        journalEntryId: entryRef.id,
        accountId: 'income_summary', // Temporary account
        accountNumber: '3900',
        accountName: 'Income Summary',
        description: 'Year-end closing - Revenue accounts closed',
        debitAmount: 0.0,
        creditAmount: totalRevenues,
        createdAt: DateTime.now(),
      ));

      final journalEntry = JournalEntryModel(
        id: entryRef.id,
        entryNumber: await _generateEntryNumber(),
        entryDate: DateTime.now(),
        description: 'Year-end closing - Revenue accounts',
        entryType: JournalEntryType.closing,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: totalRevenues,
        totalCredits: totalRevenues,
        referenceNumber: 'YEC-REV-$closingId',
        sourceTransactionType: 'year_end_closing',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: _uid,
      );

      await entryRef.set(journalEntry.toJson());
      log('Created revenue closing journal entry: ${journalEntry.id}');

      return journalEntry;
    } catch (e) {
      log('Error creating revenue closing entry: $e');
      rethrow;
    }
  }

  /// Generate next journal entry number
  Future<String> _generateEntryNumber() async {
    try {
      final snapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('entryNumber', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) {
        return 'JE-001';
      }

      final lastEntry = snapshot.docs.first.data();
      final lastNumber = lastEntry['entryNumber'] as String;
      final numberPart = int.tryParse(lastNumber.split('-').last) ?? 0;
      final nextNumber = numberPart + 1;

      return 'JE-${nextNumber.toString().padLeft(3, '0')}';
    } catch (e) {
      log('Error generating entry number: $e');
      return 'JE-001';
    }
  }

  /// Update year-end closing record
  Future<void> updateYearEndClosing(YearEndClosingModel closing) async {
    log('Updating year-end closing: ${closing.id}');

    try {
      await _firestore
          .collection(AppCollection.yearEndClosingsCollection)
          .doc(closing.id)
          .update(closing.copyWith(updatedAt: DateTime.now()).toJson());

      log('Updated year-end closing successfully');
    } catch (e) {
      log('Error updating year-end closing: $e');
      rethrow;
    }
  }

  /// Get year-end closings for company
  Future<List<YearEndClosingModel>> getYearEndClosings() async {
    log('Fetching year-end closings');

    try {
      final snapshot = await _firestore
          .collection(AppCollection.yearEndClosingsCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('createdAt', descending: true)
          .get();

      final closings = snapshot.docs
          .map((doc) => YearEndClosingModel.fromJson(doc.data()))
          .toList();

      log('Fetched ${closings.length} year-end closings');
      return closings;
    } catch (e) {
      log('Error fetching year-end closings: $e');
      rethrow;
    }
  }

  /// Create closing journal entry for expense accounts
  Future<JournalEntryModel> createExpenseClosingEntry({
    required List<ChartOfAccountsModel> expenseAccounts,
    required String createdBy,
    required String closingId,
  }) async {
    log('Creating expense closing journal entry');

    try {
      final entryRef =
          _firestore.collection(AppCollection.journalEntriesCollection).doc();

      // Calculate total expenses
      final totalExpenses = expenseAccounts.fold<double>(
        0.0,
        (total, account) => total + account.balance,
      );

      // Create journal entry lines
      final lines = <JournalEntryLineModel>[];

      // Credit each expense account to close it
      for (final account in expenseAccounts) {
        lines.add(JournalEntryLineModel(
          id: '',
          journalEntryId: entryRef.id,
          accountId: account.id,
          accountNumber: account.accountNumber,
          accountName: account.accountName,
          description: 'Year-end closing - ${account.accountName}',
          debitAmount: 0.0,
          creditAmount: account.balance,
          createdAt: DateTime.now(),
        ));
      }

      // Debit Income Summary account
      lines.add(JournalEntryLineModel(
        id: '',
        journalEntryId: entryRef.id,
        accountId: 'income_summary',
        accountNumber: '3900',
        accountName: 'Income Summary',
        description: 'Year-end closing - Expense accounts closed',
        debitAmount: totalExpenses,
        creditAmount: 0.0,
        createdAt: DateTime.now(),
      ));

      final journalEntry = JournalEntryModel(
        id: entryRef.id,
        entryNumber: await _generateEntryNumber(),
        entryDate: DateTime.now(),
        description: 'Year-end closing - Expense accounts',
        entryType: JournalEntryType.closing,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: totalExpenses,
        totalCredits: totalExpenses,
        referenceNumber: 'YEC-EXP-$closingId',
        sourceTransactionType: 'year_end_closing',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: _uid,
      );

      await entryRef.set(journalEntry.toJson());
      log('Created expense closing journal entry: ${journalEntry.id}');

      return journalEntry;
    } catch (e) {
      log('Error creating expense closing entry: $e');
      rethrow;
    }
  }

  /// Create retained earnings transfer entry
  Future<JournalEntryModel> createRetainedEarningsTransferEntry({
    required double netIncome,
    required ChartOfAccountsModel retainedEarningsAccount,
    required String createdBy,
    required String closingId,
  }) async {
    log('Creating retained earnings transfer entry for net income: $netIncome');

    try {
      final entryRef =
          _firestore.collection(AppCollection.journalEntriesCollection).doc();

      final lines = <JournalEntryLineModel>[];

      if (netIncome > 0) {
        // Net income (profit) - Debit Income Summary, Credit Retained Earnings
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: entryRef.id,
            accountId: 'income_summary',
            accountNumber: '3900',
            accountName: 'Income Summary',
            description: 'Transfer net income to retained earnings',
            debitAmount: netIncome,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: entryRef.id,
            accountId: retainedEarningsAccount.id,
            accountNumber: retainedEarningsAccount.accountNumber,
            accountName: retainedEarningsAccount.accountName,
            description: 'Net income transferred from Income Summary',
            debitAmount: 0.0,
            creditAmount: netIncome,
            createdAt: DateTime.now(),
          ),
        ]);
      } else {
        // Net loss - Credit Income Summary, Debit Retained Earnings
        final netLoss = netIncome.abs();
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: entryRef.id,
            accountId: retainedEarningsAccount.id,
            accountNumber: retainedEarningsAccount.accountNumber,
            accountName: retainedEarningsAccount.accountName,
            description: 'Net loss transferred from Income Summary',
            debitAmount: netLoss,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: entryRef.id,
            accountId: 'income_summary',
            accountNumber: '3900',
            accountName: 'Income Summary',
            description: 'Transfer net loss to retained earnings',
            debitAmount: 0.0,
            creditAmount: netLoss,
            createdAt: DateTime.now(),
          ),
        ]);
      }

      final journalEntry = JournalEntryModel(
        id: entryRef.id,
        entryNumber: await _generateEntryNumber(),
        entryDate: DateTime.now(),
        description: netIncome > 0
            ? 'Year-end closing - Net income transfer to retained earnings'
            : 'Year-end closing - Net loss transfer to retained earnings',
        entryType: JournalEntryType.closing,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: netIncome.abs(),
        totalCredits: netIncome.abs(),
        referenceNumber: 'YEC-RE-$closingId',
        sourceTransactionType: 'year_end_closing',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: _uid,
      );

      await entryRef.set(journalEntry.toJson());
      log('Created retained earnings transfer entry: ${journalEntry.id}');

      return journalEntry;
    } catch (e) {
      log('Error creating retained earnings transfer entry: $e');
      rethrow;
    }
  }

  /// Update account balances after closing
  Future<void> updateAccountBalances({
    required List<ChartOfAccountsModel> revenueAccounts,
    required List<ChartOfAccountsModel> expenseAccounts,
    required ChartOfAccountsModel retainedEarningsAccount,
    required double netIncome,
  }) async {
    log('Updating account balances after year-end closing');

    try {
      final batch = _firestore.batch();

      // Zero out revenue account balances
      for (final account in revenueAccounts) {
        final accountRef = _firestore
            .collection(AppCollection.chartOfAccountsCollection)
            .doc(account.id);
        batch.update(accountRef, {
          'balance': 0.0,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }

      // Zero out expense account balances
      for (final account in expenseAccounts) {
        final accountRef = _firestore
            .collection(AppCollection.chartOfAccountsCollection)
            .doc(account.id);
        batch.update(accountRef, {
          'balance': 0.0,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }

      // Update retained earnings balance
      final newRetainedEarningsBalance =
          retainedEarningsAccount.balance + netIncome;
      final retainedEarningsRef = _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(retainedEarningsAccount.id);
      batch.update(retainedEarningsRef, {
        'balance': newRetainedEarningsBalance,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      await batch.commit();
      log('Updated account balances successfully');
    } catch (e) {
      log('Error updating account balances: $e');
      rethrow;
    }
  }

  /// Listen to year-end closings stream
  Stream<List<YearEndClosingModel>> listenToYearEndClosings() {
    log('Setting up year-end closings stream');

    return _firestore
        .collection(AppCollection.yearEndClosingsCollection)
        .where('uid', isEqualTo: _uid)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => YearEndClosingModel.fromJson(doc.data()))
            .toList());
  }
}
