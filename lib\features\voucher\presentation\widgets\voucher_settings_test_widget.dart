// DEBUG WIDGET - COMMENTED OUT FOR PRODUCTION
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:logestics/core/services/voucher_payment_settings_service.dart';
// import 'package:logestics/core/utils/snackbar_utils.dart';
// import 'package:logestics/models/finance/chart_of_accounts_model.dart';

// /// Test widget for voucher payment settings functionality
// /// This widget can be used to test the settings service independently
// class VoucherSettingsTestWidget extends StatefulWidget {
//   const VoucherSettingsTestWidget({super.key});

//   @override
//   State<VoucherSettingsTestWidget> createState() =>
//       _VoucherSettingsTestWidgetState();
// }

// class _VoucherSettingsTestWidgetState extends State<VoucherSettingsTestWidget> {
//   late final VoucherPaymentSettingsService _settingsService;
//   Map<String, ChartOfAccountsModel?> _loadedSettings = {};
//   bool _isLoading = false;

//   @override
//   void initState() {
//     super.initState();
//     try {
//       _settingsService = Get.find<VoucherPaymentSettingsService>();
//       _loadSettings();
//     } catch (e) {
//       SnackbarUtils.showError('Error', 'Settings service not available: $e');
//     }
//   }

//   void _loadSettings() {
//     setState(() {
//       _isLoading = true;
//     });

//     try {
//       _loadedSettings = _settingsService.loadAllAccountSettings();
//       SnackbarUtils.showSuccess('Success', 'Settings loaded successfully');
//     } catch (e) {
//       SnackbarUtils.showError('Error', 'Failed to load settings: $e');
//     } finally {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }

//   void _clearAllSettings() async {
//     try {
//       await _settingsService.clearAllSettings();
//       _loadSettings(); // Reload to show cleared state
//       SnackbarUtils.showSuccess('Success', 'All settings cleared');
//     } catch (e) {
//       SnackbarUtils.showError('Error', 'Failed to clear settings: $e');
//     }
//   }

//   void _testSaveSettings() async {
//     try {
//       // Create mock accounts for testing
//       final mockBrokerAccount = ChartOfAccountsModel(
//         id: 'test-broker-001',
//         accountNumber: 'TEST-001',
//         accountName: 'Test Broker Account',
//         accountType: AccountType.accountsPayable,
//         category: AccountCategory.liabilities,
//         balance: 0.0,
//         isActive: true,
//         createdAt: DateTime.now(),
//         uid: 'test-uid',
//       );

//       final mockMunshianaAccount = ChartOfAccountsModel(
//         id: 'test-munshiana-001',
//         accountNumber: 'TEST-002',
//         accountName: 'Test Munshiana Account',
//         accountType: AccountType.equityServiceRevenue,
//         category: AccountCategory.equity,
//         balance: 0.0,
//         isActive: true,
//         createdAt: DateTime.now(),
//         uid: 'test-uid',
//       );

//       // Save test accounts
//       await _settingsService.saveBrokerAccount(mockBrokerAccount);
//       await _settingsService.saveMunshianaAccount(mockMunshianaAccount);

//       _loadSettings(); // Reload to show saved state
//       SnackbarUtils.showSuccess('Success', 'Test settings saved');
//     } catch (e) {
//       SnackbarUtils.showError('Error', 'Failed to save test settings: $e');
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Card(
//       margin: const EdgeInsets.all(16),
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               children: [
//                 Icon(Icons.bug_report, color: Colors.orange),
//                 const SizedBox(width: 8),
//                 Text(
//                   'Voucher Settings Test Widget',
//                   style: TextStyle(
//                     fontSize: 18,
//                     fontWeight: FontWeight.bold,
//                     color: Colors.orange,
//                   ),
//                 ),
//               ],
//             ),
//             const SizedBox(height: 16),
//             if (_isLoading)
//               const Center(child: CircularProgressIndicator())
//             else ...[
//               Text(
//                 'Loaded Settings:',
//                 style: TextStyle(fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 8),
//               ..._loadedSettings.entries.map((entry) {
//                 final account = entry.value;
//                 return Padding(
//                   padding: const EdgeInsets.symmetric(vertical: 2),
//                   child: Row(
//                     children: [
//                       SizedBox(
//                         width: 100,
//                         child: Text(
//                           '${entry.key}:',
//                           style: TextStyle(fontWeight: FontWeight.w500),
//                         ),
//                       ),
//                       Expanded(
//                         child: Text(
//                           account != null
//                               ? '${account.displayName} (${account.accountNumber})'
//                               : 'Not configured',
//                           style: TextStyle(
//                             color: account != null ? Colors.green : Colors.grey,
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                 );
//               }),
//               const SizedBox(height: 16),
//               Row(
//                 children: [
//                   ElevatedButton.icon(
//                     onPressed: _loadSettings,
//                     icon: const Icon(Icons.refresh),
//                     label: const Text('Reload Settings'),
//                   ),
//                   const SizedBox(width: 8),
//                   ElevatedButton.icon(
//                     onPressed: _testSaveSettings,
//                     icon: const Icon(Icons.save),
//                     label: const Text('Save Test Data'),
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: Colors.blue,
//                       foregroundColor: Colors.white,
//                     ),
//                   ),
//                   const SizedBox(width: 8),
//                   ElevatedButton.icon(
//                     onPressed: _clearAllSettings,
//                     icon: const Icon(Icons.clear_all),
//                     label: const Text('Clear All'),
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: Colors.red,
//                       foregroundColor: Colors.white,
//                     ),
//                   ),
//                 ],
//               ),
//               const SizedBox(height: 16),
//               Container(
//                 padding: const EdgeInsets.all(12),
//                 decoration: BoxDecoration(
//                   color: Colors.blue.shade50,
//                   borderRadius: BorderRadius.circular(8),
//                   border: Border.all(color: Colors.blue.shade200),
//                 ),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Row(
//                       children: [
//                         Icon(Icons.info, color: Colors.blue, size: 16),
//                         const SizedBox(width: 4),
//                         Text(
//                           'Test Instructions:',
//                           style: TextStyle(
//                             fontWeight: FontWeight.bold,
//                             color: Colors.blue.shade700,
//                           ),
//                         ),
//                       ],
//                     ),
//                     const SizedBox(height: 8),
//                     Text(
//                       '1. Click "Save Test Data" to create mock account settings\n'
//                       '2. Click "Reload Settings" to verify persistence\n'
//                       '3. Click "Clear All" to reset settings\n'
//                       '4. Check that settings persist across app restarts',
//                       style: TextStyle(
//                         fontSize: 12,
//                         color: Colors.blue.shade700,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ],
//         ),
//       ),
//     );
//   }
// }
