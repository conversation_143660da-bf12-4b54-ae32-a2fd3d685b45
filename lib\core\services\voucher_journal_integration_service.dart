import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/voucher_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import 'automatic_journal_entry_service.dart';
import 'transaction_account_mapping_service.dart';
import 'account_ledger_service.dart';

/// Service for integrating voucher transactions with automatic journal entry generation
class VoucherJournalIntegrationService {
  final AutomaticJournalEntryService _automaticJournalService;
  final GeneralLedgerFirebaseService _generalLedgerService;
  final JournalEntryFirebaseService _journalEntryService;
  final TransactionAccountMappingService _mappingService;
  final AccountLedgerService _accountLedgerService;

  VoucherJournalIntegrationService(
    this._automaticJournalService,
    this._generalLedgerService,
    this._journalEntryService,
    this._mappingService,
    this._accountLedgerService,
  );

  /// Get current user's display name for audit trail
  String get _currentUserName =>
      FirebaseAuth.instance.currentUser?.displayName ??
      FirebaseAuth.instance.currentUser?.email ??
      'System User';

  /// Generate and create journal entries for a voucher transaction
  Future<bool> processVoucherTransaction(
      VoucherModel voucher, String uid) async {
    try {
      log('Processing voucher transaction for journal entries: ${voucher.voucherNumber}');

      // Generate journal entries for the voucher
      final journalEntries =
          await _automaticJournalService.generateVoucherJournalEntries(
        voucher: voucher,
        uid: uid,
        createdBy: _currentUserName, // Use actual authenticated user
      );

      if (journalEntries.isEmpty) {
        final errorMsg =
            'No journal entries generated for voucher: ${voucher.voucherNumber}. This indicates a configuration issue with Chart of Accounts or account mappings.';
        log(errorMsg);
        throw Exception(errorMsg);
      }

      // Create all journal entries and update account balances
      final List<String> errors = [];
      for (final journalEntry in journalEntries) {
        try {
          // Use the proper journal entry service that handles ID generation and lines
          await _journalEntryService.createJournalEntry(journalEntry);
          log('Successfully created journal entry for voucher: ${voucher.voucherNumber}');

          // Update account balances for all lines in this journal entry
          await _updateAccountBalancesForJournalEntry(journalEntry, uid);

          // Create individual account ledger entries
          final ledgerSuccess = await _accountLedgerService
              .createLedgerEntriesFromJournalEntry(journalEntry);

          if (!ledgerSuccess) {
            errors.add(
                'Failed to create ledger entries for journal entry: ${journalEntry.entryNumber}');
          }
        } catch (e) {
          final errorMsg =
              'Failed to create journal entry for voucher ${voucher.voucherNumber}: $e';
          log(errorMsg);
          errors.add(errorMsg);
        }
      }

      if (errors.isNotEmpty) {
        log('Voucher integration completed with errors: ${errors.join('; ')}');
        throw Exception(
            'Journal entry integration failed: ${errors.join('; ')}');
      }

      log('Successfully completed all journal entry integrations for voucher: ${voucher.voucherNumber}');
      return true;
    } catch (e) {
      log('Error processing voucher transaction: $e');
      rethrow; // Propagate the error to the calling service
    }
  }

  /// Process voucher transaction from Map format
  Future<bool> processVoucherTransactionFromMap(
    Map<String, dynamic> voucherData,
    String uid,
  ) async {
    try {
      // Convert Map to VoucherModel
      final voucher = VoucherModel.fromJson(voucherData);
      return await processVoucherTransaction(voucher, uid);
    } catch (e) {
      log('Error converting voucher data to model: $e');
      return false;
    }
  }

  /// Batch process multiple voucher transactions
  Future<BatchProcessResult> batchProcessVoucherTransactions(
    List<VoucherModel> vouchers,
    String uid,
  ) async {
    int successCount = 0;
    int failureCount = 0;
    final List<String> failedVoucherNumbers = [];

    log('Batch processing ${vouchers.length} voucher transactions');

    for (final voucher in vouchers) {
      final success = await processVoucherTransaction(voucher, uid);
      if (success) {
        successCount++;
      } else {
        failureCount++;
        failedVoucherNumbers.add(voucher.voucherNumber);
      }
    }

    log('Batch processing completed: $successCount successful, $failureCount failed');

    return BatchProcessResult(
      totalProcessed: vouchers.length,
      successCount: successCount,
      failureCount: failureCount,
      failedTransactionIds: failedVoucherNumbers,
    );
  }

  /// Validate voucher transaction for journal entry generation
  Future<VoucherValidationResult> validateVoucherForJournalEntry(
    VoucherModel voucher,
    String uid,
  ) async {
    try {
      final issues = <String>[];

      // Check if voucher has valid amounts
      if (voucher.totalFreight <= 0) {
        issues.add('Voucher total freight must be greater than zero');
      }

      // Check if voucher has valid number
      if (voucher.voucherNumber.isEmpty) {
        issues.add('Voucher must have a valid voucher number');
      }

      // Check if required accounts exist for journal entry generation
      // Only check default mappings if user hasn't selected explicit accounts
      final hasExplicitAccounts = _hasExplicitChartOfAccounts(voucher);

      if (!hasExplicitAccounts) {
        // User hasn't selected explicit accounts, check if default mappings exist
        final mapping = await _mappingService.getVoucherAccountMapping(uid);
        if (mapping == null) {
          issues.add(
              'Account configuration required. Please:\n1. Configure Voucher Payment Settings with Chart of Accounts\n2. Or select accounts manually in the voucher form\n3. Ensure all accounts exist in Chart of Accounts section');
        }
      } else {
        // User has selected explicit accounts, validate they are properly set
        final accountValidation = _validateExplicitAccounts(voucher);
        issues.addAll(accountValidation);
      }

      // Note: Removed strict validation for broker and munshiana accounts
      // The AutomaticJournalEntryService has a fallback mechanism that uses
      // default account mappings when explicit Chart of Accounts aren't selected.
      // This allows vouchers to be processed even without explicit account selection.

      // Only validate if we have explicit Chart of Accounts but they're invalid
      if (voucher.brokerFees > 0 &&
          voucher.brokerAccountId != null &&
          voucher.brokerAccountId!.isNotEmpty) {
        // If broker account ID is explicitly set, validate it exists
        // This validation could be added later if needed
      }

      if (voucher.munshianaFees > 0 &&
          voucher.munshianaAccountId != null &&
          voucher.munshianaAccountId!.isNotEmpty) {
        // If munshiana account ID is explicitly set, validate it exists
        // This validation could be added later if needed
      }

      // Note: Made tax and profit validations more lenient
      // These are optional fields and the system can work without them
      // Only validate if explicitly set but invalid

      // Optional: Validate sales tax account if explicitly set
      if (voucher.calculatedTax > 0 &&
          voucher.salesTaxAccountId != null &&
          voucher.salesTaxAccountId!.isNotEmpty) {
        // Could add validation to check if account exists
      }

      // Optional: Validate freight tax account if explicitly set
      if (voucher.calculatedFreightTax > 0 &&
          voucher.freightTaxAccountId != null &&
          voucher.freightTaxAccountId!.isNotEmpty) {
        // Could add validation to check if account exists
      }

      // Optional: Validate profit account if explicitly set
      if (voucher.calculatedProfit > 0 &&
          voucher.profitAccountId != null &&
          voucher.profitAccountId!.isNotEmpty) {
        // Could add validation to check if account exists
      }

      return VoucherValidationResult(
        isValid: issues.isEmpty,
        issues: issues,
      );
    } catch (e) {
      log('Error validating voucher for journal entry: $e');
      return VoucherValidationResult(
        isValid: false,
        issues: ['Validation error: $e'],
      );
    }
  }

  /// Check if voucher has explicit Chart of Accounts selected
  bool _hasExplicitChartOfAccounts(VoucherModel voucher) {
    return (voucher.companyFreightAccountId != null &&
            voucher.companyFreightAccountId!.isNotEmpty) ||
        (voucher.brokerAccountId != null &&
            voucher.brokerAccountId!.isNotEmpty) ||
        (voucher.munshianaAccountId != null &&
            voucher.munshianaAccountId!.isNotEmpty) ||
        (voucher.salesTaxAccountId != null &&
            voucher.salesTaxAccountId!.isNotEmpty) ||
        (voucher.freightTaxAccountId != null &&
            voucher.freightTaxAccountId!.isNotEmpty) ||
        (voucher.profitAccountId != null &&
            voucher.profitAccountId!.isNotEmpty) ||
        (voucher.truckFreightAccountId != null &&
            voucher.truckFreightAccountId!.isNotEmpty);
  }

  /// Validate explicitly selected accounts
  List<String> _validateExplicitAccounts(VoucherModel voucher) {
    final issues = <String>[];

    // Only validate accounts that have fees/amounts AND have been explicitly set to non-null
    // This prevents validation failures when voucher payment settings are partially configured

    // Validate broker account if fees are present AND account ID was explicitly set
    if (voucher.brokerFees > 0) {
      if (voucher.brokerAccountId != null && voucher.brokerAccountId!.isEmpty) {
        issues.add('Broker fees are set but broker account ID is empty');
      } else if (voucher.brokerAccountId == null) {
        // Account ID is null - this is acceptable if using default mappings
        // No validation error needed here
      }
    }

    // Validate munshiana account if fees are present AND account ID was explicitly set
    if (voucher.munshianaFees > 0) {
      if (voucher.munshianaAccountId != null &&
          voucher.munshianaAccountId!.isEmpty) {
        issues.add('Munshiana fees are set but munshiana account ID is empty');
      } else if (voucher.munshianaAccountId == null) {
        // Account ID is null - this is acceptable if using default mappings
        // No validation error needed here
      }
    }

    // Validate company freight account if amount is present AND account ID was explicitly set
    if (voucher.companyFreight > 0) {
      if (voucher.companyFreightAccountId != null &&
          voucher.companyFreightAccountId!.isEmpty) {
        issues.add(
            'Company freight is set but company freight account ID is empty');
      } else if (voucher.companyFreightAccountId == null) {
        // Account ID is null - this is acceptable if using default mappings
        // No validation error needed here
      }
    }

    return issues;
  }

  /// Get journal entries associated with a voucher
  Future<List<JournalEntryModel>> getJournalEntriesForVoucher(
    String voucherNumber,
    String uid,
  ) async {
    try {
      final allEntries =
          await _generalLedgerService.getJournalEntries(uid, null, null);

      return allEntries.fold(
        (failure) {
          log('Failed to fetch journal entries: ${failure.message}');
          return <JournalEntryModel>[];
        },
        (entries) {
          return entries
              .where((entry) =>
                  (entry.sourceTransactionId == voucherNumber &&
                      (entry.sourceTransactionType?.startsWith('voucher') ==
                          true)) &&
                  entry.uid == uid)
              .toList();
        },
      );
    } catch (e) {
      log('Error fetching journal entries for voucher: $e');
      return <JournalEntryModel>[];
    }
  }

  /// Check if a voucher already has associated journal entries
  Future<bool> hasExistingJournalEntries(
      String voucherNumber, String uid) async {
    final entries = await getJournalEntriesForVoucher(voucherNumber, uid);
    return entries.isNotEmpty;
  }

  /// Reverse journal entries for a voucher (when voucher is deleted/modified)
  Future<bool> reverseVoucherJournalEntries(
      String voucherNumber, String uid) async {
    try {
      final entries = await getJournalEntriesForVoucher(voucherNumber, uid);

      if (entries.isEmpty) {
        log('No journal entries found for voucher: $voucherNumber');
        return true; // Nothing to reverse
      }

      bool allReversed = true;
      for (final entry in entries) {
        final result = await _generalLedgerService.reverseJournalEntry(
          entry.id,
          'Voucher transaction reversed',
          'system', // createdBy parameter
        );

        result.fold(
          (failure) {
            log('Failed to reverse journal entry ${entry.id}: ${failure.message}');
            allReversed = false;
          },
          (success) {
            log('Successfully reversed journal entry: ${entry.id}');
          },
        );
      }

      return allReversed;
    } catch (e) {
      log('Error reversing voucher journal entries: $e');
      return false;
    }
  }

  /// Get voucher financial summary for reporting
  Future<VoucherFinancialSummary> getVoucherFinancialSummary(
    VoucherModel voucher,
  ) async {
    try {
      final netProfit =
          voucher.totalFreight - voucher.brokerFees - voucher.munshianaFees;

      return VoucherFinancialSummary(
        voucherNumber: voucher.voucherNumber,
        totalFreight: voucher.totalFreight,
        brokerFees: voucher.brokerFees,
        munshianaFees: voucher.munshianaFees,
        netProfit: netProfit,
        hasJournalEntries: await hasExistingJournalEntries(
            voucher.voucherNumber, ''), // uid would be passed
      );
    } catch (e) {
      log('Error getting voucher financial summary: $e');
      return VoucherFinancialSummary(
        voucherNumber: voucher.voucherNumber,
        totalFreight: 0.0,
        brokerFees: 0.0,
        munshianaFees: 0.0,
        netProfit: 0.0,
        hasJournalEntries: false,
      );
    }
  }

  /// Update account balances for all lines in a journal entry
  Future<void> _updateAccountBalancesForJournalEntry(
    JournalEntryModel journalEntry,
    String uid,
  ) async {
    try {
      log('Updating account balances for journal entry: ${journalEntry.entryNumber}');
      final List<String> balanceErrors = [];

      for (final line in journalEntry.lines) {
        // Update balance for debit amount
        if (line.debitAmount > 0) {
          final result = await _generalLedgerService.updateAccountBalance(
            line.accountId,
            line.debitAmount,
            true, // isDebit
          );

          result.fold(
            (failure) {
              final errorMsg =
                  'Failed to update account balance for debit on account ${line.accountId}: ${failure.message}';
              log(errorMsg);
              balanceErrors.add(errorMsg);
            },
            (success) =>
                log('Updated account balance for debit: ${line.accountId}'),
          );
        }

        // Update balance for credit amount
        if (line.creditAmount > 0) {
          final result = await _generalLedgerService.updateAccountBalance(
            line.accountId,
            line.creditAmount,
            false, // isCredit
          );

          result.fold(
            (failure) {
              final errorMsg =
                  'Failed to update account balance for credit on account ${line.accountId}: ${failure.message}';
              log(errorMsg);
              balanceErrors.add(errorMsg);
            },
            (success) =>
                log('Updated account balance for credit: ${line.accountId}'),
          );
        }
      }

      if (balanceErrors.isNotEmpty) {
        throw Exception(
            'Account balance update errors: ${balanceErrors.join('; ')}');
      }

      log('Successfully updated all account balances for journal entry: ${journalEntry.entryNumber}');
    } catch (e) {
      log('Error updating account balances for journal entry: $e');
      rethrow; // Propagate the error to the calling method
    }
  }
}

/// Result class for batch processing operations
class BatchProcessResult {
  final int totalProcessed;
  final int successCount;
  final int failureCount;
  final List<String> failedTransactionIds;

  BatchProcessResult({
    required this.totalProcessed,
    required this.successCount,
    required this.failureCount,
    required this.failedTransactionIds,
  });

  double get successRate =>
      totalProcessed > 0 ? successCount / totalProcessed : 0.0;
  bool get hasFailures => failureCount > 0;
  bool get allSuccessful => failureCount == 0;
}

/// Result class for voucher validation
class VoucherValidationResult {
  final bool isValid;
  final List<String> issues;

  VoucherValidationResult({
    required this.isValid,
    required this.issues,
  });

  String get issuesText => issues.join(', ');
}

/// Financial summary for a voucher
class VoucherFinancialSummary {
  final String voucherNumber;
  final double totalFreight;
  final double brokerFees;
  final double munshianaFees;
  final double netProfit;
  final bool hasJournalEntries;

  VoucherFinancialSummary({
    required this.voucherNumber,
    required this.totalFreight,
    required this.brokerFees,
    required this.munshianaFees,
    required this.netProfit,
    required this.hasJournalEntries,
  });
}
