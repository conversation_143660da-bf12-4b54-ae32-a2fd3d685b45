import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/debug/test_atomic_voucher_transactions.dart';

/// Debug screen for testing atomic transaction implementation
class AtomicTransactionDebugScreen extends StatefulWidget {
  const AtomicTransactionDebugScreen({super.key});

  @override
  State<AtomicTransactionDebugScreen> createState() =>
      _AtomicTransactionDebugScreenState();
}

class _AtomicTransactionDebugScreenState
    extends State<AtomicTransactionDebugScreen> {
  final AtomicVoucherTransactionTest _testRunner =
      AtomicVoucherTransactionTest();
  bool _isRunningTests = false;
  String _testResults = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Atomic Transaction Tests'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Atomic Transaction Testing',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This screen tests the atomic transaction implementation for voucher operations. '
                      'All operations should either succeed completely or fail completely with proper rollback.',
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isRunningTests ? null : _runAllTests,
                      child: _isRunningTests
                          ? const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                ),
                                SizedBox(width: 8),
                                Text('Running Tests...'),
                              ],
                            )
                          : const Text('Run All Atomic Transaction Tests'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isRunningTests ? null : _testVoucherCreation,
                    child: const Text('Test Creation'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isRunningTests ? null : _testVoucherUpdate,
                    child: const Text('Test Update'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isRunningTests ? null : _testFailureScenario,
                    child: const Text('Test Failure'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isRunningTests ? null : _testDuplicateNumber,
                    child: const Text('Test Duplicate'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isRunningTests ? null : _testReadBeforeWrite,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
              ),
              child: const Text('Test Firebase Read-Before-Write Fix'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isRunningTests ? null : _testJournalEntryLineItems,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
              ),
              child: const Text('Test Journal Entry Line Items'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isRunningTests ? null : _testPaymentJournalEntries,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
              ),
              child: const Text('Test Payment Journal Entries'),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Test Results',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _testResults.isEmpty
                                ? 'No tests run yet. Click a button above to start testing.'
                                : _testResults,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _clearResults,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
              ),
              child: const Text('Clear Results'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _runAllTests() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _addResult('❌ Error: No authenticated user found');
      return;
    }

    setState(() {
      _isRunningTests = true;
      _testResults = 'Starting comprehensive atomic transaction tests...\n\n';
    });

    try {
      await _testRunner.runAllTests(user.uid);
      _addResult('\n✅ All tests completed successfully!');
    } catch (e) {
      _addResult('\n❌ Test suite failed with exception: $e');
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  Future<void> _testVoucherCreation() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _addResult('❌ Error: No authenticated user found');
      return;
    }

    setState(() {
      _isRunningTests = true;
    });

    _addResult('Testing atomic voucher creation...\n');

    try {
      await _testRunner.testAtomicVoucherCreation(user.uid);
    } catch (e) {
      _addResult('❌ Voucher creation test failed: $e');
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  Future<void> _testVoucherUpdate() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _addResult('❌ Error: No authenticated user found');
      return;
    }

    setState(() {
      _isRunningTests = true;
    });

    _addResult('Testing atomic voucher update...\n');

    try {
      await _testRunner.testAtomicVoucherUpdate(user.uid);
    } catch (e) {
      _addResult('❌ Voucher update test failed: $e');
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  Future<void> _testFailureScenario() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _addResult('❌ Error: No authenticated user found');
      return;
    }

    setState(() {
      _isRunningTests = true;
    });

    _addResult('Testing failure scenario with rollback...\n');

    try {
      await _testRunner.testAtomicVoucherCreationFailure(user.uid);
    } catch (e) {
      _addResult('❌ Failure scenario test failed: $e');
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  Future<void> _testDuplicateNumber() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _addResult('❌ Error: No authenticated user found');
      return;
    }

    setState(() {
      _isRunningTests = true;
    });

    _addResult('Testing duplicate voucher number handling...\n');

    try {
      await _testRunner.testDuplicateVoucherNumber(user.uid);
    } catch (e) {
      _addResult('❌ Duplicate number test failed: $e');
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  void _addResult(String result) {
    setState(() {
      _testResults += '$result\n';
    });
  }

  Future<void> _testReadBeforeWrite() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _addResult('❌ Error: No authenticated user found');
      return;
    }

    setState(() {
      _isRunningTests = true;
    });

    _addResult('Testing Firebase read-before-write compliance...\n');

    try {
      await _testRunner.testFirebaseReadBeforeWrite(user.uid);
    } catch (e) {
      _addResult('❌ Read-before-write test failed: $e');
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  Future<void> _testJournalEntryLineItems() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _addResult('❌ Error: No authenticated user found');
      return;
    }

    setState(() {
      _isRunningTests = true;
    });

    _addResult('Testing journal entry line items generation...\n');

    try {
      await _testRunner.testJournalEntryLineItems(user.uid);
    } catch (e) {
      _addResult('❌ Journal entry line items test failed: $e');
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  Future<void> _testPaymentJournalEntries() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      _addResult('❌ Error: No authenticated user found');
      return;
    }

    setState(() {
      _isRunningTests = true;
    });

    _addResult('Testing payment journal entry creation...\n');

    try {
      await _testRunner.testPaymentJournalEntryCreation(user.uid);
    } catch (e) {
      _addResult('❌ Payment journal entry test failed: $e');
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  void _clearResults() {
    setState(() {
      _testResults = '';
    });
  }
}
