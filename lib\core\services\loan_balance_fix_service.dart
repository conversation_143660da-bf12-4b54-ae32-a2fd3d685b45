import 'dart:developer' as dev;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../../features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import 'account_type_helper_service.dart';
import 'balance_recalculation_service.dart';

/// Comprehensive service to fix loan account balance calculation issues
/// Addresses problems with manual loans, voucher-based loans, and balance synchronization
class LoanBalanceFixService {
  final JournalEntryFirebaseService _journalService;
  final ChartOfAccountsRepository _accountsRepository;
  final BalanceRecalculationService _balanceRecalculationService;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  LoanBalanceFixService({
    required JournalEntryFirebaseService journalService,
    required ChartOfAccountsRepository accountsRepository,
    required BalanceRecalculationService balanceRecalculationService,
  })  : _journalService = journalService,
        _accountsRepository = accountsRepository,
        _balanceRecalculationService = balanceRecalculationService;

  /// Comprehensive fix for all loan balance calculation issues
  Future<LoanBalanceFixResult> fixAllLoanBalanceIssues(String uid) async {
    dev.log('🔧 Starting comprehensive loan balance fix for company: $uid');

    final result = LoanBalanceFixResult(uid: uid, fixedAt: DateTime.now());

    try {
      // Step 1: Fix account type configuration
      dev.log('📋 Step 1: Fixing loan account type configuration...');
      await fixLoanAccountTypeConfiguration(uid, result);

      // Step 2: Fix running balance calculations
      dev.log('💰 Step 2: Fixing running balance calculations...');
      await fixRunningBalanceCalculations(uid, result);

      // Step 3: Fix current balance synchronization
      dev.log('🔄 Step 3: Fixing current balance synchronization...');
      await fixCurrentBalanceSynchronization(uid, result);

      // Step 4: Fix manual loan workflow
      dev.log('✋ Step 4: Fixing manual loan workflow...');
      await fixManualLoanWorkflow(uid, result);

      // Step 5: Fix voucher-based loan workflow
      dev.log('📄 Step 5: Fixing voucher-based loan workflow...');
      await fixVoucherLoanWorkflow(uid, result);

      // Step 6: Recalculate all loan account balances
      dev.log('🔢 Step 6: Recalculating all loan account balances...');
      await recalculateAllLoanAccountBalances(uid, result);

      result.success = true;
      dev.log('✅ Comprehensive loan balance fix completed successfully');
    } catch (e) {
      result.success = false;
      result.errorMessage = e.toString();
      dev.log('❌ Error during loan balance fix: $e');
    }

    return result;
  }

  /// Fix loan account type configuration to ensure they are Asset accounts
  Future<void> fixLoanAccountTypeConfiguration(
      String uid, LoanBalanceFixResult result) async {
    try {
      final accountsResult = await _accountsRepository.getAccounts();

      await accountsResult.fold(
        (failure) async {
          result.errors.add('Failed to load accounts: ${failure.message}');
        },
        (accounts) async {
          final loanReceivableAccounts = accounts
              .where((account) =>
                  account.accountName.toLowerCase().contains('loan') &&
                  account.accountName.toLowerCase().contains('receivable'))
              .toList();

          for (final account in loanReceivableAccounts) {
            // Check if account is properly configured as Asset
            if (account.category != AccountCategory.assets) {
              dev.log('🔧 Fixing account type for: ${account.accountName}');

              // Update account to be Asset type
              await _firestore
                  .collection('chart_of_accounts')
                  .where('id', isEqualTo: account.id)
                  .where('uid', isEqualTo: uid)
                  .get()
                  .then((snapshot) async {
                if (snapshot.docs.isNotEmpty) {
                  await snapshot.docs.first.reference.update({
                    'accountType': AccountType.accountsReceivable.name,
                    'category': AccountCategory.assets.name,
                    'updatedAt': Timestamp.fromDate(DateTime.now()),
                    'fixNote':
                        'Account type corrected to Asset during loan balance fix',
                  });

                  result.accountsFixed++;
                  dev.log('✅ Fixed account type for: ${account.accountName}');
                }
              });
            }
          }
        },
      );
    } catch (e) {
      result.errors.add('Error fixing account type configuration: $e');
      dev.log('❌ Error fixing account type configuration: $e');
    }
  }

  /// Fix running balance calculations for all loan-related journal entries
  Future<void> fixRunningBalanceCalculations(
      String uid, LoanBalanceFixResult result) async {
    try {
      final entries = await _journalService.getJournalEntries();

      // Find loan-related entries with missing or incorrect running balances
      final loanEntries = entries
          .where((entry) =>
              entry.description.toLowerCase().contains('loan') ||
              entry.sourceTransactionType?.contains('loan') == true)
          .toList();

      dev.log(
          '📊 Found ${loanEntries.length} loan-related journal entries to check');

      for (final entry in loanEntries) {
        bool needsUpdate = false;
        final updatedLines = <JournalEntryLineModel>[];

        for (final line in entry.lines) {
          if (line.accountName.toLowerCase().contains('loan') &&
              line.accountName.toLowerCase().contains('receivable')) {
            // Get account details to determine correct balance calculation
            final accountsResult = await _accountsRepository.getAccounts();
            final ChartOfAccountsModel? account = accountsResult.fold(
              (failure) => null,
              (accounts) {
                try {
                  return accounts.firstWhere((acc) => acc.id == line.accountId);
                } catch (e) {
                  return null;
                }
              },
            );

            if (account != null) {
              // Recalculate running balance using correct logic
              final previousBalance = await _getPreviousRunningBalance(
                  line.accountId, entry.entryDate, uid);
              final balanceChange =
                  AccountTypeHelperService.calculateBalanceChange(
                accountType: account.accountType,
                debitAmount: line.debitAmount,
                creditAmount: line.creditAmount,
              );

              final correctRunningBalance = previousBalance + balanceChange;

              // Check if running balance needs correction
              if (line.runningBalance == null ||
                  (line.runningBalance! - correctRunningBalance).abs() > 0.01) {
                needsUpdate = true;
                updatedLines
                    .add(line.copyWith(runningBalance: correctRunningBalance));

                dev.log(
                    '🔧 Correcting running balance for ${account.accountName}: ${line.runningBalance} → $correctRunningBalance');
              } else {
                updatedLines.add(line);
              }
            } else {
              updatedLines.add(line);
            }
          } else {
            updatedLines.add(line);
          }
        }

        // Update journal entry if needed
        if (needsUpdate) {
          await _updateJournalEntryRunningBalances(entry.id, updatedLines);
          result.journalEntriesFixed++;
        }
      }
    } catch (e) {
      result.errors.add('Error fixing running balance calculations: $e');
      dev.log('❌ Error fixing running balance calculations: $e');
    }
  }

  /// Fix current balance synchronization between stored and calculated balances
  Future<void> fixCurrentBalanceSynchronization(
      String uid, LoanBalanceFixResult result) async {
    try {
      final accountsResult = await _accountsRepository.getAccounts();

      await accountsResult.fold(
        (failure) async {
          result.errors.add(
              'Failed to load accounts for synchronization: ${failure.message}');
        },
        (accounts) async {
          final loanReceivableAccounts = accounts
              .where((account) =>
                  account.accountName.toLowerCase().contains('loan') &&
                  account.accountName.toLowerCase().contains('receivable'))
              .toList();

          for (final account in loanReceivableAccounts) {
            try {
              // Calculate correct balance from journal entries
              final calculatedBalance = await _calculateCorrectAccountBalance(
                  account.id, uid, account.accountType);

              // Check if stored balance needs correction
              final difference = (account.balance - calculatedBalance).abs();
              if (difference > 0.01) {
                dev.log(
                    '🔧 Synchronizing balance for ${account.accountName}: ${account.balance} → $calculatedBalance');

                // Update stored balance
                await _firestore
                    .collection('chart_of_accounts')
                    .where('id', isEqualTo: account.id)
                    .where('uid', isEqualTo: uid)
                    .get()
                    .then((snapshot) async {
                  if (snapshot.docs.isNotEmpty) {
                    await snapshot.docs.first.reference.update({
                      'balance': calculatedBalance,
                      'updatedAt': Timestamp.fromDate(DateTime.now()),
                      'balanceSyncNote':
                          'Balance synchronized during loan balance fix - was ${account.balance}, corrected to $calculatedBalance',
                    });

                    result.balancesSynchronized++;
                    dev.log(
                        '✅ Synchronized balance for: ${account.accountName}');
                  }
                });
              }
            } catch (e) {
              result.errors.add(
                  'Error synchronizing balance for ${account.accountName}: $e');
              dev.log(
                  '❌ Error synchronizing balance for ${account.accountName}: $e');
            }
          }
        },
      );
    } catch (e) {
      result.errors.add('Error fixing current balance synchronization: $e');
      dev.log('❌ Error fixing current balance synchronization: $e');
    }
  }

  /// Fix manual loan workflow balance calculations
  Future<void> fixManualLoanWorkflow(
      String uid, LoanBalanceFixResult result) async {
    try {
      final entries = await _journalService.getJournalEntries();

      // Find manual loan entries
      final manualLoanEntries = entries
          .where((entry) =>
              entry.sourceTransactionType?.contains('manual_loan') == true ||
              entry.description.toLowerCase().contains('manual loan'))
          .toList();

      dev.log(
          '📊 Found ${manualLoanEntries.length} manual loan entries to fix');

      for (final entry in manualLoanEntries) {
        // Recalculate and update running balances for this entry
        await _recalculateEntryRunningBalances(entry, uid);
        result.manualLoanEntriesFixed++;
      }
    } catch (e) {
      result.errors.add('Error fixing manual loan workflow: $e');
      dev.log('❌ Error fixing manual loan workflow: $e');
    }
  }

  /// Fix voucher-based loan workflow balance calculations
  Future<void> fixVoucherLoanWorkflow(
      String uid, LoanBalanceFixResult result) async {
    try {
      final entries = await _journalService.getJournalEntries();

      // Find voucher-based loan entries
      final voucherLoanEntries = entries
          .where((entry) =>
              entry.sourceTransactionType?.contains('voucher') == true &&
              (entry.description.toLowerCase().contains('loan') ||
                  entry.sourceTransactionType?.contains('loan') == true))
          .toList();

      dev.log(
          '📊 Found ${voucherLoanEntries.length} voucher-based loan entries to fix');

      for (final entry in voucherLoanEntries) {
        // Recalculate and update running balances for this entry
        await _recalculateEntryRunningBalances(entry, uid);
        result.voucherLoanEntriesFixed++;
      }
    } catch (e) {
      result.errors.add('Error fixing voucher loan workflow: $e');
      dev.log('❌ Error fixing voucher loan workflow: $e');
    }
  }

  /// Recalculate all loan account balances using the balance recalculation service
  Future<void> recalculateAllLoanAccountBalances(
      String uid, LoanBalanceFixResult result) async {
    try {
      final accountsResult = await _accountsRepository.getAccounts();

      await accountsResult.fold(
        (failure) async {
          result.errors.add(
              'Failed to load accounts for recalculation: ${failure.message}');
        },
        (accounts) async {
          final loanReceivableAccounts = accounts
              .where((account) =>
                  account.accountName.toLowerCase().contains('loan') &&
                  account.accountName.toLowerCase().contains('receivable'))
              .toList();

          for (final account in loanReceivableAccounts) {
            try {
              dev.log('🔢 Recalculating balances for: ${account.accountName}');

              // Use the balance recalculation service
              await _balanceRecalculationService.recalculateAccountBalance(
                accountId: account.id,
                uid: uid,
              );

              result.accountsRecalculated++;
              dev.log('✅ Recalculated balances for: ${account.accountName}');
            } catch (e) {
              result.errors.add(
                  'Error recalculating balance for ${account.accountName}: $e');
              dev.log(
                  '❌ Error recalculating balance for ${account.accountName}: $e');
            }
          }
        },
      );
    } catch (e) {
      result.errors.add('Error recalculating loan account balances: $e');
      dev.log('❌ Error recalculating loan account balances: $e');
    }
  }

  /// Calculate correct account balance from journal entries
  Future<double> _calculateCorrectAccountBalance(
      String accountId, String uid, AccountType accountType) async {
    double balance = 0.0;

    try {
      final entriesSnapshot = await _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .where('status', isEqualTo: 'posted')
          .orderBy('createdAt')
          .get();

      for (final doc in entriesSnapshot.docs) {
        try {
          final entry = JournalEntryModel.fromFirestore(doc);

          for (final line in entry.lines) {
            if (line.accountId == accountId) {
              final balanceChange =
                  AccountTypeHelperService.calculateBalanceChange(
                accountType: accountType,
                debitAmount: line.debitAmount,
                creditAmount: line.creditAmount,
              );
              balance += balanceChange;
            }
          }
        } catch (e) {
          dev.log('❌ Error processing entry ${doc.id}: $e');
        }
      }
    } catch (e) {
      dev.log('❌ Error calculating correct account balance: $e');
      rethrow;
    }

    return balance;
  }

  /// Get previous running balance for an account before a specific date
  Future<double> _getPreviousRunningBalance(
      String accountId, DateTime beforeDate, String uid) async {
    try {
      final entriesSnapshot = await _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .where('status', isEqualTo: 'posted')
          .where('entryDate', isLessThan: Timestamp.fromDate(beforeDate))
          .orderBy('entryDate', descending: true)
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (entriesSnapshot.docs.isNotEmpty) {
        final entry =
            JournalEntryModel.fromFirestore(entriesSnapshot.docs.first);
        JournalEntryLineModel? line;
        try {
          line = entry.lines.firstWhere((line) => line.accountId == accountId);
        } catch (e) {
          line = null;
        }

        return line?.runningBalance ?? 0.0;
      }
    } catch (e) {
      dev.log('❌ Error getting previous running balance: $e');
    }

    return 0.0;
  }

  /// Update journal entry running balances
  Future<void> _updateJournalEntryRunningBalances(
      String entryId, List<JournalEntryLineModel> updatedLines) async {
    try {
      await _firestore.collection('journal_entries').doc(entryId).update({
        'lines': updatedLines.map((line) => line.toFirestore()).toList(),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'runningBalanceFixNote':
            'Running balances corrected during loan balance fix',
      });
    } catch (e) {
      dev.log('❌ Error updating journal entry running balances: $e');
      rethrow;
    }
  }

  /// Recalculate running balances for a specific journal entry
  Future<void> _recalculateEntryRunningBalances(
      JournalEntryModel entry, String uid) async {
    try {
      final updatedLines = <JournalEntryLineModel>[];

      for (final line in entry.lines) {
        // Get account details
        final accountsResult = await _accountsRepository.getAccounts();
        final ChartOfAccountsModel? account = accountsResult.fold(
          (failure) => null,
          (accounts) {
            try {
              return accounts.firstWhere((acc) => acc.id == line.accountId);
            } catch (e) {
              return null;
            }
          },
        );

        if (account != null) {
          // Calculate correct running balance
          final previousBalance = await _getPreviousRunningBalance(
              line.accountId, entry.entryDate, uid);
          final balanceChange = AccountTypeHelperService.calculateBalanceChange(
            accountType: account.accountType,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
          );

          final correctRunningBalance = previousBalance + balanceChange;
          updatedLines
              .add(line.copyWith(runningBalance: correctRunningBalance));
        } else {
          updatedLines.add(line);
        }
      }

      // Update the journal entry
      await _updateJournalEntryRunningBalances(entry.id, updatedLines);
    } catch (e) {
      dev.log('❌ Error recalculating entry running balances: $e');
      rethrow;
    }
  }
}

/// Result of loan balance fix operation
class LoanBalanceFixResult {
  final String uid;
  final DateTime fixedAt;
  bool success = false;
  String? errorMessage;
  int accountsFixed = 0;
  int journalEntriesFixed = 0;
  int balancesSynchronized = 0;
  int manualLoanEntriesFixed = 0;
  int voucherLoanEntriesFixed = 0;
  int accountsRecalculated = 0;
  List<String> errors = [];

  LoanBalanceFixResult({required this.uid, required this.fixedAt});

  @override
  String toString() {
    return 'LoanBalanceFixResult(success: $success, accounts: $accountsFixed, entries: $journalEntriesFixed, synchronized: $balancesSynchronized, manual: $manualLoanEntriesFixed, voucher: $voucherLoanEntriesFixed, recalculated: $accountsRecalculated, errors: ${errors.length})';
  }
}
