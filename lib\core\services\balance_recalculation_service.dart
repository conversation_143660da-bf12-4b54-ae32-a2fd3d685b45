import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../utils/app_constants/firebase/collection_names.dart';
import 'account_type_helper_service.dart';

/// Service to handle running balance recalculation when backdated entries are added
class BalanceRecalculationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String get _uid => _auth.currentUser?.uid ?? '';

  /// Recalculate running balances for all accounts affected by a backdated journal entry
  Future<void> recalculateBalancesForBackdatedEntry({
    required JournalEntryModel backdatedEntry,
    required List<String> affectedAccountIds,
  }) async {
    try {
      log('Starting balance recalculation for backdated entry: ${backdatedEntry.entryNumber}');
      log('Entry date: ${backdatedEntry.entryDate}');
      log('Affected accounts: ${affectedAccountIds.length}');

      // Process each affected account
      for (final accountId in affectedAccountIds) {
        await _recalculateAccountBalance(accountId, backdatedEntry.entryDate);
      }

      log('Completed balance recalculation for backdated entry');
    } catch (e) {
      log('Error in balance recalculation: $e');
      rethrow;
    }
  }

  /// Public method to recalculate balance for a specific account
  Future<void> recalculateAccountBalance({
    required String accountId,
    required String uid,
  }) async {
    try {
      log('Recalculating balance for account: $accountId');

      // Recalculate from the beginning of time for this account
      final earliestDate = DateTime(2000, 1, 1);
      await _recalculateAccountBalance(accountId, earliestDate);

      log('Completed balance recalculation for account: $accountId');
    } catch (e) {
      log('Error recalculating account balance: $e');
      rethrow;
    }
  }

  /// Recalculate running balance for a specific account from a given date forward
  Future<void> _recalculateAccountBalance(
      String accountId, DateTime fromDate) async {
    try {
      log('Recalculating balance for account: $accountId from date: $fromDate');

      // Get account details for balance calculation logic
      final accountDoc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('id', isEqualTo: accountId)
          .limit(1)
          .get();

      if (accountDoc.docs.isEmpty) {
        log('Account not found: $accountId');
        return;
      }

      final account =
          ChartOfAccountsModel.fromJson(accountDoc.docs.first.data());

      // Get all journal entries that affect this account from the backdated date forward
      final journalEntriesQuery = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .where('status', isEqualTo: JournalEntryStatus.posted.name)
          .where('entryDate',
              isGreaterThanOrEqualTo: Timestamp.fromDate(fromDate))
          .orderBy('entryDate')
          .orderBy('createdAt') // Secondary sort for same-date entries
          .get();

      // Filter entries that affect this account and extract relevant lines
      final accountTransactions = <_AccountTransaction>[];
      for (final doc in journalEntriesQuery.docs) {
        final entry = JournalEntryModel.fromFirestore(doc);
        final accountLines =
            entry.lines.where((line) => line.accountId == accountId);

        for (final line in accountLines) {
          accountTransactions.add(_AccountTransaction(
            entryDate: entry.entryDate,
            createdAt: entry.createdAt,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
            lineId: line.id,
            journalEntryId: entry.id,
          ));
        }
      }

      // Calculate opening balance as of the day before the recalculation date
      final openingBalance = await _getAccountBalanceAsOfDate(accountId,
          account.accountType, fromDate.subtract(const Duration(days: 1)));

      log('Opening balance for $accountId as of ${fromDate.subtract(const Duration(days: 1))}: $openingBalance');

      // Recalculate running balances chronologically
      double runningBalance = openingBalance;
      final batch = _firestore.batch();

      for (final transaction in accountTransactions) {
        // Calculate balance change using proper accounting principles
        final balanceChange = AccountTypeHelperService.calculateBalanceChange(
          accountType: account.accountType,
          debitAmount: transaction.debitAmount,
          creditAmount: transaction.creditAmount,
        );

        runningBalance += balanceChange;

        // Update the journal entry line with new running balance
        // Note: This would require adding runningBalance field to JournalEntryLineModel
        // For now, we'll update the account's current balance at the end
        log('Transaction ${transaction.lineId}: Balance change: $balanceChange, New running balance: $runningBalance');
      }

      // Update the account's current balance to the final calculated balance
      final accountRef = accountDoc.docs.first.reference;
      batch.update(accountRef, {'balance': runningBalance});

      await batch.commit();
      log('Updated account $accountId balance to: $runningBalance');
    } catch (e) {
      log('Error recalculating balance for account $accountId: $e');
      rethrow;
    }
  }

  /// Get account balance as of a specific date
  Future<double> _getAccountBalanceAsOfDate(
      String accountId, AccountType accountType, DateTime asOfDate) async {
    try {
      // Get all journal entries affecting this account up to the specified date
      final journalEntriesQuery = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .where('status', isEqualTo: JournalEntryStatus.posted.name)
          .where('entryDate', isLessThanOrEqualTo: Timestamp.fromDate(asOfDate))
          .get();

      double balance = 0.0;

      for (final doc in journalEntriesQuery.docs) {
        final entry = JournalEntryModel.fromFirestore(doc);
        final accountLines =
            entry.lines.where((line) => line.accountId == accountId);

        for (final line in accountLines) {
          final balanceChange = AccountTypeHelperService.calculateBalanceChange(
            accountType: accountType,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
          );
          balance += balanceChange;
        }
      }

      return balance;
    } catch (e) {
      log('Error calculating balance as of date for account $accountId: $e');
      return 0.0;
    }
  }

  /// Trigger balance recalculation when a journal entry is posted
  Future<void> onJournalEntryPosted(JournalEntryModel journalEntry) async {
    try {
      // Check if this is a backdated entry (entry date is before today)
      final today = DateTime.now();
      final isBackdated = journalEntry.entryDate
          .isBefore(DateTime(today.year, today.month, today.day));

      if (isBackdated) {
        log('Detected backdated journal entry: ${journalEntry.entryNumber}');

        // Get all affected account IDs
        final affectedAccountIds =
            journalEntry.lines.map((line) => line.accountId).toSet().toList();

        // Trigger recalculation
        await recalculateBalancesForBackdatedEntry(
          backdatedEntry: journalEntry,
          affectedAccountIds: affectedAccountIds,
        );
      } else {
        log('Journal entry ${journalEntry.entryNumber} is current-dated, no recalculation needed');
      }
    } catch (e) {
      log('Error in onJournalEntryPosted: $e');
      // Don't rethrow to avoid breaking the posting process
    }
  }
}

/// Helper class for transaction data during recalculation
class _AccountTransaction {
  final DateTime entryDate;
  final DateTime createdAt;
  final double debitAmount;
  final double creditAmount;
  final String lineId;
  final String journalEntryId;

  _AccountTransaction({
    required this.entryDate,
    required this.createdAt,
    required this.debitAmount,
    required this.creditAmount,
    required this.lineId,
    required this.journalEntryId,
  });
}
