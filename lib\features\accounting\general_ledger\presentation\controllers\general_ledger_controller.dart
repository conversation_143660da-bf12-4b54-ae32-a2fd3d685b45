import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../../../../models/finance/journal_entry_model.dart';
import '../../../../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../../chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import '../../../journal_entries/repositories/journal_entry_repository.dart';

class GeneralLedgerController extends GetxController {
  final ChartOfAccountsRepository chartOfAccountsRepository;
  final JournalEntryRepository journalEntryRepository;
  final GeneralLedgerFirebaseService generalLedgerFirebaseService;

  GeneralLedgerController({
    required this.chartOfAccountsRepository,
    required this.journalEntryRepository,
    required this.generalLedgerFirebaseService,
  });

  // Observable variables
  final RxList<ChartOfAccountsModel> accounts = <ChartOfAccountsModel>[].obs;
  final RxMap<String, double> accountBalances = <String, double>{}.obs;
  final RxList<String> loadingBalances = <String>[].obs;
  final RxBool isLoading = false.obs;

  // Search and filtering
  final TextEditingController searchController = TextEditingController();
  final RxString searchQuery = ''.obs;
  final Rx<AccountType?> selectedAccountType = Rx<AccountType?>(null);
  final Rx<bool?> selectedAccountStatus = Rx<bool?>(null);
  final Rx<ChartOfAccountsModel?> selectedAccount =
      Rx<ChartOfAccountsModel?>(null);

  // Date filtering
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  @override
  void onInit() {
    super.onInit();
    loadData();

    // Set up search listener
    searchController.addListener(() {
      searchQuery.value = searchController.text;
    });
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// Load all data (accounts and balances)
  Future<void> loadData() async {
    await loadAccounts();
    await loadAccountBalances();
  }

  /// Load all accounts
  Future<void> loadAccounts() async {
    try {
      isLoading.value = true;
      final result = await chartOfAccountsRepository.getAllAccounts();
      result.fold(
        (failure) {
          log('Error loading accounts: ${failure.message}');
          SnackbarUtils.showError(
              'Error', 'Error loading accounts: ${failure.message}');
        },
        (accountsList) {
          accounts.value = accountsList;
          log('Loaded ${accountsList.length} accounts');
        },
      );
    } catch (e) {
      log('Exception loading accounts: $e');
      SnackbarUtils.showError('Error', 'Error loading accounts');
    } finally {
      isLoading.value = false;
    }
  }

  /// Load account balances for all accounts
  Future<void> loadAccountBalances() async {
    try {
      for (final account in accounts) {
        await loadAccountBalance(account.id);
      }
    } catch (e) {
      log('Exception loading account balances: $e');
      SnackbarUtils.showError('Error', 'Error loading account balances');
    }
  }

  /// Load balance for a specific account
  Future<void> loadAccountBalance(String accountId) async {
    try {
      loadingBalances.add(accountId);
      final result =
          await journalEntryRepository.calculateAccountBalance(accountId);
      result.fold(
        (failure) {
          log('Error loading balance for account $accountId: ${failure.message}');
          accountBalances[accountId] = 0.0; // Default to 0 on error
        },
        (balance) {
          accountBalances[accountId] = balance;
          log('Loaded balance for account $accountId: $balance');
        },
      );
    } catch (e) {
      log('Exception loading balance for account $accountId: $e');
      accountBalances[accountId] = 0.0; // Default to 0 on error
    } finally {
      loadingBalances.remove(accountId);
    }
  }

  /// Get filtered accounts based on search and filters
  List<ChartOfAccountsModel> get filteredAccounts {
    var filtered = accounts.toList();

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filtered = filtered.where((account) {
        return account.accountName.toLowerCase().contains(query) ||
            account.accountNumber.toLowerCase().contains(query) ||
            (account.description?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Apply account type filter
    if (selectedAccountType.value != null) {
      filtered = filtered.where((account) {
        return account.accountType == selectedAccountType.value;
      }).toList();
    }

    // Apply account status filter
    if (selectedAccountStatus.value != null) {
      filtered = filtered.where((account) {
        return account.isActive == selectedAccountStatus.value;
      }).toList();
    }

    // Sort by account number
    filtered.sort((a, b) => a.accountNumber.compareTo(b.accountNumber));

    return filtered;
  }

  /// Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Clear search
  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
  }

  /// Clear all filters
  void clearFilters() {
    selectedAccountType.value = null;
    selectedAccountStatus.value = null;
    startDate.value = null;
    endDate.value = null;
    clearSearch();
  }

  /// Apply filters
  void applyFilters({
    AccountType? accountType,
    bool? accountStatus,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    selectedAccountType.value = accountType;
    selectedAccountStatus.value = accountStatus;
    startDate.value = fromDate;
    endDate.value = toDate;
  }

  /// Get account transactions for a specific account using proper journal entry dates
  Future<List<JournalEntryModel>> getAccountTransactions(
      String accountId) async {
    try {
      final result = await generalLedgerFirebaseService.getAccountTransactions(
        accountId,
        limit: 100, // Default limit for general ledger view
      );
      return result.fold(
        (failure) {
          log('Error loading transactions for account $accountId: ${failure.message}');
          SnackbarUtils.showError(
              'Error', 'Error loading transactions: ${failure.message}');
          return <JournalEntryModel>[];
        },
        (journalEntries) {
          log('Loaded ${journalEntries.length} journal entries for account $accountId');
          return journalEntries;
        },
      );
    } catch (e) {
      log('Exception loading transactions for account $accountId: $e');
      SnackbarUtils.showError('Error', 'Error loading transactions');
      return <JournalEntryModel>[];
    }
  }

  /// Get summary statistics
  Map<String, dynamic> get summaryStats {
    final totalAccounts = accounts.length;
    final activeAccounts = accounts.where((a) => a.isActive).length;
    final inactiveAccounts = accounts.where((a) => !a.isActive).length;

    final totalAssets = accounts
        .where((a) => a.category == AccountCategory.assets)
        .map((a) => accountBalances[a.id] ?? 0.0)
        .fold(0.0, (sum, balance) => sum + balance);

    final totalLiabilities = accounts
        .where((a) => a.category == AccountCategory.liabilities)
        .map((a) => accountBalances[a.id] ?? 0.0)
        .fold(0.0, (sum, balance) => sum + balance);

    final totalEquity = accounts
        .where((a) => a.category == AccountCategory.equity)
        .map((a) => accountBalances[a.id] ?? 0.0)
        .fold(0.0, (sum, balance) => sum + balance);

    return {
      'totalAccounts': totalAccounts,
      'activeAccounts': activeAccounts,
      'inactiveAccounts': inactiveAccounts,
      'totalAssets': totalAssets,
      'totalLiabilities': totalLiabilities,
      'totalEquity': totalEquity,
      'balanceCheck': totalAssets - (totalLiabilities + totalEquity),
    };
  }

  /// Get accounts by type
  List<ChartOfAccountsModel> getAccountsByType(AccountType type) {
    return accounts.where((account) => account.accountType == type).toList();
  }

  /// Get accounts with non-zero balances
  List<ChartOfAccountsModel> get accountsWithBalances {
    return accounts.where((account) {
      final balance = accountBalances[account.id] ?? 0.0;
      return balance.abs() > 0.01; // Consider balances > 1 cent as non-zero
    }).toList();
  }

  /// Get accounts with zero balances
  List<ChartOfAccountsModel> get accountsWithZeroBalances {
    return accounts.where((account) {
      final balance = accountBalances[account.id] ?? 0.0;
      return balance.abs() <= 0.01; // Consider balances <= 1 cent as zero
    }).toList();
  }

  /// Refresh specific account balance
  Future<void> refreshAccountBalance(String accountId) async {
    await loadAccountBalance(accountId);
  }

  /// Refresh all account balances
  Future<void> refreshAllBalances() async {
    accountBalances.clear();
    await loadAccountBalances();
  }

  /// Export general ledger data (placeholder for future implementation)
  Future<void> exportGeneralLedger() async {
    try {
      // TODO: Implement Excel export functionality
      SnackbarUtils.showInfo('Info', 'Export functionality coming soon');
    } catch (e) {
      log('Exception exporting general ledger: $e');
      SnackbarUtils.showError('Error', 'Error exporting general ledger');
    }
  }

  /// Get trial balance data
  Map<String, dynamic> get trialBalance {
    final assets = <Map<String, dynamic>>[];
    final liabilities = <Map<String, dynamic>>[];
    final equity = <Map<String, dynamic>>[];
    final revenue = <Map<String, dynamic>>[];
    final expenses = <Map<String, dynamic>>[];

    double totalDebits = 0.0;
    double totalCredits = 0.0;

    for (final account in accounts) {
      final balance = accountBalances[account.id] ?? 0.0;
      if (balance.abs() <= 0.01) continue; // Skip zero balances

      final accountData = {
        'account': account,
        'balance': balance,
        'debit': balance > 0 ? balance : 0.0,
        'credit': balance < 0 ? balance.abs() : 0.0,
      };

      switch (account.category) {
        case AccountCategory.assets:
          assets.add(accountData);
          totalDebits += balance > 0 ? balance : 0.0;
          totalCredits += balance < 0 ? balance.abs() : 0.0;
          break;
        case AccountCategory.liabilities:
          liabilities.add(accountData);
          totalDebits += balance > 0 ? balance : 0.0;
          totalCredits += balance < 0 ? balance.abs() : 0.0;
          break;
        case AccountCategory.equity:
          equity.add(accountData);
          totalDebits += balance > 0 ? balance : 0.0;
          totalCredits += balance < 0 ? balance.abs() : 0.0;
          break;
        case AccountCategory.revenue:
          revenue.add(accountData);
          totalDebits += balance > 0 ? balance : 0.0;
          totalCredits += balance < 0 ? balance.abs() : 0.0;
          break;
        case AccountCategory.expenses:
          expenses.add(accountData);
          totalDebits += balance > 0 ? balance : 0.0;
          totalCredits += balance < 0 ? balance.abs() : 0.0;
          break;
      }
    }

    return {
      'assets': assets,
      'liabilities': liabilities,
      'equity': equity,
      'revenue': revenue,
      'expenses': expenses,
      'totalDebits': totalDebits,
      'totalCredits': totalCredits,
      'isBalanced': (totalDebits - totalCredits).abs() < 0.01,
      'difference': totalDebits - totalCredits,
    };
  }
}
