import 'dart:developer' as dev;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';

import '../../features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import 'account_type_helper_service.dart';

/// Service to identify and clean up corrupted transaction data
/// Handles balance inconsistencies, invalid entries, and data integrity issues
class TransactionDataCleanupService {
  final ChartOfAccountsRepository _accountsRepository;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  TransactionDataCleanupService({
    required ChartOfAccountsRepository accountsRepository,
  }) : _accountsRepository = accountsRepository;

  /// Comprehensive data cleanup and validation
  Future<DataCleanupResult> performDataCleanup(String uid) async {
    dev.log(
        '🧹 Starting comprehensive transaction data cleanup for company: $uid');

    final result = DataCleanupResult();

    try {
      // Step 1: Identify corrupted journal entries
      dev.log('📋 Step 1: Identifying corrupted journal entries...');
      final corruptedEntries = await _identifyCorruptedJournalEntries(uid);
      result.corruptedEntriesFound = corruptedEntries.length;
      dev.log('Found ${corruptedEntries.length} corrupted journal entries');

      // Step 2: Identify invalid account references
      dev.log('🔗 Step 2: Identifying invalid account references...');
      final invalidReferences = await _identifyInvalidAccountReferences(uid);
      result.invalidReferencesFound = invalidReferences.length;
      dev.log('Found ${invalidReferences.length} invalid account references');

      // Step 3: Identify balance inconsistencies
      dev.log('⚖️ Step 3: Identifying balance inconsistencies...');
      final balanceIssues = await _identifyBalanceInconsistencies(uid);
      result.balanceInconsistenciesFound = balanceIssues.length;
      dev.log('Found ${balanceIssues.length} balance inconsistencies');

      // Step 4: Clean up corrupted entries (if requested)
      if (corruptedEntries.isNotEmpty) {
        dev.log('🗑️ Step 4: Cleaning up corrupted entries...');
        final cleanedEntries =
            await _cleanupCorruptedEntries(corruptedEntries, uid);
        result.entriesCleaned = cleanedEntries;
        dev.log('Cleaned up $cleanedEntries corrupted entries');
      }

      // Step 5: Fix balance inconsistencies
      if (balanceIssues.isNotEmpty) {
        dev.log('🔧 Step 5: Fixing balance inconsistencies...');
        final fixedBalances =
            await _fixBalanceInconsistencies(balanceIssues, uid);
        result.balancesFixed = fixedBalances;
        dev.log('Fixed $fixedBalances balance inconsistencies');
      }

      result.success = true;
      dev.log('✅ Data cleanup completed successfully');
    } catch (e) {
      dev.log('❌ Error during data cleanup: $e');
      result.success = false;
      result.errorMessage = e.toString();
    }

    return result;
  }

  /// Identify journal entries with corrupted or invalid data
  Future<List<JournalEntryModel>> _identifyCorruptedJournalEntries(
      String uid) async {
    final corruptedEntries = <JournalEntryModel>[];

    try {
      final entriesSnapshot = await _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .get();

      for (final doc in entriesSnapshot.docs) {
        try {
          final entry = JournalEntryModel.fromFirestore(doc);

          // Check for various corruption indicators
          if (_isEntryCorrupted(entry)) {
            corruptedEntries.add(entry);
            dev.log(
                '🚨 Corrupted entry found: ${entry.entryNumber} - ${entry.description}');
          }
        } catch (e) {
          dev.log('❌ Error parsing journal entry ${doc.id}: $e');
          // Entry is corrupted if it can't be parsed
        }
      }
    } catch (e) {
      dev.log('❌ Error identifying corrupted entries: $e');
    }

    return corruptedEntries;
  }

  /// Check if a journal entry is corrupted
  bool _isEntryCorrupted(JournalEntryModel entry) {
    // Check for invalid amounts (NaN, Infinity, negative when shouldn't be)
    for (final line in entry.lines) {
      if (line.debitAmount.isNaN ||
          line.creditAmount.isNaN ||
          line.debitAmount.isInfinite ||
          line.creditAmount.isInfinite ||
          line.debitAmount < 0 ||
          line.creditAmount < 0) {
        return true;
      }

      // Check for both debit and credit on same line (should be one or the other)
      if (line.debitAmount > 0 && line.creditAmount > 0) {
        return true;
      }

      // Check for zero amounts on both sides
      if (line.debitAmount == 0 && line.creditAmount == 0) {
        return true;
      }
    }

    // Check for unbalanced entries (debits != credits)
    if (!entry.isBalanced) {
      return true;
    }

    // Check for entries with no lines
    if (entry.lines.isEmpty) {
      return true;
    }

    // Check for entries with only one line (violates double-entry)
    if (entry.lines.length < 2) {
      return true;
    }

    // Check for invalid dates (future dates beyond reasonable limit)
    final futureLimit = DateTime.now().add(const Duration(days: 365));
    if (entry.entryDate.isAfter(futureLimit)) {
      return true;
    }

    // Check for extremely old dates (before 1900)
    if (entry.entryDate.isBefore(DateTime(1900))) {
      return true;
    }

    return false;
  }

  /// Identify journal entry lines with invalid account references
  Future<List<String>> _identifyInvalidAccountReferences(String uid) async {
    final invalidReferences = <String>[];

    try {
      // Get all accounts for validation
      final accountsResult = await _accountsRepository.getAccounts();
      final validAccountIds = accountsResult.fold(
        (failure) => <String>[],
        (accounts) => accounts.map((account) => account.id).toSet(),
      );

      final entriesSnapshot = await _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .get();

      for (final doc in entriesSnapshot.docs) {
        try {
          final entry = JournalEntryModel.fromFirestore(doc);

          for (final line in entry.lines) {
            if (!validAccountIds.contains(line.accountId)) {
              invalidReferences.add('${entry.entryNumber}: ${line.accountId}');
              dev.log(
                  '🔗 Invalid account reference: ${line.accountId} in entry ${entry.entryNumber}');
            }
          }
        } catch (e) {
          dev.log('❌ Error checking references in entry ${doc.id}: $e');
        }
      }
    } catch (e) {
      dev.log('❌ Error identifying invalid references: $e');
    }

    return invalidReferences;
  }

  /// Identify accounts with balance inconsistencies
  Future<List<BalanceInconsistency>> _identifyBalanceInconsistencies(
      String uid) async {
    final inconsistencies = <BalanceInconsistency>[];

    try {
      final accountsResult = await _accountsRepository.getAccounts();
      final accounts = accountsResult.fold(
        (failure) => <ChartOfAccountsModel>[],
        (accounts) => accounts,
      );

      for (final account in accounts) {
        try {
          // Calculate balance from journal entries
          final calculatedBalance =
              await _calculateAccountBalanceFromJournalEntries(
                  account.id, uid, account.accountType);

          // Compare with stored balance
          final storedBalance = account.balance;
          final difference = (calculatedBalance - storedBalance).abs();

          // Consider significant if difference > 0.01 (1 cent)
          if (difference > 0.01) {
            inconsistencies.add(BalanceInconsistency(
              accountId: account.id,
              accountName: account.accountName,
              storedBalance: storedBalance,
              calculatedBalance: calculatedBalance,
              difference: difference,
            ));

            dev.log(
                '⚖️ Balance inconsistency: ${account.accountName} - Stored: $storedBalance, Calculated: $calculatedBalance, Diff: $difference');
          }
        } catch (e) {
          dev.log(
              '❌ Error checking balance for account ${account.accountName}: $e');
        }
      }
    } catch (e) {
      dev.log('❌ Error identifying balance inconsistencies: $e');
    }

    return inconsistencies;
  }

  /// Calculate account balance from journal entries using proper accounting principles
  Future<double> _calculateAccountBalanceFromJournalEntries(
      String accountId, String uid, AccountType accountType) async {
    double balance = 0.0;

    try {
      final entriesSnapshot = await _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .where('status', isEqualTo: 'posted')
          .orderBy('createdAt')
          .get();

      for (final doc in entriesSnapshot.docs) {
        try {
          final entry = JournalEntryModel.fromFirestore(doc);

          for (final line in entry.lines) {
            if (line.accountId == accountId) {
              final balanceChange =
                  AccountTypeHelperService.calculateBalanceChange(
                accountType: accountType,
                debitAmount: line.debitAmount,
                creditAmount: line.creditAmount,
              );
              balance += balanceChange;
            }
          }
        } catch (e) {
          dev.log(
              '❌ Error processing entry ${doc.id} for balance calculation: $e');
        }
      }
    } catch (e) {
      dev.log('❌ Error calculating balance from journal entries: $e');
    }

    return balance;
  }

  /// Clean up corrupted journal entries
  Future<int> _cleanupCorruptedEntries(
      List<JournalEntryModel> corruptedEntries, String uid) async {
    int cleanedCount = 0;

    for (final entry in corruptedEntries) {
      try {
        // For now, we'll mark corrupted entries as cancelled rather than deleting them
        // This preserves audit trail while removing them from calculations
        await _firestore.collection('journal_entries').doc(entry.id).update({
          'status': 'cancelled',
          'updatedAt': Timestamp.fromDate(DateTime.now()),
          'cleanupNote':
              'Entry marked as cancelled due to data corruption during cleanup',
        });

        cleanedCount++;
        dev.log(
            '🗑️ Marked corrupted entry as cancelled: ${entry.entryNumber}');
      } catch (e) {
        dev.log('❌ Error cleaning up entry ${entry.entryNumber}: $e');
      }
    }

    return cleanedCount;
  }

  /// Fix balance inconsistencies by updating stored balances
  Future<int> _fixBalanceInconsistencies(
      List<BalanceInconsistency> inconsistencies, String uid) async {
    int fixedCount = 0;

    for (final inconsistency in inconsistencies) {
      try {
        // Update the stored balance to match the calculated balance
        await _firestore
            .collection('chart_of_accounts')
            .where('id', isEqualTo: inconsistency.accountId)
            .where('uid', isEqualTo: uid)
            .get()
            .then((snapshot) async {
          if (snapshot.docs.isNotEmpty) {
            await snapshot.docs.first.reference.update({
              'balance': inconsistency.calculatedBalance,
              'updatedAt': Timestamp.fromDate(DateTime.now()),
              'balanceFixNote':
                  'Balance corrected during data cleanup - was ${inconsistency.storedBalance}, corrected to ${inconsistency.calculatedBalance}',
            });

            fixedCount++;
            dev.log(
                '🔧 Fixed balance for ${inconsistency.accountName}: ${inconsistency.storedBalance} → ${inconsistency.calculatedBalance}');
          }
        });
      } catch (e) {
        dev.log('❌ Error fixing balance for ${inconsistency.accountName}: $e');
      }
    }

    return fixedCount;
  }
}

/// Result of data cleanup operation
class DataCleanupResult {
  bool success = false;
  String? errorMessage;
  int corruptedEntriesFound = 0;
  int invalidReferencesFound = 0;
  int balanceInconsistenciesFound = 0;
  int entriesCleaned = 0;
  int balancesFixed = 0;

  @override
  String toString() {
    return 'DataCleanupResult(success: $success, corrupted: $corruptedEntriesFound, invalid refs: $invalidReferencesFound, balance issues: $balanceInconsistenciesFound, cleaned: $entriesCleaned, fixed: $balancesFixed)';
  }
}

/// Represents a balance inconsistency between stored and calculated values
class BalanceInconsistency {
  final String accountId;
  final String accountName;
  final double storedBalance;
  final double calculatedBalance;
  final double difference;

  BalanceInconsistency({
    required this.accountId,
    required this.accountName,
    required this.storedBalance,
    required this.calculatedBalance,
    required this.difference,
  });
}
