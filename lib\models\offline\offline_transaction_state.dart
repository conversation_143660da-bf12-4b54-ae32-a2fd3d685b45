/// Enumeration for offline transaction states
enum OfflineTransactionState {
  pending('pending'),
  syncing('syncing'),
  synced('synced'),
  conflict('conflict'),
  failed('failed'),
  interrupted('interrupted');
  
  const OfflineTransactionState(this.value);
  final String value;
  
  static OfflineTransactionState fromString(String value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => OfflineTransactionState.pending,
    );
  }
  
  /// Check if the transaction is in a final state
  bool get isFinal => this == synced || this == failed;
  
  /// Check if the transaction can be retried
  bool get canRetry => this == failed || this == interrupted;
  
  /// Check if the transaction is actively being processed
  bool get isActive => this == syncing;
  
  /// Check if the transaction needs user attention
  bool get needsAttention => this == conflict || this == failed;
}

/// Enumeration for sync operation types
enum SyncOperationType {
  voucherCreate('voucher_create'),
  voucherUpdate('voucher_update'),
  journalEntryCreate('journal_entry_create'),
  journalEntryUpdate('journal_entry_update'),
  balanceUpdate('balance_update'),
  expenseCreate('expense_create'),
  depositCreate('deposit_create'),
  loanCreate('loan_create'),
  billCreate('bill_create'),
  chartAccountCreate('chart_account_create'),
  chartAccountUpdate('chart_account_update');
  
  const SyncOperationType(this.value);
  final String value;
  
  static SyncOperationType fromString(String value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => SyncOperationType.voucherCreate,
    );
  }
  
  /// Check if operation is financial (affects balances)
  bool get isFinancial => [
    voucherCreate,
    voucherUpdate,
    journalEntryCreate,
    journalEntryUpdate,
    balanceUpdate,
    expenseCreate,
    depositCreate,
    loanCreate,
  ].contains(this);
  
  /// Check if operation requires atomic transaction
  bool get requiresAtomicTransaction => [
    voucherCreate,
    voucherUpdate,
    journalEntryCreate,
    balanceUpdate,
  ].contains(this);
}

/// Enumeration for conflict resolution strategies
enum ConflictResolutionStrategy {
  lastWriteWins('last_write_wins'),
  userMediated('user_mediated'),
  automaticMerge('automatic_merge'),
  serverAuthoritative('server_authoritative'),
  clientAuthoritative('client_authoritative');
  
  const ConflictResolutionStrategy(this.value);
  final String value;
  
  static ConflictResolutionStrategy fromString(String value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => ConflictResolutionStrategy.userMediated,
    );
  }
}

/// Enumeration for sync priority levels
enum SyncPriority {
  low(1),
  normal(2),
  high(3),
  critical(4);
  
  const SyncPriority(this.value);
  final int value;
  
  static SyncPriority fromInt(int value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => SyncPriority.normal,
    );
  }
  
  /// Check if this priority is higher than another
  bool isHigherThan(SyncPriority other) => value > other.value;
  
  /// Check if this priority is lower than another
  bool isLowerThan(SyncPriority other) => value < other.value;
}
