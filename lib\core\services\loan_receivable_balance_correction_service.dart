import 'dart:developer' as dev;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import 'account_type_helper_service.dart';

/// Service specifically designed to fix loan receivable account balance calculation issues
/// Addresses the critical problem where loan receivable accounts show negative balances
/// when they should show positive balances after debit entries
class LoanReceivableBalanceCorrectionService {
  final ChartOfAccountsRepository _accountsRepository;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  LoanReceivableBalanceCorrectionService({
    required ChartOfAccountsRepository accountsRepository,
  }) : _accountsRepository = accountsRepository;

  /// Fix all loan receivable account balance calculation issues
  Future<LoanReceivableFixResult> fixLoanReceivableBalances(String uid) async {
    dev.log('🔧 Starting loan receivable balance correction for company: $uid');

    final result = LoanReceivableFixResult(uid: uid, fixedAt: DateTime.now());

    try {
      // Step 1: Find loan receivable accounts
      final loanReceivableAccounts = await _findLoanReceivableAccounts(uid);
      result.accountsFound = loanReceivableAccounts.length;

      if (loanReceivableAccounts.isEmpty) {
        result.success = false;
        result.errorMessage = 'No loan receivable accounts found';
        return result;
      }

      dev.log(
          '📊 Found ${loanReceivableAccounts.length} loan receivable accounts');

      // Step 2: Fix each loan receivable account
      for (final account in loanReceivableAccounts) {
        try {
          final accountResult = await _fixSingleAccountBalance(account, uid);
          result.accountsFixed += accountResult.fixed ? 1 : 0;
          result.journalEntriesFixed += accountResult.entriesFixed;
          result.details.add(accountResult);
        } catch (e) {
          result.errors.add('Error fixing account ${account.accountName}: $e');
          dev.log('❌ Error fixing account ${account.accountName}: $e');
        }
      }

      result.success = true;
      dev.log('✅ Loan receivable balance correction completed');
    } catch (e) {
      result.success = false;
      result.errorMessage = e.toString();
      dev.log('❌ Error during loan receivable balance correction: $e');
    }

    return result;
  }

  /// Find all loan receivable accounts
  Future<List<ChartOfAccountsModel>> _findLoanReceivableAccounts(
      String uid) async {
    final accountsResult = await _accountsRepository.getAccounts();

    return accountsResult.fold(
      (failure) {
        dev.log('❌ Failed to load accounts: ${failure.message}');
        return <ChartOfAccountsModel>[];
      },
      (accounts) {
        // Find accounts that are likely loan receivable accounts
        final loanReceivableAccounts = accounts.where((account) {
          final nameContainsLoan =
              account.accountName.toLowerCase().contains('loan');
          final nameContainsReceivable =
              account.accountName.toLowerCase().contains('receivable');
          final isAssetAccount = account.category == AccountCategory.assets;

          return nameContainsLoan && nameContainsReceivable && isAssetAccount;
        }).toList();

        // Also include accounts receivable that might be used for loans
        if (loanReceivableAccounts.isEmpty) {
          final accountsReceivable = accounts
              .where((account) =>
                  account.accountType == AccountType.accountsReceivable &&
                  account.category == AccountCategory.assets)
              .toList();

          dev.log(
              '⚠️ No specific loan receivable accounts found, including ${accountsReceivable.length} accounts receivable');
          return accountsReceivable;
        }

        return loanReceivableAccounts;
      },
    );
  }

  /// Fix balance calculation for a single account
  Future<AccountFixResult> _fixSingleAccountBalance(
      ChartOfAccountsModel account, String uid) async {
    dev.log('🔧 Fixing balance for account: ${account.accountName}');

    final result = AccountFixResult(
      accountId: account.id,
      accountName: account.accountName,
      originalBalance: account.balance,
    );

    try {
      // Get all journal entries for this account in chronological order
      final journalEntries =
          await _getJournalEntriesForAccount(account.id, uid);

      if (journalEntries.isEmpty) {
        dev.log(
            '⚠️ No journal entries found for account: ${account.accountName}');
        result.calculatedBalance = 0.0;
        result.fixed = account.balance != 0.0;

        if (result.fixed) {
          await _updateAccountBalance(account.id, uid, 0.0);
          result.newBalance = 0.0;
        }

        return result;
      }

      // Recalculate running balances chronologically
      double runningBalance = 0.0;
      int entriesFixed = 0;

      for (final entry in journalEntries) {
        final accountLines =
            entry.lines.where((line) => line.accountId == account.id).toList();

        if (accountLines.isEmpty) continue;

        bool entryNeedsUpdate = false;
        final updatedLines = <JournalEntryLineModel>[];

        // Process all lines in the entry
        for (final line in entry.lines) {
          if (line.accountId == account.id) {
            // Calculate correct balance change using proper accounting principles
            final balanceChange =
                AccountTypeHelperService.calculateBalanceChange(
              accountType: account.accountType,
              debitAmount: line.debitAmount,
              creditAmount: line.creditAmount,
            );

            runningBalance += balanceChange;

            dev.log(
                '📝 Entry: ${entry.entryNumber} | Debit: ${line.debitAmount} | Credit: ${line.creditAmount} | Change: $balanceChange | Running: $runningBalance');

            // Check if running balance needs correction
            if (line.runningBalance == null ||
                (line.runningBalance! - runningBalance).abs() > 0.01) {
              entryNeedsUpdate = true;
              updatedLines.add(line.copyWith(runningBalance: runningBalance));

              dev.log(
                  '🔧 Correcting running balance: ${line.runningBalance} → $runningBalance');
            } else {
              updatedLines.add(line);
            }
          } else {
            updatedLines.add(line);
          }
        }

        // Update journal entry if needed
        if (entryNeedsUpdate) {
          await _updateJournalEntryRunningBalances(entry.id, updatedLines);
          entriesFixed++;
        }
      }

      result.calculatedBalance = runningBalance;
      result.entriesFixed = entriesFixed;

      // Check if account balance needs correction
      final balanceDifference = (account.balance - runningBalance).abs();
      if (balanceDifference > 0.01) {
        await _updateAccountBalance(account.id, uid, runningBalance);
        result.fixed = true;
        result.newBalance = runningBalance;

        dev.log(
            '✅ Corrected account balance: ${account.balance} → $runningBalance');
      } else {
        result.newBalance = account.balance;
        dev.log('✅ Account balance is already correct: $runningBalance');
      }
    } catch (e) {
      result.error = e.toString();
      dev.log('❌ Error fixing account ${account.accountName}: $e');
    }

    return result;
  }

  /// Get journal entries for a specific account in chronological order
  Future<List<JournalEntryModel>> _getJournalEntriesForAccount(
      String accountId, String uid) async {
    try {
      final entriesSnapshot = await _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .where('status', isEqualTo: 'posted')
          .orderBy('createdAt') // Use creation order for consistency
          .get();

      final accountEntries = <JournalEntryModel>[];

      for (final doc in entriesSnapshot.docs) {
        try {
          final entry = JournalEntryModel.fromFirestore(doc);

          // Check if this entry has lines for our account
          final hasAccountLine =
              entry.lines.any((line) => line.accountId == accountId);
          if (hasAccountLine) {
            accountEntries.add(entry);
          }
        } catch (e) {
          dev.log('⚠️ Error parsing journal entry ${doc.id}: $e');
        }
      }

      return accountEntries;
    } catch (e) {
      dev.log('❌ Error getting journal entries for account $accountId: $e');
      return [];
    }
  }

  /// Update journal entry running balances
  Future<void> _updateJournalEntryRunningBalances(
      String entryId, List<JournalEntryLineModel> updatedLines) async {
    try {
      await _firestore.collection('journal_entries').doc(entryId).update({
        'lines': updatedLines.map((line) => line.toFirestore()).toList(),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'loanReceivableFixNote':
            'Running balances corrected for loan receivable account',
      });
    } catch (e) {
      dev.log('❌ Error updating journal entry running balances: $e');
      rethrow;
    }
  }

  /// Update account balance
  Future<void> _updateAccountBalance(
      String accountId, String uid, double newBalance) async {
    try {
      await _firestore
          .collection('chart_of_accounts')
          .where('id', isEqualTo: accountId)
          .where('uid', isEqualTo: uid)
          .get()
          .then((snapshot) async {
        if (snapshot.docs.isNotEmpty) {
          await snapshot.docs.first.reference.update({
            'balance': newBalance,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
            'loanReceivableBalanceFixNote':
                'Balance corrected during loan receivable balance fix',
          });
        }
      });
    } catch (e) {
      dev.log('❌ Error updating account balance: $e');
      rethrow;
    }
  }

  /// Validate that the fix worked correctly
  Future<ValidationResult> validateFix(String uid) async {
    dev.log('🔍 Validating loan receivable balance fix for company: $uid');

    final result = ValidationResult();

    try {
      final loanReceivableAccounts = await _findLoanReceivableAccounts(uid);

      for (final account in loanReceivableAccounts) {
        final journalEntries =
            await _getJournalEntriesForAccount(account.id, uid);

        // Calculate expected balance
        double expectedBalance = 0.0;
        for (final entry in journalEntries) {
          for (final line
              in entry.lines.where((l) => l.accountId == account.id)) {
            final balanceChange =
                AccountTypeHelperService.calculateBalanceChange(
              accountType: account.accountType,
              debitAmount: line.debitAmount,
              creditAmount: line.creditAmount,
            );
            expectedBalance += balanceChange;
          }
        }

        // Get current stored balance
        final accountsResult = await _accountsRepository.getAccounts();
        final ChartOfAccountsModel? currentAccount = accountsResult.fold(
          (failure) => null,
          (accounts) {
            try {
              return accounts.firstWhere((acc) => acc.id == account.id);
            } catch (e) {
              return null;
            }
          },
        );

        if (currentAccount != null) {
          final balanceDifference =
              (currentAccount.balance - expectedBalance).abs();

          if (balanceDifference <= 0.01) {
            result.validAccounts++;
            dev.log(
                '✅ Account ${account.accountName}: Balance is correct ($expectedBalance)');
          } else {
            result.invalidAccounts++;
            result.issues.add(
                'Account ${account.accountName}: Expected $expectedBalance, got ${currentAccount.balance}');
            dev.log(
                '❌ Account ${account.accountName}: Balance mismatch - Expected $expectedBalance, got ${currentAccount.balance}');
          }
        }

        result.totalAccounts++;
      }

      result.success = result.invalidAccounts == 0;
    } catch (e) {
      result.success = false;
      result.issues.add('Validation error: $e');
      dev.log('❌ Error during validation: $e');
    }

    return result;
  }
}

/// Result of loan receivable balance fix operation
class LoanReceivableFixResult {
  final String uid;
  final DateTime fixedAt;
  bool success = false;
  String? errorMessage;
  int accountsFound = 0;
  int accountsFixed = 0;
  int journalEntriesFixed = 0;
  List<AccountFixResult> details = [];
  List<String> errors = [];

  LoanReceivableFixResult({required this.uid, required this.fixedAt});

  @override
  String toString() {
    return 'LoanReceivableFixResult(success: $success, found: $accountsFound, fixed: $accountsFixed, entries: $journalEntriesFixed, errors: ${errors.length})';
  }
}

/// Result of fixing a single account
class AccountFixResult {
  final String accountId;
  final String accountName;
  final double originalBalance;
  double calculatedBalance = 0.0;
  double newBalance = 0.0;
  bool fixed = false;
  int entriesFixed = 0;
  String? error;

  AccountFixResult({
    required this.accountId,
    required this.accountName,
    required this.originalBalance,
  });

  @override
  String toString() {
    return 'AccountFixResult($accountName: $originalBalance → $newBalance, fixed: $fixed, entries: $entriesFixed)';
  }
}

/// Result of validation operation
class ValidationResult {
  bool success = false;
  int totalAccounts = 0;
  int validAccounts = 0;
  int invalidAccounts = 0;
  List<String> issues = [];

  @override
  String toString() {
    return 'ValidationResult(success: $success, valid: $validAccounts/$totalAccounts, issues: ${issues.length})';
  }
}
