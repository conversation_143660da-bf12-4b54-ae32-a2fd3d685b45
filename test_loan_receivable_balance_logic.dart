import 'dart:developer' as dev;
import 'lib/models/finance/chart_of_accounts_model.dart';
import 'lib/core/services/account_type_helper_service.dart';

/// Simple test to verify loan receivable balance calculation logic
/// This test specifically addresses the issue where loan receivable accounts
/// show negative balances when they should show positive balances
void main() {
  print('🧪 Testing Loan Receivable Balance Calculation Logic');
  print('=' * 60);
  
  // Test the exact scenario from the screenshot
  testSpecificScenario();
  
  // Test all asset account types
  testAssetAccountTypes();
  
  // Test running balance calculation
  testRunningBalanceCalculation();
  
  // Test journal entry scenario
  testJournalEntryScenario();
  
  print('\n✅ All tests completed');
}

void testSpecificScenario() {
  print('\n🎯 Test 1: Specific Loan Receivable Scenario');
  print('Scenario: Loan Receivable account with Rs. 40,000 debit entry');
  
  // Test the exact case from the screenshot
  const accountType = AccountType.accountsReceivable; // Typical loan receivable type
  const debitAmount = 40000.0;
  const creditAmount = 0.0;
  const initialBalance = 0.0;
  
  print('Initial balance: Rs. ${initialBalance.toStringAsFixed(2)}');
  print('Transaction: Debit Rs. ${debitAmount.toStringAsFixed(2)}');
  
  // Calculate balance change
  final balanceChange = AccountTypeHelperService.calculateBalanceChange(
    accountType: accountType,
    debitAmount: debitAmount,
    creditAmount: creditAmount,
  );
  
  final newBalance = initialBalance + balanceChange;
  
  print('Balance change: ${balanceChange >= 0 ? '+' : ''}Rs. ${balanceChange.toStringAsFixed(2)}');
  print('New balance: ${newBalance >= 0 ? '+' : ''}Rs. ${newBalance.toStringAsFixed(2)}');
  
  // Verify the result
  if (newBalance == 40000.0) {
    print('✅ CORRECT: Asset account shows positive balance after debit');
  } else {
    print('❌ INCORRECT: Expected +Rs. 40,000.00, got ${newBalance >= 0 ? '+' : ''}Rs. ${newBalance.toStringAsFixed(2)}');
  }
  
  // Test the accounting principle
  final isDebitAccount = AccountTypeHelperService.isDebitAccount(accountType);
  print('Is debit account (Asset/Expense): $isDebitAccount');
  
  if (isDebitAccount && balanceChange > 0) {
    print('✅ CORRECT: Debit increases asset account balance');
  } else if (!isDebitAccount) {
    print('❌ INCORRECT: Loan receivable should be a debit account (Asset)');
  } else {
    print('❌ INCORRECT: Debit should increase asset account balance');
  }
}

void testAssetAccountTypes() {
  print('\n🏦 Test 2: All Asset Account Types');
  
  final assetTypes = [
    AccountType.accountsReceivable,
    AccountType.currentAssets,
    AccountType.cash,
    AccountType.bank,
    AccountType.inventory,
    AccountType.fixedAssets,
    AccountType.otherAssets,
  ];
  
  for (final accountType in assetTypes) {
    print('\nTesting: ${accountType.displayName}');
    
    // Test debit (should increase)
    final debitChange = AccountTypeHelperService.calculateBalanceChange(
      accountType: accountType,
      debitAmount: 1000.0,
      creditAmount: 0.0,
    );
    
    // Test credit (should decrease)
    final creditChange = AccountTypeHelperService.calculateBalanceChange(
      accountType: accountType,
      debitAmount: 0.0,
      creditAmount: 1000.0,
    );
    
    print('  Debit Rs. 1,000: ${debitChange >= 0 ? '+' : ''}Rs. ${debitChange.toStringAsFixed(2)}');
    print('  Credit Rs. 1,000: ${creditChange >= 0 ? '+' : ''}Rs. ${creditChange.toStringAsFixed(2)}');
    
    // Verify accounting principles
    if (debitChange == 1000.0 && creditChange == -1000.0) {
      print('  ✅ CORRECT: Asset account behavior');
    } else {
      print('  ❌ INCORRECT: Expected debit +1000, credit -1000');
    }
  }
}

void testRunningBalanceCalculation() {
  print('\n💰 Test 3: Running Balance Calculation');
  
  const accountType = AccountType.accountsReceivable;
  double runningBalance = 0.0;
  
  print('Initial running balance: Rs. ${runningBalance.toStringAsFixed(2)}');
  
  // Transaction 1: Loan approval (debit)
  print('\nTransaction 1: Loan approval - Debit Rs. 40,000');
  final change1 = AccountTypeHelperService.calculateBalanceChange(
    accountType: accountType,
    debitAmount: 40000.0,
    creditAmount: 0.0,
  );
  runningBalance += change1;
  print('Balance change: ${change1 >= 0 ? '+' : ''}Rs. ${change1.toStringAsFixed(2)}');
  print('Running balance: ${runningBalance >= 0 ? '+' : ''}Rs. ${runningBalance.toStringAsFixed(2)}');
  
  // Transaction 2: Partial repayment (credit)
  print('\nTransaction 2: Partial repayment - Credit Rs. 15,000');
  final change2 = AccountTypeHelperService.calculateBalanceChange(
    accountType: accountType,
    debitAmount: 0.0,
    creditAmount: 15000.0,
  );
  runningBalance += change2;
  print('Balance change: ${change2 >= 0 ? '+' : ''}Rs. ${change2.toStringAsFixed(2)}');
  print('Running balance: ${runningBalance >= 0 ? '+' : ''}Rs. ${runningBalance.toStringAsFixed(2)}');
  
  // Transaction 3: Full repayment (credit)
  print('\nTransaction 3: Full repayment - Credit Rs. 25,000');
  final change3 = AccountTypeHelperService.calculateBalanceChange(
    accountType: accountType,
    debitAmount: 0.0,
    creditAmount: 25000.0,
  );
  runningBalance += change3;
  print('Balance change: ${change3 >= 0 ? '+' : ''}Rs. ${change3.toStringAsFixed(2)}');
  print('Running balance: ${runningBalance >= 0 ? '+' : ''}Rs. ${runningBalance.toStringAsFixed(2)}');
  
  // Verify final balance
  if (runningBalance == 0.0) {
    print('✅ CORRECT: Final balance is zero after full repayment');
  } else {
    print('❌ INCORRECT: Expected Rs. 0.00, got ${runningBalance >= 0 ? '+' : ''}Rs. ${runningBalance.toStringAsFixed(2)}');
  }
}

void testJournalEntryScenario() {
  print('\n📝 Test 4: Journal Entry Scenario');
  print('Simulating loan approval journal entry creation');
  
  // Simulate the journal entry for loan approval
  const loanAmount = 40000.0;
  
  print('\nJournal Entry for Loan Approval:');
  print('Amount: Rs. ${loanAmount.toStringAsFixed(2)}');
  
  // Line 1: Debit Loan Receivable (Asset account)
  const loanReceivableType = AccountType.accountsReceivable;
  final receivableChange = AccountTypeHelperService.calculateBalanceChange(
    accountType: loanReceivableType,
    debitAmount: loanAmount,
    creditAmount: 0.0,
  );
  
  print('\nLine 1: Loan Receivable Account');
  print('  Account Type: ${loanReceivableType.displayName} (${loanReceivableType.category.displayName})');
  print('  Debit: Rs. ${loanAmount.toStringAsFixed(2)}');
  print('  Balance Change: ${receivableChange >= 0 ? '+' : ''}Rs. ${receivableChange.toStringAsFixed(2)}');
  
  // Line 2: Credit Cash (Asset account)
  const cashType = AccountType.cash;
  final cashChange = AccountTypeHelperService.calculateBalanceChange(
    accountType: cashType,
    debitAmount: 0.0,
    creditAmount: loanAmount,
  );
  
  print('\nLine 2: Cash Account');
  print('  Account Type: ${cashType.displayName} (${cashType.category.displayName})');
  print('  Credit: Rs. ${loanAmount.toStringAsFixed(2)}');
  print('  Balance Change: ${cashChange >= 0 ? '+' : ''}Rs. ${cashChange.toStringAsFixed(2)}');
  
  // Verify double-entry bookkeeping
  print('\nDouble-Entry Verification:');
  print('  Total Debits: Rs. ${loanAmount.toStringAsFixed(2)}');
  print('  Total Credits: Rs. ${loanAmount.toStringAsFixed(2)}');
  print('  Balanced: ${loanAmount == loanAmount ? '✅ YES' : '❌ NO'}');
  
  // Verify balance changes
  print('\nBalance Change Verification:');
  if (receivableChange == 40000.0) {
    print('  ✅ Loan Receivable: Correctly increases by Rs. 40,000');
  } else {
    print('  ❌ Loan Receivable: Expected +Rs. 40,000, got ${receivableChange >= 0 ? '+' : ''}Rs. ${receivableChange.toStringAsFixed(2)}');
  }
  
  if (cashChange == -40000.0) {
    print('  ✅ Cash: Correctly decreases by Rs. 40,000');
  } else {
    print('  ❌ Cash: Expected -Rs. 40,000, got ${cashChange >= 0 ? '+' : ''}Rs. ${cashChange.toStringAsFixed(2)}');
  }
  
  // Simulate account balances after transaction
  print('\nSimulated Account Balances After Transaction:');
  
  double loanReceivableBalance = 0.0; // Assuming starting balance of 0
  double cashBalance = 100000.0; // Assuming starting cash balance
  
  loanReceivableBalance += receivableChange;
  cashBalance += cashChange;
  
  print('  Loan Receivable Balance: ${loanReceivableBalance >= 0 ? '+' : ''}Rs. ${loanReceivableBalance.toStringAsFixed(2)}');
  print('  Cash Balance: ${cashBalance >= 0 ? '+' : ''}Rs. ${cashBalance.toStringAsFixed(2)}');
  
  if (loanReceivableBalance > 0) {
    print('  ✅ CORRECT: Loan Receivable shows positive balance');
  } else {
    print('  ❌ INCORRECT: Loan Receivable should show positive balance');
  }
}

/// Test the specific issue from the screenshot
void testScreenshotScenario() {
  print('\n📸 Test 5: Screenshot Scenario Analysis');
  print('Analyzing the exact scenario from the provided screenshot');
  
  // From the screenshot:
  // - Account: Loan Receivable
  // - Transaction: Debit Rs. 40,000.00
  // - Current Balance: -Rs. 40,000.00 (INCORRECT)
  // - Running Balance: -Rs. 40,000.00 (INCORRECT)
  
  print('\nScreenshot Data:');
  print('  Account: Loan Receivable');
  print('  Transaction Type: Debit');
  print('  Transaction Amount: Rs. 40,000.00');
  print('  Displayed Balance: -Rs. 40,000.00 (INCORRECT)');
  print('  Expected Balance: +Rs. 40,000.00');
  
  // Test what the balance should be
  const accountType = AccountType.accountsReceivable;
  final balanceChange = AccountTypeHelperService.calculateBalanceChange(
    accountType: accountType,
    debitAmount: 40000.0,
    creditAmount: 0.0,
  );
  
  print('\nCorrect Calculation:');
  print('  Account Type: ${accountType.displayName}');
  print('  Category: ${accountType.category.displayName}');
  print('  Is Debit Account: ${AccountTypeHelperService.isDebitAccount(accountType)}');
  print('  Balance Change for Debit Rs. 40,000: ${balanceChange >= 0 ? '+' : ''}Rs. ${balanceChange.toStringAsFixed(2)}');
  
  if (balanceChange == 40000.0) {
    print('  ✅ LOGIC IS CORRECT: Balance calculation returns +Rs. 40,000');
    print('  🔍 ISSUE: The problem is likely in balance storage, retrieval, or display');
  } else {
    print('  ❌ LOGIC IS INCORRECT: Balance calculation is wrong');
  }
  
  print('\nPossible Root Causes:');
  print('  1. Account is not configured as Asset type');
  print('  2. Balance calculation logic has a bug');
  print('  3. Running balance storage is incorrect');
  print('  4. Balance display logic inverts the sign');
  print('  5. Initial account balance was set incorrectly');
}
