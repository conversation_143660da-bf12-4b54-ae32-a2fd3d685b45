import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/repositories/asset/asset_repository.dart';
import 'package:logestics/models/asset/asset_maintenance_model.dart';
import 'package:intl/intl.dart';

class MaintenanceHistoryController extends GetxController {
  final AssetRepository _assetRepository = Get.find<AssetRepository>();

  // Observable variables
  final RxList<AssetMaintenanceModel> maintenanceRecords =
      <AssetMaintenanceModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  // Asset information
  String assetId = '';

  void initializeForAsset(String assetId) {
    this.assetId = assetId;
    loadMaintenanceRecords();
  }

  Future<void> loadMaintenanceRecords() async {
    if (assetId.isEmpty) return;

    isLoading.value = true;
    error.value = '';

    try {
      final result = await _assetRepository.getMaintenanceByAssetId(assetId);

      result.fold(
        (failure) {
          error.value = failure.message;
          log('Error loading maintenance records: ${failure.message}');
        },
        (records) {
          maintenanceRecords.value = records;
          log('Successfully loaded ${records.length} maintenance records');
        },
      );
    } catch (e) {
      error.value = 'An unexpected error occurred';
      log('Unexpected error loading maintenance records: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteMaintenance(String maintenanceId) async {
    try {
      final result = await _assetRepository.deleteMaintenance(maintenanceId);

      result.fold(
        (failure) {
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        },
        (success) {
          Get.snackbar(
            'Success',
            'Maintenance record deleted successfully',
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          loadMaintenanceRecords(); // Refresh the list
        },
      );
    } catch (e) {
      log('Error deleting maintenance: $e');
      Get.snackbar(
        'Error',
        'An unexpected error occurred while deleting the maintenance record',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Computed properties
  double get totalMaintenanceCost {
    return maintenanceRecords.fold(0.0, (sum, record) => sum + record.cost);
  }

  double get averageMaintenanceCost {
    if (maintenanceRecords.isEmpty) return 0.0;
    return totalMaintenanceCost / maintenanceRecords.length;
  }

  String get lastMaintenanceDate {
    if (maintenanceRecords.isEmpty) return 'Never';

    // Records are already sorted by date (descending) from the service
    final lastMaintenance = maintenanceRecords.first;
    final now = DateTime.now();
    final difference = now.difference(lastMaintenance.maintenanceDate).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '$difference days ago';
    } else if (difference < 30) {
      final weeks = (difference / 7).floor();
      return weeks == 1 ? '1 week ago' : '$weeks weeks ago';
    } else if (difference < 365) {
      final months = (difference / 30).floor();
      return months == 1 ? '1 month ago' : '$months months ago';
    } else {
      final years = (difference / 365).floor();
      return years == 1 ? '1 year ago' : '$years years ago';
    }
  }

  List<AssetMaintenanceModel> getMaintenanceByType(String type) {
    return maintenanceRecords
        .where((record) => record.maintenanceType == type)
        .toList();
  }

  double getMaintenanceCostByType(String type) {
    return getMaintenanceByType(type)
        .fold(0.0, (sum, record) => sum + record.cost);
  }

  Map<String, double> getMaintenanceCostByMonth() {
    final Map<String, double> costByMonth = {};

    for (final record in maintenanceRecords) {
      final monthKey = DateFormat('MMM yyyy').format(record.maintenanceDate);
      costByMonth[monthKey] = (costByMonth[monthKey] ?? 0.0) + record.cost;
    }

    return costByMonth;
  }

  Map<String, int> getMaintenanceCountByType() {
    final Map<String, int> countByType = {};

    for (final record in maintenanceRecords) {
      countByType[record.maintenanceType] =
          (countByType[record.maintenanceType] ?? 0) + 1;
    }

    return countByType;
  }

  String formatCurrency(double amount) {
    final formatter = NumberFormat('#,##0.00');
    return formatter.format(amount);
  }

  String formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  String formatDateWithTime(DateTime date) {
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }

  // Refresh method for pull-to-refresh
  @override
  Future<void> refresh() async {
    await loadMaintenanceRecords();
  }

  // Search and filter methods
  List<AssetMaintenanceModel> searchMaintenanceRecords(String query) {
    if (query.isEmpty) return maintenanceRecords;

    final lowercaseQuery = query.toLowerCase();
    return maintenanceRecords.where((record) {
      return record.description.toLowerCase().contains(lowercaseQuery) ||
          record.maintenanceType.toLowerCase().contains(lowercaseQuery) ||
          record.performedBy.toLowerCase().contains(lowercaseQuery) ||
          record.notes.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  List<AssetMaintenanceModel> filterMaintenanceByDateRange(
      DateTime startDate, DateTime endDate) {
    return maintenanceRecords.where((record) {
      final recordDate = DateTime(
        record.maintenanceDate.year,
        record.maintenanceDate.month,
        record.maintenanceDate.day,
      );
      final start = DateTime(startDate.year, startDate.month, startDate.day);
      final end = DateTime(endDate.year, endDate.month, endDate.day);

      return recordDate.isAfter(start.subtract(const Duration(days: 1))) &&
          recordDate.isBefore(end.add(const Duration(days: 1)));
    }).toList();
  }

  List<AssetMaintenanceModel> filterMaintenanceByCostRange(
      double minCost, double maxCost) {
    return maintenanceRecords.where((record) {
      return record.cost >= minCost && record.cost <= maxCost;
    }).toList();
  }

  // Statistics methods
  double getMaintenanceTrend() {
    if (maintenanceRecords.length < 2) return 0.0;

    // Calculate trend based on last 6 months vs previous 6 months
    final now = DateTime.now();
    final sixMonthsAgo = DateTime(now.year, now.month - 6, now.day);
    final twelveMonthsAgo = DateTime(now.year, now.month - 12, now.day);

    final recentCost = maintenanceRecords
        .where((record) => record.maintenanceDate.isAfter(sixMonthsAgo))
        .fold(0.0, (sum, record) => sum + record.cost);

    final previousCost = maintenanceRecords
        .where((record) =>
            record.maintenanceDate.isAfter(twelveMonthsAgo) &&
            record.maintenanceDate.isBefore(sixMonthsAgo))
        .fold(0.0, (sum, record) => sum + record.cost);

    if (previousCost == 0) return recentCost > 0 ? 100.0 : 0.0;

    return ((recentCost - previousCost) / previousCost) * 100;
  }

  int getDaysUntilNextMaintenance() {
    if (maintenanceRecords.isEmpty) return -1;

    // Simple logic: assume maintenance every 90 days for preventive
    final lastPreventive = maintenanceRecords
        .where((record) => record.maintenanceType == 'Preventive')
        .firstOrNull;

    if (lastPreventive == null) return 90; // No preventive maintenance yet

    final nextDue =
        lastPreventive.maintenanceDate.add(const Duration(days: 90));
    final now = DateTime.now();

    return nextDue.difference(now).inDays;
  }

  bool get isMaintenanceOverdue {
    final daysUntilNext = getDaysUntilNextMaintenance();
    return daysUntilNext < 0;
  }

  bool get isMaintenanceDueSoon {
    final daysUntilNext = getDaysUntilNextMaintenance();
    return daysUntilNext >= 0 && daysUntilNext <= 7;
  }
}
