import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/finance/broker_model.dart';

class BrokerPaymentFirebaseService {
  late FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  BrokerPaymentFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  /// Create a new broker payment
  Future<void> createBrokerPayment(BrokerPaymentModel payment) async {
    log('Creating broker payment: ${payment.id}');
    try {
      final paymentRef = _firestore
          .collection(AppCollection.brokerPaymentsCollection)
          .doc(payment.id);

      final paymentData = payment.toJson();
      paymentData['uid'] = _uid; // Ensure current user's UID

      await paymentRef.set(paymentData);
      log('Successfully created broker payment: ${payment.id}');
    } catch (e) {
      log('Error creating broker payment: $e');
      rethrow;
    }
  }

  /// Get all broker payments for current user
  Future<List<BrokerPaymentModel>> getBrokerPayments() async {
    log('Fetching broker payments for user: $_uid');
    try {
      final querySnapshot = await _firestore
          .collection(AppCollection.brokerPaymentsCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => BrokerPaymentModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      log('Error fetching broker payments: $e');
      rethrow;
    }
  }

  /// Get broker payments for a specific broker
  Future<List<BrokerPaymentModel>> getBrokerPaymentsByBrokerId(
      String brokerId) async {
    log('Fetching payments for broker: $brokerId');
    try {
      final querySnapshot = await _firestore
          .collection(AppCollection.brokerPaymentsCollection)
          .where('uid', isEqualTo: _uid)
          .where('brokerId', isEqualTo: brokerId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => BrokerPaymentModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      log('Error fetching broker payments by broker ID: $e');
      rethrow;
    }
  }

  /// Update a broker payment
  Future<void> updateBrokerPayment(BrokerPaymentModel payment) async {
    log('Updating broker payment: ${payment.id}');
    try {
      final paymentRef = _firestore
          .collection(AppCollection.brokerPaymentsCollection)
          .doc(payment.id);

      final paymentData = payment.toJson();
      paymentData['uid'] = _uid; // Ensure current user's UID

      await paymentRef.update(paymentData);
      log('Successfully updated broker payment: ${payment.id}');
    } catch (e) {
      log('Error updating broker payment: $e');
      rethrow;
    }
  }

  /// Delete a broker payment
  Future<void> deleteBrokerPayment(String paymentId) async {
    log('Deleting broker payment: $paymentId');
    try {
      await _firestore
          .collection(AppCollection.brokerPaymentsCollection)
          .doc(paymentId)
          .delete();
      log('Successfully deleted broker payment: $paymentId');
    } catch (e) {
      log('Error deleting broker payment: $e');
      rethrow;
    }
  }

  /// Listen to broker payments in real-time
  Stream<List<BrokerPaymentModel>> listenToBrokerPayments() {
    log('Setting up real-time listener for broker payments');
    return _firestore
        .collection(AppCollection.brokerPaymentsCollection)
        .where('uid', isEqualTo: _uid)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => BrokerPaymentModel.fromJson(doc.data()))
            .toList());
  }

  /// Listen to broker payments for a specific broker
  Stream<List<BrokerPaymentModel>> listenToBrokerPaymentsByBrokerId(
      String brokerId) {
    log('Setting up real-time listener for broker payments: $brokerId');
    return _firestore
        .collection(AppCollection.brokerPaymentsCollection)
        .where('uid', isEqualTo: _uid)
        .where('brokerId', isEqualTo: brokerId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => BrokerPaymentModel.fromJson(doc.data()))
            .toList());
  }

  /// Create a broker transaction record
  Future<void> createBrokerTransaction(
      BrokerTransactionModel transaction) async {
    log('Creating broker transaction: ${transaction.id}');
    try {
      final transactionRef = _firestore
          .collection(AppCollection.brokerTransactionsCollection)
          .doc(transaction.id);

      final transactionData = transaction.toJson();
      transactionData['uid'] = _uid; // Ensure current user's UID

      await transactionRef.set(transactionData);
      log('Successfully created broker transaction: ${transaction.id}');
    } catch (e) {
      log('Error creating broker transaction: $e');
      rethrow;
    }
  }

  /// Get broker transactions for a specific broker
  Future<List<BrokerTransactionModel>> getBrokerTransactionsByBrokerId(
      String brokerId) async {
    log('Fetching transactions for broker: $brokerId');
    try {
      final querySnapshot = await _firestore
          .collection(AppCollection.brokerTransactionsCollection)
          .where('uid', isEqualTo: _uid)
          .where('brokerId', isEqualTo: brokerId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => BrokerTransactionModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      log('Error fetching broker transactions: $e');
      rethrow;
    }
  }

  /// Listen to broker transactions for a specific broker
  Stream<List<BrokerTransactionModel>> listenToBrokerTransactionsByBrokerId(
      String brokerId) {
    log('Setting up real-time listener for broker transactions: $brokerId');
    return _firestore
        .collection(AppCollection.brokerTransactionsCollection)
        .where('uid', isEqualTo: _uid)
        .where('brokerId', isEqualTo: brokerId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => BrokerTransactionModel.fromJson(doc.data()))
            .toList());
  }

  /// Calculate broker balance (total fees - total payments)
  Future<double> calculateBrokerBalance(String brokerId) async {
    log('Calculating balance for broker: $brokerId');
    try {
      final transactions = await getBrokerTransactionsByBrokerId(brokerId);

      double totalFees = 0.0;
      double totalPayments = 0.0;

      for (final transaction in transactions) {
        if (transaction.type == BrokerTransactionType.fee) {
          totalFees += transaction.amount;
        } else if (transaction.type == BrokerTransactionType.payment) {
          totalPayments += transaction.amount;
        }
      }

      final balance = totalFees - totalPayments;
      log('Broker $brokerId balance: $balance (Fees: $totalFees, Payments: $totalPayments)');
      return balance;
    } catch (e) {
      log('Error calculating broker balance: $e');
      rethrow;
    }
  }

  /// Get broker financial summary
  Future<Map<String, dynamic>> getBrokerFinancialSummary(
      String brokerId) async {
    log('Getting financial summary for broker: $brokerId');
    try {
      final transactions = await getBrokerTransactionsByBrokerId(brokerId);
      final payments = await getBrokerPaymentsByBrokerId(brokerId);

      double totalFees = 0.0;
      double totalPayments = 0.0;
      int feeTransactionCount = 0;
      int paymentTransactionCount = 0;

      for (final transaction in transactions) {
        if (transaction.type == BrokerTransactionType.fee) {
          totalFees += transaction.amount;
          feeTransactionCount++;
        } else if (transaction.type == BrokerTransactionType.payment) {
          totalPayments += transaction.amount;
          paymentTransactionCount++;
        }
      }

      final balance = totalFees - totalPayments;

      return {
        'totalFees': totalFees,
        'totalPayments': totalPayments,
        'balance': balance,
        'feeTransactionCount': feeTransactionCount,
        'paymentTransactionCount': paymentTransactionCount,
        'totalTransactionCount': transactions.length,
        'lastTransactionDate': transactions.isNotEmpty
            ? transactions.first.transactionDate.toIso8601String()
            : null,
        'lastPaymentDate': payments.isNotEmpty
            ? payments.first.paymentDate.toIso8601String()
            : null,
      };
    } catch (e) {
      log('Error getting broker financial summary: $e');
      rethrow;
    }
  }
}
