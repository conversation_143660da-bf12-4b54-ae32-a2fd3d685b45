/// Model for KM range-based rate calculations
/// Supports tiered rate structures where different distance ranges have different rates
class KmRangeRateModel {
  final int fromKm;
  final int toKm;
  final double rate;
  final String? description;
  final bool
      isPerKmRate; // New: indicates if rate should be multiplied by distance

  KmRangeRateModel({
    required this.fromKm,
    required this.toKm,
    required this.rate,
    this.description,
    this.isPerKmRate = false, // Default to fixed rate
  });

  /// Check if a distance falls within this range
  bool containsDistance(double distanceKm) {
    return distanceKm >= fromKm && distanceKm <= toKm;
  }

  /// Get the range as a string representation
  String get rangeString => '$fromKm-$toKm KM';

  /// Factory constructor from JSON
  factory KmRangeRateModel.fromJson(Map<String, dynamic> json) {
    return KmRangeRateModel(
      fromKm: json['fromKm']?.toInt() ?? 0,
      toKm: json['toKm']?.toInt() ?? 0,
      rate: (json['rate'] ?? 0.0).toDouble(),
      description: json['description']?.toString(),
      isPerKmRate: json['isPerKmRate'] ?? false,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'fromKm': fromKm,
      'toKm': toKm,
      'rate': rate,
      'description': description,
      'isPerKmRate': isPerKmRate,
    };
  }

  /// Create a copy with updated fields
  KmRangeRateModel copyWith({
    int? fromKm,
    int? toKm,
    double? rate,
    String? description,
    bool? isPerKmRate,
  }) {
    return KmRangeRateModel(
      fromKm: fromKm ?? this.fromKm,
      toKm: toKm ?? this.toKm,
      rate: rate ?? this.rate,
      description: description ?? this.description,
      isPerKmRate: isPerKmRate ?? this.isPerKmRate,
    );
  }

  @override
  String toString() {
    return 'KmRangeRateModel(fromKm: $fromKm, toKm: $toKm, rate: $rate, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is KmRangeRateModel &&
        other.fromKm == fromKm &&
        other.toKm == toKm &&
        other.rate == rate &&
        other.description == description;
  }

  @override
  int get hashCode {
    return fromKm.hashCode ^
        toKm.hashCode ^
        rate.hashCode ^
        description.hashCode;
  }
}

/// Service for managing KM range-based rate calculations
class KmRangeRateService {
  /// Find the applicable rate for a given distance from a list of ranges
  static KmRangeRateModel? findRateForDistance(
    List<KmRangeRateModel> ranges,
    double distanceKm,
  ) {
    for (final range in ranges) {
      if (range.containsDistance(distanceKm)) {
        return range;
      }
    }
    return null;
  }

  /// Get the rate value for a given distance
  static double? getRateForDistance(
    List<KmRangeRateModel> ranges,
    double distanceKm,
  ) {
    final range = findRateForDistance(ranges, distanceKm);
    return range?.rate;
  }

  /// Validate that ranges don't overlap and cover all necessary distances
  static List<String> validateRanges(List<KmRangeRateModel> ranges) {
    final errors = <String>[];

    if (ranges.isEmpty) {
      errors.add('No KM ranges defined');
      return errors;
    }

    // Sort ranges by fromKm
    final sortedRanges = List<KmRangeRateModel>.from(ranges)
      ..sort((a, b) => a.fromKm.compareTo(b.fromKm));

    // Check for overlaps and gaps
    for (int i = 0; i < sortedRanges.length; i++) {
      final current = sortedRanges[i];

      // Validate individual range
      if (current.fromKm >= current.toKm) {
        errors.add(
            'Invalid range: ${current.rangeString} - fromKm must be less than toKm');
      }

      if (current.rate < 0) {
        errors.add(
            'Invalid rate for range ${current.rangeString}: rate cannot be negative');
      }

      // Check for overlaps with next range
      if (i < sortedRanges.length - 1) {
        final next = sortedRanges[i + 1];

        if (current.toKm >= next.fromKm) {
          errors.add(
              'Overlapping ranges: ${current.rangeString} and ${next.rangeString}');
        }

        // Check for gaps (optional - might be intentional)
        if (current.toKm + 1 < next.fromKm) {
          errors.add(
              'Gap between ranges: ${current.rangeString} and ${next.rangeString}');
        }
      }
    }

    return errors;
  }

  /// Create a formula function that can be used in formula expressions
  /// Returns a function name that can be used in formulas like: KMRATE(distanceInKilometers, rangeId)
  static String createKmRateFunction(
      List<KmRangeRateModel> ranges, String rangeId) {
    return 'KMRATE_$rangeId';
  }

  /// Parse and evaluate a KM rate function call
  /// Example: KMRATE_STANDARD(150) would return the rate for 150 KM
  static double? evaluateKmRateFunction(
    String functionCall,
    double distanceKm,
    Map<String, List<KmRangeRateModel>> rangeDefinitions,
  ) {
    // Parse function call like "KMRATE_STANDARD(150)"
    final regex = RegExp(r'KMRATE_(\w+)\s*\(\s*([^)]+)\s*\)');
    final match = regex.firstMatch(functionCall);

    if (match == null) return null;

    final rangeId = match.group(1);
    final distanceStr = match.group(2);

    if (rangeId == null || distanceStr == null) return null;

    final ranges = rangeDefinitions[rangeId];
    if (ranges == null) return null;

    try {
      final distance = double.parse(distanceStr);
      return getRateForDistance(ranges, distance);
    } catch (e) {
      return null;
    }
  }

  /// Create default KM ranges based on the reference rate structure
  static List<KmRangeRateModel> createDefaultRanges({
    double baseRate = 1196.38,
    double increasedRate = 1472.47,
    double perKmRate = 7.68,
    double increasedPerKmRate = 9.45,
  }) {
    return [
      // Fixed rate ranges (1-160 KM)
      KmRangeRateModel(
          fromKm: 1,
          toKm: 40,
          rate: baseRate,
          description: 'Base rate 1-40 KM',
          isPerKmRate: false),
      KmRangeRateModel(
          fromKm: 41,
          toKm: 80,
          rate: baseRate,
          description: 'Base rate 41-80 KM',
          isPerKmRate: false),
      KmRangeRateModel(
          fromKm: 81,
          toKm: 120,
          rate: baseRate,
          description: 'Base rate 81-120 KM',
          isPerKmRate: false),
      KmRangeRateModel(
          fromKm: 121,
          toKm: 160,
          rate: perKmRate,
          description: 'Per KM rate 121-160 KM',
          isPerKmRate: true),

      // Extended ranges can be added as needed
      KmRangeRateModel(
          fromKm: 161,
          toKm: 200,
          rate: perKmRate,
          description: 'Per KM rate 161-200 KM',
          isPerKmRate: true),
      KmRangeRateModel(
          fromKm: 201,
          toKm: 300,
          rate: perKmRate,
          description: 'Per KM rate 201-300 KM',
          isPerKmRate: true),
      KmRangeRateModel(
          fromKm: 301,
          toKm: 500,
          rate: perKmRate,
          description: 'Per KM rate 301-500 KM',
          isPerKmRate: true),
      KmRangeRateModel(
          fromKm: 501,
          toKm: 1000,
          rate: perKmRate,
          description: 'Per KM rate 501-1000 KM',
          isPerKmRate: true),
      KmRangeRateModel(
          fromKm: 1001,
          toKm: 3000,
          rate: perKmRate,
          description: 'Per KM rate 1001-3000 KM',
          isPerKmRate: true),
    ];
  }
}
