import 'dart:async';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'connectivity_service.dart';

/// Service to manage offline state and coordinate offline operations
class OfflineStateService extends GetxController {
  static OfflineStateService get instance => Get.find<OfflineStateService>();
  
  // Reactive state variables
  final RxBool _isOfflineMode = false.obs;
  final RxInt _pendingOperations = 0.obs;
  final RxString _lastSyncTime = ''.obs;
  final RxBool _isSyncing = false.obs;
  final RxString _syncStatus = 'idle'.obs;
  final RxDouble _syncProgress = 0.0.obs;
  
  // Hive boxes for local storage
  Box<Map>? _syncQueueBox;
  Box<Map>? _offlineDataBox;
  Box<String>? _metadataBox;
  
  // Public getters
  bool get isOfflineMode => _isOfflineMode.value;
  int get pendingOperations => _pendingOperations.value;
  String get lastSyncTime => _lastSyncTime.value;
  bool get isSyncing => _isSyncing.value;
  String get syncStatus => _syncStatus.value;
  double get syncProgress => _syncProgress.value;
  
  // Streams for reactive UI
  Stream<bool> get offlineModeStream => _isOfflineMode.stream;
  Stream<int> get pendingOperationsStream => _pendingOperations.stream;
  Stream<bool> get syncingStream => _isSyncing.stream;
  Stream<String> get syncStatusStream => _syncStatus.stream;
  Stream<double> get syncProgressStream => _syncProgress.stream;
  
  @override
  void onInit() {
    super.onInit();
    _initializeOfflineState();
    _listenToConnectivity();
  }
  
  @override
  void onClose() {
    _syncQueueBox?.close();
    _offlineDataBox?.close();
    _metadataBox?.close();
    super.onClose();
  }
  
  /// Initialize offline state and local storage
  Future<void> _initializeOfflineState() async {
    try {
      // Initialize Hive boxes
      await _initializeHiveBoxes();
      
      // Load saved state
      await _loadSavedState();
      
      // Update pending operations count
      await _updatePendingOperationsCount();
      
      log('OfflineStateService: Initialized successfully');
    } catch (e) {
      log('OfflineStateService: Failed to initialize: $e');
    }
  }
  
  /// Initialize Hive boxes for local storage
  Future<void> _initializeHiveBoxes() async {
    try {
      _syncQueueBox = await Hive.openBox<Map>('sync_queue');
      _offlineDataBox = await Hive.openBox<Map>('offline_data');
      _metadataBox = await Hive.openBox<String>('offline_metadata');
      
      log('OfflineStateService: Hive boxes initialized');
    } catch (e) {
      log('OfflineStateService: Failed to initialize Hive boxes: $e');
      rethrow;
    }
  }
  
  /// Load saved state from local storage
  Future<void> _loadSavedState() async {
    try {
      // Load last sync time
      final lastSync = _metadataBox?.get('last_sync_time');
      if (lastSync != null) {
        _lastSyncTime.value = lastSync;
      }
      
      // Load offline mode state
      final offlineMode = _metadataBox?.get('offline_mode');
      if (offlineMode != null) {
        _isOfflineMode.value = offlineMode == 'true';
      }
      
      log('OfflineStateService: Saved state loaded');
    } catch (e) {
      log('OfflineStateService: Failed to load saved state: $e');
    }
  }
  
  /// Listen to connectivity changes
  void _listenToConnectivity() {
    final connectivityService = Get.find<ConnectivityService>();
    connectivityService.statusStream.listen(_handleConnectivityChange);
    
    // Set initial state based on current connectivity
    _handleConnectivityChange(connectivityService.status);
  }
  
  /// Handle connectivity status changes
  void _handleConnectivityChange(ConnectivityStatus status) {
    final wasOffline = _isOfflineMode.value;
    _isOfflineMode.value = status == ConnectivityStatus.offline;
    
    // Save offline mode state
    _metadataBox?.put('offline_mode', _isOfflineMode.value.toString());
    
    if (wasOffline && !_isOfflineMode.value) {
      // Coming back online
      onConnectivityRestored();
    } else if (!wasOffline && _isOfflineMode.value) {
      // Going offline
      onConnectivityLost();
    }
  }
  
  /// Called when connectivity is restored
  void onConnectivityRestored() {
    log('OfflineStateService: Connectivity restored - preparing for sync');
    
    // Update sync status
    _syncStatus.value = 'preparing';
    
    // Trigger auto-sync if there are pending operations
    if (_pendingOperations.value > 0) {
      _triggerAutoSync();
    }
  }
  
  /// Called when connectivity is lost
  void onConnectivityLost() {
    log('OfflineStateService: Connectivity lost - entering offline mode');
    
    // Update sync status
    _syncStatus.value = 'offline';
    _isSyncing.value = false;
    _syncProgress.value = 0.0;
  }
  
  /// Update pending operations count
  Future<void> _updatePendingOperationsCount() async {
    try {
      final count = _syncQueueBox?.length ?? 0;
      _pendingOperations.value = count;
      log('OfflineStateService: Pending operations count updated: $count');
    } catch (e) {
      log('OfflineStateService: Failed to update pending operations count: $e');
    }
  }
  
  /// Add operation to sync queue
  Future<void> addToSyncQueue({
    required String operationType,
    required Map<String, dynamic> data,
    List<String> dependencies = const [],
  }) async {
    try {
      final queueEntry = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'operationType': operationType,
        'data': data,
        'state': 'pending',
        'createdAt': DateTime.now().toIso8601String(),
        'dependencies': dependencies,
        'retryCount': 0,
      };
      
      await _syncQueueBox?.add(queueEntry);
      await _updatePendingOperationsCount();
      
      log('OfflineStateService: Added operation to sync queue: $operationType');
    } catch (e) {
      log('OfflineStateService: Failed to add operation to sync queue: $e');
      rethrow;
    }
  }
  
  /// Get pending operations from sync queue
  List<Map<String, dynamic>> getPendingOperations() {
    try {
      return _syncQueueBox?.values
          .where((entry) => entry['state'] == 'pending')
          .cast<Map<String, dynamic>>()
          .toList() ?? [];
    } catch (e) {
      log('OfflineStateService: Failed to get pending operations: $e');
      return [];
    }
  }
  
  /// Update sync progress
  void updateSyncProgress(double progress, String status) {
    _syncProgress.value = progress;
    _syncStatus.value = status;
    
    if (progress >= 1.0) {
      _isSyncing.value = false;
      _lastSyncTime.value = DateTime.now().toIso8601String();
      _metadataBox?.put('last_sync_time', _lastSyncTime.value);
    }
  }
  
  /// Start sync operation
  void startSync() {
    _isSyncing.value = true;
    _syncStatus.value = 'syncing';
    _syncProgress.value = 0.0;
  }
  
  /// Complete sync operation
  void completeSync() {
    _isSyncing.value = false;
    _syncStatus.value = 'completed';
    _syncProgress.value = 1.0;
    _lastSyncTime.value = DateTime.now().toIso8601String();
    _metadataBox?.put('last_sync_time', _lastSyncTime.value);
    
    // Update pending operations count
    _updatePendingOperationsCount();
  }
  
  /// Fail sync operation
  void failSync(String error) {
    _isSyncing.value = false;
    _syncStatus.value = 'failed: $error';
    _syncProgress.value = 0.0;
  }
  
  /// Trigger automatic sync
  Future<void> _triggerAutoSync() async {
    try {
      // Check if sync service is available
      if (Get.isRegistered<SyncService>()) {
        final syncService = Get.find<SyncService>();
        await syncService.syncPendingOperations();
      } else {
        log('OfflineStateService: SyncService not available for auto-sync');
      }
    } catch (e) {
      log('OfflineStateService: Failed to trigger auto-sync: $e');
    }
  }
  
  /// Clear all offline data (for testing/reset purposes)
  Future<void> clearOfflineData() async {
    try {
      await _syncQueueBox?.clear();
      await _offlineDataBox?.clear();
      await _metadataBox?.clear();
      
      _pendingOperations.value = 0;
      _lastSyncTime.value = '';
      _syncStatus.value = 'idle';
      _syncProgress.value = 0.0;
      
      log('OfflineStateService: Offline data cleared');
    } catch (e) {
      log('OfflineStateService: Failed to clear offline data: $e');
    }
  }
  
  /// Get offline statistics
  Map<String, dynamic> getOfflineStats() {
    return {
      'isOfflineMode': isOfflineMode,
      'pendingOperations': pendingOperations,
      'lastSyncTime': lastSyncTime,
      'isSyncing': isSyncing,
      'syncStatus': syncStatus,
      'syncProgress': syncProgress,
    };
  }
}

/// Placeholder for SyncService - will be implemented later
class SyncService extends GetxController {
  Future<void> syncPendingOperations() async {
    log('SyncService: Sync pending operations called');
  }
}
