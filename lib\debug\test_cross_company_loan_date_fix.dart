import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import '../core/services/automatic_journal_entry_service.dart';
import '../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../firebase_service/voucher/voucher_crud_firebase_service.dart';
import '../models/finance/loan_model.dart';
import '../models/voucher_model.dart';
import '../models/payment_transaction_model.dart';

/// Test widget to verify cross-company loan approval date fix
class TestCrossCompanyLoanDateFix {
  static Future<void> runTest() async {
    log('🧪 ========== CROSS-COMPANY LOAN DATE FIX TEST START ==========');
    
    try {
      // Check authentication
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        log('❌ No authenticated user found');
        return;
      }
      
      final currentUid = user.uid;
      log('👤 Current user UID: $currentUid');
      
      // Create test services
      final automaticJournalService = AutomaticJournalEntryService(
        ChartOfAccountsFirebaseService(),
      );
      
      // Create a test loan that simulates a cross-company scenario
      final testPaymentDate = DateTime(2024, 1, 15, 10, 30); // Original payment date
      final testApprovalDate = DateTime(2024, 1, 20, 14, 45); // Later approval date
      
      final testLoan = LoanModel(
        id: 'test-loan-123',
        uid: 'company2-uid', // Approving company (Company 2)
        requestedBy: 'company1-uid', // Original company (Company 1)
        requestedByName: 'Company 1',
        requestedTo: 'company2-uid',
        requestedToName: 'Company 2',
        fromAccountId: 'test-account-id',
        toAccountId: 'test-borrower-account-id',
        fromAccountName: 'Test Bank Account',
        toAccountName: 'Test Borrower Account',
        amount: 50000.0,
        dueDate: DateTime.now().add(const Duration(days: 30)),
        status: 'approved',
        requestDate: testPaymentDate,
        approvalDate: testApprovalDate,
        voucherPaymentId: 'test-payment-123', // Link to original payment
        notes: 'Test cross-company loan for date fix verification',
      );
      
      log('📅 Test scenario:');
      log('   Original payment date: ${testPaymentDate.toIso8601String()}');
      log('   Loan approval date: ${testApprovalDate.toIso8601String()}');
      log('   Expected journal entry date: ${testPaymentDate.toIso8601String()} (should use payment date, not approval date)');
      
      // Test the loan disbursement entry generation
      log('🔄 Testing loan disbursement entry generation...');
      
      final journalEntries = await automaticJournalService.generateLoanJournalEntries(
        loan: testLoan,
        transactionType: 'disbursement',
        uid: currentUid, // This simulates Company 2 approving the loan
        createdBy: 'test-user',
      );
      
      if (journalEntries.isEmpty) {
        log('⚠️ No journal entries generated - this might be expected if accounts are not found');
        log('💡 This test requires proper Chart of Accounts setup to work fully');
        return;
      }
      
      final disbursementEntry = journalEntries.first;
      log('✅ Generated journal entry:');
      log('   Entry ID: ${disbursementEntry.id}');
      log('   Entry Date: ${disbursementEntry.entryDate.toIso8601String()}');
      log('   Description: ${disbursementEntry.description}');
      log('   Amount: ${disbursementEntry.totalDebits}');
      
      // Verify the date fix
      final entryDate = disbursementEntry.entryDate;
      final isUsingPaymentDate = entryDate.year == testPaymentDate.year &&
          entryDate.month == testPaymentDate.month &&
          entryDate.day == testPaymentDate.day;
      
      final isUsingApprovalDate = entryDate.year == testApprovalDate.year &&
          entryDate.month == testApprovalDate.month &&
          entryDate.day == testApprovalDate.day;
      
      if (isUsingPaymentDate) {
        log('✅ SUCCESS: Journal entry is using original payment transaction date');
        log('🎯 Date fix is working correctly for cross-company loans');
      } else if (isUsingApprovalDate) {
        log('❌ ISSUE: Journal entry is still using loan approval date');
        log('🔧 The date fix may not be working as expected');
      } else {
        log('⚠️ UNKNOWN: Journal entry is using a different date');
        log('📅 Entry date: ${entryDate.toIso8601String()}');
        log('💭 This might be due to fallback logic or missing voucher data');
      }
      
      log('📊 Test Results Summary:');
      log('   Journal entries generated: ${journalEntries.length}');
      log('   Using payment date: $isUsingPaymentDate');
      log('   Using approval date: $isUsingApprovalDate');
      log('   Entry date: ${entryDate.toIso8601String()}');
      
    } catch (e) {
      log('❌ Test failed with error: $e');
    }
    
    log('🧪 ========== CROSS-COMPANY LOAN DATE FIX TEST END ==========');
  }
  
  /// Test the voucher payment date retrieval logic specifically
  static Future<void> testVoucherPaymentDateRetrieval() async {
    log('🧪 ========== VOUCHER PAYMENT DATE RETRIEVAL TEST START ==========');
    
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        log('❌ No authenticated user found');
        return;
      }
      
      final currentUid = user.uid;
      log('👤 Testing voucher payment date retrieval for UID: $currentUid');
      
      // Get recent vouchers to test with
      final voucherService = VoucherCrudFirebaseService();
      final vouchers = await voucherService.getVouchersForCompany(uid: currentUid);
      
      log('📋 Found ${vouchers.length} vouchers for testing');
      
      if (vouchers.isEmpty) {
        log('⚠️ No vouchers found - cannot test payment date retrieval');
        return;
      }
      
      // Test with the first voucher that has payments
      for (final voucherData in vouchers.take(3)) {
        final voucher = VoucherModel.fromJson(voucherData);
        final payments = voucher.paymentTransactions
            .map((p) => PaymentTransactionModel.fromMap(p))
            .toList();
        
        if (payments.isNotEmpty) {
          log('📄 Testing voucher: ${voucher.voucherNumber}');
          log('💳 Payments found: ${payments.length}');
          
          for (final payment in payments) {
            log('   Payment ID: ${payment.id}');
            log('   Payment Date: ${payment.transactionDate.toIso8601String()}');
            log('   Payment Method: ${payment.method.name}');
            log('   Amount: ${payment.amount}');
            
            // This simulates the lookup logic in the fixed code
            if (payment.method == PaymentMethod.check) {
              log('✅ Found check payment that could create cross-company loan');
              log('📅 Original transaction date that should be preserved: ${payment.transactionDate.toIso8601String()}');
            }
          }
          break;
        }
      }
      
    } catch (e) {
      log('❌ Voucher payment date retrieval test failed: $e');
    }
    
    log('🧪 ========== VOUCHER PAYMENT DATE RETRIEVAL TEST END ==========');
  }
}
