import 'dart:developer';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/models/slab/formula_variables.dart';

/// Result class for formula calculations with detailed breakdown
class FormulaCalculationResult {
  final double? finalAmount;
  final Map<String, double> stepResults;
  final List<String> calculationSteps;
  final bool isSuccess;
  final String? errorMessage;

  FormulaCalculationResult({
    this.finalAmount,
    required this.stepResults,
    required this.calculationSteps,
    required this.isSuccess,
    this.errorMessage,
  });

  /// Get a formatted calculation breakdown for display
  String get calculationBreakdown {
    if (!isSuccess) {
      return 'Calculation failed: ${errorMessage ?? 'Unknown error'}';
    }

    final breakdown = StringBuffer();
    for (int i = 0; i < calculationSteps.length; i++) {
      breakdown.writeln('Step ${i + 1}: ${calculationSteps[i]}');
    }

    if (finalAmount != null) {
      breakdown.writeln('Final Result: ${finalAmount!.toStringAsFixed(2)}');
    }

    return breakdown.toString().trim();
  }
}

/// Service for executing flexible user-defined calculation formulas
class FlexibleFormulaCalculationService {
  /// Calculate invoice amount using a custom formula
  /// Returns the calculated amount or null if calculation fails
  static double? calculateWithFormula({
    required InvoiceModel invoice,
    required CalculationFormulaModel formula,
    required Map<String, double> customColumnValues,
  }) {
    final result = calculateWithFormulaDetailed(
      invoice: invoice,
      formula: formula,
      customColumnValues: customColumnValues,
    );
    return result.finalAmount;
  }

  /// Calculate invoice amount using a custom formula with detailed breakdown
  /// Returns detailed calculation result including step-by-step breakdown
  static FormulaCalculationResult calculateWithFormulaDetailed({
    required InvoiceModel invoice,
    required CalculationFormulaModel formula,
    required Map<String, double> customColumnValues,
  }) {
    try {
      log('Starting formula calculation for invoice ${invoice.tasNumber} using formula: ${formula.formulaName}');

      // Validate formula
      if (!formula.isValid()) {
        log('Invalid formula structure');
        return FormulaCalculationResult(
          stepResults: {},
          calculationSteps: [],
          isSuccess: false,
          errorMessage: 'Invalid formula structure',
        );
      }

      // Initialize variable context with invoice data and custom columns
      final variables = _initializeVariables(invoice, customColumnValues);
      final stepResults = <String, double>{};
      final calculationSteps = <String>[];
      log('Initialized variables: $variables');

      // Execute formula steps sequentially
      for (int i = 0; i < formula.steps.length; i++) {
        final step = formula.steps[i];
        log('Executing step ${i + 1}: ${step.stepName} - ${step.formula}');

        final stepResult = _executeFormulaStep(step, variables);
        if (stepResult == null) {
          log('Failed to execute step: ${step.stepName}');
          return FormulaCalculationResult(
            stepResults: stepResults,
            calculationSteps: calculationSteps,
            isSuccess: false,
            errorMessage: 'Failed to execute step: ${step.stepName}',
          );
        }

        // Store step result in variables for use in subsequent steps
        variables[step.resultVariable] = stepResult;
        stepResults[step.resultVariable] = stepResult;

        // Create detailed calculation step description
        final stepDescription =
            '${step.stepName}: ${step.formula} = ${stepResult.toStringAsFixed(2)}';
        calculationSteps.add(stepDescription);

        log('Step ${i + 1} result: ${step.resultVariable} = $stepResult');
      }

      // Get final result
      final finalResult = variables[formula.finalResultVariable];
      if (finalResult == null) {
        log('Final result variable ${formula.finalResultVariable} not found');
        return FormulaCalculationResult(
          stepResults: stepResults,
          calculationSteps: calculationSteps,
          isSuccess: false,
          errorMessage:
              'Final result variable ${formula.finalResultVariable} not found',
        );
      }

      // Apply monetary rounding only to final monetary amounts, not to intermediate rates
      final shouldRound =
          formula.finalResultVariable.toLowerCase().contains('amount') ||
              formula.finalResultVariable.toLowerCase().contains('total') ||
              formula.finalResultVariable.toLowerCase().contains('cost') ||
              formula.finalResultVariable.toLowerCase().contains('price');

      final roundedResult =
          shouldRound ? MonetaryRounding.roundHalfUp(finalResult) : finalResult;

      log('Formula calculation completed. Final result: $finalResult → ${shouldRound ? "rounded to: $roundedResult" : "no rounding applied"}');

      return FormulaCalculationResult(
        finalAmount: roundedResult,
        stepResults: stepResults,
        calculationSteps: calculationSteps,
        isSuccess: true,
      );
    } catch (e) {
      log('Error in formula calculation: $e');
      return FormulaCalculationResult(
        stepResults: {},
        calculationSteps: [],
        isSuccess: false,
        errorMessage: 'Error in formula calculation: $e',
      );
    }
  }

  /// Initialize variables with invoice data and custom column values
  static Map<String, double> _initializeVariables(
      InvoiceModel invoice, Map<String, double> customColumnValues) {
    final totalWeightKg = invoice.numberOfBags * invoice.weightPerBag;
    final totalWeightTons = totalWeightKg / 1000;

    final variables = {
      FormulaVariables.numberOfBags: invoice.numberOfBags.toDouble(),
      FormulaVariables.weightPerBag: invoice.weightPerBag,
      FormulaVariables.totalWeightKg: totalWeightKg,
      FormulaVariables.totalWeightTons: totalWeightTons,
      FormulaVariables.distanceInKilometers:
          invoice.distanceInKilometers.toDouble(),
    };

    // Add custom column values
    variables.addAll(customColumnValues);

    return variables;
  }

  /// Execute a single formula step
  static double? _executeFormulaStep(
      FormulaStepModel step, Map<String, double> variables) {
    try {
      // Replace variables in formula with actual values
      String processedFormula = step.formula;

      log('Original formula: ${step.formula}');
      log('Available variables: $variables');

      // Replace each variable with its value
      for (final entry in variables.entries) {
        final variableName = entry.key;
        final variableValue = entry.value;
        processedFormula =
            processedFormula.replaceAll(variableName, variableValue.toString());
      }

      log('Processed formula: $processedFormula');

      // Convert display operators to calculation operators
      processedFormula = _convertOperators(processedFormula);
      log('Converted formula: $processedFormula');

      // Evaluate the mathematical expression
      final result = evaluateExpression(processedFormula);
      log('Evaluation result: $result');

      return result;
    } catch (e) {
      log('Error executing formula step ${step.stepName}: $e');
      log('Stack trace: ${StackTrace.current}');
      return null;
    }
  }

  /// Convert display operators (×, ÷) to calculation operators (*, /)
  static String _convertOperators(String formula) {
    // First handle logical operators (word-based)
    String converted = formula
        .replaceAll(RegExp(r'\bAND\b', caseSensitive: false), '&&')
        .replaceAll(RegExp(r'\bOR\b', caseSensitive: false), '||');

    // Handle both Unicode and ASCII operators
    // Note: ASCII operators (>=, <=, !=, ==) are already correct for evaluation
    // We only need to convert Unicode operators to ASCII
    return converted
        // Multiplication and division
        .replaceAll(FormulaOperations.multiplication, '*') // × → *
        .replaceAll(FormulaOperations.division, '/') // ÷ → /

        // Unicode comparison operators to ASCII
        .replaceAll(FormulaOperations.greaterThanOrEqual, '>=') // ≥ → >=
        .replaceAll(FormulaOperations.lessThanOrEqual, '<=') // ≤ → <=
        .replaceAll(FormulaOperations.notEquals, '!=') // ≠ → !=

        // Handle single equals (= → ==) but be careful not to affect compound operators
        .replaceAll(RegExp(r'(?<![><!])=(?![=])'),
            '==') // = → == (not part of >=, <=, !=, ==)

        // Single character operators - only convert if they're not part of compound operators
        .replaceAll(
            RegExp(r'>(?![=])'), '>') // > (not followed by =) - already correct
        .replaceAll(RegExp(r'<(?![=])'),
            '<'); // < (not followed by =) - already correct
  }

  /// Evaluate a mathematical expression
  /// This is a simple expression evaluator that handles basic arithmetic
  static double? evaluateExpression(String expression) {
    try {
      // Don't remove all spaces - our parser handles whitespace correctly
      // Only normalize multiple spaces to single spaces
      expression = expression.replaceAll(RegExp(r'\s+'), ' ').trim();

      // Simple expression evaluation using recursive descent parser
      final parser = _ExpressionParser(expression);
      return parser.parse();
    } catch (e) {
      log('Error evaluating expression: $expression, Error: $e');
      return null;
    }
  }

  /// Validate a formula expression syntax
  static bool validateFormulaExpression(
      String formula, List<String> availableVariables) {
    try {
      // Check for balanced parentheses
      int parenthesesCount = 0;
      for (int i = 0; i < formula.length; i++) {
        if (formula[i] == '(') parenthesesCount++;
        if (formula[i] == ')') parenthesesCount--;
        if (parenthesesCount < 0) return false; // Closing before opening
      }
      if (parenthesesCount != 0) return false; // Unbalanced parentheses

      // Check if all variables in formula are available
      for (final variable in availableVariables) {
        if (formula.contains(variable)) {
          // Variable is used, which is good
        }
      }

      // Try to parse the expression with dummy values
      String testFormula = formula;
      for (final variable in availableVariables) {
        testFormula = testFormula.replaceAll(variable, '1.0');
      }

      testFormula = _convertOperators(testFormula);
      final result = evaluateExpression(testFormula);

      return result != null;
    } catch (e) {
      return false;
    }
  }

  /// Test a formula with sample data
  static Map<String, dynamic> testFormula({
    required CalculationFormulaModel formula,
    Map<String, double>? testVariables,
  }) {
    try {
      // Use default test values if not provided
      final variables = testVariables ??
          {
            FormulaVariables.numberOfBags: 100.0,
            FormulaVariables.weightPerBag: 50.0,
            FormulaVariables.totalWeightKg: 5000.0,
            FormulaVariables.totalWeightTons: 5.0,
            FormulaVariables.distanceInKilometers: 150.0,
            // Custom columns would be added here based on slab configuration
            'sampleRate': 2.5,
          };

      final stepResults = <String, double>{};
      final stepDetails = <Map<String, dynamic>>[];

      // Execute each step
      for (int i = 0; i < formula.steps.length; i++) {
        final step = formula.steps[i];
        final stepResult = _executeFormulaStep(step, variables);

        if (stepResult == null) {
          return {
            'success': false,
            'error': 'Failed to execute step: ${step.stepName}',
            'stepResults': stepResults,
            'stepDetails': stepDetails,
          };
        }

        stepResults[step.resultVariable] = stepResult;
        variables[step.resultVariable] = stepResult;

        stepDetails.add({
          'stepName': step.stepName,
          'formula': step.formula,
          'result': stepResult,
          'resultVariable': step.resultVariable,
        });
      }

      final finalResult = variables[formula.finalResultVariable];

      return {
        'success': true,
        'finalResult': finalResult,
        'stepResults': stepResults,
        'stepDetails': stepDetails,
        'testVariables': testVariables ?? variables,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'stepResults': <String, double>{},
        'stepDetails': <Map<String, dynamic>>[],
      };
    }
  }
}

/// Enhanced expression parser supporting arithmetic and conditional logic
class _ExpressionParser {
  final String expression;
  int position = 0;

  _ExpressionParser(this.expression);

  double parse() {
    final result = parseConditionalExpression();
    if (position < expression.length) {
      throw Exception('Unexpected character at position $position');
    }
    return result;
  }

  /// Parse conditional expressions (IF-THEN-ELSE)
  double parseConditionalExpression() {
    _skipWhitespace();

    // Check for IF statement
    if (_matchKeyword('IF')) {
      return _parseIfStatement();
    }

    return parseLogicalExpression();
  }

  /// Parse IF-THEN-ELSE statements
  double _parseIfStatement() {
    _skipWhitespace();

    // Parse condition
    final condition = parseLogicalExpression();

    _skipWhitespace();
    if (!_matchKeyword('THEN')) {
      throw Exception('Expected THEN after IF condition');
    }

    _skipWhitespace();
    final thenValue = parseExpression();

    _skipWhitespace();
    double elseValue = 0.0;
    if (_matchKeyword('ELSE')) {
      _skipWhitespace();
      // Use parseConditionalExpression to handle nested IF statements
      elseValue = parseConditionalExpression();
    }

    // Return appropriate value based on condition
    return condition != 0.0 ? thenValue : elseValue;
  }

  /// Parse logical expressions (AND, OR)
  double parseLogicalExpression() {
    double result = parseComparisonExpression();

    while (position < expression.length) {
      _skipWhitespace();
      if (_matchOperator('&&')) {
        _skipWhitespace();
        final right = parseComparisonExpression();
        result = (result != 0.0 && right != 0.0) ? 1.0 : 0.0;
      } else if (_matchOperator('||')) {
        _skipWhitespace();
        final right = parseComparisonExpression();
        result = (result != 0.0 || right != 0.0) ? 1.0 : 0.0;
      } else if (_matchKeyword('AND')) {
        _skipWhitespace();
        final right = parseComparisonExpression();
        result = (result != 0.0 && right != 0.0) ? 1.0 : 0.0;
      } else if (_matchKeyword('OR')) {
        _skipWhitespace();
        final right = parseComparisonExpression();
        result = (result != 0.0 || right != 0.0) ? 1.0 : 0.0;
      } else {
        break;
      }
    }

    return result;
  }

  /// Parse comparison expressions (>=, <=, ==, !=, >, <)
  double parseComparisonExpression() {
    double result = parseExpression();

    while (position < expression.length) {
      _skipWhitespace();
      if (_matchOperator('>=')) {
        _skipWhitespace();
        final right = parseExpression();
        result = result >= right ? 1.0 : 0.0;
      } else if (_matchOperator('<=')) {
        _skipWhitespace();
        final right = parseExpression();
        result = result <= right ? 1.0 : 0.0;
      } else if (_matchOperator('==')) {
        _skipWhitespace();
        final right = parseExpression();
        result = result == right ? 1.0 : 0.0;
      } else if (_matchOperator('!=')) {
        _skipWhitespace();
        final right = parseExpression();
        result = result != right ? 1.0 : 0.0;
      } else if (_matchOperator('>')) {
        _skipWhitespace();
        final right = parseExpression();
        result = result > right ? 1.0 : 0.0;
      } else if (_matchOperator('<')) {
        _skipWhitespace();
        final right = parseExpression();
        result = result < right ? 1.0 : 0.0;
      } else {
        break;
      }
    }

    return result;
  }

  double parseExpression() {
    double result = parseTerm();

    while (position < expression.length) {
      _skipWhitespace();
      if (position >= expression.length) break;

      final char = expression[position];
      if (char == '+') {
        position++;
        result += parseTerm();
      } else if (char == '-') {
        position++;
        result -= parseTerm();
      } else {
        break;
      }
    }

    return result;
  }

  double parseTerm() {
    double result = parseFactor();

    while (position < expression.length) {
      _skipWhitespace();
      if (position >= expression.length) break;

      final char = expression[position];
      if (char == '*') {
        position++;
        result *= parseFactor();
      } else if (char == '/') {
        position++;
        final divisor = parseFactor();
        if (divisor == 0) throw Exception('Division by zero');
        result /= divisor;
      } else {
        break;
      }
    }

    return result;
  }

  double parseFactor() {
    _skipWhitespace();

    if (position >= expression.length) {
      throw Exception('Unexpected end of expression');
    }

    final char = expression[position];

    if (char == '(') {
      position++;
      final result = parseConditionalExpression();
      _skipWhitespace();
      if (position >= expression.length || expression[position] != ')') {
        throw Exception('Missing closing parenthesis');
      }
      position++;
      return result;
    }

    if (char == '-') {
      position++;
      return -parseFactor();
    }

    return parseNumber();
  }

  double parseNumber() {
    final start = position;

    while (position < expression.length) {
      final char = expression[position];
      if (char.contains(RegExp(r'[0-9.]'))) {
        position++;
      } else {
        break;
      }
    }

    if (start == position) {
      throw Exception('Expected number at position $position');
    }

    final numberStr = expression.substring(start, position);
    return double.parse(numberStr);
  }

  /// Skip whitespace characters
  void _skipWhitespace() {
    while (position < expression.length && expression[position] == ' ') {
      position++;
    }
  }

  /// Match a keyword at current position
  bool _matchKeyword(String keyword) {
    final start = position;
    _skipWhitespace();

    if (position + keyword.length <= expression.length) {
      final substr = expression.substring(position, position + keyword.length);
      if (substr.toUpperCase() == keyword.toUpperCase()) {
        // Check that it's a complete word (not part of another word)
        final nextPos = position + keyword.length;
        if (nextPos >= expression.length ||
            !RegExp(r'[a-zA-Z0-9_]').hasMatch(expression[nextPos])) {
          position = nextPos;
          _skipWhitespace(); // Skip whitespace after keyword
          return true;
        }
      }
    }

    position = start;
    return false;
  }

  /// Match an operator at current position
  bool _matchOperator(String operator) {
    _skipWhitespace();

    if (position + operator.length <= expression.length) {
      final substr = expression.substring(position, position + operator.length);
      if (substr == operator) {
        position += operator.length;
        return true;
      }
    }

    return false;
  }
}
