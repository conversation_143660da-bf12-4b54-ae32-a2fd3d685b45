import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../core/utils/app_constants/firebase/collection_names.dart';
import '../../models/finance/chart_of_accounts_model.dart';

/// Result model for paginated chart of accounts queries
class PaginatedAccountsResult {
  final List<ChartOfAccountsModel> accounts;
  final QueryDocumentSnapshot? nextPageCursor;
  final bool hasNextPage;
  final int totalCount;

  PaginatedAccountsResult({
    required this.accounts,
    this.nextPageCursor,
    required this.hasNextPage,
    required this.totalCount,
  });
}

class ChartOfAccountsFirebaseService {
  late FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  ChartOfAccountsFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  /// Create a new chart of accounts entry
  Future<void> createAccount(ChartOfAccountsModel account) async {
    log('Creating chart of accounts entry: ${account.accountName}');
    try {
      final accountRef =
          _firestore.collection(AppCollection.chartOfAccountsCollection).doc();
      final accountId = accountRef.id;

      final accountData = account.toJson();
      accountData['id'] = accountId;
      accountData['uid'] = _uid; // Add current user's UID

      await accountRef.set(accountData);
      log('Successfully created chart of accounts entry: $accountId');
    } catch (e) {
      log('Error creating chart of accounts entry: $e');
      rethrow;
    }
  }

  /// Get paginated chart of accounts entries for the current user
  Future<PaginatedAccountsResult> getAccountsPaginated({
    int limit = 25,
    QueryDocumentSnapshot? lastDocument,
    bool includeInactive = false,
    AccountCategory? category,
    String? searchQuery,
  }) async {
    log('Fetching paginated chart of accounts from Firestore (limit: $limit)');
    try {
      // Build base query
      Query query = _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid);

      // Add category filter if specified
      if (category != null) {
        query = query.where('category', isEqualTo: category.name);
      }

      // Add active status filter if not including inactive
      if (!includeInactive) {
        query = query.where('isActive', isEqualTo: true);
      }

      // Order by account number for consistent pagination
      query = query.orderBy('accountNumber').limit(limit);

      // Add pagination cursor if provided
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final snapshot = await query.get();

      var accounts = snapshot.docs
          .map((doc) {
            try {
              return ChartOfAccountsModel.fromJson(
                  doc.data() as Map<String, dynamic>);
            } catch (e) {
              log('Error parsing account document ${doc.id}: $e');
              return null;
            }
          })
          .where((account) => account != null)
          .cast<ChartOfAccountsModel>()
          .toList();

      // Apply search filter in memory if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final query = searchQuery.toLowerCase();
        accounts = accounts.where((account) {
          return account.accountName.toLowerCase().contains(query) ||
              account.accountNumber.contains(query) ||
              (account.description?.toLowerCase().contains(query) ?? false);
        }).toList();
      }

      // Get total count for pagination info
      final totalCount = await _getTotalAccountsCount(
        includeInactive: includeInactive,
        category: category,
        searchQuery: searchQuery,
      );

      // Determine if there are more pages
      QueryDocumentSnapshot? nextPageCursor;
      bool hasNextPage = false;

      if (snapshot.docs.isNotEmpty && accounts.length == limit) {
        nextPageCursor = snapshot.docs.last;
        hasNextPage = true;
      }

      log('Successfully fetched ${accounts.length} paginated accounts (total: $totalCount)');

      return PaginatedAccountsResult(
        accounts: accounts,
        nextPageCursor: nextPageCursor,
        hasNextPage: hasNextPage,
        totalCount: totalCount,
      );
    } catch (e) {
      log('Error fetching paginated chart of accounts: $e');

      // If compound query fails due to missing index, try fallback query
      if (e.toString().contains('index') ||
          e.toString().contains('FAILED_PRECONDITION')) {
        log('Attempting fallback paginated query without compound index...');
        return await _getFallbackPaginatedAccounts(
          limit: limit,
          lastDocument: lastDocument,
          includeInactive: includeInactive,
          category: category,
          searchQuery: searchQuery,
        );
      }

      rethrow;
    }
  }

  /// Get all chart of accounts entries for the current user (legacy method)
  Future<List<ChartOfAccountsModel>> getAccounts() async {
    log('Fetching chart of accounts from Firestore (legacy method)');
    final result =
        await getAccountsPaginated(limit: 1000, includeInactive: false);
    return result.accounts;
  }

  /// Get all accounts including inactive ones
  Future<List<ChartOfAccountsModel>> getAllAccounts() async {
    log('ChartOfAccountsFirebaseService: Fetching all chart of accounts from Firestore');
    log('ChartOfAccountsFirebaseService: Using UID filter: $_uid');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('accountNumber')
          .get();

      log('ChartOfAccountsFirebaseService: Raw query returned ${snapshot.docs.length} documents');

      final accounts = snapshot.docs
          .map((doc) => ChartOfAccountsModel.fromJson(doc.data()))
          .toList();

      log('ChartOfAccountsFirebaseService: Successfully parsed ${accounts.length} chart of accounts entries (including inactive)');

      // Debug: Log parsed account details
      for (var account in accounts.take(5)) {
        log('ChartOfAccountsFirebaseService: Parsed account - ${account.accountName} (${account.accountCategory.displayName}) [UID: ${account.uid}]');
      }

      return accounts;
    } catch (e) {
      log('ChartOfAccountsFirebaseService: Error fetching all chart of accounts: $e');
      rethrow;
    }
  }

  /// Get accounts by category
  Future<List<ChartOfAccountsModel>> getAccountsByCategory(
      AccountCategory category) async {
    log('Fetching chart of accounts by category: ${category.displayName}');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('category', isEqualTo: category.name)
          .where('isActive', isEqualTo: true)
          .orderBy('accountNumber')
          .get();

      final accounts = snapshot.docs
          .map((doc) => ChartOfAccountsModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${accounts.length} accounts for category: ${category.displayName}');
      return accounts;
    } catch (e) {
      log('Error fetching accounts by category: $e');
      rethrow;
    }
  }

  /// Get accounts by type
  Future<List<ChartOfAccountsModel>> getAccountsByType(
      AccountType accountType) async {
    log('Fetching chart of accounts by type: ${accountType.displayName}');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('accountType', isEqualTo: accountType.name)
          .where('isActive', isEqualTo: true)
          .orderBy('accountNumber')
          .get();

      final accounts = snapshot.docs
          .map((doc) => ChartOfAccountsModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${accounts.length} accounts for type: ${accountType.displayName}');
      return accounts;
    } catch (e) {
      log('Error fetching accounts by type: $e');
      rethrow;
    }
  }

  /// Get child accounts for a parent account
  Future<List<ChartOfAccountsModel>> getChildAccounts(
      String parentAccountId) async {
    log('Fetching child accounts for parent: $parentAccountId');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('parentAccountId', isEqualTo: parentAccountId)
          .where('isActive', isEqualTo: true)
          .orderBy('accountNumber')
          .get();

      final accounts = snapshot.docs
          .map((doc) => ChartOfAccountsModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${accounts.length} child accounts');
      return accounts;
    } catch (e) {
      log('Error fetching child accounts: $e');
      rethrow;
    }
  }

  /// Get account by ID
  Future<ChartOfAccountsModel?> getAccountById(String accountId) async {
    log('Fetching account by ID: $accountId');
    try {
      final doc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(accountId)
          .get();

      if (!doc.exists) {
        log('Account not found: $accountId');
        return null;
      }

      final data = doc.data() as Map<String, dynamic>;

      // Verify the account belongs to the current user
      if (data['uid'] != _uid) {
        log('Account does not belong to current user: $accountId');
        return null;
      }

      final account = ChartOfAccountsModel.fromJson(data);
      log('Successfully fetched account: ${account.accountName}');
      return account;
    } catch (e) {
      log('Error fetching account by ID: $e');
      rethrow;
    }
  }

  /// Get account by ID for cross-company operations (bypasses UID validation)
  /// Used for loan-based payments and cross-company transactions
  Future<ChartOfAccountsModel?> getAccountByIdCrossCompany(
      String accountId) async {
    log('Fetching account by ID (cross-company): $accountId');
    try {
      final doc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(accountId)
          .get();

      if (!doc.exists) {
        log('Account not found (cross-company): $accountId');
        return null;
      }

      final data = doc.data() as Map<String, dynamic>;
      final account = ChartOfAccountsModel.fromJson(data);
      log('Successfully fetched cross-company account: ${account.accountName} (Owner: ${account.uid})');
      return account;
    } catch (e) {
      log('Error fetching account by ID (cross-company): $e');
      rethrow;
    }
  }

  /// Update an existing account
  Future<void> updateAccount(ChartOfAccountsModel account) async {
    log('Updating account: ${account.accountName}');
    try {
      final accountData = account
          .copyWith(
            updatedAt: DateTime.now(),
            uid: _uid, // Ensure UID is preserved
          )
          .toJson();

      await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(account.id)
          .update(accountData);

      log('Successfully updated account: ${account.id}');
    } catch (e) {
      log('Error updating account: $e');
      rethrow;
    }
  }

  /// Deactivate an account (soft delete)
  Future<void> deactivateAccount(String accountId) async {
    log('Deactivating account: $accountId');
    try {
      await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(accountId)
          .update({
        'isActive': false,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      log('Successfully deactivated account: $accountId');
    } catch (e) {
      log('Error deactivating account: $e');
      rethrow;
    }
  }

  /// Reactivate an account
  Future<void> reactivateAccount(String accountId) async {
    log('Reactivating account: $accountId');
    try {
      await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(accountId)
          .update({
        'isActive': true,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      log('Successfully reactivated account: $accountId');
    } catch (e) {
      log('Error reactivating account: $e');
      rethrow;
    }
  }

  /// Check if an account has any transactions
  Future<bool> hasTransactions(String accountId) async {
    log('Checking if account has transactions: $accountId');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.transactionsCollection)
          .where('accountId', isEqualTo: accountId)
          .where('uid', isEqualTo: _uid)
          .limit(1)
          .get();

      final hasTransactions = snapshot.docs.isNotEmpty;
      log('Account $accountId has transactions: $hasTransactions');
      return hasTransactions;
    } catch (e) {
      log('Error checking account transactions: $e');
      rethrow;
    }
  }

  /// Check if an account has child accounts
  Future<bool> hasChildAccounts(String accountId) async {
    log('Checking if account has child accounts: $accountId');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('parentAccountId', isEqualTo: accountId)
          .where('uid', isEqualTo: _uid)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      final hasChildren = snapshot.docs.isNotEmpty;
      log('Account $accountId has child accounts: $hasChildren');
      return hasChildren;
    } catch (e) {
      log('Error checking child accounts: $e');
      rethrow;
    }
  }

  /// Validate if an account can be deleted
  Future<Map<String, dynamic>> validateAccountDeletion(String accountId) async {
    log('Validating account deletion: $accountId');
    try {
      final hasTransactionsResult = await hasTransactions(accountId);
      final hasChildrenResult = await hasChildAccounts(accountId);

      final canDelete = !hasTransactionsResult && !hasChildrenResult;
      final reasons = <String>[];

      if (hasTransactionsResult) {
        reasons.add('Account has existing transactions');
      }
      if (hasChildrenResult) {
        reasons.add('Account has child accounts');
      }

      return {
        'canDelete': canDelete,
        'reasons': reasons,
        'hasTransactions': hasTransactionsResult,
        'hasChildAccounts': hasChildrenResult,
      };
    } catch (e) {
      log('Error validating account deletion: $e');
      rethrow;
    }
  }

  /// Delete an account (hard delete - only for accounts without transactions or children)
  Future<void> deleteAccount(String accountId) async {
    log('Deleting account: $accountId');
    try {
      // Validate deletion first
      final validation = await validateAccountDeletion(accountId);

      if (!validation['canDelete']) {
        final reasons = (validation['reasons'] as List<String>).join(', ');
        throw Exception('Cannot delete account: $reasons');
      }

      await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(accountId)
          .delete();

      log('Successfully deleted account: $accountId');
    } catch (e) {
      log('Error deleting account: $e');
      rethrow;
    }
  }

  /// Check if account number already exists
  Future<bool> isAccountNumberExists(String accountNumber) async {
    log('Checking if account number exists: $accountNumber');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('accountNumber', isEqualTo: accountNumber)
          .limit(1)
          .get();

      final exists = snapshot.docs.isNotEmpty;
      log('Account number $accountNumber exists: $exists');
      return exists;
    } catch (e) {
      log('Error checking account number existence: $e');
      rethrow;
    }
  }

  /// Get next available account number for a category
  Future<String> getNextAccountNumber(AccountCategory category) async {
    log('Getting next account number for category: ${category.displayName}');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('category', isEqualTo: category.name)
          .orderBy('accountNumber', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) {
        // First account in this category
        final nextNumber = category.startRange.toString();
        log('First account in category, returning: $nextNumber');
        return nextNumber;
      }

      final lastAccountData = snapshot.docs.first.data();
      final lastAccountNumber = lastAccountData['accountNumber'] as String;
      final lastNumber = int.tryParse(lastAccountNumber) ?? category.startRange;

      final nextNumber = (lastNumber + 1).toString();
      log('Next account number: $nextNumber');
      return nextNumber;
    } catch (e) {
      log('Error getting next account number: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to chart of accounts
  Stream<List<ChartOfAccountsModel>> listenToAccounts() {
    try {
      return _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .where('isActive', isEqualTo: true)
          .orderBy('accountNumber')
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => ChartOfAccountsModel.fromJson(doc.data()))
              .toList())
          .handleError((error) {
        log('Error in real-time stream: $error');

        // If compound query fails, try fallback stream
        if (error.toString().contains('index') ||
            error.toString().contains('FAILED_PRECONDITION')) {
          log('Switching to fallback stream without compound index...');
          return _firestore
              .collection(AppCollection.chartOfAccountsCollection)
              .where('uid', isEqualTo: _uid)
              .snapshots()
              .map((snapshot) {
            final accounts = snapshot.docs
                .map((doc) => ChartOfAccountsModel.fromJson(doc.data()))
                .where((account) => account.isActive)
                .toList();

            // Sort in memory
            accounts.sort((a, b) => a.accountNumber.compareTo(b.accountNumber));
            return accounts;
          });
        }

        throw error;
      });
    } catch (e) {
      log('Error listening to chart of accounts: $e', error: e);

      // Return fallback stream
      try {
        return _firestore
            .collection(AppCollection.chartOfAccountsCollection)
            .where('uid', isEqualTo: _uid)
            .snapshots()
            .map((snapshot) {
          final accounts = snapshot.docs
              .map((doc) => ChartOfAccountsModel.fromJson(doc.data()))
              .where((account) => account.isActive)
              .toList();

          // Sort in memory
          accounts.sort((a, b) => a.accountNumber.compareTo(b.accountNumber));
          return accounts;
        });
      } catch (fallbackError) {
        log('Fallback stream also failed: $fallbackError');
        return Stream.value([]);
      }
    }
  }

  /// Get total count of accounts for pagination
  Future<int> _getTotalAccountsCount({
    bool includeInactive = false,
    AccountCategory? category,
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid);

      // Add category filter if specified
      if (category != null) {
        query = query.where('category', isEqualTo: category.name);
      }

      // Add active status filter if not including inactive
      if (!includeInactive) {
        query = query.where('isActive', isEqualTo: true);
      }

      final snapshot = await query.get();

      // If search query is provided, filter in memory
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final queryLower = searchQuery.toLowerCase();
        final filteredDocs = snapshot.docs.where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          final accountName =
              (data['accountName'] as String? ?? '').toLowerCase();
          final accountNumber = data['accountNumber'] as String? ?? '';
          final description =
              (data['description'] as String? ?? '').toLowerCase();

          return accountName.contains(queryLower) ||
              accountNumber.contains(searchQuery) ||
              description.contains(queryLower);
        });
        return filteredDocs.length;
      }

      return snapshot.docs.length;
    } catch (e) {
      log('Error getting total accounts count: $e');
      return 0;
    }
  }

  /// Fallback paginated query without compound indexes
  Future<PaginatedAccountsResult> _getFallbackPaginatedAccounts({
    int limit = 25,
    QueryDocumentSnapshot? lastDocument,
    bool includeInactive = false,
    AccountCategory? category,
    String? searchQuery,
  }) async {
    try {
      // Simple query with just uid filter
      final snapshot = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .get();

      var accounts = snapshot.docs
          .map((doc) {
            try {
              return ChartOfAccountsModel.fromJson(doc.data());
            } catch (e) {
              log('Error parsing account document ${doc.id}: $e');
              return null;
            }
          })
          .where((account) => account != null)
          .cast<ChartOfAccountsModel>()
          .toList();

      // Apply filters in memory
      accounts = accounts.where((account) {
        // Category filter
        if (category != null && account.category != category) {
          return false;
        }

        // Active status filter
        if (!includeInactive && !account.isActive) {
          return false;
        }

        // Search filter
        if (searchQuery != null && searchQuery.isNotEmpty) {
          final query = searchQuery.toLowerCase();
          if (!account.accountName.toLowerCase().contains(query) &&
              !account.accountNumber.contains(query) &&
              !(account.description?.toLowerCase().contains(query) ?? false)) {
            return false;
          }
        }

        return true;
      }).toList();

      // Sort by account number
      accounts.sort((a, b) => a.accountNumber.compareTo(b.accountNumber));

      final totalCount = accounts.length;

      // Apply pagination in memory
      int startIndex = 0;
      if (lastDocument != null) {
        // Find the index of the last document
        final lastAccountData = lastDocument.data() as Map<String, dynamic>;
        final lastAccountNumber = lastAccountData['accountNumber'] as String;

        startIndex = accounts.indexWhere((account) =>
            account.accountNumber.compareTo(lastAccountNumber) > 0);
        if (startIndex == -1) startIndex = accounts.length;
      }

      final endIndex = (startIndex + limit).clamp(0, accounts.length);
      final paginatedAccounts = accounts.sublist(startIndex, endIndex);

      final hasNextPage = endIndex < accounts.length;
      QueryDocumentSnapshot? nextPageCursor;

      if (hasNextPage && paginatedAccounts.isNotEmpty) {
        // Create a mock cursor for the last item
        final lastAccount = paginatedAccounts.last;
        try {
          final lastDoc = snapshot.docs.firstWhere(
            (doc) => doc.data()['id'] == lastAccount.id,
          );
          nextPageCursor = lastDoc;
        } catch (e) {
          // If we can't find the exact document, use the last document in the snapshot
          if (snapshot.docs.isNotEmpty) {
            nextPageCursor = snapshot.docs.last;
          }
        }
      }

      log('Fallback query successful: ${paginatedAccounts.length} accounts (total: $totalCount)');

      return PaginatedAccountsResult(
        accounts: paginatedAccounts,
        nextPageCursor: nextPageCursor,
        hasNextPage: hasNextPage,
        totalCount: totalCount,
      );
    } catch (e) {
      log('Fallback paginated query failed: $e');
      rethrow;
    }
  }
}
