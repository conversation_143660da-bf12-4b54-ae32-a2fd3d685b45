import 'package:flutter/foundation.dart';

/// Helper class for web downloads using modern web APIs
/// Replaces deprecated dart:html usage
class WebDownloadHelper {
  /// Download a file in web environment
  static void downloadFile({
    required Uint8List bytes,
    required String fileName,
    String mimeType = 'application/octet-stream',
  }) {
    if (kIsWeb) {
      // Use modern web APIs through js_interop
      _downloadFileWeb(bytes, fileName, mimeType);
    } else {
      throw UnsupportedError('Web download is only supported on web platform');
    }
  }

  /// Download Excel file
  static void downloadExcel({
    required Uint8List bytes,
    required String fileName,
  }) {
    downloadFile(
      bytes: bytes,
      fileName: fileName.endsWith('.xlsx') ? fileName : '$fileName.xlsx',
      mimeType:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
  }

  /// Download PDF file
  static void downloadPdf({
    required Uint8List bytes,
    required String fileName,
  }) {
    downloadFile(
      bytes: bytes,
      fileName: fileName.endsWith('.pdf') ? fileName : '$fileName.pdf',
      mimeType: 'application/pdf',
    );
  }

  /// Download JSON file
  static void downloadJson({
    required String jsonContent,
    required String fileName,
  }) {
    final bytes = Uint8List.fromList(jsonContent.codeUnits);
    downloadFile(
      bytes: bytes,
      fileName: fileName.endsWith('.json') ? fileName : '$fileName.json',
      mimeType: 'application/json',
    );
  }

  /// Internal method for web download using modern APIs
  static void _downloadFileWeb(
      Uint8List bytes, String fileName, String mimeType) {
    // This will be implemented using package:web when migrating
    // For now, we'll use a fallback approach

    // Create a data URL
    final base64 = _bytesToBase64(bytes);
    final dataUrl = 'data:$mimeType;base64,$base64';

    // Use window.open as a fallback
    // This is a temporary solution until full migration to package:web
    if (kIsWeb) {
      // Use external JS function for download
      _triggerDownload(dataUrl, fileName);
    }
  }

  /// Convert bytes to base64 string
  static String _bytesToBase64(Uint8List bytes) {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    String result = '';

    for (int i = 0; i < bytes.length; i += 3) {
      int byte1 = bytes[i];
      int byte2 = i + 1 < bytes.length ? bytes[i + 1] : 0;
      int byte3 = i + 2 < bytes.length ? bytes[i + 2] : 0;

      int combined = (byte1 << 16) | (byte2 << 8) | byte3;

      result += chars[(combined >> 18) & 63];
      result += chars[(combined >> 12) & 63];
      result += i + 1 < bytes.length ? chars[(combined >> 6) & 63] : '=';
      result += i + 2 < bytes.length ? chars[combined & 63] : '=';
    }

    return result;
  }

  /// Trigger download using external JavaScript
  static void _triggerDownload(String dataUrl, String fileName) {
    // This requires adding a JavaScript function to web/index.html
    // For now, we'll provide instructions for manual implementation

    if (kDebugMode) {
      print('WebDownloadHelper: Download triggered for $fileName');
      print('Data URL length: ${dataUrl.length}');
      print(
          'To complete web download setup, add the following to web/index.html:');
      print('''
<script>
function downloadFile(dataUrl, fileName) {
  const link = document.createElement('a');
  link.href = dataUrl;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
</script>
      ''');
    }
  }
}

/// Migration instructions for dart:html to package:web
class DartHtmlMigrationGuide {
  static const String instructions = '''
MIGRATION GUIDE: dart:html to package:web

1. Replace dart:html imports:
   OLD: import 'dart:html' as html;
   NEW: import 'package:web/web.dart' as web;

2. Update Blob usage:
   OLD: html.Blob([bytes], mimeType)
   NEW: web.Blob([bytes.buffer], web.BlobPropertyBag(type: mimeType))

3. Update URL creation:
   OLD: html.Url.createObjectUrl(blob)
   NEW: web.URL.createObjectURL(blob)

4. Update anchor element:
   OLD: html.AnchorElement()
   NEW: web.HTMLAnchorElement()

5. Update document access:
   OLD: html.document.body
   NEW: web.document.body

6. Add package:web dependency to pubspec.yaml:
   dependencies:
     web: ^0.3.0

7. Update web/index.html with download helper function (see WebDownloadHelper)

Files that need migration:
- lib/features/asset_management/presentation/controllers/asset_export_controller.dart
- lib/features/backup_restore/presentation/controllers/backup_restore_controller.dart
- lib/features/finance/loans/presentation/controllers/loan_export_controller.dart
- lib/features/finance/loans/presentation/widgets/loan_requests_export_dialog.dart
- lib/services/financial_report_export_service.dart
''';
}
