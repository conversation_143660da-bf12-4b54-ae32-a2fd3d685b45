import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/models/asset/asset_audit_model.dart';

/// Firebase Audit Trail Connection Tester
/// Tests Firebase connectivity, authentication, and audit trail queries
class FirebaseAuditTrailTester extends GetxController {
  final RxString testStatus = 'Ready to test'.obs;
  final RxList<String> testResults = <String>[].obs;
  final RxBool isRunning = false.obs;

  /// Run comprehensive Firebase audit trail tests
  Future<void> runTests() async {
    if (isRunning.value) return;

    isRunning.value = true;
    testResults.clear();
    testStatus.value = 'Running tests...';

    try {
      await _testFirebaseConnection();
      await _testAuthentication();
      await _testAuditTrailCollection();
      await _testAuditTrailQueries();

      testStatus.value = 'All tests completed';
    } catch (e) {
      _addResult('❌ CRITICAL ERROR: $e', isError: true);
      testStatus.value = 'Tests failed with errors';
    } finally {
      isRunning.value = false;
    }
  }

  /// Test 1: Firebase Connection
  Future<void> _testFirebaseConnection() async {
    _addResult('🔥 Testing Firebase Connection...');

    try {
      final firestore = FirebaseFirestore.instance;

      // Test basic Firestore connectivity
      await firestore.enableNetwork();
      _addResult('✅ Firebase Firestore connection: OK');

      // Test collection access
      await firestore
          .collection('test')
          .limit(1)
          .get(const GetOptions(source: Source.server));

      _addResult('✅ Firebase server connectivity: OK');
    } catch (e) {
      _addResult('❌ Firebase connection failed: $e', isError: true);
      rethrow;
    }
  }

  /// Test 2: Authentication Status
  Future<void> _testAuthentication() async {
    _addResult('🔐 Testing Firebase Authentication...');

    try {
      final auth = FirebaseAuth.instance;
      final user = auth.currentUser;

      if (user != null) {
        _addResult('✅ User authenticated: ${user.email}');
        _addResult('✅ User UID: ${user.uid}');
        _addResult('✅ User display name: ${user.displayName ?? 'Not set'}');

        // Test token validity
        final token = await user.getIdToken();
        if (token != null && token.isNotEmpty) {
          _addResult('✅ Auth token: Valid');
        } else {
          _addResult('⚠️ Auth token: Empty or null');
        }
      } else {
        _addResult('❌ No authenticated user found', isError: true);
        throw Exception('User not authenticated');
      }
    } catch (e) {
      _addResult('❌ Authentication test failed: $e', isError: true);
      rethrow;
    }
  }

  /// Test 3: Audit Trail Collection Access
  Future<void> _testAuditTrailCollection() async {
    _addResult('📋 Testing Audit Trail Collection Access...');

    try {
      final firestore = FirebaseFirestore.instance;
      final uid = FirebaseAuth.instance.currentUser?.uid;

      if (uid == null) {
        throw Exception('No UID available for collection test');
      }

      // Test collection exists and is accessible
      final collectionRef =
          firestore.collection(AppCollection.assetAuditTrailCollection);
      _addResult(
          '✅ Collection reference created: ${AppCollection.assetAuditTrailCollection}');

      // Test basic query (should not fail even if empty)
      final testQuery =
          await collectionRef.where('uid', isEqualTo: uid).limit(1).get();

      _addResult('✅ Collection query executed successfully');
      _addResult(
          '📊 Found ${testQuery.docs.length} audit entries for current user');

      // Test write permissions by creating a test entry
      await _testWritePermissions(collectionRef, uid);
    } catch (e) {
      _addResult('❌ Collection access failed: $e', isError: true);
      rethrow;
    }
  }

  /// Test write permissions
  Future<void> _testWritePermissions(
      CollectionReference collection, String uid) async {
    try {
      final testDoc = collection.doc();
      final testData = {
        'id': testDoc.id,
        'uid': uid,
        'assetId': 'test-asset',
        'action': 'test',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'userId': uid,
        'userName': 'Test User',
        'fieldChanges': {},
        'notes': 'Firebase connection test entry',
      };

      await testDoc.set(testData);
      _addResult('✅ Write permissions: OK (test entry created)');

      // Clean up test entry
      await testDoc.delete();
      _addResult('✅ Delete permissions: OK (test entry removed)');
    } catch (e) {
      _addResult('❌ Write/Delete permissions failed: $e', isError: true);
    }
  }

  /// Test 4: Audit Trail Queries
  Future<void> _testAuditTrailQueries() async {
    _addResult('🔍 Testing Audit Trail Queries...');

    try {
      final firestore = FirebaseFirestore.instance;
      final uid = FirebaseAuth.instance.currentUser?.uid;

      if (uid == null) {
        throw Exception('No UID available for query tests');
      }

      // Test 1: Basic audit trail query
      await _testBasicQuery(firestore, uid);

      // Test 2: Asset-specific query
      await _testAssetSpecificQuery(firestore, uid);

      // Test 3: Real-time stream
      await _testRealTimeStream(firestore, uid);
    } catch (e) {
      _addResult('❌ Query tests failed: $e', isError: true);
      rethrow;
    }
  }

  /// Test basic audit trail query
  Future<void> _testBasicQuery(FirebaseFirestore firestore, String uid) async {
    try {
      final query = await firestore
          .collection(AppCollection.assetAuditTrailCollection)
          .where('uid', isEqualTo: uid)
          .orderBy('timestamp', descending: true)
          .limit(10)
          .get();

      _addResult('✅ Basic query: OK (${query.docs.length} entries)');

      if (query.docs.isNotEmpty) {
        final firstEntry = AssetAuditModel.fromJson(query.docs.first.data());
        _addResult(
            '📄 Sample entry: ${firstEntry.action} on ${firstEntry.assetId}');
      }
    } catch (e) {
      _addResult('❌ Basic query failed: $e', isError: true);
    }
  }

  /// Test asset-specific query
  Future<void> _testAssetSpecificQuery(
      FirebaseFirestore firestore, String uid) async {
    try {
      // First, get any asset ID from existing entries
      final anyEntryQuery = await firestore
          .collection(AppCollection.assetAuditTrailCollection)
          .where('uid', isEqualTo: uid)
          .limit(1)
          .get();

      if (anyEntryQuery.docs.isNotEmpty) {
        final entry = AssetAuditModel.fromJson(anyEntryQuery.docs.first.data());
        final assetId = entry.assetId;

        final assetQuery = await firestore
            .collection(AppCollection.assetAuditTrailCollection)
            .where('uid', isEqualTo: uid)
            .where('assetId', isEqualTo: assetId)
            .orderBy('timestamp', descending: true)
            .get();

        _addResult(
            '✅ Asset-specific query: OK (${assetQuery.docs.length} entries for asset $assetId)');
      } else {
        _addResult('⚠️ Asset-specific query: No entries found to test with');
      }
    } catch (e) {
      _addResult('❌ Asset-specific query failed: $e', isError: true);
    }
  }

  /// Test real-time stream
  Future<void> _testRealTimeStream(
      FirebaseFirestore firestore, String uid) async {
    try {
      final stream = firestore
          .collection(AppCollection.assetAuditTrailCollection)
          .where('uid', isEqualTo: uid)
          .orderBy('timestamp', descending: true)
          .limit(5)
          .snapshots();

      // Listen for 2 seconds to test stream
      final subscription = stream.listen(
        (snapshot) {
          _addResult(
              '✅ Real-time stream: OK (${snapshot.docs.length} entries received)');
        },
        onError: (error) {
          _addResult('❌ Real-time stream error: $error', isError: true);
        },
      );

      // Wait 2 seconds then cancel
      await Future.delayed(const Duration(seconds: 2));
      await subscription.cancel();
    } catch (e) {
      _addResult('❌ Real-time stream test failed: $e', isError: true);
    }
  }

  /// Add test result
  void _addResult(String message, {bool isError = false}) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    final formattedMessage = '[$timestamp] $message';
    testResults.add(formattedMessage);
    log(formattedMessage);

    if (isError) {
      log('ERROR: $message');
    }
  }

  /// Clear test results
  void clearResults() {
    testResults.clear();
    testStatus.value = 'Ready to test';
  }
}
