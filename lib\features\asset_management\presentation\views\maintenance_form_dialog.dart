import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/core/utils/widgets/my_dropdown_field.dart';
import 'package:logestics/features/asset_management/presentation/controllers/maintenance_form_controller.dart';
import 'package:logestics/models/asset/asset_maintenance_model.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

class MaintenanceFormDialog extends StatefulWidget {
  final String assetId;
  final String assetName;
  final AssetMaintenanceModel? maintenance; // For editing existing maintenance

  const MaintenanceFormDialog({
    super.key,
    required this.assetId,
    required this.assetName,
    this.maintenance,
  });

  @override
  State<MaintenanceFormDialog> createState() => _MaintenanceFormDialogState();
}

class _MaintenanceFormDialogState extends State<MaintenanceFormDialog> {
  late MaintenanceFormController controller;
  late ColorNotifier notifier;

  @override
  void initState() {
    super.initState();
    controller = Get.put(MaintenanceFormController());
    controller.initializeForm(
      assetId: widget.assetId,
      assetName: widget.assetName,
      maintenance: widget.maintenance,
    );
  }

  @override
  void dispose() {
    Get.delete<MaintenanceFormController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of<ColorNotifier>(context, listen: true);
    final size = MediaQuery.of(context).size;
    final isEdit = widget.maintenance != null;

    return Dialog(
      backgroundColor: notifier.getBgColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: size.width * 0.9,
        height: size.height * 0.85,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            _buildHeader(isEdit),
            const SizedBox(height: 24),
            Expanded(
              child: Form(
                key: controller.formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildMaintenanceDetailsSection(),
                      const SizedBox(height: 24),
                      _buildAttachmentsSection(),
                      const SizedBox(height: 24),
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            _buildActionButtons(isEdit),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(bool isEdit) {
    return Row(
      children: [
        Icon(
          Icons.build,
          color: notifier.text,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isEdit ? 'Edit Maintenance Record' : 'Add Maintenance Record',
                style: TextStyle(
                  color: notifier.text,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Asset: ${widget.assetName}',
                style: TextStyle(
                  color: notifier.text.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Get.back(),
          icon: Icon(Icons.close, color: notifier.text),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Basic Information',
          style: TextStyle(
            color: notifier.text,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Obx(() => MyDropdownFormField(
                    titletext: 'Maintenance Type *',
                    hinttext: 'Select maintenance type',
                    items: MaintenanceType.allTypes,
                    initalValue:
                        controller.selectedMaintenanceType.value.isNotEmpty
                            ? controller.selectedMaintenanceType.value
                            : null,
                    onChanged: (value) =>
                        controller.selectedMaintenanceType.value = value ?? '',
                  )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: InkWell(
                onTap: () => _selectMaintenanceDate(),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                    color: notifier.textFileColor,
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_today,
                          color: notifier.text, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Obx(() => Text(
                              controller.maintenanceDate.value != null
                                  ? '${controller.maintenanceDate.value!.day}/${controller.maintenanceDate.value!.month}/${controller.maintenanceDate.value!.year}'
                                  : 'Select maintenance date *',
                              style: TextStyle(
                                color: controller.maintenanceDate.value != null
                                    ? notifier.text
                                    : notifier.text.withOpacity(0.6),
                              ),
                            )),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: MyTextFormField(
                controller: controller.costController,
                titleText: 'Maintenance Cost *',
                labelText: 'Maintenance Cost *',
                hintText: 'Enter cost amount',
                keyboardType: TextInputType.number,
                validator: (value) => controller.validatePositiveNumber(
                    value, 'Maintenance cost'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: MyTextFormField(
                controller: controller.performedByController,
                titleText: 'Performed By *',
                labelText: 'Performed By *',
                hintText: 'Enter technician/company name',
                validator: (value) =>
                    controller.validateRequired(value, 'Performed by'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMaintenanceDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Maintenance Details',
          style: TextStyle(
            color: notifier.text,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        MyTextFormField(
          controller: controller.descriptionController,
          titleText: 'Description *',
          labelText: 'Description *',
          hintText: 'Describe the maintenance work performed',
          maxLength: 500,
          validator: (value) =>
              controller.validateRequired(value, 'Description'),
        ),
      ],
    );
  }

  Widget _buildAttachmentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Attachments',
          style: TextStyle(
            color: notifier.text,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
            borderRadius: BorderRadius.circular(8),
            color: notifier.textFileColor,
          ),
          child: Column(
            children: [
              Icon(
                Icons.cloud_upload_outlined,
                size: 48,
                color: notifier.text.withOpacity(0.5),
              ),
              const SizedBox(height: 8),
              Text(
                'Upload receipts, photos, or documents',
                style: TextStyle(
                  color: notifier.text.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: controller.pickFiles,
                    icon: const Icon(Icons.attach_file, size: 16),
                    label: const Text('Choose Files'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0165FC),
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.icon(
                    onPressed: controller.pickImages,
                    icon: const Icon(Icons.photo_camera, size: 16),
                    label: const Text('Take Photo'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Obx(() {
                if (controller.selectedFiles.isEmpty) {
                  return const SizedBox();
                }
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Selected Files:',
                      style: TextStyle(
                        color: notifier.text,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...controller.selectedFiles.map((file) => Container(
                          margin: const EdgeInsets.only(bottom: 4),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.insert_drive_file,
                                  size: 16, color: notifier.text),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  file.name,
                                  style: TextStyle(
                                      color: notifier.text, fontSize: 12),
                                ),
                              ),
                              IconButton(
                                onPressed: () => controller.removeFile(file),
                                icon: const Icon(Icons.close, size: 16),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        )),
                  ],
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Additional Notes',
          style: TextStyle(
            color: notifier.text,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        MyTextFormField(
          controller: controller.notesController,
          titleText: 'Notes',
          labelText: 'Notes (Optional)',
          hintText: 'Enter any additional notes or comments',
          maxLength: 500,
        ),
      ],
    );
  }

  Widget _buildActionButtons(bool isEdit) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Get.back(),
          child: Text(
            'Cancel',
            style: TextStyle(color: notifier.text),
          ),
        ),
        const SizedBox(width: 16),
        Obx(() => ElevatedButton(
              onPressed: controller.isLoading.value ? null : _saveMaintenance,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0165FC),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: controller.isLoading.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(isEdit ? 'Update Maintenance' : 'Save Maintenance'),
            )),
      ],
    );
  }

  Future<void> _selectMaintenanceDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: controller.maintenanceDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      controller.maintenanceDate.value = date;
    }
  }

  void _saveMaintenance() async {
    if (controller.formKey.currentState?.validate() ?? false) {
      final success = await controller.saveMaintenance();
      if (success) {
        Get.back(result: true);
        Get.snackbar(
          'Success',
          widget.maintenance != null
              ? 'Maintenance record updated successfully'
              : 'Maintenance record added successfully',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    }
  }
}
