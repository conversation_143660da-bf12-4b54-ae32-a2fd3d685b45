import 'package:flutter/material.dart';
import '../../../../../core/services/accounting_validation_service.dart';

/// Widget to display validation results for journal entries
class ValidationSummaryWidget extends StatelessWidget {
  final ValidationResult validationResult;
  final bool showWarnings;
  final VoidCallback? onDismiss;

  const ValidationSummaryWidget({
    super.key,
    required this.validationResult,
    this.showWarnings = true,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    if (validationResult.isValid && !validationResult.hasWarnings) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  validationResult.isValid ? Icons.warning : Icons.error,
                  color: validationResult.isValid ? Colors.orange : Colors.red,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  validationResult.isValid
                      ? 'Validation Warnings'
                      : 'Validation Errors',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: validationResult.isValid
                            ? Colors.orange
                            : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                if (onDismiss != null)
                  IconButton(
                    icon: const Icon(Icons.close, size: 18),
                    onPressed: onDismiss,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
              ],
            ),

            const SizedBox(height: 8),

            // Errors
            if (!validationResult.isValid) ...[
              ...validationResult.errors.map((error) => _buildValidationItem(
                    context,
                    error,
                    Icons.error_outline,
                    Colors.red,
                  )),
            ],

            // Warnings
            if (showWarnings && validationResult.hasWarnings) ...[
              if (!validationResult.isValid) const SizedBox(height: 8),
              ...validationResult.warnings
                  .map((warning) => _buildValidationItem(
                        context,
                        warning,
                        Icons.warning_amber_outlined,
                        Colors.orange,
                      )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildValidationItem(
    BuildContext context,
    String message,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color.withValues(alpha: 0.8),
                  ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Compact validation indicator for forms
class ValidationIndicator extends StatelessWidget {
  final ValidationResult validationResult;
  final VoidCallback? onTap;

  const ValidationIndicator({
    super.key,
    required this.validationResult,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (validationResult.isValid && !validationResult.hasWarnings) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 16),
            const SizedBox(width: 4),
            Text(
              'Valid',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      );
    }

    final hasErrors = !validationResult.isValid;
    final color = hasErrors ? Colors.red : Colors.orange;
    final icon = hasErrors ? Icons.error : Icons.warning;
    final text = hasErrors
        ? '${validationResult.errors.length} Error${validationResult.errors.length > 1 ? 's' : ''}'
        : '${validationResult.warnings.length} Warning${validationResult.warnings.length > 1 ? 's' : ''}';

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 4),
            Text(
              text,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
            ),
            if (onTap != null) ...[
              const SizedBox(width: 4),
              Icon(Icons.info_outline, color: color, size: 14),
            ],
          ],
        ),
      ),
    );
  }
}

/// Real-time validation status widget for journal entry forms
class JournalEntryValidationStatus extends StatelessWidget {
  final ValidationResult validationResult;
  final bool showDetails;
  final VoidCallback? onToggleDetails;

  const JournalEntryValidationStatus({
    super.key,
    required this.validationResult,
    this.showDetails = false,
    this.onToggleDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Status indicator
        Row(
          children: [
            ValidationIndicator(
              validationResult: validationResult,
              onTap: onToggleDetails,
            ),
            const Spacer(),
            if (onToggleDetails != null)
              TextButton.icon(
                onPressed: onToggleDetails,
                icon: Icon(
                  showDetails ? Icons.expand_less : Icons.expand_more,
                  size: 16,
                ),
                label: Text(
                  showDetails ? 'Hide Details' : 'Show Details',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
          ],
        ),

        // Details
        if (showDetails)
          ValidationSummaryWidget(
            validationResult: validationResult,
            showWarnings: true,
          ),
      ],
    );
  }
}
