import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:excel/excel.dart' as excel;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:universal_html/html.dart' as html;

import '../../../../../models/finance/bill_model.dart';
import '../../../../../models/invoice_model.dart';
import '../../../../../firebase_service/finance/bill_firebase_service.dart';
import '../../../../../core/utils/constants/constants.dart';

class BillDetailController extends GetxController {
  final BillFirebaseService _billFirebaseService = BillFirebaseService();

  // Observable variables
  final RxBool isLoadingInvoices = false.obs;
  final RxBool isExporting = false.obs;
  final RxBool isExportingPdf = false.obs;
  final RxList<InvoiceModel> linkedInvoices = <InvoiceModel>[].obs;

  // Current bill
  BillModel? currentBill;

  /// Initialize the controller with bill data
  void initializeBill(BillModel bill) {
    currentBill = bill;
    loadLinkedInvoices();
  }

  /// Load linked invoices for the current bill
  Future<void> loadLinkedInvoices() async {
    if (currentBill == null) return;

    isLoadingInvoices.value = true;
    try {
      log('Loading linked invoices for bill: ${currentBill!.billNumber}');
      log('Linked invoice IDs: ${currentBill!.linkedInvoiceIds}');

      final invoices = await _billFirebaseService.getLinkedInvoices(
        uid: currentBill!.companyUid,
        invoiceIds: currentBill!.linkedInvoiceIds,
      );

      linkedInvoices.value = invoices;
      log('Successfully loaded ${invoices.length} linked invoices');
    } catch (e) {
      log('Error loading linked invoices: $e');
      Get.snackbar(
        'Error',
        'Failed to load linked invoices: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoadingInvoices.value = false;
    }
  }

  /// Export bill data to Excel
  Future<void> exportToExcel() async {
    if (currentBill == null || linkedInvoices.isEmpty) {
      Get.snackbar(
        'Error',
        'No data to export',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    isExporting.value = true;

    try {
      log('Generating Excel for bill: ${currentBill!.billNumber}');

      // Create Excel workbook
      final excelWorkbook = excel.Excel.createExcel();
      final sheet = excelWorkbook['Bill Report'];

      // Remove default sheet if it exists
      if (excelWorkbook.sheets.containsKey('Sheet1')) {
        excelWorkbook.delete('Sheet1');
      }

      // Calculate the starting row for data (3.5 inches = approximately 25 rows in Excel)
      const int topMarginRows = 25;
      const int headerRow = topMarginRows;
      const int dataStartRow = topMarginRows + 1;

      // Add bill number and invoice count header at the top margin area - using 6pt font for maximum compactness with borders
      final billNumberCell = sheet.cell(excel.CellIndex.indexByColumnRow(
          columnIndex: 0, rowIndex: topMarginRows - 5));
      billNumberCell.value =
          excel.TextCellValue('BILL # ${currentBill!.billNumber}');
      billNumberCell.cellStyle = excel.CellStyle(
        bold: true,
        fontSize: 6,
        fontColorHex: excel.ExcelColor.black,
        // Complete table borders for title cells
        leftBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        rightBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        topBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        bottomBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
      );

      // Add total invoice count (right side) - using 6pt font for maximum compactness with borders
      final invoiceCountCell = sheet.cell(excel.CellIndex.indexByColumnRow(
          columnIndex: 10, rowIndex: topMarginRows - 5));
      invoiceCountCell.value =
          excel.TextCellValue('Total Invoices: ${linkedInvoices.length}');
      invoiceCountCell.cellStyle = excel.CellStyle(
        bold: true,
        fontSize: 6,
        fontColorHex: excel.ExcelColor.black,
        // Complete table borders for title cells
        leftBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        rightBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        topBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        bottomBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
      );

      // Define headers with optimized text wrapping for better space utilization
      final headers = [
        'SN.',
        'Lifting\nDate', // Split for better width optimization
        'Truck\nNo',
        'Bilty\nNo',
        'Convey\nNote Number', // Split long header into multiple lines
        'Product\nName', // Split for consistency
        'Product\nTAS NO', // Split for consistency
        'Destination',
        'No of\nBags', // Split for better width
        'Weight', // Keep short as is
        'KM',
        'District',
        'HMT Rates\n(Non Fuel\nInc WHT)', // Multi-line for better space usage
        '100%\nAmount', // Split for consistency
        '80%\nAmount', // Split for consistency
        'Net\nAmount', // Split for consistency
      ];

      // Enhanced header style for professional appearance with borders and center alignment
      // Using 6pt font for maximum compactness
      final headerStyle = excel.CellStyle(
        bold: true,
        fontSize: 6,
        fontColorHex: excel.ExcelColor.black,
        // Center alignment for professional appearance
        horizontalAlign: excel.HorizontalAlign.Center,
        verticalAlign: excel.VerticalAlign.Center,
        // Enable text wrapping for multi-line headers
        textWrapping: excel.TextWrapping.WrapText,
        // Professional table borders for headers
        leftBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        rightBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        topBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        bottomBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
      );

      // Style for data cells with professional borders and center alignment
      // Using 6pt font size for compact single-page printing
      final dataStyle = excel.CellStyle(
        fontSize: 6,
        fontColorHex: excel.ExcelColor.black,
        // Center alignment for professional appearance
        horizontalAlign: excel.HorizontalAlign.Center,
        verticalAlign: excel.VerticalAlign.Center,
        // Professional table borders for data cells
        leftBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        rightBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        topBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
        bottomBorder: excel.Border(
            borderStyle: excel.BorderStyle.Thin,
            borderColorHex: excel.ExcelColor.black),
      );

      // Set header row height for better appearance
      sheet.setRowHeight(headerRow, 30.0);

      // Add headers to the header row (after top margin)
      for (int i = 0; i < headers.length; i++) {
        final cell = sheet.cell(excel.CellIndex.indexByColumnRow(
            columnIndex: i, rowIndex: headerRow));
        cell.value = excel.TextCellValue(headers[i]);
        cell.cellStyle = headerStyle;
      }

      // Add data rows
      double totalTons = 0.0;
      double total100Amount = 0.0;
      double total80Amount = 0.0;

      for (int i = 0; i < linkedInvoices.length; i++) {
        final invoice = linkedInvoices[i];
        final rowIndex = dataStartRow + i;

        // Calculate total weight in tons
        final totalWeightTons =
            (invoice.numberOfBags * invoice.weightPerBag) / 1000;

        // Format weight with dynamic decimal places
        final totalWeightFormatted = totalWeightTons % 1 == 0
            ? totalWeightTons.toInt().toString()
            : totalWeightTons
                .toStringAsFixed(2)
                .replaceAll(RegExp(r'\.?0+$'), '');

        // Use default rate for now - this should be replaced with actual slab calculation
        final hmtRate = 150.0;
        final amount100 =
            MonetaryRounding.roundHalfUp(totalWeightTons * hmtRate);
        final amount80 = MonetaryRounding.roundHalfUp(amount100 * 0.80);
        final netAmount = amount100;

        // Add to totals
        totalTons += totalWeightTons;
        total100Amount += amount100;
        total80Amount += amount80;

        final rowData = [
          (i + 1).toString(),
          invoice.orderDate != null
              ? DateFormat('dd/MM/yyyy').format(invoice.orderDate!)
              : '',
          invoice.truckNumber,
          invoice.biltyNumber,
          invoice.conveyNoteNumber,
          invoice.productName,
          invoice.tasNumber,
          invoice.stationName,
          invoice.numberOfBags.toString(),
          totalWeightFormatted,
          invoice.distanceInKilometers.toString(),
          invoice.districtName,
          _formatMonetaryAmount(hmtRate),
          _formatMonetaryAmount(amount100),
          _formatMonetaryAmount(amount80),
          _formatMonetaryAmount(netAmount),
        ];

        for (int j = 0; j < rowData.length; j++) {
          final cell = sheet.cell(excel.CellIndex.indexByColumnRow(
              columnIndex: j, rowIndex: rowIndex));
          cell.value = excel.TextCellValue(rowData[j]);
          cell.cellStyle = dataStyle; // Apply consistent data styling
        }

        // Set row height for data rows
        sheet.setRowHeight(rowIndex, 20.0);
      }

      // Add summary section
      final summaryStartRow = dataStartRow + linkedInvoices.length + 1;

      // Summary row
      final summaryData = [
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Tons',
        _formatMonetaryAmount(MonetaryRounding.roundHalfUp(totalTons)),
        'Bill Amount Exclusive of GST (100%)',
        '',
        _formatMonetaryAmount(MonetaryRounding.roundHalfUp(total100Amount)),
        _formatMonetaryAmount(MonetaryRounding.roundHalfUp(total80Amount)),
        _formatMonetaryAmount(MonetaryRounding.roundHalfUp(total100Amount)),
      ];

      for (int j = 0; j < summaryData.length; j++) {
        final cell = sheet.cell(excel.CellIndex.indexByColumnRow(
            columnIndex: j, rowIndex: summaryStartRow));
        cell.value = excel.TextCellValue(summaryData[j]);
        cell.cellStyle = excel.CellStyle(
          bold: true,
          fontSize: 6,
          fontColorHex: excel.ExcelColor.black,
          horizontalAlign: excel.HorizontalAlign.Center,
          verticalAlign: excel.VerticalAlign.Center,
          // Complete table borders for summary cells
          leftBorder: excel.Border(
              borderStyle: excel.BorderStyle.Thin,
              borderColorHex: excel.ExcelColor.black),
          rightBorder: excel.Border(
              borderStyle: excel.BorderStyle.Thin,
              borderColorHex: excel.ExcelColor.black),
          topBorder: excel.Border(
              borderStyle: excel.BorderStyle.Thin,
              borderColorHex: excel.ExcelColor.black),
          bottomBorder: excel.Border(
              borderStyle: excel.BorderStyle.Thin,
              borderColorHex: excel.ExcelColor.black),
        );
      }

      // Set optimized column widths for 8.5-inch single-page printing
      // Widths calculated to fit within 8.5 inches with wrapped headers
      // Updated with specific pixel-equivalent measurements for maximum compactness
      final columnWidths = [
        1.44, // SN. - 20px equivalent for maximum compactness
        7.0, // Lifting\nDate - reduced due to text wrapping
        4.11, // Truck\nNo - 44px equivalent for compact layout
        6.5, // Bilty\nNo - compact for bilty numbers
        5.0, // Convey\nNote Number - 52px equivalent for optimal fit
        4.0, // Product\nName - 43px equivalent for compact display
        8.0, // Product\nTAS NO - adequate for TAS numbers
        9.0, // Destination - single line, needs more space
        6.0, // No of\nBags - compact with wrapping
        3.67, // Weight - 40px equivalent for compact display
        3.5, // KM - reduced for compact 6pt font
        6.5, // District - reduced for compact 6pt font
        7.0, // HMT Rates\n(Non Fuel\nInc WHT) - reduced for compact 6pt font
        6.0, // 100%\nAmount - reduced for compact 6pt font
        4.11, // 80%\nAmount - 44px equivalent for compact display
        6.0, // Net\nAmount - reduced for compact 6pt font
      ]; // Total: ~92.33 units - optimized for maximum compactness with 6pt font

      for (int i = 0; i < headers.length && i < columnWidths.length; i++) {
        sheet.setColumnWidth(i, columnWidths[i]);
      }

      // Configure sheet for optimal printing and professional appearance
      try {
        // Set basic sheet properties for better layout
        sheet.isRTL = false; // Left-to-right reading

        log('Excel sheet configured with professional styling:');
        log('- Header row height: 30 points for better readability');
        log('- Data row height: 20 points for consistent spacing');
        log('- ALL content font size: 6pt for maximum compactness');
        log('- Titles, headers, data, and summary sections: 6pt font');
        log('- COMPLETE TABLE BORDERS: All cells have borders on all sides');
        log('- Professional grid layout with bordered cells');
        log('- Optimized column widths with pixel-equivalent measurements');
        log('- Enhanced header and data cell formatting');
        log('- Total column width: ~92.33 units for maximum compactness');
      } catch (e) {
        log('Basic styling applied successfully');
      }

      // Print layout recommendations for users
      log('');
      log('PRINT RECOMMENDATIONS:');
      log('1. Set Excel to Portrait orientation');
      log('2. Use "Scale to Fit" - 1 page width');
      log('3. Adjust margins if needed (Normal margins recommended)');
      log('4. All columns should fit on a single page width');

      // Generate Excel bytes
      final excelBytes = excelWorkbook.encode();
      if (excelBytes == null) {
        throw Exception('Failed to generate Excel file');
      }

      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final fileName =
          'Bill_${currentBill!.billNumber}_${dateFormat.format(DateTime.now())}.xlsx';

      // Download file for web
      if (kIsWeb) {
        await _downloadExcelWeb(Uint8List.fromList(excelBytes), fileName);
      } else {
        throw UnimplementedError(
            'Mobile/Desktop Excel download not implemented');
      }

      log('Excel file generated successfully: $fileName');

      // Show success message
      Get.snackbar(
        'Success',
        'Excel file "$fileName" has been downloaded',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      log('Error generating Excel file: $e');
      Get.snackbar(
        'Error',
        'Failed to generate Excel file: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isExporting.value = false;
    }
  }

  /// Export bill data to PDF with optimized single-page portrait layout
  Future<void> exportToPdf() async {
    if (currentBill == null || linkedInvoices.isEmpty) {
      Get.snackbar(
        'Error',
        'No data to export',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    isExportingPdf.value = true;

    try {
      log('Generating PDF for bill: ${currentBill!.billNumber}');

      // Create PDF document with A4 portrait page format
      final pdf = pw.Document();

      // Calculate totals
      double totalTons = 0.0;
      double total100Amount = 0.0;
      double total80Amount = 0.0;

      for (final invoice in linkedInvoices) {
        final totalWeightTons =
            (invoice.numberOfBags * invoice.weightPerBag) / 1000;
        final hmtRate =
            150.0; // Default rate - should be replaced with actual slab calculation
        final amount100 =
            MonetaryRounding.roundHalfUp(totalWeightTons * hmtRate);
        final amount80 = MonetaryRounding.roundHalfUp(amount100 * 0.80);

        totalTons += totalWeightTons;
        total100Amount += amount100;
        total80Amount += amount80;
      }

      // Add page with optimized layout for single-page portrait printing
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          orientation: pw.PageOrientation.portrait,
          margin: const pw.EdgeInsets.all(20), // 20pt margins ≈ 0.28 inches
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header section
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'BILL # ${currentBill!.billNumber}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      'Total Invoices: ${linkedInvoices.length}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 20),

                // Data table with optimized column widths for single-page portrait
                pw.Table(
                  columnWidths: {
                    0: const pw.FixedColumnWidth(25), // SN
                    1: const pw.FixedColumnWidth(60), // Lifting Date
                    2: const pw.FixedColumnWidth(50), // Truck No
                    3: const pw.FixedColumnWidth(50), // Bilty No
                    4: const pw.FixedColumnWidth(65), // Convey Note
                    5: const pw.FixedColumnWidth(55), // Product
                    6: const pw.FixedColumnWidth(55), // TAS NO
                    7: const pw.FixedColumnWidth(60), // Destination
                    8: const pw.FixedColumnWidth(35), // Bags
                    9: const pw.FixedColumnWidth(40), // Weight
                    10: const pw.FixedColumnWidth(25), // KM
                    11: const pw.FixedColumnWidth(50), // District
                    12: const pw.FixedColumnWidth(60), // HMT Rates
                    13: const pw.FixedColumnWidth(45), // 100% Amount
                    14: const pw.FixedColumnWidth(45), // 80% Amount
                    15: const pw.FixedColumnWidth(45), // Net Amount
                  }, // Total width: ~770pt ≈ 10.7" (fits A4 portrait with scaling)
                  border:
                      pw.TableBorder.all(color: PdfColors.black, width: 0.5),
                  children: [
                    // Header row
                    pw.TableRow(
                      decoration:
                          const pw.BoxDecoration(color: PdfColors.grey200),
                      children: [
                        _buildPdfCell('SN.', isHeader: true),
                        _buildPdfCell('Lifting Date', isHeader: true),
                        _buildPdfCell('Truck No', isHeader: true),
                        _buildPdfCell('Bilty No', isHeader: true),
                        _buildPdfCell('Convey Note Number', isHeader: true),
                        _buildPdfCell('Product Name', isHeader: true),
                        _buildPdfCell('Product TAS NO', isHeader: true),
                        _buildPdfCell('Destination', isHeader: true),
                        _buildPdfCell('No of Bags', isHeader: true),
                        _buildPdfCell('Weight', isHeader: true),
                        _buildPdfCell('KM', isHeader: true),
                        _buildPdfCell('District', isHeader: true),
                        _buildPdfCell('HMT Rates (Non Fuel Inc WHT)',
                            isHeader: true),
                        _buildPdfCell('100% Amount', isHeader: true),
                        _buildPdfCell('80% Amount', isHeader: true),
                        _buildPdfCell('Net Amount', isHeader: true),
                      ],
                    ),
                    // Data rows
                    ...linkedInvoices.asMap().entries.map((entry) {
                      final index = entry.key;
                      final invoice = entry.value;
                      final totalWeightTons =
                          (invoice.numberOfBags * invoice.weightPerBag) / 1000;
                      final totalWeightFormatted = totalWeightTons % 1 == 0
                          ? totalWeightTons.toInt().toString()
                          : totalWeightTons
                              .toStringAsFixed(2)
                              .replaceAll(RegExp(r'\.?0+$'), '');

                      final hmtRate = 150.0;
                      final amount100 = MonetaryRounding.roundHalfUp(
                          totalWeightTons * hmtRate);
                      final amount80 =
                          MonetaryRounding.roundHalfUp(amount100 * 0.80);
                      final netAmount = amount100;

                      return pw.TableRow(
                        children: [
                          _buildPdfCell((index + 1).toString()),
                          _buildPdfCell(invoice.orderDate != null
                              ? DateFormat('dd/MM/yyyy')
                                  .format(invoice.orderDate!)
                              : ''),
                          _buildPdfCell(invoice.truckNumber),
                          _buildPdfCell(invoice.biltyNumber),
                          _buildPdfCell(invoice.conveyNoteNumber),
                          _buildPdfCell(invoice.productName),
                          _buildPdfCell(invoice.tasNumber),
                          _buildPdfCell(invoice.stationName),
                          _buildPdfCell(invoice.numberOfBags.toString()),
                          _buildPdfCell(totalWeightFormatted),
                          _buildPdfCell(
                              invoice.distanceInKilometers.toString()),
                          _buildPdfCell(invoice.districtName),
                          _buildPdfCell(_formatMonetaryAmount(hmtRate)),
                          _buildPdfCell(_formatMonetaryAmount(amount100)),
                          _buildPdfCell(_formatMonetaryAmount(amount80)),
                          _buildPdfCell(_formatMonetaryAmount(netAmount)),
                        ],
                      );
                    }),
                  ],
                ),

                pw.SizedBox(height: 20),

                // Summary section
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                      children: [
                        pw.Text(
                          'Total Weight: ${_formatMonetaryAmount(MonetaryRounding.roundHalfUp(totalTons))} Tons',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                        pw.SizedBox(height: 5),
                        pw.Text(
                          'Bill Amount Exclusive of GST (100%): ${_formatMonetaryAmount(MonetaryRounding.roundHalfUp(total100Amount))}',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                        pw.SizedBox(height: 5),
                        pw.Text(
                          '80% Amount: ${_formatMonetaryAmount(MonetaryRounding.roundHalfUp(total80Amount))}',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      );

      // Generate PDF bytes
      final pdfBytes = await pdf.save();

      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final fileName =
          'Bill_${currentBill!.billNumber}_${dateFormat.format(DateTime.now())}.pdf';

      // Download file for web
      if (kIsWeb) {
        await _downloadPdfWeb(pdfBytes, fileName);
      } else {
        throw UnimplementedError('Mobile/Desktop PDF download not implemented');
      }

      log('PDF file generated successfully: $fileName');

      // Show success message
      Get.snackbar(
        'Success',
        'PDF file "$fileName" has been downloaded',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      log('Error generating PDF file: $e');
      Get.snackbar(
        'Error',
        'Failed to generate PDF file: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isExportingPdf.value = false;
    }
  }

  /// Build PDF table cell with consistent styling
  pw.Widget _buildPdfCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(4),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 8 : 7,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// Download PDF file for web platform
  Future<void> _downloadPdfWeb(Uint8List pdfBytes, String fileName) async {
    try {
      final blob = html.Blob([pdfBytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);

      html.AnchorElement(href: url)
        ..setAttribute('download', fileName)
        ..click();

      html.Url.revokeObjectUrl(url);

      log('PDF file downloaded successfully on web: $fileName');
    } catch (e) {
      log('Error downloading PDF file on web: $e');
      rethrow;
    }
  }

  /// Download Excel file for web platform
  Future<void> _downloadExcelWeb(Uint8List excelBytes, String fileName) async {
    try {
      final blob = html.Blob([excelBytes],
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      final url = html.Url.createObjectUrlFromBlob(blob);

      html.AnchorElement(href: url)
        ..setAttribute('download', fileName)
        ..click();

      html.Url.revokeObjectUrl(url);

      log('Excel file downloaded successfully on web: $fileName');
    } catch (e) {
      log('Error downloading Excel file on web: $e');
      rethrow;
    }
  }

  /// Format monetary amount for display
  String _formatMonetaryAmount(double amount) {
    final rounded = MonetaryRounding.roundHalfUp(amount);
    if (rounded == 0) return '0';

    // Remove unnecessary decimal places
    if (rounded % 1 == 0) {
      return rounded.toInt().toString();
    } else {
      return rounded.toStringAsFixed(2).replaceAll(RegExp(r'\.?0+$'), '');
    }
  }

  @override
  void onClose() {
    linkedInvoices.clear();
    currentBill = null;
    super.onClose();
  }
}
