import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';
import '../../models/finance/loan_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import 'automatic_journal_entry_service.dart';
import 'transaction_account_mapping_service.dart';

/// Service for integrating loan transactions with automatic journal entry generation
class LoanJournalIntegrationService {
  final AutomaticJournalEntryService _automaticJournalService;
  final GeneralLedgerFirebaseService _generalLedgerService;
  final TransactionAccountMappingService _mappingService;
  late final JournalEntryFirebaseService _journalEntryService;

  LoanJournalIntegrationService(
    this._automaticJournalService,
    this._generalLedgerService,
    this._mappingService,
  ) {
    _journalEntryService = JournalEntryFirebaseService();
  }

  /// Get current user's display name for audit trail
  String get _currentUserName =>
      FirebaseAuth.instance.currentUser?.displayName ??
      FirebaseAuth.instance.currentUser?.email ??
      'System User';

  /// Generate and create journal entries for loan disbursement with atomic balance updates
  Future<bool> processLoanDisbursement(LoanModel loan, String uid) async {
    try {
      log('Processing loan disbursement for journal entries: ${loan.id}');

      // Check if journal entries already exist for this loan (both disbursement and receipt)
      final existingDisbursementEntries =
          await _journalEntryService.getJournalEntriesBySource(
        loan.id,
        'loan_disbursement',
      );

      final existingReceiptEntries =
          await _journalEntryService.getJournalEntriesBySource(
        loan.id,
        'loan_receipt',
      );

      if (existingDisbursementEntries.isNotEmpty ||
          existingReceiptEntries.isNotEmpty) {
        log('Journal entries already exist for loan: ${loan.id}');
        return true; // Already processed
      }

      // Generate journal entries for loan disbursement
      final journalEntries =
          await _automaticJournalService.generateLoanJournalEntries(
        loan: loan,
        transactionType: 'disbursement',
        uid: uid,
        createdBy: _currentUserName, // Use actual authenticated user
      );

      if (journalEntries.isEmpty) {
        log('No journal entries generated for loan disbursement: ${loan.id}');
        return true; // Not an error, just no entries needed
      }

      // Create and post all journal entries with atomic balance updates
      bool allSuccessful = true;
      for (final journalEntry in journalEntries) {
        try {
          log('🔄 Processing journal entry for loan disbursement: ${loan.id}');
          log('📝 Journal entry details: ${journalEntry.description}');
          log('💰 Total debits: ${journalEntry.totalDebits}, Total credits: ${journalEntry.totalCredits}');
          log('📊 Lines count: ${journalEntry.lines.length}');

          // Create the journal entry with a unique ID
          final entryWithId = journalEntry.copyWith(
            id: const Uuid().v4(),
          );

          log('🔄 Creating journal entry with ID: ${entryWithId.id}');

          // Create the journal entry
          await _journalEntryService.createJournalEntry(entryWithId);
          log('✅ Journal entry created successfully');

          // Get the created entry to get its ID for posting
          log('🔍 Searching for created journal entry...');
          final createdEntries =
              await _journalEntryService.getJournalEntriesBySource(
            entryWithId.sourceTransactionId ?? '',
            entryWithId.sourceTransactionType ?? '',
          );

          if (createdEntries.isNotEmpty) {
            final createdEntry = createdEntries.first;
            log('✅ Found created journal entry: ${createdEntry.id}');
            log('📝 Entry number: ${createdEntry.entryNumber}');
            log('📊 Entry status: ${createdEntry.status.name}');

            try {
              log('🔄 Posting journal entry to update account balances...');
              // Post the journal entry to update account balances atomically
              await _journalEntryService.postJournalEntry(createdEntry.id);
              log('✅ Successfully created and posted journal entry for loan disbursement: ${loan.id}');
            } catch (e) {
              log('❌ Failed to post journal entry for loan disbursement: $e');
              allSuccessful = false;
            }
          } else {
            log('❌ Failed to find created journal entry for loan disbursement: ${loan.id}');
            log('⚠️ Source transaction ID: ${entryWithId.sourceTransactionId}');
            log('⚠️ Source transaction type: ${entryWithId.sourceTransactionType}');
            allSuccessful = false;
          }
        } catch (e) {
          log('❌ Error processing journal entry for loan disbursement: $e');
          allSuccessful = false;
        }
      }

      return allSuccessful;
    } catch (e) {
      log('Error processing loan disbursement: $e');
      return false;
    }
  }

  /// Generate and create journal entries for loan repayment with atomic balance updates
  Future<bool> processLoanRepayment(LoanModel loan, String uid) async {
    try {
      log('Processing loan repayment for journal entries: ${loan.id}');

      // Check if journal entries already exist for this loan repayment
      final existingRepaymentEntries =
          await _journalEntryService.getJournalEntriesBySource(
        loan.id,
        'loan_repayment',
      );

      if (existingRepaymentEntries.isNotEmpty) {
        log('Journal entries already exist for loan repayment: ${loan.id}');
        return true; // Already processed
      }

      // Generate journal entries for loan repayment
      final journalEntries =
          await _automaticJournalService.generateLoanJournalEntries(
        loan: loan,
        transactionType: 'repayment',
        uid: uid,
        createdBy: _currentUserName, // Use actual authenticated user
      );

      if (journalEntries.isEmpty) {
        log('No journal entries generated for loan repayment: ${loan.id}');
        return true; // Not an error, just no entries needed
      }

      // Create and post all journal entries with atomic balance updates
      bool allSuccessful = true;
      for (final journalEntry in journalEntries) {
        try {
          // Create the journal entry with a unique ID
          final entryWithId = journalEntry.copyWith(
            id: const Uuid().v4(),
          );

          // Create the journal entry
          await _journalEntryService.createJournalEntry(entryWithId);

          // Get the created entry to get its ID for posting
          final createdEntries =
              await _journalEntryService.getJournalEntriesBySource(
            entryWithId.sourceTransactionId ?? '',
            entryWithId.sourceTransactionType ?? '',
          );

          if (createdEntries.isNotEmpty) {
            final createdEntry = createdEntries.first;
            try {
              // Post the journal entry to update account balances atomically
              await _journalEntryService.postJournalEntry(createdEntry.id);
              log('Successfully created and posted journal entry for loan repayment: ${loan.id}');
            } catch (e) {
              log('Failed to post journal entry for loan repayment: $e');
              allSuccessful = false;
            }
          } else {
            log('Failed to find created journal entry for loan repayment: ${loan.id}');
            allSuccessful = false;
          }
        } catch (e) {
          log('Error processing journal entry for loan repayment: $e');
          allSuccessful = false;
        }
      }

      return allSuccessful;
    } catch (e) {
      log('Error processing loan repayment: $e');
      return false;
    }
  }

  /// Batch process multiple loan transactions
  Future<BatchProcessResult> batchProcessLoanTransactions(
    List<LoanModel> loans,
    String transactionType, // 'disbursement' or 'repayment'
    String uid,
  ) async {
    int successCount = 0;
    int failureCount = 0;
    final List<String> failedLoanIds = [];

    log('Batch processing ${loans.length} loan $transactionType transactions');

    for (final loan in loans) {
      bool success = false;
      if (transactionType == 'disbursement') {
        success = await processLoanDisbursement(loan, uid);
      } else if (transactionType == 'repayment') {
        success = await processLoanRepayment(loan, uid);
      }

      if (success) {
        successCount++;
      } else {
        failureCount++;
        failedLoanIds.add(loan.id);
      }
    }

    log('Batch processing completed: $successCount successful, $failureCount failed');

    return BatchProcessResult(
      totalProcessed: loans.length,
      successCount: successCount,
      failureCount: failureCount,
      failedTransactionIds: failedLoanIds,
    );
  }

  /// Validate loan for journal entry generation
  Future<LoanValidationResult> validateLoanForJournalEntry(
    LoanModel loan,
    String uid,
  ) async {
    try {
      final issues = <String>[];

      // Check if loan has valid amount
      if (loan.amount <= 0) {
        issues.add('Loan amount must be greater than zero');
      }

      // Check if loan has valid accounts
      if (loan.fromAccountId.isEmpty) {
        issues.add('From account must be specified');
      }

      if (loan.toAccountId.isEmpty) {
        issues.add('To account must be specified');
      }

      // Check if loan has valid status for the operation
      if (loan.status != 'approved' && loan.status != 'repaid') {
        issues
            .add('Loan must be approved or repaid to generate journal entries');
      }

      // Check if required accounts exist for journal entry generation
      log('🔍 Validating loan accounts for journal entry generation...');
      final mapping = await _mappingService.getLoanAccountMapping(uid);
      if (mapping == null) {
        log('❌ Required accounts for loan journal entries not found');
        issues.add('Required accounts for loan journal entries not found');
      } else {
        log('✅ Found all required loan accounts:');
        log('   - Loan Receivable: ${mapping.loansReceivableAccount.displayName}');
        log('   - Loan Payable: ${mapping.loansPayableAccount.displayName}');
        log('   - Cash/Asset: ${mapping.cashAccount.displayName}');
      }

      return LoanValidationResult(
        isValid: issues.isEmpty,
        issues: issues,
      );
    } catch (e) {
      log('Error validating loan for journal entry: $e');
      return LoanValidationResult(
        isValid: false,
        issues: ['Validation error: $e'],
      );
    }
  }

  /// Get journal entries associated with a loan
  Future<List<JournalEntryModel>> getJournalEntriesForLoan(
    String loanId,
    String uid,
  ) async {
    try {
      final allEntries =
          await _generalLedgerService.getJournalEntries(uid, null, null);

      return allEntries.fold(
        (failure) {
          log('Failed to fetch journal entries: ${failure.message}');
          return <JournalEntryModel>[];
        },
        (entries) {
          return entries
              .where((entry) =>
                  (entry.sourceTransactionId == loanId &&
                      (entry.sourceTransactionType?.startsWith('loan') ==
                          true)) &&
                  entry.uid == uid)
              .toList();
        },
      );
    } catch (e) {
      log('Error fetching journal entries for loan: $e');
      return <JournalEntryModel>[];
    }
  }

  /// Check if a loan already has associated journal entries
  Future<bool> hasExistingJournalEntries(String loanId, String uid) async {
    final entries = await getJournalEntriesForLoan(loanId, uid);
    return entries.isNotEmpty;
  }

  /// Reverse journal entries for a loan (when loan is cancelled/modified)
  Future<bool> reverseLoanJournalEntries(String loanId, String uid) async {
    try {
      final entries = await getJournalEntriesForLoan(loanId, uid);

      if (entries.isEmpty) {
        log('No journal entries found for loan: $loanId');
        return true; // Nothing to reverse
      }

      bool allReversed = true;
      for (final entry in entries) {
        final result = await _generalLedgerService.reverseJournalEntry(
          entry.id,
          'Loan transaction reversed',
          'system', // createdBy parameter
        );

        result.fold(
          (failure) {
            log('Failed to reverse journal entry ${entry.id}: ${failure.message}');
            allReversed = false;
          },
          (success) {
            log('Successfully reversed journal entry: ${entry.id}');
          },
        );
      }

      return allReversed;
    } catch (e) {
      log('Error reversing loan journal entries: $e');
      return false;
    }
  }

  /// Get loan financial summary for reporting
  Future<LoanFinancialSummary> getLoanFinancialSummary(
    LoanModel loan,
    String uid,
  ) async {
    try {
      final hasJournalEntries = await hasExistingJournalEntries(loan.id, uid);

      return LoanFinancialSummary(
        loanId: loan.id,
        amount: loan.amount,
        status: loan.status,
        fromAccountName: loan.fromAccountName,
        toAccountName: loan.toAccountName,
        requestedByName: loan.requestedByName,
        requestedToName: loan.requestedToName,
        dueDate: loan.dueDate,
        hasJournalEntries: hasJournalEntries,
      );
    } catch (e) {
      log('Error getting loan financial summary: $e');
      return LoanFinancialSummary(
        loanId: loan.id,
        amount: 0.0,
        status: 'unknown',
        fromAccountName: '',
        toAccountName: '',
        requestedByName: '',
        requestedToName: '',
        dueDate: DateTime.now(),
        hasJournalEntries: false,
      );
    }
  }
}

/// Result class for batch processing operations
class BatchProcessResult {
  final int totalProcessed;
  final int successCount;
  final int failureCount;
  final List<String> failedTransactionIds;

  BatchProcessResult({
    required this.totalProcessed,
    required this.successCount,
    required this.failureCount,
    required this.failedTransactionIds,
  });

  double get successRate =>
      totalProcessed > 0 ? successCount / totalProcessed : 0.0;
  bool get hasFailures => failureCount > 0;
  bool get allSuccessful => failureCount == 0;
}

/// Result class for loan validation
class LoanValidationResult {
  final bool isValid;
  final List<String> issues;

  LoanValidationResult({
    required this.isValid,
    required this.issues,
  });

  String get issuesText => issues.join(', ');
}

/// Financial summary for a loan
class LoanFinancialSummary {
  final String loanId;
  final double amount;
  final String status;
  final String fromAccountName;
  final String toAccountName;
  final String requestedByName;
  final String requestedToName;
  final DateTime dueDate;
  final bool hasJournalEntries;

  LoanFinancialSummary({
    required this.loanId,
    required this.amount,
    required this.status,
    required this.fromAccountName,
    required this.toAccountName,
    required this.requestedByName,
    required this.requestedToName,
    required this.dueDate,
    required this.hasJournalEntries,
  });
}
