import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'connectivity_service.dart';
import 'offline_state_service.dart';

/// Service to handle startup errors gracefully
class StartupErrorHandler {
  static bool _hasInitializationErrors = false;
  static final List<String> _errors = [];

  /// Check if there are any initialization errors
  static bool get hasErrors => _hasInitializationErrors;

  /// Get list of initialization errors
  static List<String> get errors => List.unmodifiable(_errors);

  /// Handle connectivity service initialization error
  static void handleConnectivityError(dynamic error) {
    final errorMessage = 'Connectivity service failed to initialize: $error';
    log(errorMessage);
    _errors.add(errorMessage);
    _hasInitializationErrors = true;

    // For web platform, this is expected - continue without connectivity monitoring
    if (kIsWeb) {
      log('Running on web platform - connectivity monitoring disabled');
    }
  }

  /// Handle offline service initialization error
  static void handleOfflineServiceError(String serviceName, dynamic error) {
    final errorMessage =
        'Offline service ($serviceName) failed to initialize: $error';
    log(errorMessage);
    _errors.add(errorMessage);
    _hasInitializationErrors = true;
  }

  /// Handle dependency injection error
  static void handleDependencyError(String dependencyName, dynamic error) {
    final errorMessage =
        'Dependency ($dependencyName) failed to initialize: $error';
    log(errorMessage);
    _errors.add(errorMessage);
    _hasInitializationErrors = true;
  }

  /// Try to recover from initialization errors
  static Future<void> attemptRecovery() async {
    log('Attempting to recover from initialization errors...');

    try {
      // Try to register missing services with fallback implementations
      await _registerFallbackServices();
      log('Recovery attempt completed');
    } catch (e) {
      log('Recovery attempt failed: $e');
    }
  }

  /// Register fallback services for critical dependencies
  static Future<void> _registerFallbackServices() async {
    // Register a fallback connectivity service if the real one failed
    if (!Get.isRegistered<ConnectivityService>()) {
      try {
        Get.put(FallbackConnectivityService(), permanent: true);
        log('Registered fallback connectivity service');
      } catch (e) {
        log('Failed to register fallback connectivity service: $e');
      }
    }

    // Register fallback offline state service if needed
    if (!Get.isRegistered<OfflineStateService>()) {
      try {
        Get.put(FallbackOfflineStateService(), permanent: true);
        log('Registered fallback offline state service');
      } catch (e) {
        log('Failed to register fallback offline state service: $e');
      }
    }
  }

  /// Clear all errors (for testing or reset)
  static void clearErrors() {
    _hasInitializationErrors = false;
    _errors.clear();
  }

  /// Get a summary of initialization status
  static Map<String, dynamic> getInitializationSummary() {
    return {
      'hasErrors': _hasInitializationErrors,
      'errorCount': _errors.length,
      'errors': _errors,
      'platform': kIsWeb ? 'web' : 'mobile',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// Fallback connectivity service that assumes online status
class FallbackConnectivityService extends GetxController {
  final Rx<ConnectivityStatus> _status = ConnectivityStatus.online.obs;

  ConnectivityStatus get status => _status.value;
  bool get isOnline => true; // Always assume online in fallback mode
  bool get isOffline => false;
  Stream<ConnectivityStatus> get statusStream => _status.stream;

  @override
  void onInit() {
    super.onInit();
    log('FallbackConnectivityService: Initialized - assuming online status');
  }

  Future<void> refreshStatus() async {
    // No-op in fallback mode
  }

  Future<bool> waitForOnline(
      {Duration timeout = const Duration(minutes: 5)}) async {
    return true; // Always return true in fallback mode
  }

  bool shouldProceedOffline(String operationType) {
    return false; // Never proceed offline in fallback mode
  }
}

/// Fallback offline state service with minimal functionality
class FallbackOfflineStateService extends GetxController {
  final RxBool _isOfflineMode = false.obs;
  final RxInt _pendingOperations = 0.obs;

  bool get isOfflineMode => false; // Always online in fallback mode
  int get pendingOperations => 0;
  String get lastSyncTime => '';

  Stream<bool> get offlineModeStream => _isOfflineMode.stream;
  Stream<int> get pendingOperationsStream => _pendingOperations.stream;

  @override
  void onInit() {
    super.onInit();
    log('FallbackOfflineStateService: Initialized - offline features disabled');
  }

  void onConnectivityRestored() {
    // No-op in fallback mode
  }

  void onConnectivityLost() {
    // No-op in fallback mode
  }

  Future<void> addToSyncQueue({
    required String operationType,
    required Map<String, dynamic> data,
    List<String> dependencies = const [],
  }) async {
    log('FallbackOfflineStateService: Sync queue operation ignored - offline features disabled');
  }

  Map<String, dynamic> getOfflineStats() {
    return {
      'isOfflineMode': false,
      'pendingOperations': 0,
      'lastSyncTime': '',
      'fallbackMode': true,
    };
  }
}

// ConnectivityStatus enum is imported from connectivity_service.dart
