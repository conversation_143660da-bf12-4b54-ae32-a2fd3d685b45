import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:logestics/features/slab/presentation/widgets/km_range_builder_widget.dart';
import 'package:logestics/models/slab/km_range_rate_model.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

void main() {
  group('KM Range Builder Widget Tests', () {
    late List<KmRangeRateModel> capturedRanges;
    late String capturedFormula;

    Widget createTestWidget({
      List<KmRangeRateModel> initialRanges = const [],
      bool readOnly = false,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider(
            create: (context) => ColorNotifier(),
            child: KmRangeBuilderWidget(
              initialRanges: initialRanges,
              onRangesChanged: (ranges) {
                capturedRanges = ranges;
              },
              onFormulaGenerated: (formula) {
                capturedFormula = formula;
              },
              readOnly: readOnly,
            ),
          ),
        ),
      );
    }

    setUp(() {
      capturedRanges = [];
      capturedFormula = '';
    });

    testWidgets('should display KM Range Builder header', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      expect(find.text('KM Range Builder'), findsOneWidget);
      expect(find.byIcon(Icons.route), findsOneWidget);
    });

    testWidgets('should display default range when no initial ranges provided', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // Should have one default range
      expect(capturedRanges.length, equals(1));
      expect(capturedRanges.first.fromKm, equals(1));
      expect(capturedRanges.first.toKm, equals(40));
      expect(capturedRanges.first.rate, equals(1196.38));
    });

    testWidgets('should display initial ranges when provided', (WidgetTester tester) async {
      final initialRanges = [
        KmRangeRateModel(fromKm: 1, toKm: 50, rate: 1000.0, description: 'Test range 1'),
        KmRangeRateModel(fromKm: 51, toKm: 100, rate: 1500.0, description: 'Test range 2'),
      ];

      await tester.pumpWidget(createTestWidget(initialRanges: initialRanges));
      
      expect(capturedRanges.length, equals(2));
      expect(capturedRanges[0].fromKm, equals(1));
      expect(capturedRanges[0].toKm, equals(50));
      expect(capturedRanges[1].fromKm, equals(51));
      expect(capturedRanges[1].toKm, equals(100));
    });

    testWidgets('should generate correct formula for single range', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // Should generate formula for default range
      expect(capturedFormula, contains('IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38'));
    });

    testWidgets('should generate correct formula for multiple ranges', (WidgetTester tester) async {
      final initialRanges = [
        KmRangeRateModel(fromKm: 1, toKm: 40, rate: 1196.38, description: 'Range 1'),
        KmRangeRateModel(fromKm: 41, toKm: 80, rate: 1196.38, description: 'Range 2'),
        KmRangeRateModel(fromKm: 81, toKm: 120, rate: 7.68, description: 'Range 3'),
      ];

      await tester.pumpWidget(createTestWidget(initialRanges: initialRanges));
      
      // Should generate nested IF-ELSE formula
      expect(capturedFormula, contains('IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38'));
      expect(capturedFormula, contains('ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38'));
      expect(capturedFormula, contains('ELSE 7.68'));
    });

    testWidgets('should display Add Range button when not read-only', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(readOnly: false));
      
      expect(find.text('Add Range'), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
    });

    testWidgets('should not display Add Range button when read-only', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(readOnly: true));
      
      expect(find.text('Add Range'), findsNothing);
    });

    testWidgets('should display action buttons when not read-only', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(readOnly: false));
      
      expect(find.text('Load Default Ranges'), findsOneWidget);
      expect(find.text('Clear All'), findsOneWidget);
    });

    testWidgets('should display test calculator', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      expect(find.text('Test Your Ranges'), findsOneWidget);
      expect(find.text('Test Distance (KM)'), findsOneWidget);
      expect(find.text('Enter a distance to test'), findsOneWidget);
    });

    testWidgets('should display formula preview', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      expect(find.text('Generated Formula Preview'), findsOneWidget);
      expect(find.text('This formula will be automatically used in your slab calculation.'), findsOneWidget);
    });

    testWidgets('should display range table headers', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      expect(find.text('From KM'), findsOneWidget);
      expect(find.text('To KM'), findsOneWidget);
      expect(find.text('Rate'), findsOneWidget);
      expect(find.text('Description'), findsOneWidget);
    });

    testWidgets('should validate ranges and show errors for overlapping ranges', (WidgetTester tester) async {
      final overlappingRanges = [
        KmRangeRateModel(fromKm: 1, toKm: 50, rate: 1000.0, description: 'Range 1'),
        KmRangeRateModel(fromKm: 40, toKm: 80, rate: 1500.0, description: 'Range 2'), // Overlaps with Range 1
      ];

      await tester.pumpWidget(createTestWidget(initialRanges: overlappingRanges));
      await tester.pump(); // Allow validation to complete
      
      // Should show validation errors
      expect(find.text('Validation Errors'), findsOneWidget);
      expect(find.byIcon(Icons.warning), findsOneWidget);
    });
  });

  group('KM Range Rate Service Tests', () {
    test('should find correct rate for distance within range', () {
      final ranges = [
        KmRangeRateModel(fromKm: 1, toKm: 40, rate: 1196.38),
        KmRangeRateModel(fromKm: 41, toKm: 80, rate: 1196.38),
        KmRangeRateModel(fromKm: 81, toKm: 120, rate: 7.68),
      ];

      expect(KmRangeRateService.getRateForDistance(ranges, 25), equals(1196.38));
      expect(KmRangeRateService.getRateForDistance(ranges, 50), equals(1196.38));
      expect(KmRangeRateService.getRateForDistance(ranges, 100), equals(7.68));
    });

    test('should return null for distance outside all ranges', () {
      final ranges = [
        KmRangeRateModel(fromKm: 1, toKm: 40, rate: 1196.38),
        KmRangeRateModel(fromKm: 41, toKm: 80, rate: 1196.38),
      ];

      expect(KmRangeRateService.getRateForDistance(ranges, 150), isNull);
    });

    test('should validate ranges correctly', () {
      final validRanges = [
        KmRangeRateModel(fromKm: 1, toKm: 40, rate: 1196.38),
        KmRangeRateModel(fromKm: 41, toKm: 80, rate: 1196.38),
      ];

      final errors = KmRangeRateService.validateRanges(validRanges);
      expect(errors, isEmpty);
    });

    test('should detect overlapping ranges', () {
      final overlappingRanges = [
        KmRangeRateModel(fromKm: 1, toKm: 50, rate: 1196.38),
        KmRangeRateModel(fromKm: 40, toKm: 80, rate: 1196.38), // Overlaps
      ];

      final errors = KmRangeRateService.validateRanges(overlappingRanges);
      expect(errors, isNotEmpty);
      expect(errors.first, contains('Overlapping ranges'));
    });

    test('should create default ranges', () {
      final defaultRanges = KmRangeRateService.createDefaultRanges();
      
      expect(defaultRanges, isNotEmpty);
      expect(defaultRanges.length, greaterThan(5));
      expect(defaultRanges.first.fromKm, equals(1));
      expect(defaultRanges.first.toKm, equals(40));
    });
  });
}
