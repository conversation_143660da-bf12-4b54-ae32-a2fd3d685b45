import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/models/slab/km_range_rate_model.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

/// Visual KM Range Builder Widget
/// Provides an intuitive interface for users to define distance ranges and rates
/// without needing to write complex IF-ELSE formulas manually
class KmRangeBuilderWidget extends StatefulWidget {
  final List<KmRangeRateModel> initialRanges;
  final Function(List<KmRangeRateModel>) onRangesChanged;
  final Function(String) onFormulaGenerated;
  final bool readOnly;

  const KmRangeBuilderWidget({
    super.key,
    this.initialRanges = const [],
    required this.onRangesChanged,
    required this.onFormulaGenerated,
    this.readOnly = false,
  });

  @override
  State<KmRangeBuilderWidget> createState() => _KmRangeBuilderWidgetState();
}

class _KmRangeBuilderWidgetState extends State<KmRangeBuilderWidget> {
  late ColorNotifier notifier;
  List<KmRangeRateModel> ranges = [];
  List<String> validationErrors = [];
  String _testResult = 'Enter a distance to test';

  @override
  void initState() {
    super.initState();
    ranges = List.from(widget.initialRanges);
    if (ranges.isEmpty) {
      _addDefaultRange();
    }
    // Defer validation and formula generation until after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _validateAndGenerateFormula();
    });
  }

  void _addDefaultRange() {
    ranges.add(KmRangeRateModel(
      fromKm: ranges.isEmpty ? 1 : (ranges.last.toKm + 1),
      toKm: ranges.isEmpty ? 40 : (ranges.last.toKm + 40),
      rate: 1196.38,
      description: 'New range',
    ));
  }

  void _addRange() {
    setState(() {
      _addDefaultRange();
    });
    // Call validation after setState to avoid nested setState calls
    _validateAndGenerateFormula();
  }

  void _removeRange(int index) {
    if (ranges.length > 1) {
      setState(() {
        ranges.removeAt(index);
      });
      // Call validation after setState to avoid nested setState calls
      _validateAndGenerateFormula();
    }
  }

  void _updateRange(int index,
      {int? fromKm,
      int? toKm,
      double? rate,
      String? description,
      bool? isPerKmRate}) {
    setState(() {
      ranges[index] = ranges[index].copyWith(
        fromKm: fromKm,
        toKm: toKm,
        rate: rate,
        description: description,
        isPerKmRate: isPerKmRate,
      );
    });
    // Call validation after setState to avoid nested setState calls
    _validateAndGenerateFormula();
  }

  void _validateAndGenerateFormula() {
    // Validate ranges
    final newValidationErrors = KmRangeRateService.validateRanges(ranges);

    // Generate formula
    final formula = _generateFormulaFromRanges();

    // Update state if widget is mounted
    if (mounted) {
      setState(() {
        validationErrors = newValidationErrors;
      });
    } else {
      // If not mounted, update directly (during initialization)
      validationErrors = newValidationErrors;
    }

    // Notify parent components
    widget.onRangesChanged(ranges);
    widget.onFormulaGenerated(formula);
  }

  String _generateFormulaFromRanges() {
    if (ranges.isEmpty) return '';

    // Sort ranges by fromKm
    final sortedRanges = List<KmRangeRateModel>.from(ranges)
      ..sort((a, b) => a.fromKm.compareTo(b.fromKm));

    // Generate nested IF-ELSE formula that handles both fixed and per-KM rates
    String formula = '';
    for (int i = 0; i < sortedRanges.length; i++) {
      final range = sortedRanges[i];

      // For per-KM rates, multiply by distance; for fixed rates, use rate directly
      final rateExpression = range.isPerKmRate
          ? '${range.rate} × distanceInKilometers'
          : '${range.rate}';

      if (i == 0) {
        // First condition
        formula =
            'IF distanceInKilometers >= ${range.fromKm} AND distanceInKilometers <= ${range.toKm} THEN $rateExpression';
      } else if (i == sortedRanges.length - 1) {
        // Last condition (ELSE)
        formula += ' ELSE $rateExpression';
      } else {
        // Middle conditions
        formula +=
            ' ELSE IF distanceInKilometers >= ${range.fromKm} AND distanceInKilometers <= ${range.toKm} THEN $rateExpression';
      }
    }

    return formula;
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: notifier.getfillborder),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildRangesTable(),
          const SizedBox(height: 16),
          if (!widget.readOnly) _buildActionButtons(),
          if (validationErrors.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildValidationErrors(),
          ],
          const SizedBox(height: 16),
          _buildTestCalculator(),
          const SizedBox(height: 16),
          _buildFormulaPreview(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.route,
          color: notifier.text,
          size: 24,
        ),
        const SizedBox(width: 12),
        Text(
          'KM Range Builder',
          style: AppTextStyles.invoiceHeaderStyle.copyWith(
            color: notifier.text,
            fontSize: 18,
          ),
        ),
        const Spacer(),
        if (!widget.readOnly)
          ElevatedButton.icon(
            onPressed: _addRange,
            icon: const Icon(Icons.add, size: 16),
            label: const Text('Add Range'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xff0f79f3),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
      ],
    );
  }

  Widget _buildRangesTable() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: notifier.getfillborder),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: notifier.getHoverColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'From KM',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'To KM',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Rate',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Description',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Per KM',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                ),
                if (!widget.readOnly)
                  SizedBox(
                    width: 50,
                    child: Text(
                      'Action',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: notifier.text,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Rows
          ...ranges.asMap().entries.map((entry) {
            final index = entry.key;
            final range = entry.value;
            return _buildRangeRow(index, range);
          }),
        ],
      ),
    );
  }

  Widget _buildRangeRow(int index, KmRangeRateModel range) {
    final isEvenRow = index % 2 == 0;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isEvenRow
            ? notifier.getBgColor
            : notifier.getHoverColor.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: notifier.getfillborder.withOpacity(0.5),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // From KM
          Expanded(
            flex: 2,
            child: _buildNumberField(
              value: range.fromKm.toString(),
              onChanged: (value) {
                final fromKm = int.tryParse(value);
                if (fromKm != null) {
                  _updateRange(index, fromKm: fromKm);
                }
              },
              readOnly: widget.readOnly,
            ),
          ),
          const SizedBox(width: 8),
          // To KM
          Expanded(
            flex: 2,
            child: _buildNumberField(
              value: range.toKm.toString(),
              onChanged: (value) {
                final toKm = int.tryParse(value);
                if (toKm != null) {
                  _updateRange(index, toKm: toKm);
                }
              },
              readOnly: widget.readOnly,
            ),
          ),
          const SizedBox(width: 8),
          // Rate
          Expanded(
            flex: 3,
            child: _buildNumberField(
              value: range.rate.toString(),
              onChanged: (value) {
                final rate = double.tryParse(value);
                if (rate != null) {
                  _updateRange(index, rate: rate);
                }
              },
              readOnly: widget.readOnly,
              isDecimal: true,
            ),
          ),
          const SizedBox(width: 8),
          // Description
          Expanded(
            flex: 3,
            child: _buildTextField(
              value: range.description ?? '',
              onChanged: (value) {
                _updateRange(index, description: value);
              },
              readOnly: widget.readOnly,
            ),
          ),
          const SizedBox(width: 8),
          // Per KM Rate checkbox
          Expanded(
            flex: 2,
            child: widget.readOnly
                ? Text(
                    range.isPerKmRate ? 'Yes' : 'No',
                    style: TextStyle(
                      color: notifier.text,
                      fontSize: 14,
                    ),
                  )
                : Checkbox(
                    value: range.isPerKmRate,
                    onChanged: (value) {
                      _updateRange(index, isPerKmRate: value ?? false);
                    },
                    activeColor: const Color(0xff0f79f3),
                  ),
          ),
          if (!widget.readOnly) ...[
            const SizedBox(width: 8),
            // Action
            SizedBox(
              width: 50,
              child: IconButton(
                onPressed: ranges.length > 1 ? () => _removeRange(index) : null,
                icon: Icon(
                  Icons.delete,
                  color: ranges.length > 1 ? Colors.red : Colors.grey,
                  size: 20,
                ),
                tooltip: ranges.length > 1
                    ? 'Remove range'
                    : 'Cannot remove last range',
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNumberField({
    required String value,
    required Function(String) onChanged,
    required bool readOnly,
    bool isDecimal = false,
  }) {
    return TextFormField(
      initialValue: value,
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        isDense: true,
      ),
      keyboardType: isDecimal
          ? TextInputType.numberWithOptions(decimal: true)
          : TextInputType.number,
      inputFormatters: isDecimal
          ? [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))]
          : [FilteringTextInputFormatter.digitsOnly],
      readOnly: readOnly,
      onChanged: onChanged,
      style: TextStyle(
        color: notifier.text,
        fontSize: 14,
      ),
    );
  }

  Widget _buildTextField({
    required String value,
    required Function(String) onChanged,
    required bool readOnly,
  }) {
    return TextFormField(
      initialValue: value,
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        isDense: true,
      ),
      readOnly: readOnly,
      onChanged: onChanged,
      style: TextStyle(
        color: notifier.text,
        fontSize: 14,
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        ElevatedButton.icon(
          onPressed: _loadDefaultRanges,
          icon: const Icon(Icons.restore, size: 16),
          label: const Text('Load Default Ranges'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _clearAllRanges,
          icon: const Icon(Icons.clear_all, size: 16),
          label: const Text('Clear All'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildValidationErrors() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning,
                color: Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Validation Errors',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...validationErrors.map((error) => Padding(
                padding: const EdgeInsets.only(left: 28, bottom: 4),
                child: Text(
                  '• $error',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 14,
                  ),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildFormulaPreview() {
    final formula = _generateFormulaFromRanges();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: notifier.getHoverColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: notifier.getfillborder),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.code,
                color: notifier.text,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Generated Formula Preview',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: notifier.getBgColor,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: notifier.getfillborder),
            ),
            child: Text(
              formula.isEmpty ? 'No formula generated' : formula,
              style: TextStyle(
                color: notifier.text,
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This formula will be automatically used in your slab calculation.',
            style: TextStyle(
              color: notifier.text.withOpacity(0.7),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _loadDefaultRanges() {
    setState(() {
      ranges = KmRangeRateService.createDefaultRanges();
    });
    // Call validation after setState to avoid nested setState calls
    _validateAndGenerateFormula();
  }

  void _clearAllRanges() {
    setState(() {
      ranges.clear();
      _addDefaultRange();
    });
    // Call validation after setState to avoid nested setState calls
    _validateAndGenerateFormula();
  }

  void _testDistance(String value) {
    if (value.isEmpty) {
      setState(() {
        _testResult = 'Enter a distance to test';
      });
      return;
    }

    final distance = double.tryParse(value);
    if (distance == null) {
      setState(() {
        _testResult = 'Invalid distance';
      });
      return;
    }

    final applicableRange =
        KmRangeRateService.findRateForDistance(ranges, distance);
    if (applicableRange != null) {
      setState(() {
        _testResult =
            'Rate: ₹${applicableRange.rate} (${applicableRange.rangeString})';
      });
    } else {
      setState(() {
        _testResult = 'No rate found for $distance KM';
      });
    }
  }

  Widget _buildTestCalculator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xff0f79f3).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border:
            Border.all(color: const Color(0xff0f79f3).withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calculate,
                color: const Color(0xff0f79f3),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Test Your Ranges',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xff0f79f3),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Enter a distance to see which rate would be applied:',
            style: TextStyle(
              color: notifier.text,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Test Distance (KM)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    isDense: true,
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))
                  ],
                  onChanged: _testDistance,
                  style: TextStyle(
                    color: notifier.text,
                    fontSize: 14,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 3,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: notifier.getBgColor,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: notifier.getfillborder),
                  ),
                  child: Text(
                    _testResult,
                    style: TextStyle(
                      color: notifier.text,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
