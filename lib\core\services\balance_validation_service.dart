import 'dart:developer' as dev;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';

import 'account_type_helper_service.dart';

/// Service to validate and correct account balance discrepancies
/// Ensures stored balances match calculated balances from journal entries
class BalanceValidationService {
  final ChartOfAccountsRepository _accountsRepository;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  BalanceValidationService({
    required ChartOfAccountsRepository accountsRepository,
  }) : _accountsRepository = accountsRepository;

  /// Validate all account balances and return validation report
  Future<BalanceValidationReport> validateAllAccountBalances(String uid) async {
    dev.log('🔍 Starting balance validation for company: $uid');

    final report =
        BalanceValidationReport(uid: uid, validatedAt: DateTime.now());

    try {
      final accountsResult = await _accountsRepository.getAccounts();
      final accounts = accountsResult.fold(
        (failure) {
          report.errors.add('Failed to load accounts: ${failure.message}');
          return <ChartOfAccountsModel>[];
        },
        (accounts) => accounts,
      );

      dev.log('📊 Validating ${accounts.length} accounts...');

      for (final account in accounts) {
        try {
          final validation = await _validateAccountBalance(account, uid);
          report.accountValidations.add(validation);

          if (!validation.isValid) {
            report.invalidAccounts++;
            dev.log(
                '❌ Balance mismatch: ${account.accountName} - Stored: ${account.balance}, Calculated: ${validation.calculatedBalance}');
          } else {
            report.validAccounts++;
          }
        } catch (e) {
          report.errors
              .add('Error validating account ${account.accountName}: $e');
          dev.log('❌ Error validating account ${account.accountName}: $e');
        }
      }

      report.totalAccounts = accounts.length;
      report.success = true;

      dev.log(
          '✅ Balance validation completed: ${report.validAccounts} valid, ${report.invalidAccounts} invalid');
    } catch (e) {
      report.success = false;
      report.errors.add('Validation failed: $e');
      dev.log('❌ Balance validation failed: $e');
    }

    return report;
  }

  /// Validate a single account balance
  Future<AccountBalanceValidation> _validateAccountBalance(
      ChartOfAccountsModel account, String uid) async {
    final validation = AccountBalanceValidation(
      accountId: account.id,
      accountName: account.accountName,
      accountType: account.accountType,
      storedBalance: account.balance,
    );

    try {
      // Calculate balance from journal entries
      validation.calculatedBalance = await _calculateBalanceFromJournalEntries(
          account.id, uid, account.accountType);

      // Check if balances match (within 1 cent tolerance)
      final difference =
          (validation.calculatedBalance - validation.storedBalance).abs();
      validation.difference = difference;
      validation.isValid = difference <= 0.01;

      if (!validation.isValid) {
        validation.issues.add(
            'Balance mismatch: stored ${validation.storedBalance}, calculated ${validation.calculatedBalance}');
      }

      // Additional validations
      await _performAdditionalValidations(validation, account, uid);
    } catch (e) {
      validation.isValid = false;
      validation.issues.add('Error calculating balance: $e');
      dev.log('❌ Error validating balance for ${account.accountName}: $e');
    }

    return validation;
  }

  /// Calculate account balance from journal entries using proper accounting principles
  Future<double> _calculateBalanceFromJournalEntries(
      String accountId, String uid, AccountType accountType) async {
    double balance = 0.0;

    try {
      final entriesSnapshot = await _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .where('status',
              isEqualTo: 'posted') // Only posted entries affect balance
          .orderBy('createdAt') // Process in creation order
          .get();

      for (final doc in entriesSnapshot.docs) {
        try {
          final entry = JournalEntryModel.fromFirestore(doc);

          for (final line in entry.lines) {
            if (line.accountId == accountId) {
              final balanceChange =
                  AccountTypeHelperService.calculateBalanceChange(
                accountType: accountType,
                debitAmount: line.debitAmount,
                creditAmount: line.creditAmount,
              );
              balance += balanceChange;
            }
          }
        } catch (e) {
          dev.log('❌ Error processing journal entry ${doc.id}: $e');
        }
      }
    } catch (e) {
      dev.log('❌ Error calculating balance from journal entries: $e');
      rethrow;
    }

    return balance;
  }

  /// Perform additional validations on account
  Future<void> _performAdditionalValidations(
      AccountBalanceValidation validation,
      ChartOfAccountsModel account,
      String uid) async {
    try {
      // Check for negative balances in accounts that shouldn't have them
      if (validation.calculatedBalance < 0) {
        switch (account.accountType.category) {
          case AccountCategory.assets:
            validation.warnings.add('Asset account has negative balance');
            break;
          case AccountCategory.revenue:
            validation.warnings.add('Revenue account has negative balance');
            break;
          default:
            // Negative balances are normal for liabilities, equity, and some expenses
            break;
        }
      }

      // Check for unusually large balances
      if (validation.calculatedBalance.abs() > 1000000) {
        validation.warnings.add(
            'Account has unusually large balance: ${validation.calculatedBalance}');
      }

      // Check for accounts with transactions but zero balance
      if (validation.calculatedBalance == 0) {
        final hasTransactions = await _accountHasTransactions(account.id, uid);
        if (hasTransactions) {
          validation.warnings.add(
              'Account has transactions but zero balance - may indicate offsetting entries');
        }
      }
    } catch (e) {
      validation.warnings.add('Error performing additional validations: $e');
    }
  }

  /// Check if account has any transactions
  Future<bool> _accountHasTransactions(String accountId, String uid) async {
    try {
      final snapshot = await _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .where('lines', arrayContains: {'accountId': accountId})
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      dev.log('❌ Error checking transactions for account $accountId: $e');
      return false;
    }
  }

  /// Correct balance discrepancies by updating stored balances
  Future<BalanceCorrectionResult> correctBalanceDiscrepancies(String uid,
      {bool dryRun = false}) async {
    dev.log(
        '🔧 Starting balance correction for company: $uid (dryRun: $dryRun)');

    final result = BalanceCorrectionResult(
        uid: uid, correctedAt: DateTime.now(), dryRun: dryRun);

    try {
      final validationReport = await validateAllAccountBalances(uid);

      if (!validationReport.success) {
        result.success = false;
        result.errors.addAll(validationReport.errors);
        return result;
      }

      final invalidValidations =
          validationReport.accountValidations.where((v) => !v.isValid).toList();

      dev.log(
          '📊 Found ${invalidValidations.length} accounts with balance discrepancies');

      for (final validation in invalidValidations) {
        try {
          if (dryRun) {
            result.corrections.add(BalanceCorrection(
              accountId: validation.accountId,
              accountName: validation.accountName,
              oldBalance: validation.storedBalance,
              newBalance: validation.calculatedBalance,
              difference: validation.difference,
              corrected: false,
            ));
          } else {
            await _correctAccountBalance(validation, uid);
            result.corrections.add(BalanceCorrection(
              accountId: validation.accountId,
              accountName: validation.accountName,
              oldBalance: validation.storedBalance,
              newBalance: validation.calculatedBalance,
              difference: validation.difference,
              corrected: true,
            ));
            result.correctedCount++;
          }
        } catch (e) {
          result.errors.add(
              'Error correcting balance for ${validation.accountName}: $e');
          dev.log(
              '❌ Error correcting balance for ${validation.accountName}: $e');
        }
      }

      result.success = true;
      dev.log(
          '✅ Balance correction completed: ${result.correctedCount} accounts corrected');
    } catch (e) {
      result.success = false;
      result.errors.add('Balance correction failed: $e');
      dev.log('❌ Balance correction failed: $e');
    }

    return result;
  }

  /// Correct a single account balance
  Future<void> _correctAccountBalance(
      AccountBalanceValidation validation, String uid) async {
    try {
      await _firestore
          .collection('chart_of_accounts')
          .where('id', isEqualTo: validation.accountId)
          .where('uid', isEqualTo: uid)
          .get()
          .then((snapshot) async {
        if (snapshot.docs.isNotEmpty) {
          await snapshot.docs.first.reference.update({
            'balance': validation.calculatedBalance,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
            'balanceCorrectionNote':
                'Balance corrected from ${validation.storedBalance} to ${validation.calculatedBalance} during validation',
          });

          dev.log(
              '🔧 Corrected balance for ${validation.accountName}: ${validation.storedBalance} → ${validation.calculatedBalance}');
        }
      });
    } catch (e) {
      dev.log('❌ Error updating balance for ${validation.accountName}: $e');
      rethrow;
    }
  }
}

/// Report of balance validation results
class BalanceValidationReport {
  final String uid;
  final DateTime validatedAt;
  bool success = false;
  int totalAccounts = 0;
  int validAccounts = 0;
  int invalidAccounts = 0;
  List<AccountBalanceValidation> accountValidations = [];
  List<String> errors = [];

  BalanceValidationReport({required this.uid, required this.validatedAt});

  @override
  String toString() {
    return 'BalanceValidationReport(success: $success, total: $totalAccounts, valid: $validAccounts, invalid: $invalidAccounts, errors: ${errors.length})';
  }
}

/// Validation result for a single account
class AccountBalanceValidation {
  final String accountId;
  final String accountName;
  final AccountType accountType;
  final double storedBalance;
  double calculatedBalance = 0.0;
  double difference = 0.0;
  bool isValid = false;
  List<String> issues = [];
  List<String> warnings = [];

  AccountBalanceValidation({
    required this.accountId,
    required this.accountName,
    required this.accountType,
    required this.storedBalance,
  });
}

/// Result of balance correction operation
class BalanceCorrectionResult {
  final String uid;
  final DateTime correctedAt;
  final bool dryRun;
  bool success = false;
  int correctedCount = 0;
  List<BalanceCorrection> corrections = [];
  List<String> errors = [];

  BalanceCorrectionResult(
      {required this.uid, required this.correctedAt, required this.dryRun});

  @override
  String toString() {
    return 'BalanceCorrectionResult(success: $success, corrected: $correctedCount, dryRun: $dryRun, errors: ${errors.length})';
  }
}

/// Individual balance correction
class BalanceCorrection {
  final String accountId;
  final String accountName;
  final double oldBalance;
  final double newBalance;
  final double difference;
  final bool corrected;

  BalanceCorrection({
    required this.accountId,
    required this.accountName,
    required this.oldBalance,
    required this.newBalance,
    required this.difference,
    required this.corrected,
  });

  @override
  String toString() {
    return 'BalanceCorrection($accountName: $oldBalance → $newBalance, diff: $difference, corrected: $corrected)';
  }
}
