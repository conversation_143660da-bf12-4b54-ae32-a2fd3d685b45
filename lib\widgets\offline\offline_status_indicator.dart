import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/services/connectivity_service.dart';
import '../../core/services/offline_state_service.dart';
import '../../core/services/sync_service.dart';

/// Widget that displays the current offline/online status and sync progress
class OfflineStatusIndicator extends StatelessWidget {
  final bool showDetails;
  final VoidCallback? onTap;
  
  const OfflineStatusIndicator({
    super.key,
    this.showDetails = false,
    this.onTap,
  });
  
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ConnectivityService>(
      builder: (connectivityService) {
        return GetBuilder<OfflineStateService>(
          builder: (offlineStateService) {
            return GetBuilder<SyncService>(
              builder: (syncService) {
                return GestureDetector(
                  onTap: onTap ?? () => _showOfflineStatusDialog(context),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getStatusColor(connectivityService, offlineStateService, syncService),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getStatusIcon(connectivityService, offlineStateService, syncService),
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          _getStatusText(connectivityService, offlineStateService, syncService),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (showDetails && offlineStateService.pendingOperations > 0) ...[
                          const SizedBox(width: 6),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              '${offlineStateService.pendingOperations}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
  
  Color _getStatusColor(ConnectivityService connectivity, OfflineStateService offline, SyncService sync) {
    if (connectivity.isOffline) {
      return Colors.orange;
    } else if (sync.isSyncing) {
      return Colors.blue;
    } else if (offline.pendingOperations > 0) {
      return Colors.amber;
    } else {
      return Colors.green;
    }
  }
  
  IconData _getStatusIcon(ConnectivityService connectivity, OfflineStateService offline, SyncService sync) {
    if (connectivity.isOffline) {
      return Icons.cloud_off;
    } else if (sync.isSyncing) {
      return Icons.sync;
    } else if (offline.pendingOperations > 0) {
      return Icons.cloud_upload;
    } else {
      return Icons.cloud_done;
    }
  }
  
  String _getStatusText(ConnectivityService connectivity, OfflineStateService offline, SyncService sync) {
    if (connectivity.isOffline) {
      return 'Offline';
    } else if (sync.isSyncing) {
      return 'Syncing...';
    } else if (offline.pendingOperations > 0) {
      return 'Pending';
    } else {
      return 'Online';
    }
  }
  
  void _showOfflineStatusDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const OfflineStatusDialog(),
    );
  }
}

/// Detailed offline status dialog
class OfflineStatusDialog extends StatelessWidget {
  const OfflineStatusDialog({super.key});
  
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ConnectivityService>(
      builder: (connectivityService) {
        return GetBuilder<OfflineStateService>(
          builder: (offlineStateService) {
            return GetBuilder<SyncService>(
              builder: (syncService) {
                return AlertDialog(
                  title: Row(
                    children: [
                      Icon(
                        connectivityService.isOnline ? Icons.cloud_done : Icons.cloud_off,
                        color: connectivityService.isOnline ? Colors.green : Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      Text(connectivityService.isOnline ? 'Online' : 'Offline'),
                    ],
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStatusRow(
                        'Connection Status',
                        connectivityService.isOnline ? 'Connected' : 'Disconnected',
                        connectivityService.isOnline ? Colors.green : Colors.red,
                      ),
                      const SizedBox(height: 8),
                      _buildStatusRow(
                        'Pending Operations',
                        '${offlineStateService.pendingOperations}',
                        offlineStateService.pendingOperations > 0 ? Colors.orange : Colors.green,
                      ),
                      const SizedBox(height: 8),
                      _buildStatusRow(
                        'Sync Status',
                        syncService.syncStatus,
                        syncService.isSyncing ? Colors.blue : Colors.grey,
                      ),
                      if (syncService.isSyncing) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Sync Progress',
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: syncService.syncProgress,
                          backgroundColor: Colors.grey[300],
                          valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${(syncService.syncProgress * 100).toInt()}% complete',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                      if (offlineStateService.lastSyncTime.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        _buildStatusRow(
                          'Last Sync',
                          _formatLastSyncTime(offlineStateService.lastSyncTime),
                          Colors.grey,
                        ),
                      ],
                    ],
                  ),
                  actions: [
                    if (connectivityService.isOnline && offlineStateService.pendingOperations > 0)
                      TextButton(
                        onPressed: () {
                          syncService.triggerSync();
                          Navigator.of(context).pop();
                        },
                        child: const Text('Sync Now'),
                      ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Close'),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }
  
  Widget _buildStatusRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }
  
  String _formatLastSyncTime(String isoString) {
    try {
      final dateTime = DateTime.parse(isoString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inHours < 1) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inDays < 1) {
        return '${difference.inHours}h ago';
      } else {
        return '${difference.inDays}d ago';
      }
    } catch (e) {
      return 'Unknown';
    }
  }
}

/// Sync progress indicator widget
class SyncProgressIndicator extends StatelessWidget {
  final bool showText;
  
  const SyncProgressIndicator({
    super.key,
    this.showText = true,
  });
  
  @override
  Widget build(BuildContext context) {
    return GetBuilder<SyncService>(
      builder: (syncService) {
        if (!syncService.isSyncing) {
          return const SizedBox.shrink();
        }
        
        return Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.withOpacity(0.3)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (showText) ...[
                          Text(
                            'Syncing Operations',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: Colors.blue,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                        ],
                        LinearProgressIndicator(
                          value: syncService.syncProgress,
                          backgroundColor: Colors.grey[300],
                          valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (showText) ...[
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      syncService.syncStatus,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.blue,
                      ),
                    ),
                    Text(
                      '${syncService.completedOperations}/${syncService.totalOperations}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.blue,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}

/// Offline mode banner
class OfflineModeBanner extends StatelessWidget {
  const OfflineModeBanner({super.key});
  
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ConnectivityService>(
      builder: (connectivityService) {
        if (connectivityService.isOnline) {
          return const SizedBox.shrink();
        }
        
        return GetBuilder<OfflineStateService>(
          builder: (offlineStateService) {
            return Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.orange,
              child: Row(
                children: [
                  const Icon(
                    Icons.cloud_off,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'You are offline. ${offlineStateService.pendingOperations} operations will sync when online.',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
