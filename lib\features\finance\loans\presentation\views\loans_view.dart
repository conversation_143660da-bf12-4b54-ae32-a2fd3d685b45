import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/features/finance/loans/presentation/controllers/loans_controller.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:provider/provider.dart';
import 'package:logestics/main.dart';
import 'package:logestics/features/voucher/presentation/widgets/chart_of_accounts_dropdown.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
// DEBUG IMPORT - COMMENTED OUT FOR PRODUCTION
// import 'package:logestics/debug/loan_request_test_widget.dart';
import 'package:logestics/debug/loan_account_mapping_test.dart';

class LoansView extends StatefulWidget {
  const LoansView({super.key});

  @override
  State<LoansView> createState() => _LoansViewState();
}

class _LoansViewState extends State<LoansView> {
  late final LoansController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<LoansController>();

    // Defer data loading to after the initial build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadAllLoans();
    });
  }

  @override
  void didUpdateWidget(LoansView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Refresh data when widget is updated (e.g., when navigating back to this screen)
    _refreshData();
  }

  /// Refresh data when screen becomes visible
  void _refreshData() {
    if (controller.isScreenActive) {
      controller.onScreenVisible();
    }
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return RefreshIndicator(
          onRefresh: () async {
            await controller.forceRefresh();
          },
          child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: width < 650 ? 55 : 40,
                  width: width,
                  child: width < 650
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Loans Management',
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyles.titleStyle
                                  .copyWith(color: notifier.text),
                            ),
                            const Spacer(),
                          ],
                        )
                      : Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Loans Management',
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyles.titleStyle
                                  .copyWith(color: notifier.text),
                            ),
                            const Spacer(),
                            Row(
                              children: [
                                IconButton(
                                  onPressed: () async {
                                    await controller.forceRefresh();
                                  },
                                  icon: Icon(
                                    Icons.refresh,
                                    color: notifier.text,
                                  ),
                                  tooltip: 'Refresh Data',
                                ),
                                const SizedBox(width: 16),
                                ElevatedButton.icon(
                                  onPressed: () =>
                                      _showLoanRequestForm(context),
                                  icon: const Icon(Icons.add),
                                  label: const Text('Request Loan'),
                                ),
                              ],
                            ),
                          ],
                        ),
                ),
                const SizedBox(height: 20),
                // DEBUG WIDGET - COMMENTED OUT FOR PRODUCTION
                // Test widget for loan request bug fix
                // const LoanRequestTestWidget(),
                const SizedBox(height: 20),
                // Test widget for loan account mapping
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: notifier.getBgColor,
                    borderRadius: BorderRadius.circular(10),
                    border:
                        Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.account_balance, color: Colors.orange),
                          const SizedBox(width: 8),
                          Text(
                            'Loan Account Mapping Test',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: notifier.text,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Test the loan account mapping functionality to debug journal entry creation issues.',
                        style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7)),
                      ),
                      const SizedBox(height: 12),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const LoanAccountMappingTestWidget(),
                            ),
                          );
                        },
                        icon: const Icon(Icons.bug_report),
                        label: const Text('Test Account Mapping'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                // Loan Account Settings
                _buildLoanAccountSettings(),
                const SizedBox(height: 20),
                _buildNavigationSection(),
                const SizedBox(height: 24),
                _buildQuickStatsSection(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildNavigationSection() {
    return Container(
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Loan Features',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: notifier.text,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 160,
            child: Row(
              children: [
                Expanded(
                  child: _buildNavCard(
                    title: 'Loan Requests',
                    icon: Icons.request_page,
                    description:
                        'View and manage incoming and outgoing loan requests',
                    onTap: () => Get.toNamed('/finance/loans/requests'),
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildNavCard(
                    title: 'Active Loans',
                    icon: Icons.account_balance,
                    description:
                        'Manage your current active loans and repayments',
                    onTap: () => Get.toNamed('/finance/loans/active'),
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildNavCard(
                    title: 'Loan History',
                    icon: Icons.history,
                    description:
                        'View complete history of all loan transactions',
                    onTap: () => Get.toNamed('/finance/loans/history'),
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStatsSection() {
    return Obx(() {
      if (controller.isLoadingActive.value ||
          controller.isLoadingIncoming.value ||
          controller.isLoadingOutgoing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return Container(
        decoration: BoxDecoration(
          color: notifier.getBgColor,
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Overview',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: notifier.text,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 100,
              child: Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Pending Requests',
                      controller.incomingRequests.length.toString(),
                      Icons.pending_actions,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'Active Loans',
                      controller.activeLoans.length.toString(),
                      Icons.account_balance_wallet,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'Your Requests',
                      controller.outgoingRequests.length.toString(),
                      Icons.send,
                      Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildNavCard(
      {required String title,
      required IconData icon,
      required String description,
      required VoidCallback onTap,
      required Color color}) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: color, size: 24),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Text(
                  description,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Colors.grey,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 3,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLoanRequestForm(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Request Loan'),
          content: SizedBox(
            width: 500,
            child: Form(
              key: controller.formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Obx(() {
                    if (controller.isLoadingChartAccounts.value) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (controller.chartOfAccounts.isEmpty) {
                      return Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: const Text(
                            'No Chart of Accounts available. Please add an Asset account first.'),
                      );
                    }

                    return AssetAccountDropdown(
                      labelText: 'Borrower Account',
                      hintText:
                          'Select the account that will receive the loan funds',
                      selectedAccount: controller.selectedChartAccount.value,
                      onChanged: (ChartOfAccountsModel? account) {
                        if (account != null && !controller.isProcessing.value) {
                          controller.setSelectedChartAccount(account);
                        }
                      },
                      isRequired: true,
                      validator: controller.validateChartAccount,
                    );
                  }),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: controller.amountController,
                    decoration: InputDecoration(
                      labelText: 'Amount (PKR)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    keyboardType: TextInputType.number,
                    enabled: !controller.isProcessing.value,
                    validator: controller.validateAmount,
                  ),
                  const SizedBox(height: 16),
                  Obx(() {
                    if (controller.isLoadingUsers.value) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (controller.availableCompanies.isEmpty) {
                      return Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: const Text('No companies available.'),
                      );
                    }

                    // Validate that selected company exists in the list
                    final validSelectedCompany = controller
                                    .selectedCompany.value !=
                                null &&
                            controller.availableCompanies.any((company) =>
                                company.uid == controller.selectedCompany.value)
                        ? controller.selectedCompany.value
                        : null;

                    return DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: 'Request From (User)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        filled: true,
                        fillColor: notifier.textFileColor,
                      ),
                      value: validSelectedCompany,
                      items: controller.availableCompanies.map((user) {
                        return DropdownMenuItem<String>(
                          value: user.uid,
                          child: Text(user.companyName),
                        );
                      }).toList(),
                      onChanged: controller.isProcessing.value
                          ? null
                          : (String? userId) {
                              if (userId != null) {
                                controller.setSelectedCompany(userId);
                              }
                            },
                      validator: controller.validateCompany,
                    );
                  }),
                  const SizedBox(height: 16),
                  InkWell(
                    onTap: controller.isProcessing.value
                        ? null
                        : () async {
                            final DateTime? picked = await showDatePicker(
                              context: context,
                              initialDate: controller.selectedDueDate.value ??
                                  DateTime.now().add(const Duration(days: 30)),
                              firstDate: DateTime.now(),
                              lastDate:
                                  DateTime.now().add(const Duration(days: 365)),
                            );
                            if (picked != null) {
                              controller.setDueDate(picked);
                            }
                          },
                    child: Obx(() {
                      return TextFormField(
                        decoration: InputDecoration(
                          labelText: 'Due Date',
                          suffixIcon: const Icon(Icons.calendar_today),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        enabled: false,
                        controller: TextEditingController(
                          text: _formatDate(controller.selectedDueDate.value ??
                              DateTime.now().add(const Duration(days: 30))),
                        ),
                        validator: controller.validateDueDate,
                      );
                    }),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: controller.notesController,
                    decoration: InputDecoration(
                      labelText: 'Notes',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: notifier.textFileColor,
                    ),
                    enabled: !controller.isProcessing.value,
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: controller.isProcessing.value
                  ? null
                  : () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            Obx(() {
              return ElevatedButton(
                onPressed: controller.isProcessing.value
                    ? null
                    : () async {
                        if (controller.formKey.currentState!.validate()) {
                          final success = await controller.submitLoanRequest();
                          if (success) {
                            // ignore: use_build_context_synchronously
                            Navigator.pop(context);
                          }
                        }
                      },
                child: controller.isProcessing.value
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Submit Request'),
              );
            }),
          ],
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Widget _buildLoanAccountSettings() {
    return Obx(() {
      return Container(
        decoration: BoxDecoration(
          color: notifier.cardColor,
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with toggle button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Icons.settings, color: Colors.blue),
                    const SizedBox(width: 8),
                    Text(
                      'Loan Account Settings',
                      style: AppTextStyles.titleStyle.copyWith(
                        color: notifier.text,
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
                IconButton(
                  icon: Icon(
                    controller.showLoanAccountSettings.value
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: Colors.blue,
                  ),
                  onPressed: controller.toggleLoanAccountSettings,
                ),
              ],
            ),

            // Settings content (expandable)
            if (controller.showLoanAccountSettings.value) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),

              // Description
              Text(
                'Configure default accounts for loan journal entries. These accounts will be used when creating journal entries for loan approvals.',
                style: TextStyle(
                  color: notifier.text.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 24),

              // Loan Receivable Account
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Loan Receivable Account',
                          style: TextStyle(
                            color: notifier.text,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Asset account for tracking money lent out',
                          style: TextStyle(
                            color: notifier.text.withOpacity(0.7),
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Obx(() {
                          return LoanReceivableAccountDropdown(
                            selectedAccount:
                                controller.selectedLoanReceivableAccount.value,
                            onChanged: (account) {
                              if (account != null) {
                                controller
                                    .setSelectedLoanReceivableAccount(account);
                              }
                            },
                            validator: controller.validateLoanReceivableAccount,
                          );
                        }),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Loan Payable Account',
                          style: TextStyle(
                            color: notifier.text,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Liability account for tracking money borrowed',
                          style: TextStyle(
                            color: notifier.text.withOpacity(0.7),
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Obx(() {
                          return LoanPayableAccountDropdown(
                            selectedAccount:
                                controller.selectedLoanPayableAccount.value,
                            onChanged: (account) {
                              if (account != null) {
                                controller
                                    .setSelectedLoanPayableAccount(account);
                              }
                            },
                            validator: controller.validateLoanPayableAccount,
                          );
                        }),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),

              // Accounting explanation
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Journal Entry Creation Logic',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• When lending money: DEBIT Loan Receivable, CREDIT Lender Account\n'
                      '• When borrowing money: DEBIT Borrower Account, CREDIT Loan Payable\n'
                      '• Asset accounts increase with debits, decrease with credits\n'
                      '• Liability accounts increase with credits, decrease with debits',
                      style: TextStyle(
                        fontSize: 12,
                        color: notifier.text,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      );
    });
  }
}
