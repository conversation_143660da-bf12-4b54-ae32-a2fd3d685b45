import 'dart:developer' as dev;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../../models/finance/journal_entry_model.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../../../../features/accounting/journal_entries/repositories/journal_entry_repository.dart';
import '../../../../../core/services/account_type_helper_service.dart';

/// Model for account-specific transaction view
class AccountJournalTransaction {
  final String id;
  final String journalEntryId;
  final String entryNumber;
  final DateTime entryDate;
  final String description;
  final double debitAmount;
  final double creditAmount;
  final double runningBalance;
  final String referenceType;
  final String? referenceId;
  final JournalEntryStatus status;
  final DateTime createdAt; // Journal entry creation time for proper sorting

  AccountJournalTransaction({
    required this.id,
    required this.journalEntryId,
    required this.entryNumber,
    required this.entryDate,
    required this.description,
    required this.debitAmount,
    required this.creditAmount,
    required this.runningBalance,
    required this.referenceType,
    this.referenceId,
    required this.status,
    required this.createdAt,
  });

  /// Get the transaction amount (positive for debits, negative for credits)
  double get amount => debitAmount > 0 ? debitAmount : -creditAmount;

  /// Check if this is a debit transaction
  bool get isDebit => debitAmount > 0;

  /// Check if this is a credit transaction
  bool get isCredit => creditAmount > 0;
}

/// Result model for paginated account transactions
class PaginatedAccountTransactionResult {
  final List<AccountJournalTransaction> transactions;
  final bool hasNextPage;
  final QueryDocumentSnapshot? nextPageCursor;
  final int totalCount;

  PaginatedAccountTransactionResult({
    required this.transactions,
    required this.hasNextPage,
    this.nextPageCursor,
    required this.totalCount,
  });
}

/// Service for retrieving account-specific journal entry transactions
class AccountJournalTransactionService {
  final JournalEntryRepository _journalRepository;

  AccountJournalTransactionService(this._journalRepository);

  /// Get paginated transactions for a specific account
  Future<PaginatedAccountTransactionResult> getAccountTransactionsPaginated({
    required String accountId,
    required String uid,
    required AccountCategory accountCategory,
    int limit = 25,
    QueryDocumentSnapshot? lastDocument,
    DateTime? startDate,
    DateTime? endDate,
    List<JournalEntryStatus>? statusFilter,
  }) async {
    try {
      dev.log('🔍 Loading transactions for account: $accountId');
      dev.log(
          '🔍 Status filter: ${statusFilter?.map((s) => s.name).toList() ?? 'null (no filter)'}');
      dev.log(
          '🔍 Date range: ${startDate?.toString() ?? 'null'} to ${endDate?.toString() ?? 'null'}');
      dev.log('🔍 Limit: $limit');

      // Get journal entries that contain transactions for this account
      final journalEntriesResult =
          await _journalRepository.getJournalEntriesForAccount(
        accountId: accountId,
        uid: uid,
        limit: limit,
        lastDocument: lastDocument,
        startDate: startDate,
        endDate: endDate,
        statusFilter: statusFilter,
      );

      return await journalEntriesResult.fold(
        (failure) async {
          dev.log('Failed to load journal entries: ${failure.message}');
          return PaginatedAccountTransactionResult(
            transactions: [],
            hasNextPage: false,
            totalCount: 0,
          );
        },
        (journalEntries) async {
          dev.log(
              '📋 Retrieved ${journalEntries.length} journal entries for account');

          // Log details of retrieved journal entries to check for voucher entries
          for (int i = 0; i < journalEntries.length && i < 5; i++) {
            final entry = journalEntries[i];
            dev.log(
                '📋 Entry $i: ${entry.entryNumber} - ${entry.description} - Status: ${entry.status.name} - Type: ${entry.entryType.name} - Source: ${entry.sourceTransactionType ?? 'manual'}');
          }

          // Get opening balance for the account before the date range
          double openingBalance = 0.0;
          if (startDate != null) {
            // Get balance as of the day before start date
            final dayBeforeStart = startDate.subtract(const Duration(days: 1));
            openingBalance = await getAccountBalanceAsOfDate(
              accountId: accountId,
              uid: uid,
              asOfDate: dayBeforeStart,
              accountCategory: accountCategory,
            );
          } else {
            // If no start date, start from 0.0 and calculate running balance
            // from all transactions chronologically
            // Note: The stored account balance already includes all transactions,
            // so using it as opening balance would double-count transactions
            openingBalance = 0.0;
            dev.log(
                'No start date specified, starting from opening balance: $openingBalance');
          }

          // Extract account-specific transactions using stored ledger balances
          final transactions =
              await _extractAccountTransactionsWithStoredBalances(
            journalEntries,
            accountId,
            accountCategory,
            uid,
            openingBalance,
          );

          return PaginatedAccountTransactionResult(
            transactions: transactions,
            hasNextPage: journalEntries.length == limit,
            nextPageCursor: journalEntries.isNotEmpty
                ? null
                : null, // TODO: Implement cursor
            totalCount: transactions.length,
          );
        },
      );
    } catch (e) {
      dev.log('Error loading account transactions: $e');
      return PaginatedAccountTransactionResult(
        transactions: [],
        hasNextPage: false,
        totalCount: 0,
      );
    }
  }

  /// Extract transactions for a specific account from journal entries
  List<AccountJournalTransaction> _extractAccountTransactions(
    List<JournalEntryModel> journalEntries,
    String accountId,
    AccountCategory accountCategory,
    double openingBalance,
  ) {
    // Create a list to hold all transactions with their creation order
    final allTransactions = <_TransactionWithOrder>[];

    // Extract all transactions for this account with their creation order
    for (int entryIndex = 0; entryIndex < journalEntries.length; entryIndex++) {
      final entry = journalEntries[entryIndex];
      final accountLines =
          entry.lines.where((line) => line.accountId == accountId);

      for (final line in accountLines) {
        allTransactions.add(_TransactionWithOrder(
          entry: entry,
          line: line,
          entryCreationOrder: entryIndex, // Preserve original entry order
        ));
      }
    }

    // Sort by transaction date (oldest first) for proper running balance calculation
    allTransactions.sort((a, b) {
      final dateComparison = a.entry.entryDate.compareTo(b.entry.entryDate);
      if (dateComparison != 0) return dateComparison;
      // If same date, sort by creation time to maintain consistency
      return a.entry.createdAt.compareTo(b.entry.createdAt);
    });

    // Calculate running balances in chronological order
    double runningBalance = openingBalance;
    final transactions = <AccountJournalTransaction>[];

    for (final transactionWithOrder in allTransactions) {
      final entry = transactionWithOrder.entry;
      final line = transactionWithOrder.line;

      // Update running balance based on account type using proper accounting principles
      final balanceChange =
          AccountTypeHelperService.calculateBalanceChangeByCategory(
        accountCategory: accountCategory,
        debitAmount: line.debitAmount,
        creditAmount: line.creditAmount,
      );
      runningBalance += balanceChange;

      transactions.add(AccountJournalTransaction(
        id: line.id,
        journalEntryId: entry.id,
        entryNumber: entry.entryNumber,
        entryDate: entry.entryDate,
        description: line.description,
        debitAmount: line.debitAmount,
        creditAmount: line.creditAmount,
        runningBalance: runningBalance,
        referenceType: line.referenceType ?? 'journal_entry',
        referenceId: line.referenceId,
        status: entry.status,
        createdAt: entry.createdAt,
      ));
    }

    // Sort by journal entry creation time (newest entries first) for display
    // This ensures new entries always appear at the top regardless of transaction date
    transactions.sort((a, b) {
      // Sort by journal entry creation time (newest first)
      final createdAtComparison = b.createdAt.compareTo(a.createdAt);
      if (createdAtComparison != 0) return createdAtComparison;

      // If same creation time, sort by line order within entry
      return a.id.compareTo(b.id);
    });

    return transactions;
  }

  /// Extract transactions for a specific account with proper cumulative running balance calculation
  Future<List<AccountJournalTransaction>>
      _extractAccountTransactionsWithStoredBalances(
    List<JournalEntryModel> journalEntries,
    String accountId,
    AccountCategory accountCategory,
    String uid,
    double openingBalance,
  ) async {
    try {
      dev.log(
          '🔄 Calculating cumulative running balances for account: $accountId');
      dev.log('📊 Opening balance: $openingBalance');

      // Create a list to hold all transactions with their creation order
      final allTransactions = <_TransactionWithOrder>[];

      // Extract all transactions for this account with their creation order
      for (int entryIndex = 0;
          entryIndex < journalEntries.length;
          entryIndex++) {
        final entry = journalEntries[entryIndex];
        final accountLines =
            entry.lines.where((line) => line.accountId == accountId);

        for (final line in accountLines) {
          allTransactions.add(_TransactionWithOrder(
            entry: entry,
            line: line,
            entryCreationOrder: entryIndex, // Preserve original entry order
          ));
        }
      }

      // Sort by transaction date (chronological order) for proper running balance calculation
      allTransactions.sort((a, b) {
        final dateComparison = a.entry.entryDate.compareTo(b.entry.entryDate);
        if (dateComparison != 0) return dateComparison;
        // If same date, sort by creation time to maintain consistency
        return a.entry.createdAt.compareTo(b.entry.createdAt);
      });

      dev.log(
          '📅 Sorted ${allTransactions.length} transactions chronologically for balance calculation');

      // Calculate cumulative running balances in chronological order
      double runningBalance = openingBalance;
      final transactions = <AccountJournalTransaction>[];

      for (final transactionWithOrder in allTransactions) {
        final entry = transactionWithOrder.entry;
        final line = transactionWithOrder.line;

        // Calculate balance change for this transaction based on account type
        final balanceChange =
            AccountTypeHelperService.calculateBalanceChangeByCategory(
          accountCategory: accountCategory,
          debitAmount: line.debitAmount,
          creditAmount: line.creditAmount,
        );

        // Update running balance with this transaction's impact
        runningBalance += balanceChange;

        dev.log('💰 Transaction ${entry.entryNumber}: '
            'Debit: ${line.debitAmount}, Credit: ${line.creditAmount}, '
            'Change: $balanceChange, Running Balance: $runningBalance');

        transactions.add(AccountJournalTransaction(
          id: line.id,
          journalEntryId: entry.id,
          entryNumber: entry.entryNumber,
          entryDate: entry.entryDate,
          description: line.description,
          debitAmount: line.debitAmount,
          creditAmount: line.creditAmount,
          runningBalance: runningBalance, // Cumulative running balance
          referenceType: line.referenceType ?? 'journal_entry',
          referenceId: line.referenceId,
          status: entry.status,
          createdAt: entry.createdAt,
        ));
      }

      // Sort by journal entry creation time (newest entries first) for display
      transactions.sort((a, b) {
        // Sort by journal entry creation time (newest first)
        final createdAtComparison = b.createdAt.compareTo(a.createdAt);
        if (createdAtComparison != 0) return createdAtComparison;

        // If same creation time, sort by line order within entry
        return a.id.compareTo(b.id);
      });

      // Debug: Log the first few transactions to verify display sorting
      dev.log('🎯 Final display order (newest first):');
      for (int i = 0; i < transactions.length && i < 3; i++) {
        final tx = transactions[i];
        dev.log(
            '   ${i + 1}. ${tx.entryNumber} - ${tx.description} - Created: ${tx.createdAt} - Balance: ${tx.runningBalance}');
      }

      dev.log(
          '✅ Built ${transactions.length} transactions with cumulative running balances');
      dev.log(
          '📈 Final running balance: ${transactions.isNotEmpty ? transactions.first.runningBalance : openingBalance}');

      return transactions;
    } catch (e) {
      dev.log('❌ Error calculating cumulative running balances: $e');
      // Fall back to original method if there's an error
      return _extractAccountTransactions(
          journalEntries, accountId, accountCategory, openingBalance);
    }
  }

  /// Calculate running balance for account based on account type
  double _calculateRunningBalance(
    double currentBalance,
    double debitAmount,
    double creditAmount,
    AccountCategory accountCategory,
  ) {
    // Account types that increase with debits
    final debitAccounts = [
      AccountCategory.assets,
      AccountCategory.expenses,
    ];

    // Account types that increase with credits
    final creditAccounts = [
      AccountCategory.liabilities,
      AccountCategory.equity,
      AccountCategory.revenue,
    ];

    if (debitAccounts.contains(accountCategory)) {
      // For debit accounts: debits increase, credits decrease
      return currentBalance + debitAmount - creditAmount;
    } else if (creditAccounts.contains(accountCategory)) {
      // For credit accounts: credits increase, debits decrease
      return currentBalance + creditAmount - debitAmount;
    } else {
      // Default behavior
      return currentBalance + debitAmount - creditAmount;
    }
  }

  /// Get transaction count for an account
  Future<int> getAccountTransactionCount({
    required String accountId,
    required String uid,
    DateTime? startDate,
    DateTime? endDate,
    List<JournalEntryStatus>? statusFilter,
  }) async {
    try {
      final result = await _journalRepository.getJournalEntriesForAccount(
        accountId: accountId,
        uid: uid,
        limit: 1000, // Large limit to get count
        startDate: startDate,
        endDate: endDate,
        statusFilter: statusFilter,
      );

      return result.fold(
        (failure) => 0,
        (journalEntries) {
          int count = 0;
          for (final entry in journalEntries) {
            count +=
                entry.lines.where((line) => line.accountId == accountId).length;
          }
          return count;
        },
      );
    } catch (e) {
      dev.log('Error getting transaction count: $e');
      return 0;
    }
  }

  /// Get account balance as of a specific date
  Future<double> getAccountBalanceAsOfDate({
    required String accountId,
    required String uid,
    required DateTime asOfDate,
    required AccountCategory accountCategory,
  }) async {
    try {
      final result = await _journalRepository.getJournalEntriesForAccount(
        accountId: accountId,
        uid: uid,
        endDate: asOfDate,
        limit: 1000, // Large limit to get all transactions
        statusFilter: [
          JournalEntryStatus.posted
        ], // Only include posted entries
      );

      return result.fold(
        (failure) => 0.0,
        (journalEntries) {
          double balance = 0.0;

          for (final entry in journalEntries) {
            final accountLines =
                entry.lines.where((line) => line.accountId == accountId);

            for (final line in accountLines) {
              balance = _calculateRunningBalance(
                balance,
                line.debitAmount,
                line.creditAmount,
                accountCategory,
              );
            }
          }

          return balance;
        },
      );
    } catch (e) {
      dev.log('Error calculating account balance: $e');
      return 0.0;
    }
  }
}

/// Helper class to maintain transaction order during balance calculation
class _TransactionWithOrder {
  final JournalEntryModel entry;
  final JournalEntryLineModel line;
  final int entryCreationOrder;

  _TransactionWithOrder({
    required this.entry,
    required this.line,
    required this.entryCreationOrder,
  });
}
