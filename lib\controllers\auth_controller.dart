import 'dart:developer';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';

/// Simple AuthController for test compatibility
/// Provides access to current user information for testing purposes
class AuthController extends GetxController {
  final FirebaseAuthService _authService = Get.find<FirebaseAuthService>();

  /// Observable current user - create our own reactive wrapper
  final Rx<User?> currentUser = Rx<User?>(null);

  /// Get current user UID
  String get uid => currentUser.value?.uid ?? '';

  /// Get current user email
  String get email => currentUser.value?.email ?? '';

  /// Check if user is authenticated
  bool get isAuthenticated => currentUser.value != null;

  @override
  void onInit() {
    super.onInit();

    // Initialize with current auth state
    currentUser.value = _authService.currentUser;

    // Listen to Firebase auth state changes
    FirebaseAuth.instance.authStateChanges().listen((User? user) {
      currentUser.value = user;
      if (user != null) {
        log('✅ AuthController: User authenticated - ${user.email}');
      } else {
        log('❌ AuthController: User not authenticated');
      }
    });
  }

  /// Sign out user
  Future<void> signOut() async {
    await _authService.signOut();
  }

  /// Get company UID (same as user UID in this system)
  String get companyUid => uid;
}
