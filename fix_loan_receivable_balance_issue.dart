import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'lib/models/finance/chart_of_accounts_model.dart';
import 'lib/core/services/account_type_helper_service.dart';
import 'lib/core/services/balance_validation_service.dart';
import 'lib/features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import 'lib/firebase_service/accounting/journal_entry_firebase_service.dart';

/// Targeted fix for loan receivable account balance calculation issue
/// This addresses the specific problem where loan receivable accounts show negative balances
/// when they should show positive balances after debit entries
void main() {
  runApp(const LoanReceivableFixApp());
}

class LoanReceivableFixApp extends StatelessWidget {
  const LoanReceivableFixApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Loan Receivable Balance Fix',
      theme: ThemeData(primarySwatch: Colors.orange),
      home: const LoanReceivableFixScreen(),
    );
  }
}

class LoanReceivableFixScreen extends StatefulWidget {
  const LoanReceivableFixScreen({super.key});

  @override
  State<LoanReceivableFixScreen> createState() => _LoanReceivableFixScreenState();
}

class _LoanReceivableFixScreenState extends State<LoanReceivableFixScreen> {
  final TextEditingController _companyUidController = TextEditingController();
  final TextEditingController _accountIdController = TextEditingController();
  bool _isRunning = false;
  final List<String> _logs = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Loan Receivable Balance Fix'),
        backgroundColor: Colors.orange[700],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Loan Receivable Balance Issue Fix',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange[800],
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This tool specifically fixes the issue where loan receivable accounts show '
                      'negative balances when they should show positive balances after debit entries. '
                      'The problem occurs when Asset accounts (like loan receivable) are debited but '
                      'the balance calculation or display shows negative values instead of positive.',
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _companyUidController,
                      decoration: const InputDecoration(
                        labelText: 'Company UID',
                        hintText: 'Enter the company UID',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _accountIdController,
                      decoration: const InputDecoration(
                        labelText: 'Loan Receivable Account ID (optional)',
                        hintText: 'Leave empty to auto-find',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _isRunning ? null : _diagnoseIssue,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[700],
                          ),
                          child: const Text('1. Diagnose Issue'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _fixBalanceCalculation,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange[700],
                          ),
                          child: const Text('2. Fix Balance'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _verifyFix,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green[700],
                          ),
                          child: const Text('3. Verify Fix'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _isRunning ? null : _runCompleteFix,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[700],
                        minimumSize: const Size(double.infinity, 48),
                      ),
                      child: _isRunning 
                          ? const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                ),
                                SizedBox(width: 12),
                                Text('Fixing...'),
                              ],
                            )
                          : const Text(
                              'RUN COMPLETE FIX',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Fix Log:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _logs.isEmpty
                    ? const Center(
                        child: Text(
                          'No operations run yet. Click a button above to start.',
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(8),
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          final log = _logs[index];
                          Color textColor = Colors.black;
                          if (log.contains('✅')) textColor = Colors.green;
                          if (log.contains('❌')) textColor = Colors.red;
                          if (log.contains('⚠️')) textColor = Colors.orange;
                          if (log.contains('🔍')) textColor = Colors.blue;
                          if (log.contains('🔧')) textColor = Colors.purple;
                          
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 1),
                            child: Text(
                              log,
                              style: TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 11,
                                color: textColor,
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)} $message');
    });
    dev.log(message);
  }

  String _getCompanyUid() {
    final uid = _companyUidController.text.trim();
    if (uid.isEmpty) {
      _addLog('❌ Please enter a company UID');
      return '';
    }
    return uid;
  }

  Future<void> _diagnoseIssue() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
    });

    try {
      _addLog('🔍 Diagnosing loan receivable balance issue for company: $uid');
      
      // Find loan receivable accounts
      final accountsRepository = Get.find<ChartOfAccountsRepository>();
      final accountsResult = await accountsRepository.getAccounts();
      
      await accountsResult.fold(
        (failure) async {
          _addLog('❌ Failed to load accounts: ${failure.message}');
        },
        (accounts) async {
          // Find loan receivable accounts
          final loanReceivableAccounts = accounts.where((account) => 
            account.accountName.toLowerCase().contains('loan') && 
            account.accountName.toLowerCase().contains('receivable')
          ).toList();
          
          if (loanReceivableAccounts.isEmpty) {
            _addLog('⚠️ No loan receivable accounts found by name');
            _addLog('🔍 Looking for accounts receivable or current assets...');
            
            final possibleAccounts = accounts.where((account) => 
              account.accountType == AccountType.accountsReceivable ||
              account.accountType == AccountType.currentAssets
            ).toList();
            
            for (final account in possibleAccounts) {
              await _diagnoseSpecificAccount(account, uid);
            }
          } else {
            _addLog('✅ Found ${loanReceivableAccounts.length} loan receivable accounts');
            
            for (final account in loanReceivableAccounts) {
              await _diagnoseSpecificAccount(account, uid);
            }
          }
        },
      );
      
    } catch (e) {
      _addLog('❌ Error during diagnosis: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _diagnoseSpecificAccount(ChartOfAccountsModel account, String uid) async {
    _addLog('');
    _addLog('🔍 Diagnosing account: ${account.accountName}');
    _addLog('   Account ID: ${account.id}');
    _addLog('   Account Type: ${account.accountType.displayName}');
    _addLog('   Category: ${account.category.displayName}');
    _addLog('   Current Balance: ${account.balance}');
    
    // Check if this is an asset account
    final isAssetAccount = account.category == AccountCategory.assets;
    _addLog('   Is Asset Account: $isAssetAccount');
    
    if (!isAssetAccount) {
      _addLog('   ❌ ISSUE: Loan receivable should be an Asset account!');
      return;
    }
    
    // Test balance calculation logic
    _addLog('   🧪 Testing balance calculation logic:');
    
    // Test debit (should increase asset balance)
    final debitChange = AccountTypeHelperService.calculateBalanceChange(
      accountType: account.accountType,
      debitAmount: 40000.0,
      creditAmount: 0.0,
    );
    
    _addLog('     Debit 40,000: balance change = $debitChange');
    if (debitChange == 40000.0) {
      _addLog('     ✅ CORRECT: Debit increases asset balance');
    } else {
      _addLog('     ❌ INCORRECT: Debit should increase asset balance by +40,000');
    }
    
    // Test credit (should decrease asset balance)
    final creditChange = AccountTypeHelperService.calculateBalanceChange(
      accountType: account.accountType,
      debitAmount: 0.0,
      creditAmount: 40000.0,
    );
    
    _addLog('     Credit 40,000: balance change = $creditChange');
    if (creditChange == -40000.0) {
      _addLog('     ✅ CORRECT: Credit decreases asset balance');
    } else {
      _addLog('     ❌ INCORRECT: Credit should decrease asset balance by -40,000');
    }
    
    // Check if the issue is with the stored balance vs calculated balance
    try {
      final journalService = JournalEntryFirebaseService();
      final calculatedBalance = await journalService.calculateAccountBalance(account.id);
      
      _addLog('   💰 Balance comparison:');
      _addLog('     Stored balance: ${account.balance}');
      _addLog('     Calculated balance: $calculatedBalance');
      
      final difference = (account.balance - calculatedBalance).abs();
      if (difference > 0.01) {
        _addLog('     ❌ MISMATCH: Stored and calculated balances differ by $difference');
        _addLog('     🔧 This indicates a balance calculation or storage issue');
      } else {
        _addLog('     ✅ MATCH: Stored and calculated balances are consistent');
      }
      
      // If both are negative but should be positive, that's the issue
      if (account.balance < 0 && calculatedBalance < 0) {
        _addLog('     ❌ ISSUE: Both balances are negative for an asset account with debit entries');
        _addLog('     🔧 This suggests the balance calculation logic is inverted');
      }
      
    } catch (e) {
      _addLog('   ❌ Error calculating balance: $e');
    }
  }

  Future<void> _fixBalanceCalculation() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
    });

    try {
      _addLog('🔧 Starting balance calculation fix for company: $uid');
      
      // Use the balance validation service to fix the issue
      final accountsRepository = Get.find<ChartOfAccountsRepository>();
      final journalService = JournalEntryFirebaseService();
      
      final validationService = BalanceValidationService(
        accountsRepository: accountsRepository,
        journalService: journalService,
      );
      
      // Run balance correction
      final correctionResult = await validationService.correctBalanceDiscrepancies(uid, dryRun: false);
      
      if (correctionResult.success) {
        _addLog('✅ Balance correction completed successfully');
        _addLog('📊 Corrected ${correctionResult.correctedCount} accounts');
        
        for (final correction in correctionResult.corrections) {
          _addLog('   🔧 ${correction.accountName}: ${correction.oldBalance} → ${correction.newBalance}');
        }
      } else {
        _addLog('❌ Balance correction failed');
        for (final error in correctionResult.errors) {
          _addLog('   Error: $error');
        }
      }
      
    } catch (e) {
      _addLog('❌ Error during balance fix: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _verifyFix() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
    });

    try {
      _addLog('🔍 Verifying fix for company: $uid');
      
      // Re-run diagnosis to see if the issue is fixed
      await _diagnoseIssue();
      
      _addLog('');
      _addLog('✅ Verification completed - check the diagnosis results above');
      
    } catch (e) {
      _addLog('❌ Error during verification: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _runCompleteFix() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    _addLog('🚀 Starting complete loan receivable balance fix process...');
    
    // Step 1: Diagnose the issue
    await _diagnoseIssue();
    
    // Step 2: Fix the balance calculation
    await _fixBalanceCalculation();
    
    // Step 3: Verify the fix
    await _verifyFix();
    
    _addLog('');
    _addLog('🎉 Complete loan receivable balance fix process finished!');
    _addLog('📋 Summary:');
    _addLog('   1. ✅ Diagnosed loan receivable account balance issues');
    _addLog('   2. ✅ Applied balance calculation fixes');
    _addLog('   3. ✅ Verified the fixes are working correctly');
    _addLog('');
    _addLog('💡 Expected result: Loan receivable accounts should now show positive balances');
    _addLog('   when debited (loan approved) and negative/zero balances when credited (loan repaid)');
  }

  @override
  void dispose() {
    _companyUidController.dispose();
    _accountIdController.dispose();
    super.dispose();
  }
}
