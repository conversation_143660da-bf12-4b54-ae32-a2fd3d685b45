import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/features/billing/presentation/controllers/billing_excel_export_controller.dart';
import 'package:logestics/services/pdf_generation_service.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/finance/bill_model.dart';
import 'package:logestics/models/user_model.dart';
import 'package:logestics/core/utils/constants/constants.dart';

void main() {
  group('Bill Template Integration Tests', () {
    late BillingExcelExportController excelController;
    late PDFGenerationService pdfService;
    late List<InvoiceModel> testInvoices;
    late BillModel testBill;
    late UserModel testUser;

    setUp(() {
      excelController = BillingExcelExportController();
      pdfService = PDFGenerationService();
      
      // Create test data
      testInvoices = _createTestInvoices();
      testBill = _createTestBill();
      testUser = _createTestUser();
    });

    group('Excel Template Integration', () {
      test('should apply correct top margin (25 rows)', () {
        // Test that the Excel export uses the correct top margin
        const expectedTopMarginRows = 25;
        const expectedHeaderRow = expectedTopMarginRows;
        const expectedDataStartRow = expectedTopMarginRows + 1;

        // These constants should be used in the Excel generation
        expect(expectedTopMarginRows, equals(25));
        expect(expectedHeaderRow, equals(25));
        expect(expectedDataStartRow, equals(26));
      });

      test('should include bill number and invoice count in header', () {
        // Test that the header includes the required information
        final invoiceCount = testInvoices.length;
        expect(invoiceCount, greaterThan(0));
        
        // Verify that the bill number format is correct
        final billNumber = testBill.billNumber;
        expect(billNumber, isNotEmpty);
        expect(billNumber, contains('-'));
      });

      test('should use optimized column widths for print layout', () {
        // Test that column widths are optimized for portrait printing
        final expectedColumnWidths = [
          6.0,  // SN. - narrow
          10.0, // Lifting Date
          9.0,  // Truck No
          9.0,  // Bilty No
          12.0, // Convey Note Number
          10.0, // Product Name
          10.0, // Product TAS NO
          11.0, // Destination
          8.0,  // No of Bags
          8.0,  // Weight
          6.0,  // KM
          10.0, // District
          12.0, // HMT Rates
          10.0, // 100% Amount
          10.0, // 80% Amount
          10.0, // Net Amount
        ];

        // Verify we have the correct number of columns
        expect(expectedColumnWidths.length, equals(16));
        
        // Verify total width is reasonable for portrait mode (should be < 160)
        final totalWidth = expectedColumnWidths.reduce((a, b) => a + b);
        expect(totalWidth, lessThan(160.0));
      });

      test('should format monetary amounts correctly', () {
        // Test monetary formatting matches template requirements
        expect(excelController.formatMonetaryAmount(123.0), equals('123'));
        expect(excelController.formatMonetaryAmount(123.50), equals('123.5'));
        expect(excelController.formatMonetaryAmount(123.75), equals('123.75'));
        expect(excelController.formatMonetaryAmount(0.0), equals('0'));
      });
    });

    group('PDF Template Integration', () {
      test('should generate PDF with correct template structure', () async {
        // Test that PDF generation service has the bill generation method
        expect(pdfService.generateBillPDF, isA<Function>());
      });

      test('should apply 3.5 inch top margin in PDF', () {
        // 3.5 inches = 252 points (72 points per inch)
        const expectedTopMarginPoints = 252.0;
        expect(expectedTopMarginPoints, equals(3.5 * 72));
      });

      test('should include invoice count in PDF header', () {
        final invoiceCount = testInvoices.length;
        expect(invoiceCount, greaterThan(0));
        
        // The PDF should display this count in the header
        final expectedHeaderText = 'Total Invoices: $invoiceCount';
        expect(expectedHeaderText, contains('Total Invoices:'));
        expect(expectedHeaderText, contains(invoiceCount.toString()));
      });
    });

    group('Template Consistency', () {
      test('should use same column structure in both Excel and PDF', () {
        // Both Excel and PDF should have the same 16 columns
        final expectedHeaders = [
          'SN.',
          'Lifting Date',
          'Truck No',
          'Bilty No',
          'Convey Note Number',
          'Product Name',
          'Product TAS NO',
          'Destination',
          'No of Bags',
          'Weight',
          'KM',
          'District',
          'HMT Rates (Non Fuel Inc WHT)',
          '100% Amount',
          '80% Amount',
          'Net Amount',
        ];

        expect(expectedHeaders.length, equals(16));
        expect(expectedHeaders.first, equals('SN.'));
        expect(expectedHeaders.last, equals('Net Amount'));
      });

      test('should apply consistent rounding in both formats', () {
        // Test that both Excel and PDF use the same rounding rules
        final testAmount = 123.75;
        final rounded = MonetaryRounding.roundHalfUp(testAmount);
        expect(rounded, equals(124.0));
        
        // Both formats should format this consistently
        final formatted = excelController.formatMonetaryAmount(rounded);
        expect(formatted, equals('124'));
      });

      test('should handle tax calculations consistently', () {
        excelController.selectedTaxAuthorities.add('SRB');
        
        final baseAmount = 1000.0;
        final taxAmount = excelController.calculateTaxAmount(baseAmount);
        expect(taxAmount, equals(150.0)); // 15% of 1000
        
        final totalWithTax = MonetaryRounding.roundHalfUp(baseAmount + taxAmount);
        expect(totalWithTax, equals(1150.0));
      });
    });

    tearDown(() {
      excelController.selectedTaxAuthorities.clear();
    });
  });
}

// Helper methods to create test data
List<InvoiceModel> _createTestInvoices() {
  return [
    InvoiceModel(
      invoiceNumber: 1,
      invoiceStatus: 'Pending Billing',
      tasNumber: 'TAS001',
      productName: 'Cement',
      numberOfBags: 100,
      weightPerBag: 50.0,
      customerName: 'Test Customer',
      truckNumber: 'ABC-123',
      conveyNoteNumber: 'CN001',
      biltyNumber: 'BLT001',
      orderDate: DateTime.now(),
      consignorName: 'Test Consignor',
      deliveryMode: 'Road',
      districtId: 'dist1',
      districtName: 'Test District',
      stationId: 'stat1',
      stationName: 'Test Station',
      fromPlaceId: 'place1',
      fromPlaceName: 'Test Place',
      distanceInKilometers: 150.0,
      consignorPickUpAddress: 'Test Address',
    ),
    InvoiceModel(
      invoiceNumber: 2,
      invoiceStatus: 'Pending Billing',
      tasNumber: 'TAS002',
      productName: 'Steel',
      numberOfBags: 75,
      weightPerBag: 60.0,
      customerName: 'Test Customer 2',
      truckNumber: 'XYZ-456',
      conveyNoteNumber: 'CN002',
      biltyNumber: 'BLT002',
      orderDate: DateTime.now(),
      consignorName: 'Test Consignor 2',
      deliveryMode: 'Road',
      districtId: 'dist2',
      districtName: 'Test District 2',
      stationId: 'stat2',
      stationName: 'Test Station 2',
      fromPlaceId: 'place2',
      fromPlaceName: 'Test Place 2',
      distanceInKilometers: 200.0,
      consignorPickUpAddress: 'Test Address 2',
    ),
  ];
}

BillModel _createTestBill() {
  return BillModel(
    billId: 'test-bill-id',
    billNumber: '001-B',
    billDate: DateTime.now(),
    totalAmount: 5000.0,
    numberOfLinkedInvoices: 2,
    billStatus: 'Pending',
    linkedInvoiceIds: ['TAS001', 'TAS002'],
    companyUid: 'test-company-uid',
  );
}

UserModel _createTestUser() {
  return UserModel(
    uid: 'test-user-uid',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'admin',
    isActive: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}
