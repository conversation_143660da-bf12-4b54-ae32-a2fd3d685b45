import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'offline_transaction_state.dart';

/// Model for sync queue entries that manage offline operations
class SyncQueueEntry {
  final String id;
  final SyncOperationType operationType;
  final Map<String, dynamic> data;
  final OfflineTransactionState state;
  final DateTime createdAt;
  final DateTime? lastSyncAttempt;
  final int retryCount;
  final String? errorMessage;
  final List<String> dependencies;
  final SyncPriority priority;
  final ConflictResolutionStrategy conflictStrategy;
  final Map<String, dynamic> metadata;
  final String? userId;
  final String? companyId;
  
  SyncQueueEntry({
    String? id,
    required this.operationType,
    required this.data,
    this.state = OfflineTransactionState.pending,
    DateTime? createdAt,
    this.lastSyncAttempt,
    this.retryCount = 0,
    this.errorMessage,
    this.dependencies = const [],
    this.priority = SyncPriority.normal,
    this.conflictStrategy = ConflictResolutionStrategy.userMediated,
    this.metadata = const {},
    this.userId,
    this.companyId,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();
  
  /// Create a new sync queue entry for voucher creation
  factory SyncQueueEntry.voucherCreate({
    required Map<String, dynamic> voucherData,
    required String userId,
    required String companyId,
    List<String> dependencies = const [],
  }) {
    return SyncQueueEntry(
      operationType: SyncOperationType.voucherCreate,
      data: voucherData,
      priority: SyncPriority.high,
      conflictStrategy: ConflictResolutionStrategy.userMediated,
      dependencies: dependencies,
      userId: userId,
      companyId: companyId,
      metadata: {
        'voucher_number': voucherData['voucherNumber'],
        'total_freight': voucherData['totalFreight'],
        'departure_date': voucherData['departureDate'],
      },
    );
  }
  
  /// Create a new sync queue entry for journal entry creation
  factory SyncQueueEntry.journalEntryCreate({
    required Map<String, dynamic> journalData,
    required String userId,
    required String companyId,
    List<String> dependencies = const [],
  }) {
    return SyncQueueEntry(
      operationType: SyncOperationType.journalEntryCreate,
      data: journalData,
      priority: SyncPriority.high,
      conflictStrategy: ConflictResolutionStrategy.serverAuthoritative,
      dependencies: dependencies,
      userId: userId,
      companyId: companyId,
      metadata: {
        'entry_number': journalData['entryNumber'],
        'total_debits': journalData['totalDebits'],
        'total_credits': journalData['totalCredits'],
      },
    );
  }
  
  /// Create a new sync queue entry for balance update
  factory SyncQueueEntry.balanceUpdate({
    required Map<String, dynamic> balanceData,
    required String userId,
    required String companyId,
    List<String> dependencies = const [],
  }) {
    return SyncQueueEntry(
      operationType: SyncOperationType.balanceUpdate,
      data: balanceData,
      priority: SyncPriority.critical,
      conflictStrategy: ConflictResolutionStrategy.serverAuthoritative,
      dependencies: dependencies,
      userId: userId,
      companyId: companyId,
      metadata: {
        'account_id': balanceData['accountId'],
        'balance_change': balanceData['balanceChange'],
      },
    );
  }
  
  /// Create from JSON
  factory SyncQueueEntry.fromJson(Map<String, dynamic> json) {
    return SyncQueueEntry(
      id: json['id'],
      operationType: SyncOperationType.fromString(json['operationType']),
      data: Map<String, dynamic>.from(json['data']),
      state: OfflineTransactionState.fromString(json['state']),
      createdAt: DateTime.parse(json['createdAt']),
      lastSyncAttempt: json['lastSyncAttempt'] != null 
          ? DateTime.parse(json['lastSyncAttempt']) 
          : null,
      retryCount: json['retryCount'] ?? 0,
      errorMessage: json['errorMessage'],
      dependencies: List<String>.from(json['dependencies'] ?? []),
      priority: SyncPriority.fromInt(json['priority'] ?? 2),
      conflictStrategy: ConflictResolutionStrategy.fromString(
          json['conflictStrategy'] ?? 'user_mediated'),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      userId: json['userId'],
      companyId: json['companyId'],
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'operationType': operationType.value,
      'data': data,
      'state': state.value,
      'createdAt': createdAt.toIso8601String(),
      'lastSyncAttempt': lastSyncAttempt?.toIso8601String(),
      'retryCount': retryCount,
      'errorMessage': errorMessage,
      'dependencies': dependencies,
      'priority': priority.value,
      'conflictStrategy': conflictStrategy.value,
      'metadata': metadata,
      'userId': userId,
      'companyId': companyId,
    };
  }
  
  /// Create a copy with updated fields
  SyncQueueEntry copyWith({
    OfflineTransactionState? state,
    DateTime? lastSyncAttempt,
    int? retryCount,
    String? errorMessage,
    List<String>? dependencies,
    SyncPriority? priority,
    ConflictResolutionStrategy? conflictStrategy,
    Map<String, dynamic>? metadata,
  }) {
    return SyncQueueEntry(
      id: id,
      operationType: operationType,
      data: data,
      state: state ?? this.state,
      createdAt: createdAt,
      lastSyncAttempt: lastSyncAttempt ?? this.lastSyncAttempt,
      retryCount: retryCount ?? this.retryCount,
      errorMessage: errorMessage ?? this.errorMessage,
      dependencies: dependencies ?? this.dependencies,
      priority: priority ?? this.priority,
      conflictStrategy: conflictStrategy ?? this.conflictStrategy,
      metadata: metadata ?? this.metadata,
      userId: userId,
      companyId: companyId,
    );
  }
  
  /// Mark as syncing
  SyncQueueEntry markAsSyncing() {
    return copyWith(
      state: OfflineTransactionState.syncing,
      lastSyncAttempt: DateTime.now(),
    );
  }
  
  /// Mark as synced
  SyncQueueEntry markAsSynced() {
    return copyWith(
      state: OfflineTransactionState.synced,
      errorMessage: null,
    );
  }
  
  /// Mark as failed
  SyncQueueEntry markAsFailed(String error) {
    return copyWith(
      state: OfflineTransactionState.failed,
      errorMessage: error,
      retryCount: retryCount + 1,
    );
  }
  
  /// Mark as conflict
  SyncQueueEntry markAsConflict(String conflictDetails) {
    return copyWith(
      state: OfflineTransactionState.conflict,
      errorMessage: conflictDetails,
    );
  }
  
  /// Check if entry can be retried
  bool get canRetry => state.canRetry && retryCount < 5;
  
  /// Check if entry is ready for sync (no pending dependencies)
  bool isReadyForSync(List<String> completedOperations) {
    return dependencies.every((dep) => completedOperations.contains(dep));
  }
  
  /// Get retry delay based on retry count (exponential backoff)
  Duration get retryDelay {
    final baseDelay = const Duration(seconds: 30);
    final multiplier = (1 << retryCount).clamp(1, 32); // 2^retryCount, max 32
    return baseDelay * multiplier;
  }
  
  /// Check if entry should be retried now
  bool shouldRetryNow() {
    if (!canRetry || lastSyncAttempt == null) return false;
    return DateTime.now().difference(lastSyncAttempt!) >= retryDelay;
  }
  
  /// Get human-readable description
  String get description {
    switch (operationType) {
      case SyncOperationType.voucherCreate:
        return 'Create voucher ${metadata['voucher_number'] ?? 'Unknown'}';
      case SyncOperationType.voucherUpdate:
        return 'Update voucher ${metadata['voucher_number'] ?? 'Unknown'}';
      case SyncOperationType.journalEntryCreate:
        return 'Create journal entry ${metadata['entry_number'] ?? 'Unknown'}';
      case SyncOperationType.balanceUpdate:
        return 'Update account balance for ${metadata['account_id'] ?? 'Unknown'}';
      default:
        return 'Sync ${operationType.value}';
    }
  }
  
  @override
  String toString() {
    return 'SyncQueueEntry(id: $id, type: ${operationType.value}, state: ${state.value}, retries: $retryCount)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SyncQueueEntry && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
