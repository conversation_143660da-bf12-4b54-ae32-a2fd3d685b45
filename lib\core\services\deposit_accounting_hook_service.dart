import 'dart:developer';
import '../../models/finance/deposit_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import 'automatic_journal_entry_service.dart';
import 'deposit_journal_integration_service.dart';

/// Service that hooks into deposit creation to automatically generate journal entries
class DepositAccountingHookService {
  static DepositAccountingHookService? _instance;
  late final DepositJournalIntegrationService _integrationService;
  late final AutomaticJournalEntryService _automaticJournalService;
  late final GeneralLedgerFirebaseService _generalLedgerService;
  late final JournalEntryFirebaseService _journalEntryService;
  late final ChartOfAccountsFirebaseService _chartOfAccountsService;

  DepositAccountingHookService._internal() {
    _initializeServices();
  }

  factory DepositAccountingHookService() {
    _instance ??= DepositAccountingHookService._internal();
    return _instance!;
  }

  void _initializeServices() {
    _chartOfAccountsService = ChartOfAccountsFirebaseService();
    _generalLedgerService = GeneralLedgerFirebaseService();
    _journalEntryService = JournalEntryFirebaseService();
    _automaticJournalService = AutomaticJournalEntryService(
      _chartOfAccountsService,
    );
    _integrationService = DepositJournalIntegrationService(
      _automaticJournalService,
      _generalLedgerService,
      _journalEntryService,
      _chartOfAccountsService,
    );
  }

  /// Hook method to be called after deposit creation
  Future<void> onDepositCreated(DepositModel deposit) async {
    try {
      log('Deposit accounting hook triggered for: ${deposit.amount}');

      // Only process deposits that use Chart of Accounts
      if (!deposit.usesChartOfAccounts) {
        log('Deposit does not use Chart of Accounts, skipping journal entry generation');
        return;
      }

      // Validate deposit for journal entry generation
      final validation =
          await _integrationService.validateDepositForJournalEntry(deposit);
      if (!validation.isValid) {
        log('Deposit validation failed: ${validation.issuesText}');
        return;
      }

      // Check if journal entries already exist
      final hasExisting = await _integrationService.hasExistingJournalEntries(
        deposit.id,
        deposit.uid,
      );

      if (hasExisting) {
        log('✅ Journal entries already exist for deposit: ${deposit.id}, skipping creation');
        return;
      }

      log('✅ No existing journal entries found, proceeding with creation');

      // Process the deposit transaction
      final success = await _integrationService.processDepositTransaction(
          deposit, deposit.uid);
      if (success) {
        log('✅ Successfully created journal entries for deposit: ${deposit.id}');
      } else {
        log('❌ Failed to create journal entries for deposit: ${deposit.id}');
        throw Exception(
            'Journal entry creation failed for deposit: ${deposit.id}');
      }
    } catch (e) {
      log('❌ Error in deposit accounting hook: $e');
      // Re-throw the exception to ensure proper error handling upstream
      rethrow;
    }
  }

  /// Hook method to be called before deposit deletion
  Future<void> onDepositDeleted(DepositModel deposit) async {
    try {
      log('Deposit deletion hook triggered for: ${deposit.id}');

      // Only process deposits that use Chart of Accounts
      if (!deposit.usesChartOfAccounts) {
        log('Deposit does not use Chart of Accounts, skipping journal entry reversal');
        return;
      }

      // Reverse journal entries for the deposit
      final success = await _integrationService.reverseDepositJournalEntries(
        deposit.id,
        deposit.uid,
      );

      if (success) {
        log('Successfully reversed journal entries for deposit: ${deposit.id}');
      } else {
        log('Failed to reverse journal entries for deposit: ${deposit.id}');
      }
    } catch (e) {
      log('Error in deposit deletion hook: $e');
    }
  }

  /// Hook method to be called after deposit update
  Future<void> onDepositUpdated(
      DepositModel oldDeposit, DepositModel newDeposit) async {
    try {
      log('Deposit update hook triggered for: ${newDeposit.id}');

      // Only process deposits that use Chart of Accounts
      if (!newDeposit.usesChartOfAccounts) {
        log('Deposit does not use Chart of Accounts, skipping journal entry update');
        return;
      }

      // If the amount or account changed, we need to reverse old entries and create new ones
      if (oldDeposit.amount != newDeposit.amount ||
          oldDeposit.sourceAccountId != newDeposit.sourceAccountId ||
          oldDeposit.destinationAccountId != newDeposit.destinationAccountId) {
        // Reverse old journal entries
        await _integrationService.reverseDepositJournalEntries(
          oldDeposit.id,
          oldDeposit.uid,
        );

        // Create new journal entries
        await onDepositCreated(newDeposit);
      }
    } catch (e) {
      log('Error in deposit update hook: $e');
    }
  }

  /// Batch process existing deposits to create journal entries
  Future<BatchProcessResult> processExistingDeposits(
      List<DepositModel> deposits, String uid) async {
    try {
      log('Processing ${deposits.length} existing deposits for journal entries');
      return await _integrationService.batchProcessDepositTransactions(
          deposits, uid);
    } catch (e) {
      log('Error processing existing deposits: $e');
      return BatchProcessResult(
        totalProcessed: deposits.length,
        successCount: 0,
        failureCount: deposits.length,
        failedTransactionIds: deposits.map((e) => e.id).toList(),
      );
    }
  }

  /// Get journal entries for a specific deposit
  Future<List<dynamic>> getDepositJournalEntries(
      String depositId, String uid) async {
    return await _integrationService.getJournalEntriesForDeposit(
        depositId, uid);
  }

  /// Check if a deposit has existing journal entries
  Future<bool> hasJournalEntries(String depositId, String uid) async {
    return await _integrationService.hasExistingJournalEntries(depositId, uid);
  }

  /// Validate a deposit for journal entry generation
  Future<DepositValidationResult> validateDeposit(DepositModel deposit) async {
    return await _integrationService.validateDepositForJournalEntry(deposit);
  }

  /// Get integration service for advanced operations
  DepositJournalIntegrationService get integrationService =>
      _integrationService;

  /// Reset the singleton instance (useful for testing)
  static void resetInstance() {
    _instance = null;
  }
}
