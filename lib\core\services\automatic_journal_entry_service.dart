import 'dart:developer' as dev;
import 'dart:math';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../../core/utils/constants/constants.dart';

import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/expense_model.dart';
import '../../models/finance/deposit_model.dart';
import '../../models/finance/bill_model.dart';
import '../../models/voucher_model.dart';
import '../../models/finance/loan_model.dart';
import '../../models/finance/account_transaction_model.dart';
import '../../models/payment_transaction_model.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../firebase_service/voucher/voucher_crud_firebase_service.dart';
import 'transaction_account_mapping_service.dart';
import 'account_type_helper_service.dart';
import 'voucher_payment_settings_service.dart';

/// Service for automatically generating journal entries from existing transactions
class AutomaticJournalEntryService {
  final ChartOfAccountsFirebaseService _chartOfAccountsService;

  AutomaticJournalEntryService(this._chartOfAccountsService);

  /// Generate journal entry from expense transaction using Chart of Accounts
  Future<JournalEntryModel?> generateExpenseJournalEntry({
    required ExpenseModel expense,
    required String uid,
    required String createdBy,
  }) async {
    try {
      dev.log('Generating expense journal entry for: ${expense.title}');

      // Check if expense uses Chart of Accounts
      if (!expense.usesChartOfAccounts) {
        dev.log(
            'Expense does not use Chart of Accounts, cannot generate journal entry');
        return null;
      }

      // Get source account (where money decreases from)
      final sourceAccount = await _chartOfAccountsService
          .getAccountById(expense.sourceAccountId!);
      if (sourceAccount == null) {
        dev.log('Source account not found: ${expense.sourceAccountId}');
        return null;
      }

      // Get destination account (where money increases to)
      final destinationAccount = await _chartOfAccountsService
          .getAccountById(expense.destinationAccountId!);
      if (destinationAccount == null) {
        dev.log(
            'Destination account not found: ${expense.destinationAccountId}');
        return null;
      }

      // Generate unique journal entry ID
      final journalEntryId = _generateJournalEntryId();

      // Create journal entry lines with proper debit/credit logic
      final lines = <JournalEntryLineModel>[];

      // Source account line (money decreases)
      final sourceAmounts = _getDebitCreditAmounts(
        account: sourceAccount,
        amount: expense.amount,
        isIncrease: false, // Money decreases from source account
      );

      lines.add(JournalEntryLineModel(
        id: _generateLineId(),
        journalEntryId: journalEntryId,
        accountId: sourceAccount.id,
        accountName: sourceAccount.accountName,
        accountNumber: sourceAccount.accountNumber,
        description: 'Expense payment: ${expense.title}',
        debitAmount: sourceAmounts['debit']!,
        creditAmount: sourceAmounts['credit']!,
        createdAt: DateTime.now(),
      ));

      // Destination account line (money increases)
      final destinationAmounts = _getDebitCreditAmounts(
        account: destinationAccount,
        amount: expense.amount,
        isIncrease: true, // Money increases to destination account
      );

      lines.add(JournalEntryLineModel(
        id: _generateLineId(),
        journalEntryId: journalEntryId,
        accountId: destinationAccount.id,
        accountName: destinationAccount.accountName,
        accountNumber: destinationAccount.accountNumber,
        description: 'Expense: ${expense.title} - ${expense.payeeName}',
        debitAmount: destinationAmounts['debit']!,
        creditAmount: destinationAmounts['credit']!,
        createdAt: DateTime.now(),
      ));

      // Calculate totals
      final totalDebits =
          lines.fold<double>(0, (sum, line) => sum + line.debitAmount);
      final totalCredits =
          lines.fold<double>(0, (sum, line) => sum + line.creditAmount);

      dev.log(
          'Expense journal entry: Debits=$totalDebits, Credits=$totalCredits');

      return JournalEntryModel(
        id: journalEntryId,
        entryNumber: '', // Will be auto-generated by Firebase service
        entryDate: expense.createdAt,
        description: 'Expense: ${expense.title} - ${expense.payeeName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: totalDebits,
        totalCredits: totalCredits,
        referenceNumber: expense.referenceNumber,
        sourceTransactionId: expense.id,
        sourceTransactionType: 'expense',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      dev.log('Error generating expense journal entry: $e');
      return null;
    }
  }

  /// Generate journal entry from deposit transaction using Chart of Accounts
  Future<JournalEntryModel?> generateDepositJournalEntry({
    required DepositModel deposit,
    required String uid,
    required String createdBy,
  }) async {
    try {
      dev.log('Generating deposit journal entry for: ${deposit.amount}');

      // Validate deposit ID is not empty
      if (deposit.id.isEmpty) {
        dev.log(
            '❌ [JOURNAL GENERATE] Deposit ID is empty, cannot generate journal entry');
        throw Exception('Deposit ID is required for journal entry generation');
      }

      // Check if deposit uses Chart of Accounts
      if (!deposit.usesChartOfAccounts) {
        dev.log(
            'Deposit does not use Chart of Accounts, cannot generate journal entry');
        return null;
      }

      // Get source account (where money decreases from)
      final sourceAccount = await _chartOfAccountsService
          .getAccountById(deposit.sourceAccountId!);
      if (sourceAccount == null) {
        dev.log('Source account not found: ${deposit.sourceAccountId}');
        return null;
      }

      // Get destination account (where money increases to)
      final destinationAccount = await _chartOfAccountsService
          .getAccountById(deposit.destinationAccountId!);
      if (destinationAccount == null) {
        dev.log(
            'Destination account not found: ${deposit.destinationAccountId}');
        return null;
      }

      // Generate unique journal entry ID
      final journalEntryId = _generateJournalEntryId();

      // Create journal entry lines with proper debit/credit logic
      final lines = <JournalEntryLineModel>[];

      // Source account line (money decreases)
      final sourceAmounts = _getDebitCreditAmounts(
        account: sourceAccount,
        amount: deposit.amount,
        isIncrease: false, // Money decreases from source account
      );

      lines.add(JournalEntryLineModel(
        id: _generateLineId(),
        journalEntryId: journalEntryId,
        accountId: sourceAccount.id,
        accountName: sourceAccount.accountName,
        accountNumber: sourceAccount.accountNumber,
        description: 'Deposit source: ${deposit.payerName}',
        debitAmount: sourceAmounts['debit']!,
        creditAmount: sourceAmounts['credit']!,
        createdAt: DateTime.now(),
      ));

      // Destination account line (money increases)
      final destinationAmounts = _getDebitCreditAmounts(
        account: destinationAccount,
        amount: deposit.amount,
        isIncrease: true, // Money increases to destination account
      );

      lines.add(JournalEntryLineModel(
        id: _generateLineId(),
        journalEntryId: journalEntryId,
        accountId: destinationAccount.id,
        accountName: destinationAccount.accountName,
        accountNumber: destinationAccount.accountNumber,
        description: 'Deposit from: ${deposit.payerName}',
        debitAmount: destinationAmounts['debit']!,
        creditAmount: destinationAmounts['credit']!,
        createdAt: DateTime.now(),
      ));

      // Calculate totals
      final totalDebits =
          lines.fold<double>(0, (sum, line) => sum + line.debitAmount);
      final totalCredits =
          lines.fold<double>(0, (sum, line) => sum + line.creditAmount);

      dev.log(
          'Deposit journal entry: Debits=$totalDebits, Credits=$totalCredits');

      // Log the critical sourceTransactionId assignment
      dev.log(
          '🔍 [JOURNAL GENERATE] Setting sourceTransactionId: ${deposit.id}');
      dev.log('🔍 [JOURNAL GENERATE] Setting sourceTransactionType: deposit');
      dev.log(
          '🔍 [JOURNAL GENERATE] Setting referenceNumber: ${deposit.referenceNumber}');

      final journalEntry = JournalEntryModel(
        id: journalEntryId,
        entryNumber: '', // Will be auto-generated by Firebase service
        entryDate: deposit.createdAt,
        description:
            'Deposit from: ${deposit.payerName} - ${deposit.categoryName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: totalDebits,
        totalCredits: totalCredits,
        referenceNumber: deposit.referenceNumber,
        sourceTransactionId: deposit.id,
        sourceTransactionType: 'deposit',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );

      // Verify the journal entry has the correct sourceTransactionId
      dev.log(
          '✅ [JOURNAL GENERATE] Created journal entry with sourceTransactionId: ${journalEntry.sourceTransactionId}');

      return journalEntry;
    } catch (e) {
      dev.log('Error generating deposit journal entry: $e');
      return null;
    }
  }

  /// Generate journal entry from bill transaction
  Future<JournalEntryModel?> generateBillJournalEntry({
    required BillModel bill,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get accounts payable account
      final accountsPayableAccount = await _getAccountByType(
        AccountType.currentLiabilities,
        'Accounts Payable',
        uid,
      );
      if (accountsPayableAccount == null) return null;

      // Get revenue/service account
      final revenueAccount = await _getAccountByType(
        AccountType.serviceRevenue,
        'Service Revenue',
        uid,
      );
      if (revenueAccount == null) return null;

      // Create journal entry lines
      final lines = <JournalEntryLineModel>[
        // Debit accounts receivable (if bill represents revenue)
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: accountsPayableAccount.id,
          accountName: accountsPayableAccount.accountName,
          accountNumber: accountsPayableAccount.accountNumber,
          description: 'Bill: ${bill.billNumber}',
          debitAmount: bill.totalAmount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit revenue account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: revenueAccount.id,
          accountName: revenueAccount.accountName,
          accountNumber: revenueAccount.accountNumber,
          description: 'Revenue from bill: ${bill.billNumber}',
          debitAmount: 0.0,
          creditAmount: bill.totalAmount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: bill.billDate,
        description:
            'Bill: ${bill.billNumber} - ${bill.customerName ?? "Multiple Customers"}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: bill.totalAmount,
        totalCredits: bill.totalAmount,
        referenceNumber: bill.billNumber,
        sourceTransactionId: bill.billId,
        sourceTransactionType: 'bill',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      dev.log('Error generating bill journal entry: $e');
      return null;
    }
  }

  /// Generate single journal entry from voucher transaction with multiple line items
  Future<List<JournalEntryModel>> generateVoucherJournalEntries({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      dev.log(
          'Generating single journal entry for voucher: ${voucher.voucherNumber}');

      // Check if voucher has Chart of Accounts explicitly selected
      final hasExplicitAccounts = _hasExplicitChartOfAccounts(voucher);

      if (hasExplicitAccounts) {
        dev.log(
            'Using explicitly selected Chart of Accounts for voucher: ${voucher.voucherNumber}');
        // Generate single journal entry with all line items
        final journalEntry = await _generateSingleVoucherJournalEntry(
          voucher: voucher,
          uid: uid,
          createdBy: createdBy,
        );

        if (journalEntry != null) {
          dev.log(
              'Successfully generated journal entry with ${journalEntry.lines.length} line items');
          return [journalEntry]; // Return single entry in list
        } else {
          dev.log(
              'FAILED: No journal entry generated for voucher with explicit accounts: ${voucher.voucherNumber}');
          throw Exception(
              'Failed to generate journal entry for voucher ${voucher.voucherNumber} despite having explicit Chart of Accounts selected. Check account configurations.');
        }
      } else {
        dev.log(
            'No explicit Chart of Accounts selected, using default account mappings for voucher: ${voucher.voucherNumber}');
        // Generate journal entry using default account mappings
        final journalEntry = await _generateVoucherJournalEntryWithDefaults(
          voucher: voucher,
          uid: uid,
          createdBy: createdBy,
        );

        if (journalEntry != null) {
          dev.log(
              'Successfully generated journal entry with default mappings: ${journalEntry.lines.length} line items');
          return [journalEntry]; // Return single entry in list
        } else {
          dev.log(
              'FAILED: No journal entry generated with default mappings for voucher: ${voucher.voucherNumber}');
          throw Exception(
              'Failed to generate journal entry for voucher ${voucher.voucherNumber} using default account mappings. Check if default accounts are properly configured in Chart of Accounts.');
        }
      }
    } catch (e) {
      dev.log('Error generating voucher journal entries: $e');
      rethrow; // Propagate the error to the calling service
    }
  }

  /// Check if voucher has explicit Chart of Accounts selected
  /// Note: Truck freight account removed since truck freight is now recorded with payments
  bool _hasExplicitChartOfAccounts(VoucherModel voucher) {
    return (voucher.companyFreightAccountId != null &&
            voucher.companyFreightAccountId!.isNotEmpty) ||
        (voucher.brokerAccountId != null &&
            voucher.brokerAccountId!.isNotEmpty) ||
        (voucher.munshianaAccountId != null &&
            voucher.munshianaAccountId!.isNotEmpty) ||
        (voucher.salesTaxAccountId != null &&
            voucher.salesTaxAccountId!.isNotEmpty) ||
        (voucher.freightTaxAccountId != null &&
            voucher.freightTaxAccountId!.isNotEmpty) ||
        (voucher.profitAccountId != null &&
            voucher.profitAccountId!.isNotEmpty);
    // Removed: truckFreightAccountId check - truck freight now recorded with payments only
  }

  /// Generate a single journal entry with multiple line items for voucher transaction
  Future<JournalEntryModel?> _generateSingleVoucherJournalEntry({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      dev.log(
          '🔍 Starting journal entry generation for voucher: ${voucher.voucherNumber}');
      dev.log(
          '📊 Voucher details: Freight=${voucher.totalFreight}, Broker=${voucher.brokerFees}, Munshiana=${voucher.munshianaFees}');

      final lines = <JournalEntryLineModel>[];
      double totalDebits = 0.0;
      double totalCredits = 0.0;

      // 1. NLC Amount (Company Freight) - Asset Account (typically Accounts Receivable)
      if (voucher.companyFreight > 0 &&
          voucher.companyFreightAccountId != null) {
        dev.log(
            '🔍 Looking up NLC account: ${voucher.companyFreightAccountId}');
        dev.log(
            '💰 Using Company Freight amount: ${voucher.companyFreight} (user-entered NLC amount)');
        final nlcAccount = await _chartOfAccountsService
            .getAccountById(voucher.companyFreightAccountId!);
        if (nlcAccount != null) {
          dev.log('✅ Found NLC account: ${nlcAccount.accountName}');
          // Use helper method to determine correct debit/credit based on account type
          final amounts = _getDebitCreditAmounts(
            account: nlcAccount,
            amount: MonetaryRounding.roundHalfUp(voucher.companyFreight),
            isIncrease: true, // NLC amount increases the receivable
          );

          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: nlcAccount.id,
            accountNumber: nlcAccount.accountNumber,
            accountName: nlcAccount.accountName,
            debitAmount: amounts['debit']!,
            creditAmount: amounts['credit']!,
            description:
                'NLC Amount (User-Entered) - Voucher #${voucher.voucherNumber}',
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalDebits += amounts['debit']!;
          totalCredits += amounts['credit']!;
        } else {
          throw Exception(
              'NLC account not found: ${voucher.companyFreightAccountId}. Please check Chart of Accounts configuration.');
        }
      }

      // 2. Broker Fees - Liability Account (typically Accounts Payable)
      if (voucher.brokerFees > 0 && voucher.brokerAccountId != null) {
        final brokerAccount = await _chartOfAccountsService
            .getAccountById(voucher.brokerAccountId!);
        if (brokerAccount != null) {
          // Use helper method to determine correct debit/credit based on account type
          final amounts = _getDebitCreditAmounts(
            account: brokerAccount,
            amount: MonetaryRounding.roundHalfUp(voucher.brokerFees),
            isIncrease: true, // Broker fees increase the payable liability
          );

          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: brokerAccount.id,
            accountNumber: brokerAccount.accountNumber,
            accountName: brokerAccount.accountName,
            debitAmount: amounts['debit']!,
            creditAmount: amounts['credit']!,
            description: 'Broker Fees - Voucher #${voucher.voucherNumber}',
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalDebits += amounts['debit']!;
          totalCredits += amounts['credit']!;
        } else {
          throw Exception(
              'Broker account not found: ${voucher.brokerAccountId}. Please check Chart of Accounts configuration.');
        }
      }

      // 3. Munshiana Fees - Equity Account (typically Service Revenue)
      if (voucher.munshianaFees > 0 && voucher.munshianaAccountId != null) {
        final munshianaAccount = await _chartOfAccountsService
            .getAccountById(voucher.munshianaAccountId!);
        if (munshianaAccount != null) {
          // Use helper method to determine correct debit/credit based on account type
          final amounts = _getDebitCreditAmounts(
            account: munshianaAccount,
            amount: MonetaryRounding.roundHalfUp(voucher.munshianaFees),
            isIncrease: true, // Munshiana fees increase the equity/revenue
          );

          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: munshianaAccount.id,
            accountNumber: munshianaAccount.accountNumber,
            accountName: munshianaAccount.accountName,
            debitAmount: amounts['debit']!,
            creditAmount: amounts['credit']!,
            description: 'Munshiana Fees - Voucher #${voucher.voucherNumber}',
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalDebits += amounts['debit']!;
          totalCredits += amounts['credit']!;
        }
      }

      // 4. Truck/Wagon Freight - REMOVED: Now recorded only when payments are made
      // Note: Truck freight journal entries are now created during payment processing
      // to ensure accounting entries reflect actual payments rather than expected amounts
      dev.log(
          '🚛 Skipping truck freight journal entry during voucher creation - will be recorded with payments');

      // Calculate truck freight for logging purposes only
      final truckFreight = voucher.totalFreight;
      dev.log(
          '📊 Truck freight amount (will be recorded with payments): $truckFreight');

      // 5. Tax Authority Payments (15% freight tax) - Liability Accounts
      // Note: calculatedTax represents the 15% freight tax, distributed among selected tax authorities
      if (voucher.calculatedTax > 0 &&
          voucher.selectedTaxAuthorities.isNotEmpty) {
        dev.log(
            '🔍 Processing tax authority payments for ${voucher.selectedTaxAuthorities.length} authorities');
        dev.log('📊 Total 15% tax amount: ${voucher.calculatedTax}');
        dev.log(
            '🏛️ Selected tax authorities: ${voucher.selectedTaxAuthorities}');

        // Calculate amount per authority (split equally)
        final amountPerAuthority =
            voucher.calculatedTax / voucher.selectedTaxAuthorities.length;
        dev.log(
            '💰 Amount per authority: ${amountPerAuthority.toStringAsFixed(2)}');

        // Create journal entries for each selected tax authority
        for (final authority in voucher.selectedTaxAuthorities) {
          final taxAuthorityAccount = await _getTaxAuthorityAccount(authority);
          if (taxAuthorityAccount != null) {
            dev.log(
                '✅ Found tax authority account for $authority: ${taxAuthorityAccount.accountName}');

            // Use helper method to determine correct debit/credit based on account type
            final amounts = _getDebitCreditAmounts(
              account: taxAuthorityAccount,
              amount: MonetaryRounding.roundHalfUp(amountPerAuthority),
              isIncrease: true, // Tax authority liability increases
            );

            final line = JournalEntryLineModel(
              id: '',
              journalEntryId: '',
              accountId: taxAuthorityAccount.id,
              accountNumber: taxAuthorityAccount.accountNumber,
              accountName: taxAuthorityAccount.accountName,
              debitAmount: amounts['debit']!,
              creditAmount: amounts['credit']!,
              description:
                  'Tax Authority Payment ($authority) - Voucher #${voucher.voucherNumber}',
              referenceId: voucher.voucherNumber,
              referenceType: 'voucher',
              createdAt: DateTime.now(),
            );
            lines.add(line);
            totalDebits += amounts['debit']!;
            totalCredits += amounts['credit']!;
          } else {
            dev.log('❌ Tax authority account not found for: $authority');
            throw Exception(
                'Tax authority account not found for $authority. Please configure tax authority accounts in voucher payment settings.');
          }
        }
      } else if (voucher.calculatedTax > 0) {
        dev.log(
            '⚠️ WARNING: Voucher has 15% tax amount but no tax authorities selected');
        throw Exception(
            'Voucher ${voucher.voucherNumber} has 15% tax amount (${voucher.calculatedTax}) but no tax authorities are selected. Please select tax authorities.');
      }

      // 6. Sales Tax (6.9%) - Liability Account (typically Tax Payable)
      // Note: calculatedFreightTax represents the 6.9% sales tax
      if (voucher.calculatedFreightTax > 0) {
        dev.log(
            '🔍 Adding 6.9% sales tax entry - Amount: ${voucher.calculatedFreightTax}');

        // Get the sales tax account - first try voucher's account, then fall back to settings
        ChartOfAccountsModel? salesTaxPayableAccount;

        if (voucher.salesTaxAccountId != null &&
            voucher.salesTaxAccountId!.isNotEmpty) {
          dev.log(
              '🔍 Using voucher sales tax account ID: ${voucher.salesTaxAccountId}');
          salesTaxPayableAccount = await _chartOfAccountsService
              .getAccountById(voucher.salesTaxAccountId!);
        }

        // If voucher doesn't have sales tax account, use tax payable account from settings
        if (salesTaxPayableAccount == null) {
          dev.log(
              '🔍 Voucher has no sales tax account, trying tax payable account from settings');
          try {
            final settingsService = Get.find<VoucherPaymentSettingsService>();
            final taxPayableAccount = settingsService.loadTaxPayableAccount();
            if (taxPayableAccount != null) {
              dev.log(
                  '🔍 Using tax payable account from settings: ${taxPayableAccount.accountName}');
              // Verify the account still exists and belongs to the current user
              salesTaxPayableAccount = await _chartOfAccountsService
                  .getAccountById(taxPayableAccount.id);
              if (salesTaxPayableAccount != null &&
                  salesTaxPayableAccount.uid == uid) {
                dev.log(
                    '✅ Verified tax payable account: ${salesTaxPayableAccount.accountName}');
              } else {
                dev.log(
                    '❌ Tax payable account verification failed or belongs to different user');
                salesTaxPayableAccount = null;
              }
            } else {
              dev.log('❌ No tax payable account found in settings');
            }
          } catch (e) {
            dev.log('❌ VoucherPaymentSettingsService not available: $e');
          }
        }

        if (salesTaxPayableAccount != null) {
          dev.log(
              '✅ Found 6.9% sales tax account: ${salesTaxPayableAccount.accountName}');
          // Use helper method to determine correct debit/credit based on account type
          final amounts = _getDebitCreditAmounts(
            account: salesTaxPayableAccount,
            amount: MonetaryRounding.roundHalfUp(voucher.calculatedFreightTax),
            isIncrease: true, // Sales tax increases the payable liability
          );

          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: salesTaxPayableAccount.id,
            accountNumber: salesTaxPayableAccount.accountNumber,
            accountName: salesTaxPayableAccount.accountName,
            debitAmount: amounts['debit']!,
            creditAmount: amounts['credit']!,
            description: 'Sales Tax (6.9%) - Voucher #${voucher.voucherNumber}',
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalDebits += amounts['debit']!;
          totalCredits += amounts['credit']!;

          dev.log('✅ Added 6.9% sales tax journal entry to voucher');
        } else {
          dev.log(
              '❌ Sales tax account not found: ${voucher.salesTaxAccountId ?? 'null'}');
          throw Exception(
              '6.9% sales tax account not configured. Please configure the 6.9% Sales Tax Account in voucher payment settings.');
        }
      } else {
        dev.log(
            'ℹ️ No sales tax entry needed - calculatedFreightTax: ${voucher.calculatedFreightTax}');
      }

      // 7. Net Profit/Loss - Equity Account (typically Retained Earnings)
      if (voucher.calculatedProfit != 0 && voucher.profitAccountId != null) {
        final profitAccount = await _chartOfAccountsService
            .getAccountById(voucher.profitAccountId!);
        if (profitAccount != null) {
          // Use helper method to determine correct debit/credit based on account type
          // For positive profit: increase equity (credit for equity accounts)
          // For negative profit (loss): decrease equity (debit for equity accounts)
          final roundedProfit =
              MonetaryRounding.roundHalfUp(voucher.calculatedProfit.abs());
          final amounts = _getDebitCreditAmounts(
            account: profitAccount,
            amount: roundedProfit, // Use rounded absolute value
            isIncrease: voucher.calculatedProfit >
                0, // Increase for profit, decrease for loss
          );

          final description = voucher.calculatedProfit > 0
              ? 'Net Profit - Voucher #${voucher.voucherNumber}'
              : 'Net Loss - Voucher #${voucher.voucherNumber}';

          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: profitAccount.id,
            accountNumber: profitAccount.accountNumber,
            accountName: profitAccount.accountName,
            debitAmount: amounts['debit']!,
            creditAmount: amounts['credit']!,
            description: description,
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalDebits += amounts['debit']!;
          totalCredits += amounts['credit']!;
        }
      }

      // Validate that we have journal entry lines
      if (lines.isEmpty) {
        dev.log(
            '❌ No journal entry lines generated for voucher: ${voucher.voucherNumber}');
        dev.log(
            '🔍 Voucher has: CompanyFreight=${voucher.companyFreight}, TotalFreight=${voucher.totalFreight}, Broker=${voucher.brokerFees}, Munshiana=${voucher.munshianaFees}');
        dev.log(
            '🔍 Account IDs: NLC=${voucher.companyFreightAccountId}, Broker=${voucher.brokerAccountId}, Munshiana=${voucher.munshianaAccountId}');
        throw Exception(
            'No journal entry lines could be generated for voucher ${voucher.voucherNumber}. This usually means required accounts are not selected or configured properly.');
      }

      // Note: Double-entry validation removed to allow unbalanced voucher saves
      dev.log(
          'Creating journal entry for voucher ${voucher.voucherNumber}: Debits=$totalDebits, Credits=$totalCredits');

      // Generate unique ID for journal entry
      final journalEntryId = _generateJournalEntryId();

      // Update all line items with the journal entry ID
      final updatedLines = lines
          .map((line) => JournalEntryLineModel(
                id: _generateLineId(),
                journalEntryId: journalEntryId,
                accountId: line.accountId,
                accountNumber: line.accountNumber,
                accountName: line.accountName,
                debitAmount: line.debitAmount,
                creditAmount: line.creditAmount,
                description: line.description,
                referenceId: line.referenceId,
                referenceType: line.referenceType,
                createdAt: line.createdAt,
              ))
          .toList();

      // Use voucher departure date for both entry date and creation date to maintain consistency
      final voucherDate = _parseVoucherDate(voucher.departureDate);

      // Create the journal entry
      return JournalEntryModel(
        id: journalEntryId,
        entryNumber: '', // Will be auto-generated by Firebase service
        entryDate: voucherDate,
        description: 'Voucher Transaction - ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: updatedLines,
        totalDebits: totalDebits,
        totalCredits: totalCredits,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher',
        createdAt: DateTime
            .now(), // Use current system time for proper chronological ordering
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      dev.log('❌ Error generating single voucher journal entry: $e');
      dev.log('🔍 Voucher data: ${voucher.toJson()}');
      throw Exception(
          'Failed to generate journal entry for voucher ${voucher.voucherNumber}: $e');
    }
  }

  /// Generate voucher journal entry using default account mappings
  Future<JournalEntryModel?> _generateVoucherJournalEntryWithDefaults({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      dev.log(
          'Generating voucher journal entry with default mappings for: ${voucher.voucherNumber}');

      // Get default account mappings from TransactionAccountMappingService
      final mappingService =
          TransactionAccountMappingService(_chartOfAccountsService);
      final mapping = await mappingService.getVoucherAccountMapping(uid);

      if (mapping == null) {
        dev.log(
            'CRITICAL: No default account mappings found for voucher transactions. Please ensure Chart of Accounts has the required account types configured.');
        throw Exception(
            'Default account mappings not found. Please configure the required accounts in Chart of Accounts: Accounts Payable, Equity Service Revenue, Revenue, Cash, Accounts Receivable, and Tax Payable accounts.');
      }

      final lines = <JournalEntryLineModel>[];
      double totalDebits = 0.0;
      double totalCredits = 0.0;

      // Calculate net cash received (Total Freight - Fees) with rounding
      final netCashReceived = MonetaryRounding.roundHalfUp(
          voucher.totalFreight - voucher.brokerFees - voucher.munshianaFees);

      // 1. Net Cash Received - Asset Account (typically Cash)
      if (netCashReceived > 0) {
        // Use helper method to determine correct debit/credit based on account type
        final amounts = _getDebitCreditAmounts(
          account: mapping.cashAccount,
          amount: netCashReceived,
          isIncrease: true, // Cash received increases the cash asset
        );

        final cashLine = JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: mapping.cashAccount.id,
          accountNumber: mapping.cashAccount.accountNumber,
          accountName: mapping.cashAccount.accountName,
          debitAmount: amounts['debit']!,
          creditAmount: amounts['credit']!,
          description: 'Net Cash Received - Voucher #${voucher.voucherNumber}',
          referenceId: voucher.voucherNumber,
          referenceType: 'voucher',
          createdAt: DateTime.now(),
        );
        lines.add(cashLine);
        totalDebits += amounts['debit']!;
        totalCredits += amounts['credit']!;
      }

      // 2. Broker Fees - Expense Account (typically Operating Expenses)
      if (voucher.brokerFees > 0) {
        // Use helper method to determine correct debit/credit based on account type
        final amounts = _getDebitCreditAmounts(
          account: mapping.brokerFeesAccount,
          amount: MonetaryRounding.roundHalfUp(voucher.brokerFees),
          isIncrease: true, // Broker fees increase the expense
        );

        final line = JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: mapping.brokerFeesAccount.id,
          accountNumber: mapping.brokerFeesAccount.accountNumber,
          accountName: mapping.brokerFeesAccount.accountName,
          debitAmount: amounts['debit']!,
          creditAmount: amounts['credit']!,
          description: 'Broker Fees - Voucher #${voucher.voucherNumber}',
          referenceId: voucher.voucherNumber,
          referenceType: 'voucher',
          createdAt: DateTime.now(),
        );
        lines.add(line);
        totalDebits += amounts['debit']!;
        totalCredits += amounts['credit']!;
      }

      // 3. Munshiana Fees - Expense Account (typically Operating Expenses)
      if (voucher.munshianaFees > 0) {
        // Use helper method to determine correct debit/credit based on account type
        final amounts = _getDebitCreditAmounts(
          account: mapping.munshianaAccount,
          amount: MonetaryRounding.roundHalfUp(voucher.munshianaFees),
          isIncrease: true, // Munshiana fees increase the expense
        );

        final line = JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: mapping.munshianaAccount.id,
          accountNumber: mapping.munshianaAccount.accountNumber,
          accountName: mapping.munshianaAccount.accountName,
          debitAmount: amounts['debit']!,
          creditAmount: amounts['credit']!,
          description: 'Munshiana Fees - Voucher #${voucher.voucherNumber}',
          referenceId: voucher.voucherNumber,
          referenceType: 'voucher',
          createdAt: DateTime.now(),
        );
        lines.add(line);
        totalDebits += amounts['debit']!;
        totalCredits += amounts['credit']!;
      }

      // 4. Freight Revenue - Revenue Account (typically Sales Revenue)
      if (voucher.totalFreight > 0) {
        // Use helper method to determine correct debit/credit based on account type
        final amounts = _getDebitCreditAmounts(
          account: mapping.freightRevenueAccount,
          amount: MonetaryRounding.roundHalfUp(voucher.totalFreight),
          isIncrease: true, // Freight revenue increases the revenue
        );

        final revenueLine = JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: mapping.freightRevenueAccount.id,
          accountNumber: mapping.freightRevenueAccount.accountNumber,
          accountName: mapping.freightRevenueAccount.accountName,
          debitAmount: amounts['debit']!,
          creditAmount: amounts['credit']!,
          description: 'Freight Revenue - Voucher #${voucher.voucherNumber}',
          referenceId: voucher.voucherNumber,
          referenceType: 'voucher',
          createdAt: DateTime.now(),
        );
        lines.add(revenueLine);
        totalDebits += amounts['debit']!;
        totalCredits += amounts['credit']!;
      }

      // 5. Sales Tax (15%) - Asset Account (representing tax receivable)
      if (voucher.calculatedTax > 0) {
        // Use helper method to determine correct debit/credit based on account type
        final amounts = _getDebitCreditAmounts(
          account: mapping
              .cashAccount, // Using cash account as fallback for default mapping
          amount: voucher.calculatedTax,
          isIncrease: false, // Tax payment decreases cash
        );

        final line = JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: mapping.cashAccount.id,
          accountNumber: mapping.cashAccount.accountNumber,
          accountName: mapping.cashAccount.accountName,
          debitAmount: amounts['debit']!,
          creditAmount: amounts['credit']!,
          description: '15% Sales Tax - Voucher #${voucher.voucherNumber}',
          referenceId: voucher.voucherNumber,
          referenceType: 'voucher',
          createdAt: DateTime.now(),
        );
        lines.add(line);
        totalDebits += amounts['debit']!;
        totalCredits += amounts['credit']!;
      }

      // 6. Freight Tax (4.6%) - Asset Account (representing tax payment)
      if (voucher.calculatedFreightTax > 0) {
        // Use helper method to determine correct debit/credit based on account type
        final amounts = _getDebitCreditAmounts(
          account: mapping
              .cashAccount, // Using cash account as fallback for default mapping
          amount: voucher.calculatedFreightTax,
          isIncrease: false, // Tax payment decreases cash
        );

        final line = JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: mapping.cashAccount.id,
          accountNumber: mapping.cashAccount.accountNumber,
          accountName: mapping.cashAccount.accountName,
          debitAmount: amounts['debit']!,
          creditAmount: amounts['credit']!,
          description: '4.6% Freight Tax - Voucher #${voucher.voucherNumber}',
          referenceId: voucher.voucherNumber,
          referenceType: 'voucher',
          createdAt: DateTime.now(),
        );
        lines.add(line);
        totalDebits += amounts['debit']!;
        totalCredits += amounts['credit']!;
      }

      // 7. Net Profit/Loss - Asset Account (representing profit retained in cash)
      if (voucher.calculatedProfit != 0) {
        // Use helper method to determine correct debit/credit based on account type
        final amounts = _getDebitCreditAmounts(
          account: mapping
              .cashAccount, // Using cash account as fallback for default mapping
          amount: voucher.calculatedProfit.abs(), // Use absolute value
          isIncrease: voucher.calculatedProfit > 0
              ? false
              : true, // Profit decreases cash, loss increases cash
        );

        final description = voucher.calculatedProfit > 0
            ? 'Net Profit - Voucher #${voucher.voucherNumber}'
            : 'Net Loss - Voucher #${voucher.voucherNumber}';

        final line = JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: mapping.cashAccount.id,
          accountNumber: mapping.cashAccount.accountNumber,
          accountName: mapping.cashAccount.accountName,
          debitAmount: amounts['debit']!,
          creditAmount: amounts['credit']!,
          description: description,
          referenceId: voucher.voucherNumber,
          referenceType: 'voucher',
          createdAt: DateTime.now(),
        );
        lines.add(line);
        totalDebits += amounts['debit']!;
        totalCredits += amounts['credit']!;
      }

      // Check if we have any lines to process
      if (lines.isEmpty) {
        dev.log(
            'No valid transactions found for voucher: ${voucher.voucherNumber}');
        return null;
      }

      // Note: Double-entry validation removed to allow unbalanced voucher saves
      dev.log(
          'Creating journal entry for voucher ${voucher.voucherNumber}: Debits=$totalDebits, Credits=$totalCredits');

      // Generate unique ID for journal entry
      final journalEntryId = _generateJournalEntryId();

      // Update all line items with the journal entry ID
      final updatedLines = lines
          .map((line) => JournalEntryLineModel(
                id: _generateLineId(),
                journalEntryId: journalEntryId,
                accountId: line.accountId,
                accountNumber: line.accountNumber,
                accountName: line.accountName,
                debitAmount: line.debitAmount,
                creditAmount: line.creditAmount,
                description: line.description,
                referenceId: line.referenceId,
                referenceType: line.referenceType,
                createdAt: line.createdAt,
              ))
          .toList();

      // Use voucher departure date for both entry date and creation date to maintain consistency
      final voucherDate = _parseVoucherDate(voucher.departureDate);

      // Create the journal entry
      return JournalEntryModel(
        id: journalEntryId,
        entryNumber: '', // Will be auto-generated by Firebase service
        entryDate: voucherDate,
        description:
            'Voucher Transaction (Default Mapping) - ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: updatedLines,
        totalDebits: totalDebits,
        totalCredits: totalCredits,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher',
        createdAt: DateTime
            .now(), // Use current system time for proper chronological ordering
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      dev.log('Error generating voucher journal entry with defaults: $e');
      return null;
    }
  }

  /// Generate journal entry from loan transaction
  Future<List<JournalEntryModel>> generateLoanJournalEntries({
    required LoanModel loan,
    required String transactionType, // 'disbursement' or 'repayment'
    required String uid,
    required String createdBy,
  }) async {
    final journalEntries = <JournalEntryModel>[];

    try {
      if (transactionType == 'disbursement') {
        // Loan disbursement entry
        final disbursementEntry = await _generateLoanDisbursementEntry(
          loan: loan,
          uid: uid,
          createdBy: createdBy,
        );
        if (disbursementEntry != null) journalEntries.add(disbursementEntry);
      } else if (transactionType == 'repayment') {
        // Loan repayment entry
        final repaymentEntry = await _generateLoanRepaymentEntry(
          loan: loan,
          uid: uid,
          createdBy: createdBy,
        );
        if (repaymentEntry != null) journalEntries.add(repaymentEntry);
      }

      return journalEntries;
    } catch (e) {
      dev.log('Error generating loan journal entries: $e');
      return [];
    }
  }

  /// Determine if a payment is cross-company ("Other") or same-company ("Own")
  /// based on account ownership rather than payment method
  Future<bool> _isOtherPaymentType(PaymentTransactionModel payment) async {
    try {
      // If no account is selected, default to "Own" payment type
      if (payment.accountId == null || payment.accountId!.isEmpty) {
        dev.log('🔍 No account selected, defaulting to Own payment type');
        return false;
      }

      // Get account details to check ownership
      final account = await _chartOfAccountsService
          .getAccountByIdCrossCompany(payment.accountId!);

      if (account == null) {
        dev.log(
            '❌ Account not found: ${payment.accountId}, defaulting to Own payment type');
        return false;
      }

      // Get current user UID for comparison
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        dev.log('❌ No current user, defaulting to Own payment type');
        return false;
      }

      // If account belongs to different company, it's "Other" payment type
      final isOtherPayment = account.uid != currentUser.uid;

      dev.log('🔍 Payment type determination:');
      dev.log(
          '   - Account: ${account.accountName} (${account.accountNumber})');
      dev.log('   - Account UID: ${account.uid}');
      dev.log('   - Current User UID: ${currentUser.uid}');
      dev.log('   - Is Other Payment: $isOtherPayment');

      return isOtherPayment;
    } catch (e) {
      dev.log(
          '❌ Error determining payment type: $e, defaulting to Own payment type');
      return false;
    }
  }

  /// Generate journal entry from payment transaction
  /// Supports both traditional and loan-based payment workflows
  Future<JournalEntryModel?> generatePaymentJournalEntry({
    required PaymentTransactionModel payment,
    required String uid,
    required String createdBy,
    bool isLoanBasedWorkflow = false,
  }) async {
    try {
      dev.log('Generating journal entry for payment: ${payment.id}');
      dev.log('Payment method: ${payment.method}, amount: ${payment.amount}');
      dev.log(
          'Loan-based workflow: ${payment.isLoanBased || isLoanBasedWorkflow}');

      // Validate payment has Chart of Accounts reference
      if (payment.accountId == null || payment.accountId!.isEmpty) {
        dev.log('❌ Payment missing Chart of Accounts reference: ${payment.id}');
        throw Exception(
            'Payment transaction must have a Chart of Accounts account selected. Please configure payment account.');
      }

      // Get the source account (Asset account - Cash/Bank)
      // Use cross-company method for loan-based payments to allow cross-company account access
      final sourceAccount = (payment.isLoanBased || isLoanBasedWorkflow)
          ? await _chartOfAccountsService
              .getAccountByIdCrossCompany(payment.accountId!)
          : await _chartOfAccountsService.getAccountById(payment.accountId!);

      if (sourceAccount == null) {
        dev.log('❌ Source account not found: ${payment.accountId}');
        throw Exception(
            'Payment source account not found: ${payment.accountId}. Please check Chart of Accounts configuration.');
      }

      dev.log(
          '✅ Found source account: ${sourceAccount.accountName} (${sourceAccount.accountNumber})');

      // Get the truck freight account from voucher payment settings
      // This ensures payments are recorded against the configured truck freight account
      // Choose between regular truck fare account and truck fare by cheque account based on payment method
      final truckFreightAccount =
          await _getTruckFreightAccountFromSettings(uid, payment.method);

      if (truckFreightAccount == null) {
        final accountType = payment.method == PaymentMethod.check
            ? 'truck fare by cheque account'
            : 'truck fare account';
        dev.log(
            '❌ $accountType not found for payment method: ${payment.method.name}');
        throw Exception(
            'Required $accountType not configured. Please configure the $accountType in voucher payment settings.');
      }

      dev.log(
          '✅ Found truck freight account: ${truckFreightAccount.accountName} (${truckFreightAccount.accountNumber})');
      dev.log(
          '🔍 Truck freight account type: ${truckFreightAccount.accountType} (Category: ${truckFreightAccount.category})');

      // Note: Sales tax is handled in voucher journal entries, not payment journal entries

      // Create journal entry lines with proper double-entry accounting
      final lines = <JournalEntryLineModel>[];

      // 1. Credit the source account (Asset decreases with credit)
      final roundedAmount = MonetaryRounding.roundHalfUp(payment.amount);
      final sourceAmounts = _getDebitCreditAmounts(
        account: sourceAccount,
        amount: roundedAmount,
        isIncrease: false, // Asset account decreases (money going out)
      );

      dev.log(
          '💰 Source account amounts - Debit: ${sourceAmounts['debit']}, Credit: ${sourceAmounts['credit']}');

      final sourceLine = JournalEntryLineModel(
        id: '',
        journalEntryId: '',
        accountId: sourceAccount.id,
        accountNumber: sourceAccount.accountNumber,
        accountName: sourceAccount.accountName,
        debitAmount: sourceAmounts['debit']!,
        creditAmount: sourceAmounts['credit']!,
        description:
            'Payment - Voucher #${payment.voucherId} - ${payment.method.name}',
        referenceId: payment.voucherId,
        referenceType: 'payment',
        createdAt: DateTime.now(),
      );
      lines.add(sourceLine);

      // 2. Debit the truck freight account (Liability decreases with debit when we pay off the liability)
      final truckFreightAmounts = _getDebitCreditAmounts(
        account: truckFreightAccount,
        amount:
            roundedAmount, // Use rounded payment amount - no sales tax split
        isIncrease:
            false, // Truck freight liability decreases (paying off the liability)
      );

      dev.log(
          '💰 Truck freight account amounts - Debit: ${truckFreightAmounts['debit']}, Credit: ${truckFreightAmounts['credit']}');

      final truckFreightLine = JournalEntryLineModel(
        id: '',
        journalEntryId: '',
        accountId: truckFreightAccount.id,
        accountNumber: truckFreightAccount.accountNumber,
        accountName: truckFreightAccount.accountName,
        debitAmount: truckFreightAmounts['debit']!,
        creditAmount: truckFreightAmounts['credit']!,
        description:
            'Truck Freight Payment - Voucher #${payment.voucherId} - ${payment.method.name}',
        referenceId: payment.voucherId,
        referenceType: 'payment',
        createdAt: DateTime.now(),
      );
      lines.add(truckFreightLine);

      // Note: Sales tax is handled in voucher journal entries, not payment journal entries

      // 3. For loan-based payments, add loan payable entry ONLY for "Other" payment types (cross-company)
      if (payment.isLoanBased || isLoanBasedWorkflow) {
        // Check if this is an "Other" payment type (cross-company)
        final isOtherPayment = await _isOtherPaymentType(payment);

        dev.log('🔄 Loan-based payment detected:');
        dev.log('   - Is Other Payment (cross-company): $isOtherPayment');

        if (isOtherPayment) {
          dev.log(
              '🔄 Creating compound journal entry for cross-company loan payment');

          final loanPayableAccount =
              await _getLoanPayableAccountFromSettings(uid);

          if (loanPayableAccount != null) {
            dev.log(
                '✅ Found loan payable account: ${loanPayableAccount.accountName} (${loanPayableAccount.accountNumber})');

            // Credit the loan payable account (Liability increases with credit)
            final loanPayableAmounts = _getDebitCreditAmounts(
              account: loanPayableAccount,
              amount: roundedAmount,
              isIncrease:
                  true, // Loan payable liability increases (we owe more)
            );

            dev.log(
                '💰 Loan payable account amounts - Debit: ${loanPayableAmounts['debit']}, Credit: ${loanPayableAmounts['credit']}');

            final loanPayableLine = JournalEntryLineModel(
              id: '',
              journalEntryId: '',
              accountId: loanPayableAccount.id,
              accountNumber: loanPayableAccount.accountNumber,
              accountName: loanPayableAccount.accountName,
              debitAmount: loanPayableAmounts['debit']!,
              creditAmount: loanPayableAmounts['credit']!,
              description:
                  'Loan Liability - Voucher #${payment.voucherId} - ${payment.method.name}',
              referenceId: payment.voucherId,
              referenceType: 'loan_payment',
              createdAt: DateTime.now(),
            );
            lines.add(loanPayableLine);

            dev.log('✅ Added loan payable entry to compound journal entry');
          } else {
            dev.log(
                '⚠️ Loan payable account not configured - creating standard payment entry only');
          }
        } else {
          dev.log('✅ Own payment type - skipping loan payable entry creation');
        }
      }

      // Generate unique journal entry ID
      final journalEntryId = _generateJournalEntryId();

      // Update line IDs with journal entry ID
      final updatedLines = lines.map((line) {
        return line.copyWith(
          id: '${journalEntryId}_${lines.indexOf(line) + 1}',
          journalEntryId: journalEntryId,
        );
      }).toList();

      // Calculate totals
      final totalDebits =
          updatedLines.fold<double>(0, (sum, line) => sum + line.debitAmount);
      final totalCredits =
          updatedLines.fold<double>(0, (sum, line) => sum + line.creditAmount);

      dev.log(
          '📊 Payment journal entry totals - Debits: $totalDebits, Credits: $totalCredits');

      // Check if this is a compound loan entry (only for "Other" payment types with loan payable entries)
      final isOtherPayment = await _isOtherPaymentType(payment);
      final isCompoundLoanEntry =
          (payment.isLoanBased || isLoanBasedWorkflow) && isOtherPayment;

      // Validate double-entry accounting (bypass for compound loan entries with loan payable)
      if (!isCompoundLoanEntry && (totalDebits - totalCredits).abs() > 0.01) {
        dev.log(
            '❌ Payment journal entry not balanced: Debits=$totalDebits, Credits=$totalCredits');
        throw Exception(
            'Payment journal entry is not balanced. Debits: $totalDebits, Credits: $totalCredits');
      } else if (isCompoundLoanEntry) {
        dev.log(
            '✅ Compound loan entry validation bypassed - Debits: $totalDebits, Credits: $totalCredits');
      }

      // Create the journal entry using payment transaction date for consistency
      final description = isCompoundLoanEntry
          ? 'Compound Loan Payment - Voucher #${payment.voucherId} - ${payment.method.name}'
          : 'Payment Transaction - Voucher #${payment.voucherId} - ${payment.method.name}';

      return JournalEntryModel(
        id: journalEntryId,
        entryNumber: '', // Will be auto-generated by Firebase service
        entryDate: payment.transactionDate,
        description: description,
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: updatedLines,
        totalDebits: totalDebits,
        totalCredits: totalCredits,
        referenceNumber: payment.voucherId,
        sourceTransactionId: payment.id,
        sourceTransactionType: 'payment',
        createdAt: DateTime
            .now(), // Use current system time for proper chronological ordering
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      dev.log('Error generating payment journal entry: $e');
      return null;
    }
  }

  /// Generate journal entry from account transaction
  Future<JournalEntryModel?> generateAccountTransactionJournalEntry({
    required AccountTransactionModel transaction,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get the account
      final account = await _getAccountByName(transaction.accountName, uid);
      if (account == null) return null;

      // Determine the offsetting account based on transaction type
      ChartOfAccountsModel? offsetAccount;
      String description = transaction.description;

      switch (transaction.type) {
        case TransactionType.deposit:
          // Credit cash account, debit accounts receivable or revenue
          offsetAccount = await _getAccountByType(
            AccountType.currentAssets,
            'Accounts Receivable',
            uid,
          );
          break;
        case TransactionType.expense:
          // Debit expense account, credit cash account
          offsetAccount = await _getAccountByType(
            AccountType.operatingExpenses,
            'General Expenses',
            uid,
          );
          break;
        case TransactionType.loan:
          // Handle loan transactions
          if (transaction.amount > 0) {
            // Loan received - debit cash, credit loan payable
            offsetAccount = await _getAccountByType(
              AccountType.longTermLiabilities,
              'Loans Payable',
              uid,
            );
          } else {
            // Loan payment - debit loan payable, credit cash
            offsetAccount = await _getAccountByType(
              AccountType.longTermLiabilities,
              'Loans Payable',
              uid,
            );
          }
          break;
        default:
          // For other transaction types, use a general account
          offsetAccount = await _getAccountByType(
            AccountType.operatingExpenses,
            'Miscellaneous Expenses',
            uid,
          );
      }

      if (offsetAccount == null) return null;

      // Create journal entry lines based on transaction amount
      final lines = <JournalEntryLineModel>[];

      if (transaction.amount > 0) {
        // Positive amount - money coming in
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: account.id,
            accountName: account.accountName,
            accountNumber: account.accountNumber,
            description: description,
            debitAmount: transaction.amount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: offsetAccount.id,
            accountName: offsetAccount.accountName,
            accountNumber: offsetAccount.accountNumber,
            description: description,
            debitAmount: 0.0,
            creditAmount: transaction.amount,
            createdAt: DateTime.now(),
          ),
        ]);
      } else {
        // Negative amount - money going out
        final positiveAmount = transaction.amount.abs();
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: offsetAccount.id,
            accountName: offsetAccount.accountName,
            accountNumber: offsetAccount.accountNumber,
            description: description,
            debitAmount: positiveAmount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: account.id,
            accountName: account.accountName,
            accountNumber: account.accountNumber,
            description: description,
            debitAmount: 0.0,
            creditAmount: positiveAmount,
            createdAt: DateTime.now(),
          ),
        ]);
      }

      final totalAmount = transaction.amount.abs();
      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: transaction.transactionDate,
        description: description,
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: totalAmount,
        totalCredits: totalAmount,
        referenceNumber: transaction.referenceId,
        sourceTransactionId: transaction.id,
        sourceTransactionType: 'account_transaction',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      dev.log('Error generating account transaction journal entry: $e');
      return null;
    }
  }

  // Helper methods
  Future<ChartOfAccountsModel?> _getAccountByName(
      String accountName, String uid) async {
    try {
      final accounts = await _chartOfAccountsService.getAccounts();
      return accounts.firstWhere(
        (account) =>
            account.accountName.toLowerCase() == accountName.toLowerCase(),
        orElse: () => accounts.first, // Fallback to first account
      );
    } catch (e) {
      return null;
    }
  }

  Future<ChartOfAccountsModel?> _getAccountByType(
    AccountType accountType,
    String preferredName,
    String uid,
  ) async {
    try {
      final accounts =
          await _chartOfAccountsService.getAccountsByType(accountType);
      // First try to find by preferred name and type
      try {
        return accounts.firstWhere(
          (account) => account.accountName
              .toLowerCase()
              .contains(preferredName.toLowerCase()),
        );
      } catch (e) {
        // If not found, get any account of the specified type
        try {
          return accounts.first;
        } catch (e) {
          return null;
        }
      }
    } catch (e) {
      return null;
    }
  }

  // Loan-specific helper methods
  Future<JournalEntryModel?> _generateLoanDisbursementEntry({
    required LoanModel loan,
    required String uid,
    required String createdBy,
  }) async {
    try {
      dev.log(
          '🔍 AutomaticJournalEntryService: Generating loan disbursement entry for loan: ${loan.id}');
      // Get the asset account (bank/cash account) - use actual selected account ID
      ChartOfAccountsModel? assetAccount;

      // For manual loan requests, fromAccountId is empty, so use toAccountId (borrower's receiving account)
      // For approved loans, fromAccountId is set (lender's account)
      if (loan.fromAccountId.isNotEmpty) {
        dev.log(
            '🔍 Using fromAccountId for asset account lookup: ${loan.fromAccountId}');
        assetAccount =
            await _chartOfAccountsService.getAccountById(loan.fromAccountId);
      } else if (loan.toAccountId.isNotEmpty) {
        dev.log(
            '🔍 Using toAccountId for asset account lookup (manual loan request): ${loan.toAccountId}');
        assetAccount =
            await _chartOfAccountsService.getAccountById(loan.toAccountId);
      }

      // If Chart of Accounts account not found, try to find by name as fallback
      if (assetAccount == null && loan.fromAccountName.isNotEmpty) {
        dev.log(
            '🔍 Fallback: Using fromAccountName for asset account lookup: ${loan.fromAccountName}');
        assetAccount = await _getAccountByName(loan.fromAccountName, uid);
      }

      // If still not found and we have toAccountName, try that as fallback
      if (assetAccount == null && loan.toAccountName.isNotEmpty) {
        dev.log(
            '🔍 Fallback: Using toAccountName for asset account lookup: ${loan.toAccountName}');
        assetAccount = await _getAccountByName(loan.toAccountName, uid);
      }

      if (assetAccount == null) {
        dev.log('❌ Asset account not found for loan: ${loan.id}');
        dev.log('   - fromAccountId: ${loan.fromAccountId}');
        dev.log('   - toAccountId: ${loan.toAccountId}');
        dev.log('   - fromAccountName: ${loan.fromAccountName}');
        dev.log('   - toAccountName: ${loan.toAccountName}');
        return null;
      }

      dev.log(
          '✅ Found asset account: ${assetAccount.displayName} (${assetAccount.id})');

      // Determine if this is a loan we're giving or receiving based on requestedBy
      final isLoanGiven =
          loan.requestedBy != uid; // We're giving loan to someone else
      final isLoanReceived =
          loan.requestedBy == uid; // We're receiving loan from someone else

      dev.log(
          '🔍 Loan direction analysis: isLoanGiven=$isLoanGiven, isLoanReceived=$isLoanReceived');
      dev.log('🔍 Loan requestedBy: ${loan.requestedBy}, current uid: $uid');

      // Get loan accounts from user settings
      ChartOfAccountsModel? loanReceivableAccount;
      ChartOfAccountsModel? loanPayableAccount;

      try {
        final storage = GetStorage();

        // Load loan receivable account (for loans we give)
        final receivableData = storage.read('loan_receivable_account');
        if (receivableData != null) {
          loanReceivableAccount = ChartOfAccountsModel.fromJson(
              Map<String, dynamic>.from(receivableData));
          dev.log(
              '✅ Using configured loan receivable account: ${loanReceivableAccount.displayName}');
        }

        // Load loan payable account (for loans we receive)
        final payableData = storage.read('loan_payable_account');
        if (payableData != null) {
          loanPayableAccount = ChartOfAccountsModel.fromJson(
              Map<String, dynamic>.from(payableData));
          dev.log(
              '✅ Using configured loan payable account: ${loanPayableAccount.displayName}');
        }
      } catch (e) {
        dev.log('⚠️ Error loading loan accounts from settings: $e');
      }

      // If not found in settings, fall back to finding by type
      if (loanReceivableAccount == null) {
        loanReceivableAccount = await _getAccountByType(
          AccountType.currentAssets,
          'Loans Receivable',
          uid,
        );
        dev.log(
            '⚠️ Using fallback loan receivable account: ${loanReceivableAccount?.displayName ?? "Not found"}');
      }

      if (loanPayableAccount == null) {
        loanPayableAccount = await _getAccountByType(
          AccountType.longTermLiabilities,
          'Loans Payable',
          uid,
        );
        dev.log(
            '⚠️ Using fallback loan payable account: ${loanPayableAccount?.displayName ?? "Not found"}');
      }

      // Create journal entry based on loan direction
      List<JournalEntryLineModel> lines;
      String description;
      String sourceTransactionType;

      if (isLoanGiven) {
        // We're giving a loan - Debit Loan Receivable, Credit Asset Account
        if (loanReceivableAccount == null) {
          dev.log(
              '❌ No loan receivable account configured for loan given: ${loan.id}');
          return null;
        }

        lines = [
          // Debit loan receivable account (asset increases)
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: loanReceivableAccount.id,
            accountName: loanReceivableAccount.accountName,
            accountNumber: loanReceivableAccount.accountNumber,
            description: 'Loan given to ${loan.requestedByName}',
            debitAmount: loan.amount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          // Credit asset account (asset decreases)
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: assetAccount.id,
            accountName: assetAccount.accountName,
            accountNumber: assetAccount.accountNumber,
            description: 'Payment for loan to ${loan.requestedByName}',
            debitAmount: 0.0,
            creditAmount: loan.amount,
            createdAt: DateTime.now(),
          ),
        ];
        description = 'Loan disbursement to ${loan.requestedByName}';
        sourceTransactionType = 'loan_disbursement';
      } else {
        // We're receiving a loan - Debit Asset Account, Credit Loan Payable
        if (loanPayableAccount == null) {
          dev.log(
              '❌ No loan payable account configured for loan received: ${loan.id}');
          return null;
        }

        lines = [
          // Debit asset account (asset increases)
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: assetAccount.id,
            accountName: assetAccount.accountName,
            accountNumber: assetAccount.accountNumber,
            description: 'Loan received from ${loan.requestedToName}',
            debitAmount: loan.amount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          // Credit loan payable account (liability increases)
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: loanPayableAccount.id,
            accountName: loanPayableAccount.accountName,
            accountNumber: loanPayableAccount.accountNumber,
            description: 'Loan payable to ${loan.requestedToName}',
            debitAmount: 0.0,
            creditAmount: loan.amount,
            createdAt: DateTime.now(),
          ),
        ];
        description = 'Loan received from ${loan.requestedToName}';
        sourceTransactionType = 'loan_receipt';
      }

      // Determine the correct entry date:
      // 1. If this loan is linked to a voucher payment, use the original payment transaction date
      // 2. Otherwise, use the loan approval date
      DateTime entryDate = loan.approvalDate ?? DateTime.now();

      // Try to get original payment transaction date if this is a voucher-based loan
      if (loan.voucherPaymentId != null && loan.voucherPaymentId!.isNotEmpty) {
        try {
          // Get the voucher from the original company (loan.requestedBy) not the approving company (uid)
          // This is crucial for cross-company loans where Company 2 approves Company 1's loan request
          final originalCompanyUid =
              loan.requestedBy.isNotEmpty ? loan.requestedBy : uid;
          dev.log(
              '🔍 Searching for original voucher payment in company: $originalCompanyUid');

          final voucherService = VoucherCrudFirebaseService();
          final vouchers = await voucherService.getVouchersForCompany(
              uid: originalCompanyUid);

          for (final voucherData in vouchers) {
            final voucher = VoucherModel.fromJson(voucherData);
            final payments = voucher.paymentTransactions
                .map((p) => PaymentTransactionModel.fromMap(p))
                .toList();

            // Find the payment with matching ID
            PaymentTransactionModel? matchingPayment;
            for (final payment in payments) {
              if (payment.id == loan.voucherPaymentId) {
                matchingPayment = payment;
                break;
              }
            }

            if (matchingPayment != null) {
              entryDate = matchingPayment.transactionDate;
              dev.log(
                  '✅ Using original payment transaction date for loan disbursement: ${entryDate.toIso8601String()}');
              dev.log(
                  '📅 Original payment date: ${matchingPayment.transactionDate}, Approval date: ${loan.approvalDate}');
              break;
            }
          }
        } catch (e) {
          dev.log(
              '⚠️ Could not retrieve original payment transaction date, using approval date: $e');
          // Fall back to approval date
        }
      }

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: entryDate,
        description: description,
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: loan.amount,
        totalCredits: loan.amount,
        referenceNumber: loan.id,
        sourceTransactionId: loan.id,
        sourceTransactionType: sourceTransactionType,
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      dev.log('Error generating loan disbursement entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateLoanRepaymentEntry({
    required LoanModel loan,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get borrower account (money coming in) - use actual selected account ID
      ChartOfAccountsModel? borrowerAccount;
      if (loan.toAccountId.isNotEmpty) {
        borrowerAccount =
            await _chartOfAccountsService.getAccountById(loan.toAccountId);
      }

      // If Chart of Accounts account not found, try to find by name as fallback
      if (borrowerAccount == null && loan.toAccountName.isNotEmpty) {
        borrowerAccount = await _getAccountByName(loan.toAccountName, uid);
      }

      if (borrowerAccount == null) {
        dev.log('❌ Borrower account not found for loan repayment: ${loan.id}');
        return null;
      }

      // Get lender account (money going to) - use actual selected account ID
      ChartOfAccountsModel? lenderAccount;
      if (loan.fromAccountId.isNotEmpty) {
        lenderAccount =
            await _chartOfAccountsService.getAccountById(loan.fromAccountId);
      }

      // If Chart of Accounts account not found, try to find by name as fallback
      if (lenderAccount == null && loan.fromAccountName.isNotEmpty) {
        lenderAccount = await _getAccountByName(loan.fromAccountName, uid);
      }

      if (lenderAccount == null) {
        dev.log('❌ Lender account not found for loan repayment: ${loan.id}');
        return null;
      }

      // Get loan payable account from user settings
      ChartOfAccountsModel? loanPayableAccount;

      // Try to get from GetStorage
      try {
        final storage = GetStorage();
        final payableData = storage.read('loan_payable_account');
        if (payableData != null) {
          loanPayableAccount = ChartOfAccountsModel.fromJson(
              Map<String, dynamic>.from(payableData));
          dev.log(
              '✅ Using configured loan payable account: ${loanPayableAccount.displayName}');
        }
      } catch (e) {
        dev.log('⚠️ Error loading loan payable account from settings: $e');
      }

      // If not found in settings, fall back to finding by type
      if (loanPayableAccount == null) {
        loanPayableAccount = await _getAccountByType(
          AccountType.longTermLiabilities,
          'Loans Payable',
          uid,
        );
        dev.log(
            '⚠️ Using fallback loan payable account: ${loanPayableAccount?.displayName ?? "Not found"}');
      }

      // If no loan payable account is configured, return null
      if (loanPayableAccount == null) {
        dev.log(
            '❌ No loan payable account configured for loan repayment: ${loan.id}');
        return null;
      }

      final lines = <JournalEntryLineModel>[
        // Debit lender account (receiving repayment)
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: lenderAccount.id,
          accountName: lenderAccount.accountName,
          accountNumber: lenderAccount.accountNumber,
          description: 'Loan repayment from ${loan.requestedByName}',
          debitAmount: loan.amount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit loan payable account (reducing liability)
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: loanPayableAccount.id,
          accountName: loanPayableAccount.accountName,
          accountNumber: loanPayableAccount.accountNumber,
          description: 'Loan repayment to ${loan.requestedToName}',
          debitAmount: 0.0,
          creditAmount: loan.amount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: loan.repaymentDate ?? DateTime.now(),
        description: 'Loan repayment from ${loan.requestedByName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: loan.amount,
        totalCredits: loan.amount,
        referenceNumber: loan.id,
        sourceTransactionId: loan.id,
        sourceTransactionType: 'loan_repayment',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      dev.log('Error generating loan repayment entry: $e');
      return null;
    }
  }

  /// Generate unique ID for journal entry
  String _generateJournalEntryId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(9999);
    return 'JE_${timestamp}_$random';
  }

  /// Generate unique ID for journal entry line
  String _generateLineId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(9999);
    return 'JEL_${timestamp}_$random';
  }

  /// Get the correct tax authority account based on the authority name
  Future<ChartOfAccountsModel?> _getTaxAuthorityAccount(
      String authority) async {
    try {
      // First, try to get the tax authority account from voucher payment settings
      try {
        final settingsService = Get.find<VoucherPaymentSettingsService>();
        ChartOfAccountsModel? taxAuthorityAccount;

        switch (authority) {
          case 'SRB (Sindh Revenue Board)':
            taxAuthorityAccount = settingsService.loadSrbTaxAccount();
            break;
          case 'PRA (Punjab Revenue Authority)':
            taxAuthorityAccount = settingsService.loadPraTaxAccount();
            break;
          case 'BRA (Balochistan Revenue Authority)':
            taxAuthorityAccount = settingsService.loadBraTaxAccount();
            break;
          case 'KRA (Khyber Revenue Authority)':
            taxAuthorityAccount = settingsService.loadKraTaxAccount();
            break;
          default:
            dev.log('Unknown tax authority: $authority');
            return null;
        }

        if (taxAuthorityAccount != null) {
          dev.log(
              '✅ Found tax authority account from settings for $authority: ${taxAuthorityAccount.accountName}');

          // Verify the account still exists and belongs to the current user
          final verifiedAccount = await _chartOfAccountsService
              .getAccountById(taxAuthorityAccount.id);
          if (verifiedAccount != null) {
            dev.log(
                '✅ Verified tax authority account: ${verifiedAccount.accountName}');
            return verifiedAccount;
          } else {
            dev.log(
                '❌ Tax authority account verification failed or no longer exists');
          }
        } else {
          dev.log(
              '❌ No tax authority account found in settings for $authority');
        }
      } catch (e) {
        dev.log('❌ VoucherPaymentSettingsService not available: $e');
      }

      // Fallback: Search for the tax authority account by name pattern
      dev.log(
          '🔍 Attempting fallback search for tax authority account: $authority');
      String searchTerm;
      switch (authority) {
        case 'SRB (Sindh Revenue Board)':
          searchTerm = 'SRB';
          break;
        case 'PRA (Punjab Revenue Authority)':
          searchTerm = 'PRA';
          break;
        case 'BRA (Balochistan Revenue Authority)':
          searchTerm = 'BRA';
          break;
        case 'KRA (Khyber Revenue Authority)':
          searchTerm = 'KRA';
          break;
        default:
          dev.log('Unknown tax authority: $authority');
          return null;
      }

      // Search for the tax authority account by name
      final accounts = await _chartOfAccountsService.getAllAccounts();
      final taxAuthorityAccount = accounts
          .where(
            (account) =>
                account.accountName
                    .toUpperCase()
                    .contains(searchTerm.toUpperCase()) &&
                (account.accountType == AccountType.currentLiabilities ||
                    account.accountType == AccountType.accountsPayable ||
                    account.accountType == AccountType.longTermLiabilities),
          )
          .firstOrNull;

      if (taxAuthorityAccount != null) {
        dev.log(
            '✅ Found tax authority account via fallback search for $authority: ${taxAuthorityAccount.accountName}');
      } else {
        dev.log(
            '❌ No tax authority account found via fallback search for $authority (searching for: $searchTerm)');
      }

      return taxAuthorityAccount;
    } catch (e) {
      dev.log('❌ Error finding tax authority account for $authority: $e');
      return null;
    }
  }

  /// Determine debit and credit amounts based on account type and transaction amount
  /// This ensures proper accounting principles are followed:
  /// - Assets & Expenses: Debit increases balance, Credit decreases balance
  /// - Liabilities, Equity & Revenue: Credit increases balance, Debit decreases balance
  Map<String, double> _getDebitCreditAmounts({
    required ChartOfAccountsModel account,
    required double amount,
    required bool isIncrease,
  }) {
    // Determine if this account increases with debits
    final increasesWithDebit =
        AccountTypeHelperService.isDebitAccount(account.accountType);

    // For an increase in value:
    // - Debit accounts (Assets, Expenses) should be debited
    // - Credit accounts (Liabilities, Equity, Revenue) should be credited
    //
    // For a decrease in value:
    // - Debit accounts (Assets, Expenses) should be credited
    // - Credit accounts (Liabilities, Equity, Revenue) should be debited

    if ((increasesWithDebit && isIncrease) ||
        (!increasesWithDebit && !isIncrease)) {
      // Debit the account
      return {'debit': amount, 'credit': 0.0};
    } else {
      // Credit the account
      return {'debit': 0.0, 'credit': amount};
    }
  }

  /// Get truck freight account from voucher payment settings
  /// Chooses between regular truck fare account and truck fare by cheque account based on payment method
  Future<ChartOfAccountsModel?> _getTruckFreightAccountFromSettings(
      String uid, PaymentMethod paymentMethod) async {
    try {
      dev.log(
          '🔍 Getting truck freight account from voucher payment settings for payment method: ${paymentMethod.name}');

      // Try to get the settings service
      try {
        final settingsService = Get.find<VoucherPaymentSettingsService>();

        // Choose the appropriate account based on payment method
        ChartOfAccountsModel? truckFreightAccount;
        if (paymentMethod == PaymentMethod.check) {
          // For check payments, use the truck fare by cheque account
          truckFreightAccount = settingsService.loadTruckFareByChequeAccount();
          dev.log('🔍 Using truck fare by cheque account for check payment');
        } else {
          // For account transfers and other methods, use the regular truck fare account
          truckFreightAccount = settingsService.loadTruckFareAccount();
          dev.log(
              '🔍 Using regular truck fare account for ${paymentMethod.name} payment');
        }

        if (truckFreightAccount != null) {
          dev.log(
              '✅ Found truck freight account from settings: ${truckFreightAccount.accountName}');

          // Verify the account still exists and belongs to the current user
          final verifiedAccount = await _chartOfAccountsService
              .getAccountById(truckFreightAccount.id);
          if (verifiedAccount != null && verifiedAccount.uid == uid) {
            dev.log(
                '✅ Verified truck freight account: ${verifiedAccount.accountName}');
            return verifiedAccount;
          } else {
            dev.log(
                '❌ Truck freight account verification failed or belongs to different user');
          }
        } else {
          dev.log(
              '❌ No truck freight account found in settings for payment method: ${paymentMethod.name}');
        }
      } catch (e) {
        dev.log('❌ VoucherPaymentSettingsService not available: $e');
      }

      // Fallback: Try to find a truck freight account by name/type
      dev.log('🔍 Attempting fallback search for truck freight account');
      final fallbackAccount = await _getAccountByType(
        AccountType.currentLiabilities,
        'Truck Freight',
        uid,
      );

      if (fallbackAccount != null) {
        dev.log(
            '✅ Found fallback truck freight account: ${fallbackAccount.accountName}');
        return fallbackAccount;
      }

      dev.log('❌ No truck freight account found via fallback search');
      return null;
    } catch (e) {
      dev.log('❌ Error getting truck freight account from settings: $e');
      return null;
    }
  }

  /// Get loan payable account from existing loan system configuration
  Future<ChartOfAccountsModel?> _getLoanPayableAccountFromSettings(
      String uid) async {
    try {
      dev.log(
          '🔍 Getting loan payable account from existing loan system configuration');

      // Use the same pattern as the existing loan system
      try {
        final storage = GetStorage();

        // Load loan payable account (for loans we receive) - same key as existing loan system
        final payableData = storage.read('loan_payable_account');
        if (payableData != null) {
          final loanPayableAccount = ChartOfAccountsModel.fromJson(
              Map<String, dynamic>.from(payableData));
          dev.log(
              '✅ Found loan payable account from loan system: ${loanPayableAccount.accountName}');

          // Verify the account still exists and belongs to the current user
          final verifiedAccount = await _chartOfAccountsService
              .getAccountById(loanPayableAccount.id);
          if (verifiedAccount != null && verifiedAccount.uid == uid) {
            dev.log(
                '✅ Verified loan payable account: ${verifiedAccount.accountName}');
            return verifiedAccount;
          } else {
            dev.log(
                '❌ Loan payable account verification failed or belongs to different user');
          }
        } else {
          dev.log(
              '❌ No loan payable account found in loan system configuration');
        }
      } catch (e) {
        dev.log('❌ Error loading loan payable account from storage: $e');
      }

      // Fallback: Try to find a loan payable account by name/type
      dev.log('🔍 Attempting fallback search for loan payable account');
      final fallbackAccount = await _getAccountByType(
        AccountType.longTermLiabilities,
        'Loans Payable',
        uid,
      );

      if (fallbackAccount != null) {
        dev.log(
            '✅ Found fallback loan payable account: ${fallbackAccount.accountName}');
        return fallbackAccount;
      }

      dev.log('❌ No loan payable account found via fallback search');
      return null;
    } catch (e) {
      dev.log('❌ Error getting loan payable account from loan system: $e');
      return null;
    }
  }

  /// Parse voucher departure date from DD/MM/YYYY format to DateTime
  DateTime _parseVoucherDate(String departureDateString) {
    try {
      dev.log('🗓️ Parsing voucher departure date: $departureDateString');

      // Handle DD/MM/YYYY format used by voucher system
      final parts = departureDateString.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        final parsedDate = DateTime(year, month, day);

        dev.log('✅ Successfully parsed voucher date: $parsedDate');
        return parsedDate;
      }

      // Fallback: try ISO format parsing
      final isoDate = DateTime.tryParse(departureDateString);
      if (isoDate != null) {
        dev.log(
            '✅ Successfully parsed voucher date using ISO format: $isoDate');
        return isoDate;
      }

      dev.log(
          '⚠️ Failed to parse voucher date, using current date as fallback');
    } catch (e) {
      dev.log('❌ Error parsing voucher departure date: $e');
    }

    // Fallback to current date if parsing fails
    final fallbackDate = DateTime.now();
    dev.log('🔄 Using fallback date: $fallbackDate');
    return fallbackDate;
  }
}
