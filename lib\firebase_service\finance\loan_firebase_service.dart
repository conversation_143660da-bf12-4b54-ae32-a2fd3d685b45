import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:either_dart/either.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/firebase_service/finance/account_transaction_firebase_service.dart';
import 'package:logestics/firebase_service/finance/deposit_firebase_service.dart';
import 'package:logestics/firebase_service/finance/expense_firebase_service.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/models/finance/expense_model.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:uuid/uuid.dart';

class LoanFirebaseService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final AccountTransactionFirebaseService _transactionApi;

  LoanFirebaseService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
    AccountTransactionFirebaseService? transactionApi,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance,
        _transactionApi = transactionApi ?? AccountTransactionFirebaseService();

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => _auth.currentUser?.uid ?? 'anonymous';

  // Collection paths using app constants
  String get _loansCollection => AppCollection.loansCollection;
  String get _accountsCollection => AppCollection.accountsCollection;

  // Request a loan with proper user ID scoping
  Future<Either<FailureObj, SuccessObj>> requestLoan(LoanModel loan) async {
    try {
      // Validate if the user is logged in
      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Validate loan data
      if (loan.amount <= 0) {
        return Left(FailureObj(
          code: 'validation-error',
          message: 'Loan amount must be greater than zero',
        ));
      }

      if (loan.toAccountId.isEmpty) {
        return Left(FailureObj(
          code: 'validation-error',
          message: 'Recipient account is required',
        ));
      }

      // Verify the borrower account belongs to current user
      // Try Chart of Accounts first, then fall back to legacy accounts
      DocumentSnapshot? borrowerAccountDoc;

      // First try Chart of Accounts
      borrowerAccountDoc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(loan.toAccountId)
          .get();

      if (borrowerAccountDoc.exists) {
        log('Using Chart of Accounts for borrower account validation: ${loan.toAccountId}');
      } else {
        // Fall back to legacy accounts
        borrowerAccountDoc = await _firestore
            .collection(_accountsCollection)
            .doc(loan.toAccountId)
            .get();
        log('Using legacy accounts for borrower account validation: ${loan.toAccountId}');
      }

      if (!borrowerAccountDoc.exists) {
        return Left(FailureObj(
          code: 'account-not-found',
          message:
              'Borrower account not found in either Chart of Accounts or legacy accounts',
        ));
      }

      final accountData = borrowerAccountDoc.data() as Map<String, dynamic>;
      if (accountData['uid'] != _uid) {
        return Left(FailureObj(
          code: 'permission-denied',
          message: 'You do not have permission to use this account',
        ));
      }

      // Generate new ID if not provided
      final String loanId = loan.id.isEmpty ? const Uuid().v4() : loan.id;

      // Create loan with user ID and proper defaults
      final newLoan = loan.copyWith(
        id: loanId,
        uid: _uid, // Set current user's UID
        requestedBy: loan.requestedBy.isEmpty ? _uid : loan.requestedBy,
        status: 'pending',
        requestDate: DateTime.now(),
      );

      // Save to Firestore
      await _firestore
          .collection(_loansCollection)
          .doc(loanId)
          .set(newLoan.toJson());

      log('Created loan request: $loanId for user: $_uid');
      return Right(SuccessObj(
        message: 'Loan request submitted successfully',
      ));
    } catch (e) {
      log('Error requesting loan: $e');
      return Left(FailureObj(
        code: 'loan-request-error',
        message: 'Failed to request loan: $e',
      ));
    }
  }

  // Request a cross-company loan (bypasses borrower account validation)
  // Used for voucher payments where the borrower account belongs to another company
  Future<Either<FailureObj, SuccessObj>> requestCrossCompanyLoan(
      LoanModel loan) async {
    try {
      // Validate if the user is logged in
      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Validate loan data
      if (loan.amount <= 0) {
        return Left(FailureObj(
          code: 'validation-error',
          message: 'Loan amount must be greater than zero',
        ));
      }

      if (loan.toAccountId.isEmpty) {
        return Left(FailureObj(
          code: 'validation-error',
          message: 'Recipient account is required',
        ));
      }

      // For cross-company loans, skip the borrower account ownership validation
      // The account belongs to another company, which is the intended behavior
      log('Creating cross-company loan - skipping borrower account ownership validation');

      // Generate new ID if not provided
      final String loanId = loan.id.isEmpty ? const Uuid().v4() : loan.id;

      // Create loan with user ID and proper defaults
      final newLoan = loan.copyWith(
        id: loanId,
        uid: _uid, // Set current user's UID (loan requester)
        requestedBy: loan.requestedBy.isEmpty ? _uid : loan.requestedBy,
        status: 'pending',
        requestDate: DateTime.now(),
      );

      // Save to Firestore
      await _firestore
          .collection(_loansCollection)
          .doc(loanId)
          .set(newLoan.toJson());

      log('Created cross-company loan request: $loanId for user: $_uid');
      return Right(SuccessObj(
        message: 'Cross-company loan request submitted successfully',
      ));
    } catch (e) {
      log('Error requesting cross-company loan: $e');
      return Left(FailureObj(
        code: 'cross-company-loan-request-error',
        message: 'Failed to request cross-company loan: $e',
      ));
    }
  }

  /// Create active cross-company loan directly (for "Other" payment types)
  /// This bypasses the pending approval workflow and creates an active loan immediately
  Future<Either<FailureObj, SuccessObj>> createActiveCrossCompanyLoan(
      LoanModel loan) async {
    final batch = _firestore.batch();

    try {
      log('🚀🚀🚀 ACTIVE LOAN SERVICE CALLED: createActiveCrossCompanyLoan method started');
      log('🚀🚀🚀 ACTIVE LOAN SERVICE: Loan amount = ${loan.amount}');
      log('🚀🚀🚀 ACTIVE LOAN SERVICE: From account = ${loan.fromAccountId}');
      log('🚀🚀🚀 ACTIVE LOAN SERVICE: This will create ACTIVE loan with status = approved');

      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Validate loan data
      if (loan.amount <= 0) {
        return Left(FailureObj(
          code: 'validation-error',
          message: 'Loan amount must be greater than zero',
        ));
      }

      if (loan.fromAccountId.isEmpty) {
        return Left(FailureObj(
          code: 'validation-error',
          message: 'Lender account is required for active loan creation',
        ));
      }

      log('🚀🚀🚀 ACTIVE LOAN SERVICE: Creating active cross-company loan - bypassing approval workflow');

      // Generate new ID if not provided
      final String loanId = loan.id.isEmpty ? const Uuid().v4() : loan.id;

      // Create loan with active status and approval date set
      final activeLoan = loan.copyWith(
        id: loanId,
        uid: _uid, // Set current user's UID (loan requester)
        requestedBy: loan.requestedBy.isEmpty ? _uid : loan.requestedBy,
        status: 'approved', // Set as approved/active immediately
        requestDate: DateTime.now(),
        approvalDate: DateTime.now(), // Set approval date to now
      );

      // Save the active loan to Firestore
      batch.set(
        _firestore.collection(_loansCollection).doc(loanId),
        activeLoan.toJson(),
      );

      // Update account balance for the lender (Company 2's account)
      // Allow overdrafts as per business requirements
      if (loan.fromAccountId.isNotEmpty) {
        // Check if this is a Chart of Accounts account
        final isChartOfAccounts =
            await _isChartOfAccountsAccount(loan.fromAccountId);

        if (isChartOfAccounts) {
          log('Chart of Accounts loan - balance updates will be handled by accounting integration');
        } else {
          // Handle legacy account balance update
          final lenderAccountDoc = await _firestore
              .collection(_accountsCollection)
              .doc(loan.fromAccountId)
              .get();

          if (lenderAccountDoc.exists) {
            final lenderAccountData =
                lenderAccountDoc.data() as Map<String, dynamic>;
            final currentBalance =
                (lenderAccountData['availableBalance'] ?? 0.0).toDouble();
            final updatedBalance = currentBalance -
                loan.amount; // Decrease balance (allow negative)

            batch.update(
              _firestore
                  .collection(_accountsCollection)
                  .doc(loan.fromAccountId),
              {'availableBalance': updatedBalance},
            );

            log('Updated lender account balance: ${loan.fromAccountId} from $currentBalance to $updatedBalance');
          }
        }
      }

      // Commit the batch transaction
      await batch.commit();

      log('🚀🚀🚀 ACTIVE LOAN SERVICE: Successfully committed batch transaction');
      log('🚀🚀🚀 ACTIVE LOAN SERVICE: Created active cross-company loan: $loanId');
      log('🚀🚀🚀 ACTIVE LOAN SERVICE: Loan amount: ${loan.amount}');
      log('🚀🚀🚀 ACTIVE LOAN SERVICE: Loan status: ${activeLoan.status} (should be "approved")');
      log('🚀🚀🚀 ACTIVE LOAN SERVICE: Approval date: ${activeLoan.approvalDate}');
      log('🚀🚀🚀 ACTIVE LOAN SERVICE: This loan should appear in Active Loans immediately!');

      return Right(SuccessObj(
        message: 'Active cross-company loan created successfully',
      ));
    } catch (e) {
      log('Error creating active cross-company loan: $e');
      return Left(FailureObj(
        code: 'creation-error',
        message: 'Failed to create active cross-company loan: $e',
      ));
    }
  }

  // Get all loan requests made to current user (with user ID filtering)
  Future<Either<FailureObj, List<LoanModel>>> getIncomingLoanRequests() async {
    try {
      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      final snapshot = await _firestore
          .collection(_loansCollection)
          .where('requestedTo', isEqualTo: _uid) // Filter by current user
          .where('status', isEqualTo: 'pending')
          .orderBy('requestDate', descending: true)
          .get();

      final loans =
          snapshot.docs.map((doc) => LoanModel.fromJson(doc.data())).toList();

      log('Retrieved ${loans.length} incoming loan requests for user: $_uid');
      return Right(loans);
    } catch (e) {
      log('Error getting incoming loan requests: $e');
      return Left(FailureObj(
        code: 'fetch-error',
        message: 'Failed to retrieve incoming loan requests: $e',
      ));
    }
  }

  // Get all loan requests made by current user (with user ID filtering)
  Future<Either<FailureObj, List<LoanModel>>> getOutgoingLoanRequests() async {
    try {
      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      final snapshot = await _firestore
          .collection(_loansCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .where('requestedBy', isEqualTo: _uid) // Double filter for security
          .orderBy('requestDate', descending: true)
          .get();

      final loans =
          snapshot.docs.map((doc) => LoanModel.fromJson(doc.data())).toList();

      log('Retrieved ${loans.length} outgoing loan requests for user: $_uid');
      return Right(loans);
    } catch (e) {
      log('Error getting outgoing loan requests: $e');
      return Left(FailureObj(
        code: 'fetch-error',
        message: 'Failed to retrieve outgoing loan requests: $e',
      ));
    }
  }

  // Approve a loan request with user permission validation
  Future<Either<FailureObj, SuccessObj>> approveLoanRequest(
      String loanId, String fromAccountId) async {
    final batch = _firestore.batch();

    try {
      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Get and validate loan document
      final loanDoc =
          await _firestore.collection(_loansCollection).doc(loanId).get();

      if (!loanDoc.exists) {
        return Left(FailureObj(
          code: 'not-found',
          message: 'Loan request not found',
        ));
      }

      final loan = LoanModel.fromJson(loanDoc.data() as Map<String, dynamic>);

      // Validate user can approve this loan
      // For regular loans: user must be the one requested to (loan.requestedTo == _uid)
      // For voucher payment loans: user can approve if they have access to the lending account
      // This allows cross-company scenarios where the same user manages multiple companies
      bool canApprove = false;

      if (loan.requestedTo == _uid) {
        // Standard case: loan was requested to this user
        canApprove = true;
        log('Loan approval: Standard case - loan requested to current user');
      } else if (loan.voucherPaymentId != null &&
          loan.voucherPaymentId!.isNotEmpty) {
        // Voucher payment loan: check if user has access to the lending account
        // This will be validated later when checking account ownership
        canApprove = true;
        log('Loan approval: Voucher payment loan - will validate account access');
      }

      if (!canApprove) {
        return Left(FailureObj(
          code: 'permission-denied',
          message: 'You do not have permission to approve this loan',
        ));
      }

      // Validate loan status
      if (loan.status != 'pending') {
        return Left(FailureObj(
          code: 'invalid-status',
          message: 'This loan request is already ${loan.status}',
        ));
      }

      // Check and validate lender account ownership
      // Try Chart of Accounts first, then fall back to legacy accounts
      DocumentSnapshot? lenderAccountDoc;
      bool isChartOfAccounts = false;

      // First try Chart of Accounts
      lenderAccountDoc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(fromAccountId)
          .get();

      if (lenderAccountDoc.exists) {
        isChartOfAccounts = true;
        log('Using Chart of Accounts for loan approval: $fromAccountId');
      } else {
        // Fall back to legacy accounts
        lenderAccountDoc = await _firestore
            .collection(_accountsCollection)
            .doc(fromAccountId)
            .get();
        log('Using legacy accounts for loan approval: $fromAccountId');
      }

      if (!lenderAccountDoc.exists) {
        return Left(FailureObj(
          code: 'account-not-found',
          message:
              'Lender account not found in either Chart of Accounts or legacy accounts',
        ));
      }

      final lenderAccountData = lenderAccountDoc.data() as Map<String, dynamic>;
      if (lenderAccountData['uid'] != _uid) {
        return Left(FailureObj(
          code: 'permission-denied',
          message: 'You do not have permission to use this account',
        ));
      }

      // Handle account balance validation based on account type
      double accountBalance = 0.0;
      String accountName = '';

      if (isChartOfAccounts) {
        // Chart of Accounts format
        accountBalance = (lenderAccountData['balance'] ?? 0.0).toDouble();
        accountName = lenderAccountData['accountName'] ?? 'Unknown Account';
        log('Chart of Accounts - Account: $accountName, Balance: $accountBalance, Loan amount: ${loan.amount}');
      } else {
        // Legacy account format
        final lenderAccount = AccountModel.fromJson(lenderAccountData);
        accountBalance = lenderAccount.availableBalance;
        accountName = lenderAccount.name;
        log('Legacy Account - Account: $accountName, Balance: $accountBalance, Loan amount: ${loan.amount}');
      }

      // Allow negative balances for cross-company check usage (consistent with other transactions)
      // Skip insufficient funds validation to permit overdrafts when needed
      if (accountBalance < loan.amount) {
        log('Account will go negative after loan approval - allowing overdraft');
      }

      // Get borrower account - use hybrid approach (Chart of Accounts first, then legacy)
      DocumentSnapshot? borrowerAccountDoc;
      bool isBorrowerChartOfAccounts = false;

      // First try Chart of Accounts
      borrowerAccountDoc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(loan.toAccountId)
          .get();

      if (borrowerAccountDoc.exists) {
        isBorrowerChartOfAccounts = true;
        log('Using Chart of Accounts for borrower account validation: ${loan.toAccountId}');
      } else {
        // Fall back to legacy accounts
        borrowerAccountDoc = await _firestore
            .collection(_accountsCollection)
            .doc(loan.toAccountId)
            .get();
        log('Using legacy accounts for borrower account validation: ${loan.toAccountId}');
      }

      if (!borrowerAccountDoc.exists) {
        return Left(FailureObj(
          code: 'account-not-found',
          message:
              'Borrower account not found in either Chart of Accounts or legacy accounts',
        ));
      }

      final borrowerAccountData =
          borrowerAccountDoc.data() as Map<String, dynamic>;

      // For Chart of Accounts, we don't need to create AccountModel, just use the data
      AccountModel? borrowerAccount;
      if (!isBorrowerChartOfAccounts) {
        borrowerAccount = AccountModel.fromJson(borrowerAccountData);
      }

      // Update the loan status
      final updatedLoan = loan.copyWith(
        status: 'approved',
        approvalDate: DateTime.now(),
        fromAccountId: fromAccountId,
        fromAccountName: accountName,
      );

      batch.set(
        _firestore.collection(_loansCollection).doc(loanId),
        updatedLoan.toJson(),
      );

      // Check if this is a cross-company check usage request
      if (loan.notes?.contains('Cross-company check usage request') ?? false) {
        // For cross-company check usage, skip balance updates here
        // Balance will be updated during expense creation to avoid double deduction
        log('Cross-company check usage detected - skipping balance updates in loan approval');

        // Commit the batch (loan status update only)
        await batch.commit();

        // Create specific financial records for cross-company check usage
        // Note: Using account data from either Chart of Accounts or legacy accounts
        if (isChartOfAccounts) {
          log('Cross-company check approval with Chart of Accounts - delegating to accounting integration');
          // Chart of Accounts integration will handle the financial records
        } else {
          final lenderAccount = AccountModel.fromJson(lenderAccountData);
          await _processCrossCompanyCheckApproval(updatedLoan, lenderAccount);
        }

        // Send approval notification for cross-company check usage
        await _sendCrossCompanyCheckApprovalNotification(updatedLoan);
      } else {
        // For regular loans, update account balances
        // Handle both Chart of Accounts and legacy accounts
        if (isChartOfAccounts) {
          log('Regular loan approval with Chart of Accounts - balance updates will be handled by accounting integration');
          // Chart of Accounts balance updates should be handled by the accounting integration service
        } else {
          final updatedLenderBalance = accountBalance - loan.amount;
          batch.update(
            _firestore.collection(_accountsCollection).doc(fromAccountId),
            {'availableBalance': updatedLenderBalance},
          );
        }

        // Update borrower account balance (only for legacy accounts)
        if (!isBorrowerChartOfAccounts && borrowerAccount != null) {
          final updatedBorrowerBalance =
              borrowerAccount.availableBalance + loan.amount;
          batch.update(
            _firestore.collection(_accountsCollection).doc(loan.toAccountId),
            {'availableBalance': updatedBorrowerBalance},
          );
        } else if (isBorrowerChartOfAccounts) {
          log('Chart of Accounts borrower account - balance updates will be handled by accounting integration');
        }

        // Commit the batch
        await batch.commit();

        // Create standard loan transaction records
        if (!isChartOfAccounts &&
            !isBorrowerChartOfAccounts &&
            borrowerAccount != null) {
          // Only create transaction records for legacy accounts (both lender and borrower)
          final lenderAccount = AccountModel.fromJson(lenderAccountData);
          await _createLoanTransactions(
              updatedLoan, lenderAccount, borrowerAccount);
        } else {
          log('Chart of Accounts loan - transaction records will be handled by accounting integration');
        }

        // Send regular loan approval notification
        await _sendLoanApprovalNotification(updatedLoan);
      }

      log('Approved loan: $loanId by user: $_uid');
      return Right(SuccessObj(
        message: 'Loan request approved successfully',
      ));
    } catch (e) {
      log('Error approving loan request: $e');
      return Left(FailureObj(
        code: 'approval-error',
        message: 'Failed to approve loan request: $e',
      ));
    }
  }

  // Reject a loan request with user permission validation
  Future<Either<FailureObj, SuccessObj>> rejectLoanRequest(
      String loanId, String reason) async {
    try {
      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Get and validate loan document
      final loanDoc =
          await _firestore.collection(_loansCollection).doc(loanId).get();

      if (!loanDoc.exists) {
        return Left(FailureObj(
          code: 'not-found',
          message: 'Loan request not found',
        ));
      }

      final loan = LoanModel.fromJson(loanDoc.data() as Map<String, dynamic>);

      // Validate user can reject this loan (requested to them)
      if (loan.requestedTo != _uid) {
        return Left(FailureObj(
          code: 'permission-denied',
          message: 'You do not have permission to reject this loan',
        ));
      }

      // Validate loan status
      if (loan.status != 'pending') {
        return Left(FailureObj(
          code: 'invalid-status',
          message: 'This loan request is already ${loan.status}',
        ));
      }

      // Update the loan status
      final updatedLoan = loan.copyWith(
        status: 'rejected',
        rejectionReason: reason,
      );

      await _firestore
          .collection(_loansCollection)
          .doc(loanId)
          .update(updatedLoan.toJson());

      // Send rejection notification
      await _sendLoanRejectionNotification(updatedLoan, reason);

      log('Rejected loan: $loanId by user: $_uid');
      return Right(SuccessObj(
        message: 'Loan request rejected',
      ));
    } catch (e) {
      log('Error rejecting loan request: $e');
      return Left(FailureObj(
        code: 'rejection-error',
        message: 'Failed to reject loan request: $e',
      ));
    }
  }

  // Repay a loan with user permission validation
  Future<Either<FailureObj, SuccessObj>> repayLoan(
      String loanId, String fromAccountId) async {
    final batch = _firestore.batch();

    try {
      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Get and validate loan document
      final loanDoc =
          await _firestore.collection(_loansCollection).doc(loanId).get();

      if (!loanDoc.exists) {
        return Left(FailureObj(
          code: 'not-found',
          message: 'Loan not found',
        ));
      }

      final loan = LoanModel.fromJson(loanDoc.data() as Map<String, dynamic>);

      // Validate user owns this loan (either they borrowed or lent)
      if (loan.uid != _uid && loan.requestedTo != _uid) {
        return Left(FailureObj(
          code: 'permission-denied',
          message: 'You do not have permission to repay this loan',
        ));
      }

      // Validate loan status
      if (loan.status != 'approved') {
        return Left(FailureObj(
          code: 'invalid-status',
          message: 'Only approved loans can be repaid',
        ));
      }

      // Check and validate borrower account ownership
      final borrowerAccountDoc = await _firestore
          .collection(_accountsCollection)
          .doc(fromAccountId)
          .get();

      if (!borrowerAccountDoc.exists) {
        return Left(FailureObj(
          code: 'account-not-found',
          message: 'Borrower account not found',
        ));
      }

      final borrowerAccountData =
          borrowerAccountDoc.data() as Map<String, dynamic>;

      // For cross-company loans, allow the borrower to repay using any account
      // Check if current user is the borrower (loan.requestedBy) or has access to the account
      final isAccountOwner = borrowerAccountData['uid'] == _uid;
      final isBorrower = loan.requestedBy == _uid;

      if (!isAccountOwner && !isBorrower) {
        return Left(FailureObj(
          code: 'permission-denied',
          message: 'You do not have permission to use this account',
        ));
      }

      final borrowerAccount = AccountModel.fromJson(borrowerAccountData);

      if (borrowerAccount.availableBalance < loan.amount) {
        return Left(FailureObj(
          code: 'insufficient-funds',
          message: 'Insufficient funds to repay the loan',
        ));
      }

      // Get lender account
      final lenderAccountDoc = await _firestore
          .collection(_accountsCollection)
          .doc(loan.fromAccountId)
          .get();

      if (!lenderAccountDoc.exists) {
        return Left(FailureObj(
          code: 'account-not-found',
          message: 'Lender account not found',
        ));
      }

      final lenderAccount = AccountModel.fromJson(
          lenderAccountDoc.data() as Map<String, dynamic>);

      // Update the loan status
      final updatedLoan = loan.copyWith(
        status: 'repaid',
        repaymentDate: DateTime.now(),
      );

      batch.set(
        _firestore.collection(_loansCollection).doc(loanId),
        updatedLoan.toJson(),
      );

      // Update borrower account balance
      final updatedBorrowerBalance =
          borrowerAccount.availableBalance - loan.amount;
      batch.update(
        _firestore.collection(_accountsCollection).doc(fromAccountId),
        {'availableBalance': updatedBorrowerBalance},
      );

      // Update lender account balance
      final updatedLenderBalance = lenderAccount.availableBalance + loan.amount;
      batch.update(
        _firestore.collection(_accountsCollection).doc(loan.fromAccountId),
        {'availableBalance': updatedLenderBalance},
      );

      // Commit the batch
      await batch.commit();

      // Create transaction records for repayment
      await _createRepaymentTransactions(
          updatedLoan, lenderAccount, borrowerAccount);

      log('Repaid loan: $loanId by user: $_uid');
      return Right(SuccessObj(
        message: 'Loan repaid successfully',
      ));
    } catch (e) {
      log('Error repaying loan: $e');
      return Left(FailureObj(
        code: 'repayment-error',
        message: 'Failed to repay loan: $e',
      ));
    }
  }

  // Get all active loans (approved but not repaid) - user scoped
  Future<Either<FailureObj, List<LoanModel>>> getActiveLoans() async {
    try {
      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Get loans where current user is involved and loan is approved
      final snapshot = await _firestore
          .collection(_loansCollection)
          .where('status', isEqualTo: 'approved')
          .where(Filter.or(
            Filter('uid', isEqualTo: _uid), // User requested the loan
            Filter('requestedTo', isEqualTo: _uid), // User was asked for loan
          ))
          .orderBy('approvalDate', descending: true)
          .get();

      final loans =
          snapshot.docs.map((doc) => LoanModel.fromJson(doc.data())).toList();

      log('Retrieved ${loans.length} active loans for user: $_uid');
      return Right(loans);
    } catch (e) {
      log('Error getting active loans: $e');
      return Left(FailureObj(
        code: 'fetch-error',
        message: 'Failed to retrieve active loans: $e',
      ));
    }
  }

  // Get loan history (all loans) - user scoped
  Future<Either<FailureObj, List<LoanModel>>> getLoanHistory() async {
    try {
      if (_uid == 'anonymous') {
        return Left(FailureObj(
          code: 'auth-error',
          message: 'User not authenticated',
        ));
      }

      // Get all loans where current user is involved
      final snapshot = await _firestore
          .collection(_loansCollection)
          .where(Filter.or(
            Filter('uid', isEqualTo: _uid), // User requested the loan
            Filter('requestedTo', isEqualTo: _uid), // User was asked for loan
          ))
          .orderBy('requestDate', descending: true)
          .get();

      final loans =
          snapshot.docs.map((doc) => LoanModel.fromJson(doc.data())).toList();

      log('Retrieved ${loans.length} loan history entries for user: $_uid');
      return Right(loans);
    } catch (e) {
      log('Error getting loan history: $e');
      return Left(FailureObj(
        code: 'fetch-error',
        message: 'Failed to retrieve loan history: $e',
      ));
    }
  }

  /// Get loan by ID
  Future<LoanModel?> getLoanById(String loanId) async {
    try {
      if (_uid == 'anonymous') {
        log('User not authenticated');
        return null;
      }

      final loanDoc =
          await _firestore.collection(_loansCollection).doc(loanId).get();

      if (!loanDoc.exists) {
        log('Loan not found: $loanId');
        return null;
      }

      final loan = LoanModel.fromJson(loanDoc.data() as Map<String, dynamic>);

      // Verify user has access to this loan
      if (loan.uid != _uid && loan.requestedTo != _uid) {
        log('User does not have permission to access loan: $loanId');
        return null;
      }

      log('Successfully fetched loan: $loanId');
      return loan;
    } catch (e) {
      log('Error fetching loan by ID: $e');
      return null;
    }
  }

  // Real-time streams for instant updates
  Stream<List<LoanModel>> listenToIncomingLoanRequests() {
    if (_uid == 'anonymous') return Stream.value([]);

    try {
      return _firestore
          .collection(_loansCollection)
          .where('requestedTo', isEqualTo: _uid)
          .where('status', isEqualTo: 'pending')
          .orderBy('requestDate', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => LoanModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to incoming loan requests: $e');
      return Stream.value([]);
    }
  }

  Stream<List<LoanModel>> listenToOutgoingLoanRequests() {
    if (_uid == 'anonymous') return Stream.value([]);

    try {
      return _firestore
          .collection(_loansCollection)
          .where('uid', isEqualTo: _uid)
          .where('requestedBy', isEqualTo: _uid)
          .orderBy('requestDate', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => LoanModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to outgoing loan requests: $e');
      return Stream.value([]);
    }
  }

  Stream<List<LoanModel>> listenToActiveLoans() {
    if (_uid == 'anonymous') return Stream.value([]);

    try {
      return _firestore
          .collection(_loansCollection)
          .where('status', isEqualTo: 'approved')
          .where(Filter.or(
            Filter('uid', isEqualTo: _uid),
            Filter('requestedTo', isEqualTo: _uid),
          ))
          .orderBy('approvalDate', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => LoanModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to active loans: $e');
      return Stream.value([]);
    }
  }

  Stream<List<LoanModel>> listenToLoanHistory() {
    if (_uid == 'anonymous') return Stream.value([]);

    try {
      return _firestore
          .collection(_loansCollection)
          .where(Filter.or(
            Filter('uid', isEqualTo: _uid),
            Filter('requestedTo', isEqualTo: _uid),
          ))
          .orderBy('requestDate', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => LoanModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to loan history: $e');
      return Stream.value([]);
    }
  }

  // Update loan with specific fields (for voucher payment integration)
  Future<Either<FailureObj, SuccessObj>> updateLoan(
      String loanId, Map<String, dynamic> updates) async {
    try {
      log('Updating loan: $loanId with updates: $updates');

      // Verify loan exists and user has permission
      final loanDoc =
          await _firestore.collection(_loansCollection).doc(loanId).get();

      if (!loanDoc.exists) {
        return Left(FailureObj(
          code: 'loan-not-found',
          message: 'Loan not found: $loanId',
        ));
      }

      final loanData = loanDoc.data() as Map<String, dynamic>;
      final loan = LoanModel.fromJson(loanData);

      // Check if user has permission to update this loan
      // User can update if they are the requester or the requested party
      if (loan.uid != _uid && loan.requestedTo != _uid) {
        return Left(FailureObj(
          code: 'permission-denied',
          message: 'You do not have permission to update this loan',
        ));
      }

      // Add timestamp to updates
      final updatesWithTimestamp = {
        ...updates,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // Update the loan document
      await _firestore
          .collection(_loansCollection)
          .doc(loanId)
          .update(updatesWithTimestamp);

      log('Successfully updated loan: $loanId');
      return Right(SuccessObj(message: 'Loan updated successfully'));
    } catch (e) {
      log('Error updating loan: $e');
      return Left(FailureObj(
        code: 'update-error',
        message: 'Failed to update loan: $e',
      ));
    }
  }

  // Private helper methods remain the same but with user ID tracking
  Future<void> _createLoanTransactions(
    LoanModel loan,
    AccountModel lenderAccount,
    AccountModel borrowerAccount,
  ) async {
    try {
      final transactionDate = loan.approvalDate ?? DateTime.now();
      final transactions = <AccountTransactionModel>[];

      // Create outgoing transaction for lender
      final lenderTransaction = AccountTransactionModel(
        id: const Uuid().v4(),
        uid: _uid, // Add user ID
        accountId: lenderAccount.id,
        accountName: lenderAccount.name,
        amount: -loan.amount, // Negative amount for outgoing funds
        transactionDate: transactionDate,
        type: TransactionType.loan, // Use loan transaction type
        description: 'Loan issued to ${borrowerAccount.name}',
        payeeId: loan.requestedBy,
        payeeName: loan.requestedByName,
        referenceId: loan.id,
        referenceName: 'Loan-${loan.id.substring(0, 8)}',
        metadata: {
          'loanId': loan.id,
          'dueDate': loan.dueDate.toIso8601String(),
          'transactionSubType': 'loan_issued',
        },
      );
      transactions.add(lenderTransaction);

      // Create incoming transaction for borrower
      final borrowerTransaction = AccountTransactionModel(
        id: const Uuid().v4(),
        uid: loan.uid, // Use borrower's UID
        accountId: borrowerAccount.id,
        accountName: borrowerAccount.name,
        amount: loan.amount, // Positive amount for incoming funds
        transactionDate: transactionDate,
        type: TransactionType.loan, // Use loan transaction type
        description: 'Loan received from ${lenderAccount.name}',
        payerId: loan.requestedTo,
        payerName: loan.requestedToName,
        referenceId: loan.id,
        referenceName: 'Loan-${loan.id.substring(0, 8)}',
        metadata: {
          'loanId': loan.id,
          'dueDate': loan.dueDate.toIso8601String(),
          'transactionSubType': 'loan_received',
        },
      );
      transactions.add(borrowerTransaction);

      // Create lender transaction (current user)
      await _transactionApi.createTransaction(lenderTransaction);
      log('Created lender transaction for loan ${loan.id}');

      // Create borrower transaction (external company)
      await _transactionApi.createTransactionForExternalCompany(
          borrowerTransaction, loan.requestedBy);
      log('Created borrower transaction for loan ${loan.id}');

      // Create deposit record for borrower (so loan appears in deposits section)
      await _createLoanDepositRecord(loan, borrowerAccount, lenderAccount);
    } catch (e) {
      log('Error creating loan transactions: $e');
      // Don't throw, just log the error as the loan was already approved
    }
  }

  /// Create deposit record for borrower when loan is approved
  Future<void> _createLoanDepositRecord(
    LoanModel loan,
    AccountModel borrowerAccount,
    AccountModel lenderAccount,
  ) async {
    try {
      log('Creating loan deposit record for borrower: ${borrowerAccount.name}');

      // Create deposit record for the borrower
      final deposit = DepositModel(
        id: '', // Will be generated by Firebase
        accountId: borrowerAccount.id,
        accountName: borrowerAccount.name,
        amount: loan.amount,
        createdAt: loan.approvalDate ?? DateTime.now(),
        categoryId: 'loan_deposit',
        categoryName: 'Loan Deposit',
        payerId: loan.requestedTo, // Lender's ID
        payerName: lenderAccount.name, // Lender's name
        referenceNumber: 'Loan-${loan.id.substring(0, 8)}',
        notes:
            'Loan received from ${lenderAccount.name} - Due: ${_formatDate(loan.dueDate)}',
      );

      // Create the deposit using the deposit service for external company
      // Skip account transaction and balance update since they're handled by loan transaction creation
      final depositService = DepositFirebaseService();
      await depositService.createDepositForExternalCompany(
        deposit,
        loan.requestedBy,
        skipAccountTransaction: true,
        skipBalanceUpdate: true,
      );

      log('Successfully created loan deposit record for ${borrowerAccount.name}');
    } catch (e) {
      log('Error creating loan deposit record: $e');
      // Don't throw, just log the error as the loan was already approved
    }
  }

  /// Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _createRepaymentTransactions(
    LoanModel loan,
    AccountModel lenderAccount,
    AccountModel borrowerAccount,
  ) async {
    try {
      final transactionDate = DateTime.now();
      final transactions = <AccountTransactionModel>[];

      // Create outgoing transaction for borrower
      final borrowerTransaction = AccountTransactionModel(
        id: const Uuid().v4(),
        uid: loan
            .requestedBy, // Use borrower's UID (the one who requested the loan)
        accountId: borrowerAccount.id,
        accountName: borrowerAccount.name,
        amount: -loan.amount, // Negative amount for outgoing funds
        transactionDate: transactionDate,
        type: TransactionType.loan, // Use loan transaction type
        description: 'Loan repayment to ${lenderAccount.name}',
        payeeId: loan.requestedTo,
        payeeName: loan.requestedToName,
        referenceId: loan.id,
        referenceName: 'Loan-${loan.id.substring(0, 8)}',
        metadata: {
          'loanId': loan.id,
          'repaymentDate': transactionDate.toIso8601String(),
          'transactionSubType': 'loan_repayment_sent',
        },
      );
      transactions.add(borrowerTransaction);

      // Create incoming transaction for lender
      final lenderTransaction = AccountTransactionModel(
        id: const Uuid().v4(),
        uid: loan
            .requestedTo, // Use lender's UID (the one who was requested to lend)
        accountId: lenderAccount.id,
        accountName: lenderAccount.name,
        amount: loan.amount, // Positive amount for incoming funds
        transactionDate: transactionDate,
        type: TransactionType.loan, // Use loan transaction type
        description: 'Loan repayment from ${borrowerAccount.name}',
        payerId: loan.requestedBy,
        payerName: loan.requestedByName,
        referenceId: loan.id,
        referenceName: 'Loan-${loan.id.substring(0, 8)}',
        metadata: {
          'loanId': loan.id,
          'repaymentDate': transactionDate.toIso8601String(),
          'transactionSubType': 'loan_repayment_received',
        },
      );
      transactions.add(lenderTransaction);

      // Create all transactions in batch using cross-company method to preserve UIDs
      await _transactionApi.createCrossCompanyTransactions(transactions);
      log('Created ${transactions.length} transactions for loan repayment ${loan.id}');
    } catch (e) {
      log('Error creating loan repayment transactions: $e');
      // Don't throw, just log the error as the loan was already repaid
    }
  }

  /// Process cross-company check approval - create financial records for check usage
  Future<void> _processCrossCompanyCheckApproval(
    LoanModel loan,
    AccountModel checkAccount,
  ) async {
    try {
      log('Processing cross-company check approval for loan: ${loan.id}');

      // Extract check number and voucher information from loan document and notes
      String checkNumber = '';
      String voucherNumber = '';

      // First try to get check number from loan document fields
      try {
        final loanDoc =
            await _firestore.collection(_loansCollection).doc(loan.id).get();
        if (loanDoc.exists) {
          final loanData = loanDoc.data() as Map<String, dynamic>;
          checkNumber = loanData['checkNumber'] as String? ?? '';
          voucherNumber = loanData['voucherId'] as String? ?? '';
        }
      } catch (e) {
        log('Could not get check number from loan document: $e');
      }

      // Fallback to extracting from loan notes if not found in document fields
      if (checkNumber.isEmpty && (loan.notes?.contains('Check #') ?? false)) {
        final parts = loan.notes!.split('Check #');
        if (parts.length > 1) {
          checkNumber = parts[1].split(' ')[0];
        }
      }

      if (voucherNumber.isEmpty && (loan.notes?.contains('voucher') ?? false)) {
        final regex =
            RegExp(r'voucher\s+([A-Za-z0-9-]+)', caseSensitive: false);
        final match = regex.firstMatch(loan.notes!);
        voucherNumber = match?.group(1) ?? '';
      }

      log('Extracted check number: $checkNumber, voucher number: $voucherNumber');

      // Create expense record for the check usage
      final expenseService = ExpenseFirebaseService();
      final expense = ExpenseModel(
        id: '', // Will be generated by Firebase
        title:
            '${loan.requestedByName} - Check #$checkNumber - Voucher #$voucherNumber',
        notes:
            'Cross-company check payment for ${loan.requestedByName} - Voucher #$voucherNumber',
        amount: loan.amount,
        accountId: checkAccount.id,
        accountName: checkAccount.name,
        payeeId: loan.requestedBy, // The company that used the check
        payeeName: loan.requestedByName, // The company that used the check
        categoryId: 'cross_company_check',
        categoryName: 'Cross-Company Check Usage',
        referenceNumber: 'V-$voucherNumber',
        createdAt:
            loan.requestDate, // Use original transaction date from loan request
        uid: _uid, // Current user (check issuer)
      );

      // Create expense record (this will also create the account transaction and update balance)
      await expenseService.createExpenseForExternalCompany(expense, _uid);

      log('Expense creation completed - account transaction and balance update handled automatically');

      log('Successfully processed cross-company check approval for loan: ${loan.id}');
      log('Created expense and account transaction for check #$checkNumber, voucher #$voucherNumber');
    } catch (e) {
      log('Error processing cross-company check approval: $e');
      // Don't throw error - the loan approval was already successful
    }
  }

  /// Send notification for cross-company check approval
  Future<void> _sendCrossCompanyCheckApprovalNotification(
      LoanModel loan) async {
    try {
      // Extract voucher number for notification
      String voucherNumber = '';
      if (loan.notes?.contains('voucher') ?? false) {
        final regex =
            RegExp(r'voucher\s+([A-Za-z0-9-]+)', caseSensitive: false);
        final match = regex.firstMatch(loan.notes!);
        voucherNumber = match?.group(1) ?? '';
      }

      // Create notification document
      final notification = {
        'id': const Uuid().v4(),
        'recipientUid':
            loan.requestedBy, // Company that requested the check usage
        'senderUid': _uid, // Company that approved the request
        'senderName': loan.requestedToName,
        'type': 'cross_company_check_approved',
        'title': 'Check Usage Request Approved',
        'message':
            'Your check usage request for Voucher #$voucherNumber has been approved by ${loan.requestedToName}. Amount: PKR ${loan.amount.toStringAsFixed(2)}',
        'data': {
          'loanId': loan.id,
          'voucherNumber': voucherNumber,
          'amount': loan.amount,
          'checkAccount': loan.fromAccountName,
        },
        'isRead': false,
        'createdAt': FieldValue.serverTimestamp(),
      };

      // Save notification to Firestore
      await _firestore.collection('notifications').add(notification);

      log('Sent cross-company check approval notification to ${loan.requestedBy}');
    } catch (e) {
      log('Error sending cross-company check approval notification: $e');
      // Don't throw error - notification failure shouldn't affect loan approval
    }
  }

  /// Send notification for regular loan approval
  Future<void> _sendLoanApprovalNotification(LoanModel loan) async {
    try {
      // Create notification document
      final notification = {
        'id': const Uuid().v4(),
        'recipientUid': loan.requestedBy, // Company that requested the loan
        'senderUid': _uid, // Company that approved the request
        'senderName': loan.requestedToName,
        'type': 'loan_approved',
        'title': 'Loan Request Approved',
        'message':
            'Your loan request has been approved by ${loan.requestedToName}. Amount: PKR ${loan.amount.toStringAsFixed(2)}',
        'data': {
          'loanId': loan.id,
          'amount': loan.amount,
          'dueDate': loan.dueDate.toIso8601String(),
          'fromAccount': loan.fromAccountName,
          'toAccount': loan.toAccountName,
        },
        'isRead': false,
        'createdAt': FieldValue.serverTimestamp(),
      };

      // Save notification to Firestore
      await _firestore.collection('notifications').add(notification);

      log('Sent loan approval notification to ${loan.requestedBy}');
    } catch (e) {
      log('Error sending loan approval notification: $e');
      // Don't throw error - notification failure shouldn't affect loan approval
    }
  }

  /// Send notification for loan rejection
  Future<void> _sendLoanRejectionNotification(
      LoanModel loan, String reason) async {
    try {
      // Determine notification type and message based on loan type
      String notificationType = 'loan_rejected';
      String title = 'Loan Request Rejected';
      String message =
          'Your loan request has been rejected by ${loan.requestedToName}. Reason: $reason';

      // Check if this is a cross-company check usage request
      if (loan.notes?.contains('Cross-company check usage request') ?? false) {
        notificationType = 'cross_company_check_rejected';
        title = 'Check Usage Request Rejected';

        String voucherNumber = '';
        if (loan.notes?.contains('voucher') ?? false) {
          final regex =
              RegExp(r'voucher\s+([A-Za-z0-9-]+)', caseSensitive: false);
          final match = regex.firstMatch(loan.notes!);
          voucherNumber = match?.group(1) ?? '';
        }

        message =
            'Your check usage request for Voucher #$voucherNumber has been rejected by ${loan.requestedToName}. Reason: $reason';
      }

      // Create notification document
      final notification = {
        'id': const Uuid().v4(),
        'recipientUid':
            loan.requestedBy, // Company that requested the loan/check usage
        'senderUid': _uid, // Company that rejected the request
        'senderName': loan.requestedToName,
        'type': notificationType,
        'title': title,
        'message': message,
        'data': {
          'loanId': loan.id,
          'amount': loan.amount,
          'rejectionReason': reason,
        },
        'isRead': false,
        'createdAt': FieldValue.serverTimestamp(),
      };

      // Save notification to Firestore
      await _firestore.collection('notifications').add(notification);

      log('Sent loan rejection notification to ${loan.requestedBy}');
    } catch (e) {
      log('Error sending loan rejection notification: $e');
      // Don't throw error - notification failure shouldn't affect loan rejection
    }
  }

  /// Helper method to check if an account ID belongs to Chart of Accounts
  Future<bool> _isChartOfAccountsAccount(String accountId) async {
    try {
      final chartAccountDoc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .doc(accountId)
          .get();
      return chartAccountDoc.exists;
    } catch (e) {
      log('Error checking if account is Chart of Accounts: $e');
      return false; // Default to legacy account if check fails
    }
  }
}
