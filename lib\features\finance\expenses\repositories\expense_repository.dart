import 'dart:developer';
import 'dart:typed_data';

import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/finance/account_firebase_service.dart';
import 'package:logestics/firebase_service/finance/expense_firebase_service.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/expense_model.dart';
import 'package:logestics/core/services/expense_accounting_hook_service.dart';

abstract class ExpenseRepository {
  // Create a new expense
  Future<Either<FailureObj, SuccessObj>> createExpense(
    ExpenseModel expense, {
    Uint8List? fileBytes,
    String? fileName,
    dynamic file,
  });

  // Get all expenses
  Future<Either<FailureObj, List<ExpenseModel>>> getExpenses();

  // Get expenses for a specific account
  Future<Either<FailureObj, List<ExpenseModel>>> getExpensesByAccount(
      String accountId);

  // Get expenses for a specific category
  Future<Either<FailureObj, List<ExpenseModel>>> getExpensesByCategory(
      String categoryId);

  // Get expenses for a specific date range
  Future<Either<FailureObj, List<ExpenseModel>>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  );

  // Update an existing expense
  Future<Either<FailureObj, SuccessObj>> updateExpense(
    ExpenseModel expense, {
    Uint8List? fileBytes,
    String? fileName,
    dynamic file,
  });

  // Delete an expense and adjust account balance
  Future<Either<FailureObj, SuccessObj>> deleteExpense(
    String expenseId,
    String accountId,
    double amount,
  );

  // Listen to real-time expense updates
  Stream<List<ExpenseModel>> listenToExpenses();
}

class ExpenseRepositoryImpl implements ExpenseRepository {
  final ExpenseFirebaseService _firebaseService;
  final AccountFirebaseService _accountFirebaseService;
  final ExpenseAccountingHookService _hookService;

  ExpenseRepositoryImpl(this._firebaseService, this._accountFirebaseService)
      : _hookService = ExpenseAccountingHookService();

  @override
  Future<Either<FailureObj, SuccessObj>> createExpense(
    ExpenseModel expense, {
    Uint8List? fileBytes,
    String? fileName,
    dynamic file,
  }) async {
    try {
      log('Creating expense in repository: ${expense.title}');

      // Check if expense uses Chart of Accounts
      if (expense.usesChartOfAccounts) {
        log('Expense uses Chart of Accounts, skipping legacy account balance update');

        // Create the expense first
        await _firebaseService.createExpense(
          expense,
          fileBytes: fileBytes,
          fileName: fileName,
          file: file,
        );

        // Trigger accounting hook for journal entry generation (Chart of Accounts)
        await _hookService.onExpenseCreated(expense);

        return Right(SuccessObj(message: 'Expense created successfully'));
      }

      // Legacy account balance update for backward compatibility
      log('Expense uses legacy account system, updating account balance');
      final accountsResult = await _accountFirebaseService.getAccounts();
      final accounts = accountsResult
          .where((account) => account.id == expense.accountId)
          .toList();

      if (accounts.isEmpty) {
        return Left(
            FailureObj(code: AppStrings.errorS, message: 'Account not found'));
      }

      final account = accounts.first;
      final newBalance = account.availableBalance - expense.amount;

      // Update account balance
      final updatedAccount = AccountModel(
        id: account.id,
        name: account.name,
        initialBalance: account.initialBalance,
        availableBalance: newBalance,
        accountNumber: account.accountNumber,
        branchCode: account.branchCode,
        branchAddress: account.branchAddress,
        createdAt: account.createdAt,
        uid: account.uid,
      );

      await _accountFirebaseService.updateAccount(updatedAccount);

      // Then create the expense
      await _firebaseService.createExpense(
        expense,
        fileBytes: fileBytes,
        fileName: fileName,
        file: file,
      );

      // For legacy expenses, we can still try to generate journal entries if possible
      await _hookService.onExpenseCreated(expense);

      return Right(SuccessObj(message: 'Expense created successfully'));
    } catch (e) {
      log('Error creating expense: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<ExpenseModel>>> getExpenses() async {
    try {
      log('Fetching all expenses');
      final expenses = await _firebaseService.getExpenses();
      return Right(expenses);
    } catch (e) {
      log('Error fetching expenses: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<ExpenseModel>>> getExpensesByAccount(
      String accountId) async {
    try {
      log('Fetching expenses for account: $accountId');
      final expenses = await _firebaseService.getExpensesByAccount(accountId);
      return Right(expenses);
    } catch (e) {
      log('Error fetching expenses by account: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<ExpenseModel>>> getExpensesByCategory(
      String categoryId) async {
    try {
      log('Fetching expenses for category: $categoryId');
      final expenses = await _firebaseService.getExpensesByCategory(categoryId);
      return Right(expenses);
    } catch (e) {
      log('Error fetching expenses by category: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<ExpenseModel>>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      log('Fetching expenses for date range');
      final expenses =
          await _firebaseService.getExpensesByDateRange(startDate, endDate);
      return Right(expenses);
    } catch (e) {
      log('Error fetching expenses by date range: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateExpense(
    ExpenseModel expense, {
    Uint8List? fileBytes,
    String? fileName,
    dynamic file,
  }) async {
    try {
      log('Updating expense: ${expense.id}');

      // Get the original expense to check if amount has changed
      final expenses = await _firebaseService.getExpenses();
      final originalExpense = expenses.firstWhere(
        (e) => e.id == expense.id,
        orElse: () => throw Exception('Expense not found'),
      );

      // If amount has changed, update account balance
      if (originalExpense.amount != expense.amount) {
        final amountDifference = expense.amount - originalExpense.amount;

        // Get the account
        final accountsResult = await _accountFirebaseService.getAccounts();
        final accounts = accountsResult
            .where((account) => account.id == expense.accountId)
            .toList();

        if (accounts.isEmpty) {
          return Left(FailureObj(
              code: AppStrings.errorS, message: 'Account not found'));
        }

        final account = accounts.first;
        final newBalance = account.availableBalance - amountDifference;

        // Update account balance
        final updatedAccount = AccountModel(
          id: account.id,
          name: account.name,
          initialBalance: account.initialBalance,
          availableBalance: newBalance,
          accountNumber: account.accountNumber,
          branchCode: account.branchCode,
          branchAddress: account.branchAddress,
          createdAt: account.createdAt,
          uid: account.uid,
        );

        await _accountFirebaseService.updateAccount(updatedAccount);
      }

      // Update the expense
      await _firebaseService.updateExpense(
        expense,
        fileBytes: fileBytes,
        fileName: fileName,
        file: file,
      );

      // Trigger accounting hook for expense update
      await _hookService.onExpenseUpdated(originalExpense, expense);

      return Right(SuccessObj(message: 'Expense updated successfully'));
    } catch (e) {
      log('Error updating expense: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteExpense(
    String expenseId,
    String accountId,
    double amount,
  ) async {
    try {
      log('Deleting expense: $expenseId');

      // Get the expense first for the hook service
      final expense = await _firebaseService.getExpenseById(expenseId);
      if (expense == null) {
        return Left(
            FailureObj(code: AppStrings.errorS, message: 'Expense not found'));
      }

      // Update account balance by adding back the expense amount
      final accountsResult = await _accountFirebaseService.getAccounts();
      final accounts =
          accountsResult.where((account) => account.id == accountId).toList();

      if (accounts.isEmpty) {
        return Left(
            FailureObj(code: AppStrings.errorS, message: 'Account not found'));
      }

      final account = accounts.first;
      final newBalance = account.availableBalance + amount;

      // Update account balance
      final updatedAccount = AccountModel(
        id: account.id,
        name: account.name,
        initialBalance: account.initialBalance,
        availableBalance: newBalance,
        accountNumber: account.accountNumber,
        branchCode: account.branchCode,
        branchAddress: account.branchAddress,
        createdAt: account.createdAt,
        uid: account.uid,
      );

      await _accountFirebaseService.updateAccount(updatedAccount);

      // Trigger accounting hook before deletion
      await _hookService.onExpenseDeleted(expense);

      // Delete the expense
      await _firebaseService.deleteExpense(expenseId);

      return Right(SuccessObj(message: 'Expense deleted successfully'));
    } catch (e) {
      log('Error deleting expense: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Stream<List<ExpenseModel>> listenToExpenses() {
    return _firebaseService.listenToExpenses();
  }
}
