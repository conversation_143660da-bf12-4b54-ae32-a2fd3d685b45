import 'dart:developer';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/models/slab/formula_variables.dart';

/// Service for migrating existing slabs to the new formula system
class SlabFormulaMigrationService {
  /// Check if a slab needs migration to the new formula system
  static bool needsMigration(SlabModel slab) {
    return slab.calculationFormula == null;
  }

  /// Migrate a slab to use the standard formula
  static SlabModel migrateToStandardFormula(SlabModel slab) {
    if (slab.calculationFormula != null) {
      log('Slab ${slab.slabName} already has a formula, no migration needed');
      return slab;
    }

    log('Migrating slab ${slab.slabName} to standard formula');

    final standardFormula = createStandardFormula();
    
    return SlabModel(
      slabId: slab.slabId,
      slabName: slab.slabName,
      startDate: slab.startDate,
      expiryDate: slab.expiryDate,
      createdAt: slab.createdAt,
      isActive: slab.isActive,
      rates: slab.rates,
      calculationFormula: standardFormula,
    );
  }

  /// Create the standard calculation formula that matches the legacy logic
  static CalculationFormulaModel createStandardFormula() {
    return CalculationFormulaModel(
      formulaId: 'standard_formula_${DateTime.now().millisecondsSinceEpoch}',
      formulaName: 'Standard Calculation',
      description: 'Legacy calculation: Total Tons × Distance × Rate',
      steps: [
        FormulaStepModel(
          stepId: 'step1',
          stepName: 'Calculate Final Amount',
          formula: 'totalWeightTons × distanceInKilometers × rateValue',
          resultVariable: 'finalAmount',
          description: 'Standard billing calculation: tons × kilometers × rate',
        ),
      ],
      finalResultVariable: 'finalAmount',
      isActive: true,
    );
  }

  /// Create a multi-step formula equivalent to the standard calculation
  static CalculationFormulaModel createMultiStepStandardFormula() {
    return CalculationFormulaModel(
      formulaId: 'multi_step_standard_${DateTime.now().millisecondsSinceEpoch}',
      formulaName: 'Multi-Step Standard Calculation',
      description: 'Standard calculation broken into multiple steps',
      steps: [
        FormulaStepModel(
          stepId: 'step1',
          stepName: 'Calculate Total Weight in Tons',
          formula: '(numberOfBags × weightPerBag) ÷ 1000',
          resultVariable: 'calculatedTons',
          description: 'Convert total weight from kg to tons',
        ),
        FormulaStepModel(
          stepId: 'step2',
          stepName: 'Calculate Distance Rate',
          formula: 'distanceInKilometers × rateValue',
          resultVariable: 'distanceRate',
          description: 'Calculate distance-based rate',
        ),
        FormulaStepModel(
          stepId: 'step3',
          stepName: 'Calculate Final Amount',
          formula: 'calculatedTons × distanceRate',
          resultVariable: 'finalAmount',
          description: 'Calculate final billing amount',
        ),
      ],
      finalResultVariable: 'finalAmount',
      isActive: true,
    );
  }

  /// Create a formula with markup/discount capabilities
  static CalculationFormulaModel createMarkupFormula(double markupPercentage) {
    final markupMultiplier = 1 + (markupPercentage / 100);
    
    return CalculationFormulaModel(
      formulaId: 'markup_formula_${DateTime.now().millisecondsSinceEpoch}',
      formulaName: 'Standard Calculation with ${markupPercentage.toStringAsFixed(1)}% Markup',
      description: 'Standard calculation with ${markupPercentage.toStringAsFixed(1)}% markup applied',
      steps: [
        FormulaStepModel(
          stepId: 'step1',
          stepName: 'Calculate Base Amount',
          formula: 'totalWeightTons × distanceInKilometers × rateValue',
          resultVariable: 'baseAmount',
          description: 'Standard billing calculation',
        ),
        FormulaStepModel(
          stepId: 'step2',
          stepName: 'Apply Markup',
          formula: 'baseAmount × $markupMultiplier',
          resultVariable: 'finalAmount',
          description: 'Apply ${markupPercentage.toStringAsFixed(1)}% markup',
        ),
      ],
      finalResultVariable: 'finalAmount',
      isActive: true,
    );
  }

  /// Batch migrate multiple slabs
  static List<SlabModel> batchMigrate(List<SlabModel> slabs) {
    final migratedSlabs = <SlabModel>[];
    int migratedCount = 0;

    for (final slab in slabs) {
      if (needsMigration(slab)) {
        migratedSlabs.add(migrateToStandardFormula(slab));
        migratedCount++;
      } else {
        migratedSlabs.add(slab);
      }
    }

    log('Batch migration completed: $migratedCount out of ${slabs.length} slabs migrated');
    return migratedSlabs;
  }

  /// Validate that a migrated formula produces the same results as legacy calculation
  static bool validateMigration(SlabModel originalSlab, SlabModel migratedSlab) {
    if (originalSlab.calculationFormula != null) {
      log('Original slab already has formula, cannot validate migration');
      return false;
    }

    if (migratedSlab.calculationFormula == null) {
      log('Migrated slab does not have formula');
      return false;
    }

    // This would require test data to validate properly
    // For now, we'll just check that the formula structure is valid
    return migratedSlab.calculationFormula!.isValid();
  }

  /// Get migration statistics for a list of slabs
  static MigrationStatistics getMigrationStatistics(List<SlabModel> slabs) {
    int totalSlabs = slabs.length;
    int slabsWithFormula = 0;
    int slabsNeedingMigration = 0;
    int activeSlabs = 0;
    int expiredSlabs = 0;

    for (final slab in slabs) {
      if (slab.calculationFormula != null) {
        slabsWithFormula++;
      } else {
        slabsNeedingMigration++;
      }

      if (slab.isActive) {
        activeSlabs++;
      }

      if (slab.isExpired) {
        expiredSlabs++;
      }
    }

    return MigrationStatistics(
      totalSlabs: totalSlabs,
      slabsWithFormula: slabsWithFormula,
      slabsNeedingMigration: slabsNeedingMigration,
      activeSlabs: activeSlabs,
      expiredSlabs: expiredSlabs,
    );
  }

  /// Create a formula template based on common business requirements
  static CalculationFormulaModel createCustomTemplate({
    required String templateName,
    required String description,
    bool includeWeightCalculation = false,
    bool includeDistanceCalculation = false,
    double? fixedMarkup,
    double? minimumAmount,
  }) {
    final steps = <FormulaStepModel>[];
    String finalVariable = 'finalAmount';

    if (includeWeightCalculation) {
      steps.add(FormulaStepModel(
        stepId: 'step${steps.length + 1}',
        stepName: 'Calculate Weight in Tons',
        formula: '(numberOfBags × weightPerBag) ÷ 1000',
        resultVariable: 'weightTons',
        description: 'Convert weight to tons',
      ));
    }

    if (includeDistanceCalculation) {
      steps.add(FormulaStepModel(
        stepId: 'step${steps.length + 1}',
        stepName: 'Calculate Distance Rate',
        formula: 'distanceInKilometers × rateValue',
        resultVariable: 'distanceRate',
        description: 'Calculate distance-based rate',
      ));
    }

    // Base calculation
    String baseFormula;
    if (includeWeightCalculation && includeDistanceCalculation) {
      baseFormula = 'weightTons × distanceRate';
    } else if (includeWeightCalculation) {
      baseFormula = 'weightTons × distanceInKilometers × rateValue';
    } else if (includeDistanceCalculation) {
      baseFormula = 'totalWeightTons × distanceRate';
    } else {
      baseFormula = 'totalWeightTons × distanceInKilometers × rateValue';
    }

    steps.add(FormulaStepModel(
      stepId: 'step${steps.length + 1}',
      stepName: 'Calculate Base Amount',
      formula: baseFormula,
      resultVariable: 'baseAmount',
      description: 'Calculate base billing amount',
    ));

    // Apply markup if specified
    if (fixedMarkup != null && fixedMarkup != 0) {
      final markupMultiplier = 1 + (fixedMarkup / 100);
      steps.add(FormulaStepModel(
        stepId: 'step${steps.length + 1}',
        stepName: 'Apply Markup',
        formula: 'baseAmount × $markupMultiplier',
        resultVariable: 'markedUpAmount',
        description: 'Apply ${fixedMarkup.toStringAsFixed(1)}% markup',
      ));
      finalVariable = 'markedUpAmount';
    } else {
      finalVariable = 'baseAmount';
    }

    // Apply minimum amount if specified
    if (minimumAmount != null && minimumAmount > 0) {
      // Note: This would require a more complex expression evaluator to handle max() function
      // For now, we'll just add a note in the description
      final lastStep = steps.last;
      steps[steps.length - 1] = lastStep.copyWith(
        description: '${lastStep.description} (Minimum amount: $minimumAmount)',
      );
    }

    return CalculationFormulaModel(
      formulaId: 'custom_template_${DateTime.now().millisecondsSinceEpoch}',
      formulaName: templateName,
      description: description,
      steps: steps,
      finalResultVariable: finalVariable,
      isActive: true,
    );
  }
}

/// Statistics about slab migration status
class MigrationStatistics {
  final int totalSlabs;
  final int slabsWithFormula;
  final int slabsNeedingMigration;
  final int activeSlabs;
  final int expiredSlabs;

  MigrationStatistics({
    required this.totalSlabs,
    required this.slabsWithFormula,
    required this.slabsNeedingMigration,
    required this.activeSlabs,
    required this.expiredSlabs,
  });

  double get migrationProgress {
    if (totalSlabs == 0) return 0.0;
    return slabsWithFormula / totalSlabs;
  }

  String get summary {
    return 'Total: $totalSlabs, With Formula: $slabsWithFormula, Need Migration: $slabsNeedingMigration, Active: $activeSlabs, Expired: $expiredSlabs';
  }

  @override
  String toString() {
    return 'MigrationStatistics($summary)';
  }
}
