import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/core/utils/app_constants/assets/app_assets.dart';
import 'package:logestics/features/finance/accounts/presentation/views/accounts_view.dart';
import 'package:logestics/features/finance/brokers/presentation/views/broker_list_view.dart';
import 'package:logestics/features/finance/check_usage/presentation/views/check_usage_view.dart';
import 'package:logestics/features/finance/deposit_categories/presentation/views/deposit_categories_view.dart';
import 'package:logestics/features/finance/deposits/presentation/views/deposits_view.dart';
import 'package:logestics/features/finance/expenses/presentation/views/expense_categories_view.dart';
import 'package:logestics/features/finance/expenses/presentation/views/expenses_view.dart';
import 'package:logestics/firebase_service/finance/fuel_card_firebase_service.dart';
import 'package:logestics/features/finance/fuel_cards/repositories/fuel_card_repository.dart';
import 'package:logestics/features/finance/fuel_cards/usecases/add_fuel_rate_use_case.dart';
import 'package:logestics/features/finance/fuel_cards/usecases/create_fuel_card_use_case.dart';
import 'package:logestics/features/finance/fuel_cards/usecases/get_fuel_cards_use_case.dart';
import 'package:logestics/features/finance/fuel_cards/usecases/get_fuel_rate_history_use_case.dart';
import 'package:logestics/features/finance/fuel_cards/usecases/get_latest_fuel_rate_use_case.dart';
import 'package:logestics/features/finance/fuel_cards/presentation/controllers/fuel_card_controller.dart';
import 'package:logestics/features/finance/fuel_cards/presentation/views/fuel_card_management_view.dart';
import 'package:logestics/features/finance/loans/presentation/views/loans_view.dart';
import 'package:logestics/features/finance/payees/presentation/payees_view.dart';
import 'package:logestics/features/finance/payers/presentation/payers_view.dart';
import 'package:logestics/models/drawer_item_model.dart';
import 'package:logestics/features/locations/presentation/districts/presentation/district_screen_view.dart';
import 'package:logestics/features/locations/presentation/regions/presentation/region_screen_view.dart';
import 'package:logestics/features/locations/presentation/stations/presentation/station_screen_view.dart';
import 'package:logestics/features/locations/presentation/zones/presentation/zone_screen_view.dart';
import 'package:logestics/features/voucher/presentation/views/voucher_screen_view.dart';
import 'package:logestics/features/billing/presentation/views/billing_screen_view.dart';
import 'package:logestics/features/finance/bills/presentation/views/bills_screen_view.dart';
import 'package:logestics/features/backup_restore/presentation/views/backup_restore_view.dart';
import 'package:logestics/features/slab/presentation/views/slab_screen_view.dart';
import 'package:logestics/features/accounting/chart_of_accounts/presentation/views/chart_of_accounts_view.dart';

import '../../authentication/domain/useCases/sign_out_use_case.dart';
import '../../invoices/presentation/views/invoice_screen_view.dart';
import '../../dashboard/presentation/screens/home_screen/home_screen.dart';

class MainDrawerController extends GetxController implements GetxService {
  var isLoading = false.obs;
  var errorMessage = ''.obs;
  Widget? currentScreen;
  final ScrollController scrollController = ScrollController();

  // Selection state
  final RxString selectedItemPath = ''.obs;
  final RxBool isDashboardHovered = false.obs;
  final RxBool isLocationsHovered = false.obs;

  // Drawer sections
  final List<DrawerSectionModel> drawerSections = [
    DrawerSectionModel(
      title: "- MAIN",
      items: [
        DrawerItemModel(
          title: "Dashboard",
          icon: AppAssets.gridWeb,
          screen: const HomeScreen(),
          children: [
            DrawerItemModel(
              title: "Admin",
              icon: AppAssets.gridWeb,
              screen: const HomeScreen(),
            ),
          ],
        ),
      ],
    ),
    DrawerSectionModel(
      title: "- System",
      items: [
        DrawerItemModel(
          title: "Locations",
          icon: AppAssets.gridWeb,
          children: [
            DrawerItemModel(
              title: "Zones",
              icon: AppAssets.gridWeb,
              screen: const ZonesScreenView(),
            ),
            DrawerItemModel(
              title: "Regions",
              icon: AppAssets.gridWeb,
              screen: const RegionsScreenView(),
            ),
            DrawerItemModel(
              title: "Districts",
              icon: AppAssets.gridWeb,
              screen: const DistrictScreenView(),
            ),
            DrawerItemModel(
              title: "Stations",
              icon: AppAssets.gridWeb,
              screen: const StationScreenView(),
            ),
          ],
        ),
        DrawerItemModel(
          title: "Invoices",
          icon: AppAssets.noteListSquare,
          screen: const InvoiceScreenView(),
        ),
        DrawerItemModel(
          title: "Billing",
          icon: AppAssets.noteListSquare,
          children: [
            DrawerItemModel(
              title: "Billing List",
              icon: AppAssets.noteListSquare,
              screen: const BillingScreenView(),
            ),
            DrawerItemModel(
              title: "Bills",
              icon: AppAssets.noteListSquare,
              screen: const BillsScreenView(),
              navigateTo: '/bills',
            ),
          ],
        ),
        DrawerItemModel(
          title: "Voucher",
          icon: AppAssets.calendarEmptyAlt,
          screen: const VoucherScreenView(),
        ),
        DrawerItemModel(
          title: "Slab Management",
          icon: AppAssets.gridWeb,
          screen: const SlabScreenView(),
          navigateTo: '/slabs',
        ),
        DrawerItemModel(
          title: "Asset Management",
          icon: AppAssets.gridWeb,
          navigateTo: '/asset-management',
        ),
        DrawerItemModel(
          title: "System Management",
          icon: AppAssets.gridWeb,
          children: [
            DrawerItemModel(
              title: "Backup & Restore",
              icon: AppAssets.gridWeb,
              screen: const BackupRestoreView(),
              navigateTo: '/backup-restore',
            ),
          ],
        ),
        DrawerItemModel(
          title: "Finance",
          icon: AppAssets.gridWeb,
          children: [
            DrawerItemModel(
              title: "Financial Dashboard",
              icon: AppAssets.gridWeb,
              navigateTo: '/accounting/dashboard',
            ),
            DrawerItemModel(
              title: "Accounts",
              icon: AppAssets.gridWeb,
              screen: const AccountsView(),
            ),
            DrawerItemModel(
              title: "Chart of Accounts",
              icon: AppAssets.gridWeb,
              screen: const ChartOfAccountsView(),
            ),
            DrawerItemModel(
              title: "Fiscal Periods",
              icon: AppAssets.gridWeb,
              navigateTo: '/accounting/fiscal-periods',
            ),
            DrawerItemModel(
              title: "Trial Balance",
              icon: AppAssets.gridWeb,
              navigateTo: '/accounting/trial-balance',
            ),
            DrawerItemModel(
              title: "Profit & Loss",
              icon: AppAssets.gridWeb,
              navigateTo: '/accounting/profit-loss',
            ),
            DrawerItemModel(
              title: "Balance Sheet",
              icon: AppAssets.gridWeb,
              navigateTo: '/accounting/balance-sheet',
            ),
            DrawerItemModel(
              title: "Journal Entries",
              icon: AppAssets.gridWeb,
              navigateTo: '/accounting/journal-entries',
            ),
            DrawerItemModel(
              title: "General Ledger",
              icon: AppAssets.gridWeb,
              navigateTo: '/accounting/general-ledger',
            ),
            DrawerItemModel(
              title: "Aged Reports",
              icon: AppAssets.gridWeb,
              navigateTo: '/accounting/aged-reports',
            ),
            DrawerItemModel(
              title: "Cash Flow Statement",
              icon: AppAssets.gridWeb,
              navigateTo: '/accounting/cash-flow-statement',
            ),
            DrawerItemModel(
              title: "Mock Data Management",
              icon: AppAssets.gridWeb,
              navigateTo: '/accounting/mock-data',
            ),
            DrawerItemModel(
              title: "Payees",
              icon: AppAssets.gridWeb,
              screen: const PayeesView(),
            ),
            DrawerItemModel(
              title: "Payers",
              icon: AppAssets.gridWeb,
              screen: const PayersView(),
            ),
            DrawerItemModel(
              title: "Brokers",
              icon: AppAssets.gridWeb,
              screen: Builder(
                builder: (context) {
                  // Initialize broker dependencies
                  AppBindings().dependencies();
                  return const BrokerListView();
                },
              ),
            ),
            DrawerItemModel(
              title: "Check Usage",
              icon: AppAssets.gridWeb,
              screen: Builder(
                builder: (context) {
                  // Initialize check usage dependencies
                  AppBindings().dependencies();
                  return const CheckUsageView();
                },
              ),
            ),
            DrawerItemModel(
              title: "Loans",
              icon: AppAssets.noteListSquare,
              screen: Builder(
                builder: (context) {
                  // Initialize loan dependencies
                  AppBindings().dependencies();
                  return const LoansView();
                },
              ),
            ),
            DrawerItemModel(
              title: "Fuel Cards",
              icon: AppAssets.gridWeb,
              screen: Builder(
                builder: (context) {
                  // Register and instantiate all dependencies for FuelCardController
                  final api = Get.put(FuelCardFirebaseService());
                  final repo = Get.put(FuelCardRepositoryImpl(api));
                  Get.put<FuelCardRepository>(repo);

                  final createUseCase = Get.put(CreateFuelCardUseCase(repo));
                  final getCardsUseCase = Get.put(GetFuelCardsUseCase(repo));
                  final addRateUseCase = Get.put(AddFuelRateUseCase(repo));
                  final getLatestRateUseCase =
                      Get.put(GetLatestFuelRateUseCase(repo));
                  final getFuelRateHistoryUseCase =
                      Get.put(GetFuelRateHistoryUseCase(repo));

                  // Register the controller
                  Get.put(FuelCardController(
                    repository: repo,
                    createFuelCardUseCase: createUseCase,
                    getFuelCardsUseCase: getCardsUseCase,
                    addFuelRateUseCase: addRateUseCase,
                    getLatestFuelRateUseCase: getLatestRateUseCase,
                    getFuelRateHistoryUseCase: getFuelRateHistoryUseCase,
                  ));

                  return const FuelCardManagementView();
                },
              ),
            ),
            DrawerItemModel(
              title: "Deposit Categories",
              icon: AppAssets.gridWeb,
              screen: const DepositCategoriesView(),
            ),
            DrawerItemModel(
              title: "Deposits",
              icon: AppAssets.gridWeb,
              screen: const DepositsView(),
            ),
            DrawerItemModel(
              title: "Expense Categories",
              icon: AppAssets.gridWeb,
              screen: const ExpenseCategoriesView(),
            ),
            DrawerItemModel(
              title: "Expenses",
              icon: AppAssets.gridWeb,
              screen: const ExpensesView(),
            ),
          ],
        ),
      ],
    ),
  ];

  MainDrawerController() {
    // Set initial screen
    currentScreen = drawerSections[0].items[0].screen;
    selectedItemPath.value = 'main/dashboard';
  }

  Future<void> signOut() async {
    isLoading.value = true;
    errorMessage.value = '';

    final result = await Get.find<SignOutUseCase>().execute();

    result.fold(
      (failure) {
        errorMessage.value = failure.message;
        isLoading.value = false;
      },
      (_) {
        isLoading.value = false;
      },
    );
  }

  void updateSelectedScreen(Widget? screen, String path, {String? navigateTo}) {
    if (navigateTo != null) {
      Get.toNamed(navigateTo);
    } else if (screen != null) {
      currentScreen = screen;
      selectedItemPath.value = path;
      update();
    }
  }

  bool isItemSelected(String path) {
    return selectedItemPath.value == path;
  }

  void updateDashboardHover(bool value) {
    isDashboardHovered.value = value;
    update();
  }

  void updateLocationsHover(bool value) {
    isLocationsHovered.value = value;
    update();
  }
}
