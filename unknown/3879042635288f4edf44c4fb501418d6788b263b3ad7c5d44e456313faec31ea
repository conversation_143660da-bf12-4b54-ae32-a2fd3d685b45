import 'dart:developer';
import 'dart:math' as math;
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/journal_entry_model.dart';

import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';

import '../utils/snackbar_utils.dart';

class FinancialMockDataService extends GetxService {
  final ChartOfAccountsFirebaseService _chartOfAccountsService = Get.find();
  final JournalEntryFirebaseService _journalEntryService = Get.find();

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  final RxBool isGenerating = false.obs;
  final RxString currentStep = ''.obs;
  final RxDouble progress = 0.0.obs;

  /// Generate comprehensive mock data for the financial system
  Future<void> generateComprehensiveMockData() async {
    try {
      isGenerating.value = true;
      progress.value = 0.0;

      // Step 1: Create Chart of Accounts
      currentStep.value = 'Creating Chart of Accounts...';
      await _createChartOfAccounts();
      progress.value = 0.3;

      // Step 2: Create Journal Entries
      currentStep.value = 'Creating Journal Entries...';
      await _createJournalEntries();
      progress.value = 0.6;

      // Step 3: Update General Ledger
      currentStep.value = 'Updating General Ledger...';
      await _updateGeneralLedger();
      progress.value = 0.9;

      // Step 4: Finalize
      currentStep.value = 'Finalizing...';
      await Future.delayed(const Duration(milliseconds: 500));
      progress.value = 1.0;

      SnackbarUtils.showSuccess('Success', 'Mock data generated successfully!');
    } catch (e) {
      SnackbarUtils.showError('Error', 'Failed to generate mock data: $e');
    } finally {
      isGenerating.value = false;
      currentStep.value = '';
      progress.value = 0.0;
    }
  }

  /// Create comprehensive chart of accounts
  Future<void> _createChartOfAccounts() async {
    final accounts = _getComprehensiveChartOfAccounts();

    for (final account in accounts) {
      try {
        await _chartOfAccountsService.createAccount(account);
        await Future.delayed(
            const Duration(milliseconds: 50)); // Prevent rate limiting
      } catch (e) {
        log('Error creating account ${account.accountName}: $e');
      }
    }
  }

  /// Create realistic journal entries
  Future<void> _createJournalEntries() async {
    final entries = _getRealisticJournalEntries();

    for (final entry in entries) {
      try {
        await _journalEntryService.createJournalEntry(entry);
        await Future.delayed(
            const Duration(milliseconds: 50)); // Prevent rate limiting
      } catch (e) {
        log('Error creating journal entry: $e');
      }
    }
  }

  /// Update general ledger with transactions
  Future<void> _updateGeneralLedger() async {
    // This would typically be handled automatically by journal entry creation
    // For now, we'll just add a small delay to simulate processing
    await Future.delayed(const Duration(seconds: 1));
  }

  /// Get comprehensive chart of accounts
  List<ChartOfAccountsModel> _getComprehensiveChartOfAccounts() {
    final DateTime now = DateTime.now();

    return [
      // ASSETS (1000-1999)
      ChartOfAccountsModel(
        id: 'cash-1001',
        accountNumber: '1001',
        accountName: 'Cash',
        description: 'Cash on hand and petty cash',
        category: AccountCategory.assets,
        accountType: AccountType.cash,
        balance: 25000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'bank-1002',
        accountNumber: '1002',
        accountName: 'Bank - Current Account',
        description: 'Primary business bank account',
        category: AccountCategory.assets,
        accountType: AccountType.bank,
        balance: 150000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'ar-1101',
        accountNumber: '1101',
        accountName: 'Accounts Receivable',
        description: 'Money owed by customers',
        category: AccountCategory.assets,
        accountType: AccountType.accountsReceivable,
        balance: 75000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'inventory-1201',
        accountNumber: '1201',
        accountName: 'Inventory',
        description: 'Goods held for sale',
        category: AccountCategory.assets,
        accountType: AccountType.inventory,
        balance: 50000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'equipment-1301',
        accountNumber: '1301',
        accountName: 'Office Equipment',
        description: 'Computers, furniture, and office equipment',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        balance: 35000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'vehicles-1302',
        accountNumber: '1302',
        accountName: 'Vehicles',
        description: 'Company vehicles and transportation equipment',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        balance: 120000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),

      // LIABILITIES (2000-2999)
      ChartOfAccountsModel(
        id: 'ap-2001',
        accountNumber: '2001',
        accountName: 'Accounts Payable',
        description: 'Money owed to suppliers',
        category: AccountCategory.liabilities,
        accountType: AccountType.accountsPayable,
        balance: 45000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'loan-2101',
        accountNumber: '2101',
        accountName: 'Bank Loan',
        description: 'Long-term bank loan',
        category: AccountCategory.liabilities,
        accountType: AccountType.loansPayable,
        balance: 200000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'tax-payable-2201',
        accountNumber: '2201',
        accountName: 'Tax Payable',
        description: 'Taxes owed to authorities',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        balance: 15000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),

      // EQUITY (3000-3999)
      ChartOfAccountsModel(
        id: 'capital-3001',
        accountNumber: '3001',
        accountName: 'Owner\'s Capital',
        description: 'Owner\'s investment in the business',
        category: AccountCategory.equity,
        accountType: AccountType.ownersEquity,
        balance: 300000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'retained-3101',
        accountNumber: '3101',
        accountName: 'Retained Earnings',
        description: 'Accumulated profits retained in business',
        category: AccountCategory.equity,
        accountType: AccountType.retainedEarnings,
        balance: 85000.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),

      // REVENUE (4000-4999)
      ChartOfAccountsModel(
        id: 'sales-4001',
        accountNumber: '4001',
        accountName: 'Sales Revenue',
        description: 'Revenue from product sales',
        category: AccountCategory.revenue,
        accountType: AccountType.salesRevenue,
        balance: 0.00, // Revenue accounts typically start at 0
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'service-4101',
        accountNumber: '4101',
        accountName: 'Service Revenue',
        description: 'Revenue from services provided',
        category: AccountCategory.revenue,
        accountType: AccountType.serviceRevenue,
        balance: 0.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),

      // EXPENSES (5000-5999)
      ChartOfAccountsModel(
        id: 'salaries-5001',
        accountNumber: '5001',
        accountName: 'Salaries Expense',
        description: 'Employee salaries and wages',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        balance: 0.00, // Expense accounts typically start at 0
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'rent-5101',
        accountNumber: '5101',
        accountName: 'Rent Expense',
        description: 'Office and warehouse rent',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        balance: 0.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'utilities-5201',
        accountNumber: '5201',
        accountName: 'Utilities Expense',
        description: 'Electricity, water, internet, phone',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        balance: 0.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'fuel-5301',
        accountNumber: '5301',
        accountName: 'Fuel Expense',
        description: 'Vehicle fuel and transportation costs',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        balance: 0.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
      ChartOfAccountsModel(
        id: 'interest-5401',
        accountNumber: '5401',
        accountName: 'Interest Expense',
        description: 'Interest paid on loans',
        category: AccountCategory.expenses,
        accountType: AccountType.interestExpense,
        balance: 0.00,
        isActive: true,
        uid: _uid,
        createdAt: now,
      ),
    ];
  }

  /// Get realistic journal entries for testing
  List<JournalEntryModel> _getRealisticJournalEntries() {
    final DateTime now = DateTime.now();
    final math.Random random = math.Random();

    final List<JournalEntryModel> entries = [];

    // Generate entries for the last 3 months
    for (int i = 0; i < 90; i++) {
      final DateTime entryDate = now.subtract(Duration(days: i));

      // Generate 1-3 entries per day
      final int entriesPerDay = random.nextInt(3) + 1;

      for (int j = 0; j < entriesPerDay; j++) {
        entries.addAll(_generateDailyTransactions(entryDate, _uid));
      }
    }

    return entries;
  }

  /// Generate realistic daily transactions
  List<JournalEntryModel> _generateDailyTransactions(
      DateTime date, String uid) {
    final math.Random random = math.Random();
    final List<JournalEntryModel> entries = [];

    // Transaction types with their probabilities
    final transactionTypes = [
      'sales',
      'expense_payment',
      'salary_payment',
      'loan_payment',
      'utility_payment',
      'fuel_purchase',
      'equipment_purchase'
    ];

    final String transactionType =
        transactionTypes[random.nextInt(transactionTypes.length)];

    switch (transactionType) {
      case 'sales':
        entries.add(_createSalesTransaction(date, uid, random));
        break;
      case 'expense_payment':
        entries.add(_createExpenseTransaction(date, uid, random));
        break;
      case 'salary_payment':
        entries.add(_createSalaryTransaction(date, uid, random));
        break;
      // Add more transaction types as needed
    }

    return entries;
  }

  /// Create a sales transaction
  JournalEntryModel _createSalesTransaction(
      DateTime date, String uid, math.Random random) {
    final double amount =
        (random.nextDouble() * 10000 + 1000); // $1,000 - $11,000
    final entryId =
        'je-sales-${date.millisecondsSinceEpoch}-${random.nextInt(1000)}';

    return JournalEntryModel(
      id: entryId,
      entryNumber:
          'JE-${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}-${random.nextInt(999) + 1}',
      description: 'Sales transaction - Customer payment',
      entryDate: date,
      entryType: JournalEntryType.automatic,
      status: JournalEntryStatus.posted,
      totalDebits: amount,
      totalCredits: amount,
      uid: _uid,
      createdAt: date,
      createdBy: 'system',
      lines: [
        JournalEntryLineModel(
          id: 'line-1',
          journalEntryId: entryId,
          accountId: 'bank-1002',
          accountNumber: '1002',
          accountName: 'Bank - Current Account',
          description: 'Customer payment received',
          debitAmount: amount,
          creditAmount: 0.0,
          createdAt: date,
        ),
        JournalEntryLineModel(
          id: 'line-2',
          journalEntryId: entryId,
          accountId: 'sales-4001',
          accountNumber: '4001',
          accountName: 'Sales Revenue',
          description: 'Sales revenue',
          debitAmount: 0.0,
          creditAmount: amount,
          createdAt: date,
        ),
      ],
    );
  }

  /// Create an expense transaction
  JournalEntryModel _createExpenseTransaction(
      DateTime date, String uid, math.Random random) {
    final double amount = (random.nextDouble() * 2000 + 200); // $200 - $2,200

    final entryId =
        'je-expense-${date.millisecondsSinceEpoch}-${random.nextInt(1000)}';

    return JournalEntryModel(
      id: entryId,
      entryNumber:
          'JE-${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}-${random.nextInt(999) + 1}',
      description: 'Operating expense payment',
      entryDate: date,
      entryType: JournalEntryType.automatic,
      status: JournalEntryStatus.posted,
      totalDebits: amount,
      totalCredits: amount,
      uid: _uid,
      createdAt: date,
      createdBy: 'system',
      lines: [
        JournalEntryLineModel(
          id: 'line-1',
          journalEntryId: entryId,
          accountId: 'utilities-5201',
          accountNumber: '5201',
          accountName: 'Utilities Expense',
          description: 'Monthly utility payment',
          debitAmount: amount,
          creditAmount: 0.0,
          createdAt: date,
        ),
        JournalEntryLineModel(
          id: 'line-2',
          journalEntryId: entryId,
          accountId: 'bank-1002',
          accountNumber: '1002',
          accountName: 'Bank - Current Account',
          description: 'Payment made',
          debitAmount: 0.0,
          creditAmount: amount,
          createdAt: date,
        ),
      ],
    );
  }

  /// Create a salary payment transaction
  JournalEntryModel _createSalaryTransaction(
      DateTime date, String uid, math.Random random) {
    final double amount =
        (random.nextDouble() * 5000 + 3000); // $3,000 - $8,000

    final entryId =
        'je-salary-${date.millisecondsSinceEpoch}-${random.nextInt(1000)}';

    return JournalEntryModel(
      id: entryId,
      entryNumber:
          'JE-${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}-${random.nextInt(999) + 1}',
      description: 'Monthly salary payment',
      entryDate: date,
      entryType: JournalEntryType.automatic,
      status: JournalEntryStatus.posted,
      totalDebits: amount,
      totalCredits: amount,
      uid: _uid,
      createdAt: date,
      createdBy: 'system',
      lines: [
        JournalEntryLineModel(
          id: 'line-1',
          journalEntryId: entryId,
          accountId: 'salaries-5001',
          accountNumber: '5001',
          accountName: 'Salaries Expense',
          description: 'Employee salary payment',
          debitAmount: amount,
          creditAmount: 0.0,
          createdAt: date,
        ),
        JournalEntryLineModel(
          id: 'line-2',
          journalEntryId: entryId,
          accountId: 'bank-1002',
          accountNumber: '1002',
          accountName: 'Bank - Current Account',
          description: 'Salary payment',
          debitAmount: 0.0,
          creditAmount: amount,
          createdAt: date,
        ),
      ],
    );
  }
}
