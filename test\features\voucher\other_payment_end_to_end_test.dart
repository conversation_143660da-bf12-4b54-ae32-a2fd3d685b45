import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/payment_transaction_model.dart';

void main() {
  group('Other Payment End-to-End Tests', () {
    test('should preserve Other payment type through serialization', () {
      // Arrange: Create Other payment
      final otherPayment = PaymentTransactionModel(
        id: 'payment_001',
        voucherId: 'V-TEST-001',
        method: PaymentMethod.accountTransfer, // This is "Other" payment type
        status: PaymentStatus.paid,
        amount: 50000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: 'cross-company-account-id',
        accountName: 'Company 2 Bank Account',
        notes: 'Cross-company Other payment test',
      );

      // Act: Convert to map and back (simulating Firestore save/load)
      final paymentMap = otherPayment.toMap();
      final reconstructedPayment = PaymentTransactionModel.fromMap(paymentMap);

      // Assert: Verify payment method is preserved correctly
      expect(
          reconstructedPayment.method, equals(PaymentMethod.accountTransfer));
      expect(reconstructedPayment.method.name, equals('accountTransfer'));
      expect(reconstructedPayment.amount, equals(50000.0));
      expect(
          reconstructedPayment.accountId, equals('cross-company-account-id'));

      print(
          '✅ Other payment method preserved through serialization: ${reconstructedPayment.method.name}');
      print('✅ Payment amount preserved: ${reconstructedPayment.amount}');
      print('✅ Account ID preserved: ${reconstructedPayment.accountId}');
    });

    test(
        'should correctly identify workflow routing for Other payment in voucher context',
        () {
      // Arrange: Create voucher with Other payment
      final otherPaymentMap = {
        'id': 'payment_002',
        'voucherId': 'V-TEST-002',
        'method': 'accountTransfer', // String format as stored in Firestore
        'status': 'paid',
        'amount': 75000.0,
        'pendingAmount': 0.0,
        'transactionDate': DateTime.now().toIso8601String(),
        'accountId': 'other-company-account',
        'accountName': 'Other Company Bank Account',
        'notes': 'Other payment workflow test',
      };

      // Act: Convert from map (simulating Firestore load)
      final payment = PaymentTransactionModel.fromMap(otherPaymentMap);

      // Simulate workflow detection logic
      final shouldUseLoanWorkflow = payment.method == PaymentMethod.check ||
          payment.method == PaymentMethod.accountTransfer;
      final isOtherPaymentType =
          payment.method == PaymentMethod.accountTransfer;

      // Assert: Verify correct workflow routing
      expect(payment.method, equals(PaymentMethod.accountTransfer));
      expect(shouldUseLoanWorkflow, isTrue);
      expect(isOtherPaymentType, isTrue);

      print('✅ Payment method from map: ${payment.method.name}');
      print('✅ Should use loan workflow: $shouldUseLoanWorkflow');
      print('✅ Is Other payment type: $isOtherPaymentType');

      // Simulate the actual routing logic from VoucherAccountingHookService
      if (shouldUseLoanWorkflow) {
        if (isOtherPaymentType) {
          print(
              '🔄 Would route to: PENDING LOAN REQUEST (createLoanRequestFromPayment)');
          expect(true, isTrue); // This should be the path taken
        } else {
          print(
              '🔄 Would route to: ACTIVE LOAN CREATION (createActiveLoanFromPayment)');
          expect(false, isTrue); // This should NOT happen for Other payments
        }
      } else {
        print(
            '🔄 Would route to: TRADITIONAL WORKFLOW (generatePaymentJournalEntry)');
        expect(false, isTrue); // This should NOT happen for Other payments
      }
    });

    test('should handle payment method string conversion correctly', () {
      // Arrange: Test different string formats that might come from Firestore
      final testCases = [
        {
          'method': 'accountTransfer',
          'expected': PaymentMethod.accountTransfer
        },
        {'method': 'check', 'expected': PaymentMethod.check},
        {'method': 'cash', 'expected': PaymentMethod.cash},
        {'method': 'fuelCard', 'expected': PaymentMethod.fuelCard},
      ];

      for (final testCase in testCases) {
        // Act: Create payment from map with string method
        final paymentMap = {
          'id': 'test_payment',
          'voucherId': 'V-TEST',
          'method': testCase['method'],
          'status': 'paid',
          'amount': 10000.0,
          'pendingAmount': 0.0,
          'transactionDate': DateTime.now().toIso8601String(),
        };

        final payment = PaymentTransactionModel.fromMap(paymentMap);

        // Assert: Verify correct enum conversion
        expect(payment.method, equals(testCase['expected']));
        print('✅ ${testCase['method']} -> ${payment.method.name}');
      }
    });

    test('should verify Other payment creates correct loan workflow flags', () {
      // Arrange: Create Other payment
      final otherPayment = PaymentTransactionModel(
        id: 'payment_003',
        voucherId: 'V-TEST-003',
        method: PaymentMethod.accountTransfer,
        status: PaymentStatus.paid,
        amount: 60000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: 'cross-company-account-3',
        accountName: 'Cross Company Account 3',
        notes: 'Loan workflow flag test',
      );

      // Act: Test all the conditions used in the actual workflow
      final shouldUseLoanWorkflow =
          otherPayment.method == PaymentMethod.check ||
              otherPayment.method == PaymentMethod.accountTransfer;
      final isOtherPaymentType =
          otherPayment.method == PaymentMethod.accountTransfer;
      final isCheckPaymentType = otherPayment.method == PaymentMethod.check;

      // Assert: Verify all flags are correct for Other payment
      expect(shouldUseLoanWorkflow, isTrue,
          reason: 'Other payments should use loan workflow');
      expect(isOtherPaymentType, isTrue,
          reason: 'accountTransfer should be identified as Other payment');
      expect(isCheckPaymentType, isFalse,
          reason: 'accountTransfer should NOT be identified as check payment');

      // Verify the exact logic from VoucherAccountingHookService
      if (shouldUseLoanWorkflow) {
        if (isOtherPaymentType) {
          // This is the path that should be taken for Other payments
          print('✅ CORRECT PATH: Other payment would create ACTIVE loan');
          expect(true, isTrue);
        } else {
          // This path should be taken for check payments only
          print(
              '❌ WRONG PATH: Other payment would create PENDING loan request');
          expect(false, isTrue,
              reason: 'Other payments should not create pending loan requests');
        }
      } else {
        print('❌ WRONG PATH: Other payment would use traditional workflow');
        expect(false, isTrue,
            reason: 'Other payments should use loan workflow');
      }
    });

    test(
        'should simulate complete voucher processing workflow for Other payment',
        () {
      // Arrange: Create complete voucher with Other payment
      final voucherData = {
        'voucherNumber': 'V-COMPLETE-001',
        'driverName': 'Complete Test Driver',
        'totalFreight': 120000.0,
        'brokerFee': 6000.0,
        'munshiana': 2400.0,
        'nlcFreightTax': 5520.0,
        'netProfit': 46080.0,
        'paymentTransactions': [
          {
            'id': 'payment_complete_001',
            'voucherId': 'V-COMPLETE-001',
            'method': 'accountTransfer', // Other payment type
            'status': 'paid',
            'amount': 120000.0,
            'pendingAmount': 0.0,
            'transactionDate': DateTime.now().toIso8601String(),
            'accountId': 'complete-cross-company-account',
            'accountName': 'Complete Cross Company Account',
            'notes': 'Complete workflow test - Other payment',
          }
        ],
        // Required account IDs
        'brokerAccountId': 'broker-account-complete',
        'munshianaAccountId': 'munshiana-account-complete',
        'nlcFreightTaxAccountId': 'nlc-tax-account-complete',
        'profitAccountId': 'profit-account-complete',
      };

      // Act: Process the voucher data (simulating VoucherAccountingHookService)
      final paymentMaps =
          voucherData['paymentTransactions'] as List<Map<String, dynamic>>;
      final payments = paymentMaps
          .map((map) => PaymentTransactionModel.fromMap(map))
          .toList();

      // Process each payment (simulating the actual loop in VoucherAccountingHookService)
      for (final payment in payments) {
        final shouldUseLoanWorkflow = payment.method == PaymentMethod.check ||
            payment.method == PaymentMethod.accountTransfer;

        if (shouldUseLoanWorkflow) {
          final isOtherPaymentType =
              payment.method == PaymentMethod.accountTransfer;

          if (isOtherPaymentType) {
            // This should be the path taken for Other payments
            print(
                '✅ PENDING LOAN PATH: Payment ${payment.id} (${payment.method.name}) -> createLoanRequestFromPayment');
            expect(payment.method, equals(PaymentMethod.accountTransfer));
            expect(payment.amount, equals(120000.0));
            expect(payment.accountId, equals('complete-cross-company-account'));
          } else {
            // This should be the path for check payments
            print(
                '⚠️ PENDING LOAN PATH: Payment ${payment.id} (${payment.method.name}) -> createLoanRequestFromPayment');
            expect(payment.method, equals(PaymentMethod.check));
          }
        } else {
          // This should be the path for cash/fuel card payments
          print(
              'ℹ️ TRADITIONAL PATH: Payment ${payment.id} (${payment.method.name}) -> generatePaymentJournalEntry');
          expect(
              [PaymentMethod.cash, PaymentMethod.fuelCard]
                  .contains(payment.method),
              isTrue);
        }
      }

      // Assert: Verify the complete workflow would be correct
      expect(payments, hasLength(1));
      final otherPayment = payments.first;
      expect(otherPayment.method, equals(PaymentMethod.accountTransfer));
      print('✅ Complete voucher workflow test passed for Other payment');
    });
  });
}
