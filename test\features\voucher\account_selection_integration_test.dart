import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/models/voucher_model.dart';
import 'package:logestics/features/voucher/repositories/voucher_repository.dart';

void main() {
  group('Account Selection Integration Tests', () {
    group('Account ID/Name Validation', () {
      test('should properly validate voucher with Chart of Accounts fields',
          () {
        // Create test Chart of Accounts
        final brokerAccount = ChartOfAccountsModel(
          id: 'broker-account-id',
          accountNumber: '3000',
          accountName: 'Broker Fees Payable',
          category: AccountCategory.liabilities,
          accountType: AccountType.accountsPayable,
          uid: 'test-uid',
          createdAt: DateTime.now(),
        );

        final munshianaAccount = ChartOfAccountsModel(
          id: 'munshiana-account-id',
          accountNumber: '3001',
          accountName: 'Munshiana Payable',
          category: AccountCategory.equity,
          accountType: AccountType.equityServiceRevenue,
          uid: 'test-uid',
          createdAt: DateTime.now(),
        );

        // Create voucher data with proper account ID and name mapping
        final voucherData = {
          'voucherNumber': 'TEST-001',
          'voucherStatus': 'Active',
          'departureDate': '2024-01-15',
          'driverName': 'Test Driver',
          'invoiceTasNumberList': ['TAS-001'],
          'invoiceBiltyNumberList': ['BILTY-001'],
          'weightInTons': 25,
          'productName': 'Test Product',
          'totalNumberOfBags': 500,
          'brokerType': 'Outsource',
          'brokerName': 'Test Broker',
          'brokerFees': 5000.0,
          'munshianaFees': 3000.0,
          'driverPhoneNumber': '**********',
          'truckNumber': 'TRK-001',
          'conveyNoteNumber': 'CN-001',
          'totalFreight': 50000.0,
          'companyFreight': 50000.0,
          'settledFreight': 45000.0,
          'paymentTransactions': [
            {
              'id': 'payment-1',
              'voucherId': 'TEST-001',
              'method': 'cash',
              'status': 'paid',
              'amount': 10000.0,
              'pendingAmount': 0.0,
              'transactionDate': DateTime.now().toIso8601String(),
              'accountId': 'cash-account-id',
              'accountName': 'Cash Account',
              'notes': 'Test payment',
            }
          ],
          // FIXED: Proper account ID and name mapping
          'brokerAccountId': brokerAccount.id,
          'brokerAccount': brokerAccount.accountName, // Name, not ID
          'munshianaAccountId': munshianaAccount.id,
          'munshianaAccount': munshianaAccount.accountName, // Name, not ID
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        };

        // Convert to VoucherModel to test validation
        final voucher = VoucherModel.fromJson(voucherData);

        // Verify the mapping is correct
        expect(voucher.brokerAccountId, equals(brokerAccount.id));
        expect(voucher.brokerAccount, equals(brokerAccount.accountName));
        expect(voucher.munshianaAccountId, equals(munshianaAccount.id));
        expect(voucher.munshianaAccount, equals(munshianaAccount.accountName));

        // Test that validation would pass with proper mapping
        expect(voucher.brokerAccountId, isNotEmpty);
        expect(voucher.brokerAccount, isNotEmpty);
        expect(voucher.munshianaAccountId, isNotEmpty);
        expect(voucher.munshianaAccount, isNotEmpty);
      });

      test('should fail validation when account ID is set but name is missing',
          () {
        // Create voucher data with ID but missing name (the bug scenario)
        final voucherData = {
          'voucherNumber': 'TEST-002',
          'voucherStatus': 'Active',
          'departureDate': '2024-01-15',
          'driverName': 'Test Driver',
          'invoiceTasNumberList': ['TAS-002'],
          'invoiceBiltyNumberList': ['BILTY-002'],
          'weightInTons': 25,
          'productName': 'Test Product',
          'totalNumberOfBags': 500,
          'brokerType': 'Outsource',
          'brokerName': 'Test Broker',
          'brokerFees': 5000.0,
          'munshianaFees': 3000.0,
          'driverPhoneNumber': '**********',
          'truckNumber': 'TRK-002',
          'conveyNoteNumber': 'CN-002',
          'totalFreight': 50000.0,
          'companyFreight': 50000.0,
          'settledFreight': 45000.0,
          'paymentTransactions': [],
          // BUG: Account ID is set but account name is empty/missing
          'brokerAccountId': 'broker-account-id',
          'brokerAccount': '', // Empty name - should cause validation error
          'munshianaAccountId': 'munshiana-account-id',
          'munshianaAccount': '', // Empty name - should cause validation error
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        };

        // Convert to VoucherModel
        final voucher = VoucherModel.fromJson(voucherData);

        // Verify the problematic state
        expect(voucher.brokerAccountId, isNotEmpty);
        expect(voucher.brokerAccount,
            isEmpty); // This should trigger validation error
        expect(voucher.munshianaAccountId, isNotEmpty);
        expect(voucher.munshianaAccount,
            isEmpty); // This should trigger validation error

        // This represents the validation error scenario
        final hasValidationIssue = (voucher.brokerAccountId != null &&
                voucher.brokerAccountId!.isNotEmpty &&
                (voucher.brokerAccount == null ||
                    voucher.brokerAccount!.isEmpty)) ||
            (voucher.munshianaAccountId != null &&
                voucher.munshianaAccountId!.isNotEmpty &&
                (voucher.munshianaAccount == null ||
                    voucher.munshianaAccount!.isEmpty));

        expect(hasValidationIssue, isTrue);
      });
    });

    group('Account Number Display', () {
      test('should preserve account numbers correctly', () {
        // Test account number preservation
        final testAccounts = [
          {
            'inputNumber': '3000',
            'expectedNumber': '3000',
            'category': AccountCategory.liabilities,
          },
          {
            'inputNumber': '2000',
            'expectedNumber': '2000',
            'category': AccountCategory.liabilities,
          },
          {
            'inputNumber': '1000',
            'expectedNumber': '1000',
            'category': AccountCategory.assets,
          },
          {
            'inputNumber': '4000',
            'expectedNumber': '4000',
            'category': AccountCategory.revenue,
          },
          {
            'inputNumber': '5000',
            'expectedNumber': '5000',
            'category': AccountCategory.expenses,
          },
        ];

        for (final testCase in testAccounts) {
          final account = ChartOfAccountsModel(
            id: 'test-id',
            accountNumber: testCase['inputNumber'] as String,
            accountName: 'Test Account',
            category: testCase['category'] as AccountCategory,
            accountType: AccountType.cash,
            uid: 'test-uid',
            createdAt: DateTime.now(),
          );

          // Verify account number is preserved
          expect(account.accountNumber, equals(testCase['expectedNumber']));

          // Verify display name includes account number
          expect(account.displayName, contains(testCase['expectedNumber']));

          // Verify JSON serialization preserves account number
          final json = account.toJson();
          expect(json['accountNumber'], equals(testCase['expectedNumber']));

          // Verify deserialization preserves account number
          final deserializedAccount = ChartOfAccountsModel.fromJson(json);
          expect(deserializedAccount.accountNumber,
              equals(testCase['expectedNumber']));
        }
      });

      test('should handle account number ranges correctly', () {
        // Test account number ranges for each category
        final categoryRanges = {
          AccountCategory.assets: (start: 1000, end: 1999),
          AccountCategory.liabilities: (start: 2000, end: 2999),
          AccountCategory.equity: (start: 3000, end: 3999),
          AccountCategory.revenue: (start: 4000, end: 4999),
          AccountCategory.expenses: (start: 5000, end: 5999),
        };

        for (final entry in categoryRanges.entries) {
          final category = entry.key;
          final range = entry.value;

          // Verify range properties
          expect(category.startRange, equals(range.start));
          expect(category.endRange, equals(range.end));

          // Test account numbers within range
          final testNumber = range.start.toString();
          final account = ChartOfAccountsModel(
            id: 'test-id',
            accountNumber: testNumber,
            accountName: 'Test Account',
            category: category,
            accountType: AccountType.cash,
            uid: 'test-uid',
            createdAt: DateTime.now(),
          );

          expect(account.accountNumber, equals(testNumber));
          expect(int.parse(account.accountNumber),
              greaterThanOrEqualTo(range.start));
          expect(
              int.parse(account.accountNumber), lessThanOrEqualTo(range.end));
        }
      });
    });

    group('Voucher Form Data Mapping', () {
      test('should map Chart of Accounts selections correctly', () {
        // Simulate voucher form data creation (like getVoucherFromInput)
        final selectedBrokerChartAccount = ChartOfAccountsModel(
          id: 'broker-chart-id',
          accountNumber: '3000',
          accountName: 'Broker Fees Payable',
          category: AccountCategory.liabilities,
          accountType: AccountType.accountsPayable,
          uid: 'test-uid',
          createdAt: DateTime.now(),
        );

        final selectedMunshianaChartAccount = ChartOfAccountsModel(
          id: 'munshiana-chart-id',
          accountNumber: '3001',
          accountName: 'Munshiana Payable',
          category: AccountCategory.equity,
          accountType: AccountType.equityServiceRevenue,
          uid: 'test-uid',
          createdAt: DateTime.now(),
        );

        // Simulate the FIXED mapping logic from getVoucherFromInput
        final voucherData = {
          // FIXED: Set account names for legacy fields, not IDs
          'brokerAccount': selectedBrokerChartAccount.accountName,
          'munshianaAccount': selectedMunshianaChartAccount.accountName,
          // Chart of Accounts fields - Set IDs correctly
          'brokerAccountId': selectedBrokerChartAccount.id,
          'munshianaAccountId': selectedMunshianaChartAccount.id,
          // Other required fields
          'voucherNumber': 'TEST-003',
          'voucherStatus': 'Active',
          'departureDate': '2024-01-15',
          'driverName': 'Test Driver',
          'invoiceTasNumberList': ['TAS-003'],
          'invoiceBiltyNumberList': ['BILTY-003'],
          'weightInTons': 25,
          'productName': 'Test Product',
          'totalNumberOfBags': 500,
          'brokerType': 'Outsource',
          'brokerName': 'Test Broker',
          'brokerFees': 5000.0,
          'munshianaFees': 3000.0,
          'driverPhoneNumber': '**********',
          'truckNumber': 'TRK-003',
          'conveyNoteNumber': 'CN-003',
          'totalFreight': 50000.0,
          'paymentTransactions': [],
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        };

        // Verify the mapping is correct
        expect(voucherData['brokerAccount'],
            equals(selectedBrokerChartAccount.accountName));
        expect(voucherData['brokerAccountId'],
            equals(selectedBrokerChartAccount.id));
        expect(voucherData['munshianaAccount'],
            equals(selectedMunshianaChartAccount.accountName));
        expect(voucherData['munshianaAccountId'],
            equals(selectedMunshianaChartAccount.id));

        // Convert to VoucherModel and verify
        final voucher = VoucherModel.fromJson(voucherData);
        expect(voucher.brokerAccount,
            equals(selectedBrokerChartAccount.accountName));
        expect(voucher.brokerAccountId, equals(selectedBrokerChartAccount.id));
        expect(voucher.munshianaAccount,
            equals(selectedMunshianaChartAccount.accountName));
        expect(voucher.munshianaAccountId,
            equals(selectedMunshianaChartAccount.id));

        // Verify validation would pass
        final hasValidationIssue = (voucher.brokerAccountId != null &&
                voucher.brokerAccountId!.isNotEmpty &&
                (voucher.brokerAccount == null ||
                    voucher.brokerAccount!.isEmpty)) ||
            (voucher.munshianaAccountId != null &&
                voucher.munshianaAccountId!.isNotEmpty &&
                (voucher.munshianaAccount == null ||
                    voucher.munshianaAccount!.isEmpty));

        expect(hasValidationIssue, isFalse);
      });
    });
  });
}

/// Helper function to create test Chart of Accounts
ChartOfAccountsModel createTestAccount({
  required String id,
  required String accountNumber,
  required String accountName,
  required AccountCategory category,
  required AccountType accountType,
}) {
  return ChartOfAccountsModel(
    id: id,
    accountNumber: accountNumber,
    accountName: accountName,
    category: category,
    accountType: accountType,
    uid: 'test-uid',
    createdAt: DateTime.now(),
  );
}

/// Helper function to create test voucher data
Map<String, dynamic> createTestVoucherData({
  required String voucherNumber,
  String? brokerAccountId,
  String? brokerAccountName,
  String? munshianaAccountId,
  String? munshianaAccountName,
}) {
  return {
    'voucherNumber': voucherNumber,
    'voucherStatus': 'Active',
    'departureDate': '2024-01-15',
    'driverName': 'Test Driver',
    'invoiceTasNumberList': ['TAS-001'],
    'invoiceBiltyNumberList': ['BILTY-001'],
    'weightInTons': 25,
    'productName': 'Test Product',
    'totalNumberOfBags': 500,
    'brokerType': 'Outsource',
    'brokerName': 'Test Broker',
    'brokerFees': 5000.0,
    'munshianaFees': 3000.0,
    'driverPhoneNumber': '**********',
    'truckNumber': 'TRK-001',
    'conveyNoteNumber': 'CN-001',
    'totalFreight': 50000.0,
    'companyFreight': 50000.0,
    'settledFreight': 45000.0,
    'paymentTransactions': [],
    'brokerAccountId': brokerAccountId ?? '',
    'brokerAccount': brokerAccountName ?? '',
    'munshianaAccountId': munshianaAccountId ?? '',
    'munshianaAccount': munshianaAccountName ?? '',
    'createdAt': DateTime.now().millisecondsSinceEpoch,
  };
}
