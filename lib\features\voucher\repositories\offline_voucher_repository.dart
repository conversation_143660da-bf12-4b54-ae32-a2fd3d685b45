import 'dart:async';
import 'dart:developer';
import 'package:dartz/dartz.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/shared_services/failure_obj.dart';
import '../../../core/shared_services/success_obj.dart';
import '../../../core/services/connectivity_service.dart';
import '../../../core/services/offline_voucher_service.dart';
import '../../../core/services/sync_service.dart';
import 'voucher_repository.dart';

/// Enhanced voucher repository with offline capabilities
class OfflineVoucherRepository implements VoucherRepository {
  final VoucherRepository _onlineRepository;
  final ConnectivityService _connectivityService;
  final OfflineVoucherService _offlineVoucherService;

  OfflineVoucherRepository({
    required VoucherRepository onlineRepository,
    required ConnectivityService connectivityService,
    required OfflineVoucherService offlineVoucherService,
  })  : _onlineRepository = onlineRepository,
        _connectivityService = connectivityService,
        _offlineVoucherService = offlineVoucherService;

  @override
  Future<Either<FailureObj, SuccessObj>> createVoucher({
    required String uid,
    required Map<String, dynamic> voucher,
  }) async {
    try {
      log('OfflineVoucherRepository: Creating voucher ${voucher['voucherNumber']}');

      // Check connectivity
      if (_connectivityService.isOnline) {
        // Try online creation first
        log('OfflineVoucherRepository: Online - attempting direct creation');
        final result =
            await _onlineRepository.createVoucher(uid: uid, voucher: voucher);

        return result.fold(
          (failure) async {
            // If online creation fails, fall back to offline
            log('OfflineVoucherRepository: Online creation failed, falling back to offline: ${failure.message}');
            return await _offlineVoucherService.createVoucherOffline(
                uid: uid, voucherData: voucher);
          },
          (success) {
            log('OfflineVoucherRepository: Online creation successful');
            return Right(success);
          },
        );
      } else {
        // Create offline
        log('OfflineVoucherRepository: Offline - creating voucher offline');
        return await _offlineVoucherService.createVoucherOffline(
            uid: uid, voucherData: voucher);
      }
    } catch (e) {
      log('OfflineVoucherRepository: Unexpected error during voucher creation: $e');

      // Try offline creation as last resort
      try {
        return await _offlineVoucherService.createVoucherOffline(
            uid: uid, voucherData: voucher);
      } catch (offlineError) {
        return Left(FailureObj(
          code: 'voucher-creation-failed',
          message:
              'Failed to create voucher both online and offline: $e, $offlineError',
        ));
      }
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateVoucher({
    required String uid,
    required Map<String, dynamic> voucher,
  }) async {
    try {
      log('OfflineVoucherRepository: Updating voucher ${voucher['voucherNumber']}');

      // Check connectivity
      if (_connectivityService.isOnline) {
        // Try online update first
        log('OfflineVoucherRepository: Online - attempting direct update');
        final result =
            await _onlineRepository.updateVoucher(uid: uid, voucher: voucher);

        return result.fold(
          (failure) async {
            // If online update fails, store for later sync
            log('OfflineVoucherRepository: Online update failed, storing for sync: ${failure.message}');
            // For updates, we might want to store the update operation for later sync
            // This would require extending the offline service to handle updates
            return Left(failure);
          },
          (success) {
            log('OfflineVoucherRepository: Online update successful');
            return Right(success);
          },
        );
      } else {
        // Store update for later sync
        log('OfflineVoucherRepository: Offline - storing update for later sync');
        return Left(FailureObj(
          code: 'offline-update-not-supported',
          message:
              'Voucher updates are not supported in offline mode. Changes will be synced when online.',
        ));
      }
    } catch (e) {
      log('OfflineVoucherRepository: Unexpected error during voucher update: $e');
      return Left(FailureObj(
        code: 'voucher-update-failed',
        message: 'Failed to update voucher: $e',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteVoucher({
    required String uid,
    required String voucherNumber,
  }) async {
    try {
      log('OfflineVoucherRepository: Deleting voucher $voucherNumber');

      // Check connectivity
      if (_connectivityService.isOnline) {
        // Try online deletion
        log('OfflineVoucherRepository: Online - attempting direct deletion');
        return await _onlineRepository.deleteVoucher(
            uid: uid, voucherNumber: voucherNumber);
      } else {
        // Store deletion for later sync
        log('OfflineVoucherRepository: Offline - deletion not supported');
        return Left(FailureObj(
          code: 'offline-delete-not-supported',
          message: 'Voucher deletion is not supported in offline mode.',
        ));
      }
    } catch (e) {
      log('OfflineVoucherRepository: Unexpected error during voucher deletion: $e');
      return Left(FailureObj(
        code: 'voucher-delete-failed',
        message: 'Failed to delete voucher: $e',
      ));
    }
  }

  @override
  Stream<List<DocumentChange>> listenToVoucher({required String uid}) {
    // For offline repository, we primarily use the online repository's stream
    // but we could enhance this to merge with offline voucher changes
    return _onlineRepository.listenToVoucher(uid: uid);
  }

  /// Get vouchers (additional method not in interface)
  Future<Either<FailureObj, List<Map<String, dynamic>>>> getVouchers({
    required String uid,
  }) async {
    try {
      log('OfflineVoucherRepository: Getting vouchers for uid: $uid');

      if (_connectivityService.isOnline) {
        // Get online vouchers - this would need to be implemented in the online repository
        log('OfflineVoucherRepository: Online - would fetch from server if method existed');
        // Since getVouchers doesn't exist in VoucherRepository interface,
        // we'll just return offline vouchers for now
        return _getOfflineVouchers();
      } else {
        // Return offline vouchers only
        log('OfflineVoucherRepository: Offline - returning local vouchers');
        return _getOfflineVouchers();
      }
    } catch (e) {
      log('OfflineVoucherRepository: Unexpected error getting vouchers: $e');
      return Left(FailureObj(
        code: 'get-vouchers-failed',
        message: 'Failed to get vouchers: $e',
      ));
    }
  }

  /// Get voucher by number (additional method not in interface)
  Future<Either<FailureObj, Map<String, dynamic>?>> getVoucherByNumber({
    required String uid,
    required String voucherNumber,
  }) async {
    try {
      log('OfflineVoucherRepository: Getting voucher by number: $voucherNumber');

      // Check offline vouchers first
      final offlineVoucher =
          _offlineVoucherService.getOfflineVoucher(voucherNumber);
      if (offlineVoucher != null) {
        log('OfflineVoucherRepository: Found voucher in offline storage');
        return Right(offlineVoucher.toJson());
      }

      if (_connectivityService.isOnline) {
        // Since getVoucherByNumber doesn't exist in VoucherRepository interface,
        // we'll just return null for now
        log('OfflineVoucherRepository: Online - method not available in interface');
        return Right(null);
      } else {
        // Not found offline and no connectivity
        log('OfflineVoucherRepository: Voucher not found offline and no connectivity');
        return Right(null);
      }
    } catch (e) {
      log('OfflineVoucherRepository: Unexpected error getting voucher by number: $e');
      return Left(FailureObj(
        code: 'get-voucher-failed',
        message: 'Failed to get voucher: $e',
      ));
    }
  }

  /// Get offline vouchers only
  Either<FailureObj, List<Map<String, dynamic>>> _getOfflineVouchers() {
    try {
      final offlineVouchers = _offlineVoucherService.offlineVouchers
          .map((voucher) => voucher.toJson())
          .toList();

      log('OfflineVoucherRepository: Returning ${offlineVouchers.length} offline vouchers');
      return Right(offlineVouchers);
    } catch (e) {
      log('OfflineVoucherRepository: Failed to get offline vouchers: $e');
      return Left(FailureObj(
        code: 'offline-vouchers-failed',
        message: 'Failed to get offline vouchers: $e',
      ));
    }
  }

  /// Merge online vouchers with offline vouchers
  Either<FailureObj, List<Map<String, dynamic>>> _mergeWithOfflineVouchers(
      List<Map<String, dynamic>> onlineVouchers) {
    try {
      final mergedVouchers = <Map<String, dynamic>>[];
      final onlineVoucherNumbers = <String>{};

      // Add online vouchers
      for (final voucher in onlineVouchers) {
        mergedVouchers.add(voucher);
        onlineVoucherNumbers.add(voucher['voucherNumber'] as String);
      }

      // Add offline vouchers that are not already online
      for (final offlineVoucher in _offlineVoucherService.offlineVouchers) {
        if (!onlineVoucherNumbers.contains(offlineVoucher.voucherNumber)) {
          final voucherData = offlineVoucher.toJson();
          // Add offline indicator
          voucherData['isOffline'] = true;
          voucherData['syncStatus'] = offlineVoucher.syncStatusDescription;
          mergedVouchers.add(voucherData);
        }
      }

      log('OfflineVoucherRepository: Merged ${onlineVouchers.length} online + ${_offlineVoucherService.offlineVouchers.length} offline vouchers');
      return Right(mergedVouchers);
    } catch (e) {
      log('OfflineVoucherRepository: Failed to merge vouchers: $e');
      return Left(FailureObj(
        code: 'merge-vouchers-failed',
        message: 'Failed to merge vouchers: $e',
      ));
    }
  }

  /// Get offline voucher statistics
  Map<String, dynamic> getOfflineStats() {
    final offlineVouchers = _offlineVoucherService.offlineVouchers;
    final pendingVouchers = _offlineVoucherService.getPendingVouchers();

    return {
      'totalOfflineVouchers': offlineVouchers.length,
      'pendingVouchers': pendingVouchers.length,
      'syncedVouchers': offlineVouchers.length - pendingVouchers.length,
      'isOnline': _connectivityService.isOnline,
    };
  }

  /// Force sync of offline vouchers
  Future<Either<FailureObj, SuccessObj>> syncOfflineVouchers() async {
    if (_connectivityService.isOffline) {
      return Left(FailureObj(
        code: 'offline',
        message: 'Cannot sync while offline',
      ));
    }

    try {
      final syncService = Get.find<SyncService>();
      final result = await syncService.syncPendingOperations();

      return result.fold(
        (failure) => Left(failure),
        (success) => Right(success),
      );
    } catch (e) {
      log('OfflineVoucherRepository: Failed to sync offline vouchers: $e');
      return Left(FailureObj(
        code: 'sync-failed',
        message: 'Failed to sync offline vouchers: $e',
      ));
    }
  }
}
