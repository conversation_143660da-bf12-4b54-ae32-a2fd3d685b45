import 'dart:developer';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/features/finance/brokers/repositories/broker_payment_repository.dart';
import 'package:logestics/models/finance/broker_model.dart';
import 'package:uuid/uuid.dart';

class BrokerFinancialService {
  final BrokerPaymentRepository _brokerPaymentRepository;

  BrokerFinancialService(this._brokerPaymentRepository);

  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';
  String get _currentUserName =>
      FirebaseAuth.instance.currentUser?.displayName ?? 'System';

  /// Record a payment made to a broker (tracking only - no real accounting entries)
  Future<Either<FailureObj, BrokerPaymentModel>> recordBrokerPayment({
    required String brokerId,
    required String brokerName,
    required double amount,
    required BrokerPaymentMethod method,
    required DateTime paymentDate,
    String? paymentAccountName, // For display/tracking purposes only
    String? notes,
    String? checkNumber,
    String? bankName,
    DateTime? checkIssueDate,
    DateTime? checkExpiryDate,
    String? referenceNumber,
  }) async {
    try {
      log('Recording broker payment: $amount to $brokerName');

      final paymentId = const Uuid().v4();

      // Create the broker payment model (tracking only)
      final payment = BrokerPaymentModel(
        id: paymentId,
        brokerId: brokerId,
        brokerName: brokerName,
        amount: amount,
        method: method,
        status: BrokerPaymentStatus.paid,
        paymentDate: paymentDate,
        notes: notes,
        accountId: 'tracking_only', // No real account integration
        accountName: paymentAccountName ?? 'Payment Account',
        checkNumber: checkNumber,
        bankName: bankName,
        checkIssueDate: checkIssueDate,
        checkExpiryDate: checkExpiryDate,
        referenceNumber: referenceNumber,
        uid: _uid,
        createdBy: _currentUserName,
      );

      // Save the payment (tracking only - no journal entries)
      final paymentResult =
          await _brokerPaymentRepository.createBrokerPayment(payment);

      return paymentResult.fold(
        (failure) => Left(failure),
        (_) async {
          // Create broker transaction record
          await _createBrokerTransactionRecord(
            brokerId: brokerId,
            brokerName: brokerName,
            type: BrokerTransactionType.payment,
            amount: amount,
            transactionDate: paymentDate,
            description: 'Payment to broker - ${referenceNumber ?? paymentId}',
            paymentId: paymentId,
          );

          log('Successfully recorded broker payment: $paymentId');
          return Right(payment);
        },
      );
    } catch (e) {
      log('Error recording broker payment: $e');
      return Left(FailureObj(
        code: 'broker-payment-error',
        message: 'Failed to record broker payment: $e',
      ));
    }
  }

  /// Record a broker fee from voucher (tracking only)
  Future<Either<FailureObj, void>> recordBrokerFee({
    required String brokerId,
    required String brokerName,
    required double amount,
    required DateTime transactionDate,
    required String voucherId,
    required String voucherNumber,
  }) async {
    try {
      log('Recording broker fee: $amount for voucher $voucherNumber');

      // Create broker transaction record (tracking only)
      await _createBrokerTransactionRecord(
        brokerId: brokerId,
        brokerName: brokerName,
        type: BrokerTransactionType.fee,
        amount: amount,
        transactionDate: transactionDate,
        description: 'Broker fee for voucher $voucherNumber',
        voucherId: voucherId,
        voucherNumber: voucherNumber,
      );

      log('Successfully recorded broker fee for voucher: $voucherNumber');
      return const Right(null);
    } catch (e) {
      log('Error recording broker fee: $e');
      return Left(FailureObj(
        code: 'broker-fee-error',
        message: 'Failed to record broker fee: $e',
      ));
    }
  }

  /// Get broker financial summary
  Future<Either<FailureObj, Map<String, dynamic>>> getBrokerFinancialSummary(
      String brokerId) async {
    try {
      return await _brokerPaymentRepository.getBrokerFinancialSummary(brokerId);
    } catch (e) {
      log('Error getting broker financial summary: $e');
      return Left(FailureObj(
        code: 'broker-summary-error',
        message: 'Failed to get broker financial summary: $e',
      ));
    }
  }

  /// Get broker transaction history
  Future<Either<FailureObj, List<BrokerTransactionModel>>>
      getBrokerTransactionHistory(String brokerId) async {
    try {
      return await _brokerPaymentRepository
          .getBrokerTransactionsByBrokerId(brokerId);
    } catch (e) {
      log('Error getting broker transaction history: $e');
      return Left(FailureObj(
        code: 'broker-history-error',
        message: 'Failed to get broker transaction history: $e',
      ));
    }
  }

  /// Get broker payment history
  Future<Either<FailureObj, List<BrokerPaymentModel>>> getBrokerPaymentHistory(
      String brokerId) async {
    try {
      return await _brokerPaymentRepository
          .getBrokerPaymentsByBrokerId(brokerId);
    } catch (e) {
      log('Error getting broker payment history: $e');
      return Left(FailureObj(
        code: 'broker-payment-history-error',
        message: 'Failed to get broker payment history: $e',
      ));
    }
  }

  /// Calculate broker balance
  Future<Either<FailureObj, double>> calculateBrokerBalance(
      String brokerId) async {
    try {
      return await _brokerPaymentRepository.calculateBrokerBalance(brokerId);
    } catch (e) {
      log('Error calculating broker balance: $e');
      return Left(FailureObj(
        code: 'broker-balance-error',
        message: 'Failed to calculate broker balance: $e',
      ));
    }
  }

  /// Create broker transaction record (tracking only)
  Future<void> _createBrokerTransactionRecord({
    required String brokerId,
    required String brokerName,
    required BrokerTransactionType type,
    required double amount,
    required DateTime transactionDate,
    required String description,
    String? voucherId,
    String? voucherNumber,
    String? paymentId,
  }) async {
    final transaction = BrokerTransactionModel(
      id: const Uuid().v4(),
      brokerId: brokerId,
      brokerName: brokerName,
      type: type,
      amount: amount,
      transactionDate: transactionDate,
      description: description,
      voucherId: voucherId,
      voucherNumber: voucherNumber,
      paymentId: paymentId,
      journalEntryId: null, // No journal entries for tracking-only system
      uid: _uid,
    );

    await _brokerPaymentRepository.createBrokerTransaction(transaction);
  }
}
