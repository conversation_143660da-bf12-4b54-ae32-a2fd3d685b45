/// Model for individual calculation steps in a formula
class FormulaStepModel {
  final String stepId;
  final String stepName; // User-friendly name like "Calculate Weight Rate"
  final String
      formula; // Mathematical expression like "distanceInKilometers × rateValue"
  final String
      resultVariable; // Variable name to store result like "weightRate"
  final String? description; // Optional description for the step

  FormulaStepModel({
    required this.stepId,
    required this.stepName,
    required this.formula,
    required this.resultVariable,
    this.description,
  });

  /// Factory constructor from JSON
  factory FormulaStepModel.fromJson(Map<String, dynamic> json) {
    return FormulaStepModel(
      stepId: json['stepId']?.toString() ?? '',
      stepName: json['stepName']?.toString() ?? '',
      formula: json['formula']?.toString() ?? '',
      resultVariable: json['resultVariable']?.toString() ?? '',
      description: json['description']?.toString(),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'stepId': stepId,
      'stepName': stepName,
      'formula': formula,
      'resultVariable': resultVariable,
      'description': description,
    };
  }

  /// Create a copy with updated fields
  FormulaStepModel copyWith({
    String? stepId,
    String? stepName,
    String? formula,
    String? resultVariable,
    String? description,
  }) {
    return FormulaStepModel(
      stepId: stepId ?? this.stepId,
      stepName: stepName ?? this.stepName,
      formula: formula ?? this.formula,
      resultVariable: resultVariable ?? this.resultVariable,
      description: description ?? this.description,
    );
  }

  @override
  String toString() {
    return 'FormulaStepModel(stepId: $stepId, stepName: $stepName, formula: $formula, resultVariable: $resultVariable, description: $description)';
  }
}

/// Model for complete calculation formula with multiple steps
class CalculationFormulaModel {
  final String formulaId;
  final String
      formulaName; // User-friendly name like "Standard Ton-KM Calculation"
  final String? description; // Optional description
  final List<FormulaStepModel> steps; // Sequential calculation steps
  final String finalResultVariable; // Which step result is the final amount
  final bool isActive; // Whether this formula is currently active

  CalculationFormulaModel({
    required this.formulaId,
    required this.formulaName,
    this.description,
    required this.steps,
    required this.finalResultVariable,
    this.isActive = true,
  });

  /// Factory constructor from JSON
  factory CalculationFormulaModel.fromJson(Map<String, dynamic> json) {
    return CalculationFormulaModel(
      formulaId: json['formulaId']?.toString() ?? '',
      formulaName: json['formulaName']?.toString() ?? '',
      description: json['description']?.toString(),
      steps: json['steps'] != null
          ? (json['steps'] as List)
              .map((step) => FormulaStepModel.fromJson(step))
              .toList()
          : [],
      finalResultVariable: json['finalResultVariable']?.toString() ?? '',
      isActive: json['isActive'] ?? true,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'formulaId': formulaId,
      'formulaName': formulaName,
      'description': description,
      'steps': steps.map((step) => step.toJson()).toList(),
      'finalResultVariable': finalResultVariable,
      'isActive': isActive,
    };
  }

  /// Create a copy with updated fields
  CalculationFormulaModel copyWith({
    String? formulaId,
    String? formulaName,
    String? description,
    List<FormulaStepModel>? steps,
    String? finalResultVariable,
    bool? isActive,
  }) {
    return CalculationFormulaModel(
      formulaId: formulaId ?? this.formulaId,
      formulaName: formulaName ?? this.formulaName,
      description: description ?? this.description,
      steps: steps ?? this.steps,
      finalResultVariable: finalResultVariable ?? this.finalResultVariable,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Get step by result variable name
  FormulaStepModel? getStepByResultVariable(String variableName) {
    try {
      return steps.firstWhere((step) => step.resultVariable == variableName);
    } catch (e) {
      return null;
    }
  }

  /// Validate formula structure
  bool isValid() {
    if (steps.isEmpty) return false;
    if (finalResultVariable.isEmpty) return false;

    // Check if final result variable exists in steps
    final finalStep = getStepByResultVariable(finalResultVariable);
    if (finalStep == null) return false;

    // Check for duplicate result variables
    final resultVariables = steps.map((step) => step.resultVariable).toList();
    final uniqueVariables = resultVariables.toSet();
    if (resultVariables.length != uniqueVariables.length) return false;

    return true;
  }

  @override
  String toString() {
    return 'CalculationFormulaModel(formulaId: $formulaId, formulaName: $formulaName, steps: ${steps.length}, finalResultVariable: $finalResultVariable, isActive: $isActive)';
  }
}

class SlabModel {
  String slabId;
  final String slabName;
  final DateTime startDate;
  final DateTime expiryDate;
  final DateTime createdAt;
  final bool isActive;
  final List<SlabRateModel> rates;
  final CalculationFormulaModel? calculationFormula; // Optional custom formula

  SlabModel({
    required this.slabId,
    required this.slabName,
    required this.startDate,
    required this.expiryDate,
    required this.createdAt,
    this.isActive = true,
    required this.rates,
    this.calculationFormula,
  });

  /// Check if slab is valid for a given date
  bool isValidForDate(DateTime date) {
    return isActive &&
        date.isAfter(startDate.subtract(const Duration(days: 1))) &&
        date.isBefore(expiryDate.add(const Duration(days: 1)));
  }

  /// Check if slab is expired
  bool get isExpired {
    return DateTime.now().isAfter(expiryDate);
  }

  /// Check if slab uses custom calculation formula
  bool get hasCustomFormula {
    return calculationFormula != null && calculationFormula!.isValid();
  }

  /// Get calculation method description
  String get calculationMethodDescription {
    if (hasCustomFormula) {
      return 'Custom Formula: ${calculationFormula!.formulaName}';
    }
    return 'Standard Calculation: Total Tons × Distance × Rate';
  }

  /// Get rate for a specific district
  SlabRateModel? getRateForDistrict(String districtId) {
    try {
      return rates.firstWhere((rate) => rate.districtId == districtId);
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'slabId': slabId,
      'slabName': slabName,
      'startDate': startDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'isActive': isActive,
      'rates': rates.map((rate) => rate.toJson()).toList(),
      'calculationFormula': calculationFormula?.toJson(),
    };
  }

  factory SlabModel.fromJson(Map<String, dynamic> json) {
    return SlabModel(
      slabId: json['slabId'] ?? '',
      slabName: json['slabName'] ?? '',
      startDate: DateTime.parse(json['startDate']),
      expiryDate: DateTime.parse(json['expiryDate']),
      createdAt: DateTime.parse(json['createdAt']),
      isActive: json['isActive'] ?? true,
      rates: (json['rates'] as List<dynamic>?)
              ?.map((rateJson) => SlabRateModel.fromJson(rateJson))
              .toList() ??
          [],
      calculationFormula: json['calculationFormula'] != null
          ? CalculationFormulaModel.fromJson(json['calculationFormula'])
          : null,
    );
  }

  @override
  String toString() {
    return 'SlabModel{slabId: $slabId, slabName: $slabName, startDate: $startDate, expiryDate: $expiryDate, isActive: $isActive, ratesCount: ${rates.length}, hasCustomFormula: $hasCustomFormula}';
  }
}

class SlabRateModel {
  final String regionId;
  final String regionName;
  final String districtId;
  final String districtName;
  final double hmtRate; // HMT (Fuel + Non-Fuel) Rate
  final double nonFuelRate; // Non-Fuel Rate
  final Map<String, dynamic>
      customColumns; // For dynamic columns like WHT, GST, etc.

  SlabRateModel({
    required this.regionId,
    required this.regionName,
    required this.districtId,
    required this.districtName,
    required this.hmtRate,
    required this.nonFuelRate,
    this.customColumns = const {},
  });

  /// Get value from custom column
  dynamic getCustomValue(String columnName) {
    return customColumns[columnName];
  }

  /// Create a copy with updated custom columns
  SlabRateModel copyWithCustomColumns(Map<String, dynamic> newCustomColumns) {
    return SlabRateModel(
      regionId: regionId,
      regionName: regionName,
      districtId: districtId,
      districtName: districtName,
      hmtRate: hmtRate,
      nonFuelRate: nonFuelRate,
      customColumns: {...customColumns, ...newCustomColumns},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'regionId': regionId,
      'regionName': regionName,
      'districtId': districtId,
      'districtName': districtName,
      'hmtRate': hmtRate,
      'nonFuelRate': nonFuelRate,
      'customColumns': customColumns,
    };
  }

  factory SlabRateModel.fromJson(Map<String, dynamic> json) {
    return SlabRateModel(
      regionId: json['regionId'] ?? '',
      regionName: json['regionName'] ?? '',
      districtId: json['districtId'] ?? '',
      districtName: json['districtName'] ?? '',
      hmtRate: (json['hmtRate'] ?? 0.0).toDouble(),
      nonFuelRate: (json['nonFuelRate'] ?? 0.0).toDouble(),
      customColumns: Map<String, dynamic>.from(json['customColumns'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'SlabRateModel{regionId: $regionId, regionName: $regionName, districtId: $districtId, districtName: $districtName, hmtRate: $hmtRate, nonFuelRate: $nonFuelRate, customColumns: $customColumns}';
  }
}
