import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/constants/formatters.dart';
import 'package:logestics/core/utils/constants/validators.dart';
import 'package:logestics/core/utils/widgets/custom_button.dart';
import 'package:logestics/core/utils/widgets/section_widget.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/models/invoice_model.dart';

import '../../../../core/utils/widgets/my_dropdown_field.dart';
import '../../../../core/utils/widgets/my_text_field.dart';
import '../controllers/invoice_form_controller.dart';

class InvoiceFormView extends GetView<InvoiceFormController> {
  final InvoiceModel? currentInvoice;
  final bool readOnly;

  const InvoiceFormView({
    super.key,
    this.currentInvoice,
    this.readOnly = false,
  });

  @override
  Widget build(BuildContext context) {
    // Initialize controller with current invoice if editing
    if (currentInvoice != null) {
      controller.setCurrentInvoice(currentInvoice!);
    }

    notifier = Provider.of(context, listen: true);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;
    final isMediumScreen = size.width < 1200 && size.width >= 600;

    return Form(
      key: controller.formKey,
      child: Scaffold(
        backgroundColor: notifier.getBgColor.withAlpha(80),
        body: Dialog(
          backgroundColor: notifier.getBgColor,
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          child: Container(
            width: size.width * 0.9,
            height: size.height * 0.9,
            padding: EdgeInsets.all(isSmallScreen ? 12 : 24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  notifier.getBgColor,
                  notifier.getBgColor.withAlpha(95),
                ],
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header Section
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: notifier.text.withAlpha(10),
                        width: 1,
                      ),
                    ),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.topRight,
                      colors: [
                        notifier.getBgColor.withAlpha(80),
                        notifier.getBgColor,
                      ],
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          currentInvoice != null
                              ? readOnly
                                  ? "View Invoice"
                                  : "Edit Invoice"
                              : AppStrings.addNewInvoice,
                          overflow: TextOverflow.ellipsis,
                          style: AppTextStyles.titleStyle.copyWith(
                            color: notifier.text,
                            fontSize: isSmallScreen ? 20 : 24,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: notifier.text.withAlpha(10),
                        ),
                        child: IconButton(
                          onPressed: () => Get.back(),
                          icon: Icon(Icons.close, color: notifier.text),
                          padding: const EdgeInsets.all(8),
                          constraints: const BoxConstraints(),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Form Content
                Expanded(
                  child: SingleChildScrollView(
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        return Obx(() => Wrap(
                              spacing: 24,
                              runSpacing: 24,
                              children: [
                                // Invoice Basic Information
                                SectionWidget(
                                  title: "Invoice Information",
                                  fields: [
                                    MyTextFormField(
                                      titleText: AppStrings.invoiceNumber,
                                      labelText: AppStrings.invoiceNumber,
                                      hintText: AppStrings.invoiceNumberHint,
                                      controller:
                                          controller.invoiceNumberController,
                                      textInputFormatter: [digitsOnlyFormatter],
                                      validator: validateInvoiceNumber,
                                      enabled: false,
                                      readOnly: readOnly,
                                    ),
                                    MyDropdownFormField(
                                      titletext: AppStrings.invoiceStatus,
                                      hinttext: AppStrings.select,
                                      items: AppStrings.invoiceStatusOptions,
                                      initalValue: controller
                                          .invoiceStatusSelected.value,
                                      onChanged: readOnly
                                          ? null
                                          : (value) {
                                              controller.setStatus = value;
                                            },
                                      validator: validateInvoiceStatus,
                                      validatorMode:
                                          AutovalidateMode.onUserInteraction,
                                      enabled: !readOnly,
                                    ),
                                    MyTextFormField(
                                      titleText: AppStrings.tasNumber,
                                      labelText: AppStrings.tasNumber,
                                      hintText: AppStrings.tasNumberHint,
                                      controller:
                                          controller.tasNumberController,
                                      validator: validateTasNumber,
                                      textInputFormatter: [digitsOnlyFormatter],
                                      readOnly: readOnly,
                                      onFieldSubmitted: readOnly
                                          ? null
                                          : (value) {
                                              if (value.isNotEmpty) {
                                                controller
                                                    .populateFromTasNumber(
                                                        value);
                                              }
                                            },
                                    ),
                                  ],
                                  isSmallScreen: isSmallScreen,
                                  isMediumScreen: isMediumScreen,
                                ),

                                // Product Details
                                SectionWidget(
                                  title: "Product Details",
                                  fields: [
                                    MyTextFormField(
                                      titleText: AppStrings.productName,
                                      labelText: AppStrings.productName,
                                      hintText: AppStrings.productNameHint,
                                      controller:
                                          controller.productNameController,
                                      validator: validateProductName,
                                      readOnly: readOnly,
                                    ),
                                    MyTextFormField(
                                      titleText: AppStrings.numberOfBags,
                                      labelText: AppStrings.quantity,
                                      hintText: AppStrings.numberOfBagsHint,
                                      controller:
                                          controller.numberOfBagsController,
                                      textInputFormatter: [digitsOnlyFormatter],
                                      validator: validateNumberOfBags,
                                      readOnly: readOnly,
                                    ),
                                    MyTextFormField(
                                      titleText: AppStrings.weightPerBag,
                                      labelText: AppStrings.weightPerBag,
                                      hintText: AppStrings.weightPerBagHint,
                                      controller:
                                          controller.weightPerBagController,
                                      textInputFormatter: [digitsOnlyFormatter],
                                      validator: validateWeightPerBag,
                                      readOnly: readOnly,
                                    )
                                  ],
                                  isSmallScreen: isSmallScreen,
                                  isMediumScreen: isMediumScreen,
                                ),

                                // Customer & Transport Details
                                SectionWidget(
                                  title: "Customer & Transport Details",
                                  fields: [
                                    MyTextFormField(
                                      titleText: AppStrings.customerName,
                                      labelText: AppStrings.cusAgeName,
                                      hintText: AppStrings.customerNameHint,
                                      controller:
                                          controller.customerNameController,
                                      validator: validateCustomerName,
                                      readOnly: readOnly,
                                    ),
                                    MyTextFormField(
                                      titleText: AppStrings.truckWagonNumber,
                                      labelText: AppStrings.truckWagonNumber,
                                      hintText: 'ABC-1234',
                                      controller:
                                          controller.truckNumberController,
                                      validator: validateTruckNumber,
                                      textInputFormatter: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp(r'[A-Za-z0-9\s,.-/]')),
                                        // LengthLimitingTextInputFormatter(8),
                                        // MaskTextInputFormatter(
                                        //     mask: 'UUU-####',
                                        //     filter: {
                                        //       "U": RegExp(r'[A-Za-z]'),
                                        //       "#": RegExp(r'[0-9]')
                                        //     })
                                      ],
                                      readOnly: readOnly,
                                    ),
                                    MyTextFormField(
                                        titleText: AppStrings.conveyNoteNumber,
                                        labelText: AppStrings.conveyNoteNumber,
                                        hintText: AppStrings.conveyanceNoteHint,
                                        controller: controller
                                            .conveyNoteNumberController,
                                        validator: validateConveyNoteNumber,
                                        textInputFormatter: [
                                          digitsOnlyFormatter
                                        ],
                                        readOnly: readOnly),
                                    // Bilty Number field - always visible
                                    MyTextFormField(
                                        titleText: "Bilty Number",
                                        labelText: "Bilty Number",
                                        hintText: "Enter bilty number",
                                        controller:
                                            controller.biltyNumberController,
                                        textInputFormatter: [
                                          digitsOnlyFormatter
                                        ],
                                        readOnly: readOnly),
                                    MyTextFormField(
                                      titleText: AppStrings.orderDate,
                                      hintText: AppStrings.select,
                                      labelText: AppStrings.orderDate,
                                      controller:
                                          controller.orderDateController,
                                      validator: validateOrderDate,
                                      onTap: () {
                                        controller.selectOrderDate(context);
                                      },
                                      suffixIcon: IconButton(
                                        onPressed: () {
                                          controller.selectOrderDate(context);
                                        },
                                        icon: Icon(Icons.calendar_today_rounded,
                                            color: notifier.text),
                                      ),
                                      readOnly: readOnly,
                                    )
                                  ],
                                  isSmallScreen: isSmallScreen,
                                  isMediumScreen: isMediumScreen,
                                ),

                                SectionWidget(
                                  title: "Consignor & Delivery Details",
                                  fields: [
                                    MyTextFormField(
                                      titleText: AppStrings.consignorName,
                                      labelText: AppStrings.consignorName,
                                      hintText: AppStrings.consignorNameHint,
                                      controller:
                                          controller.consignorNameController,
                                      validator: validateConsignorName,
                                      readOnly: readOnly,
                                    ),
                                    MyDropdownFormField(
                                      titletext: AppStrings.deliveryMode,
                                      hinttext: AppStrings.select,
                                      items: AppStrings.deliveryModeOptions,
                                      initalValue: controller
                                              .deliveryModeSelected
                                              .value
                                              .isNotEmpty
                                          ? AppStrings.deliveryModeOptions
                                                  .contains(controller
                                                      .deliveryModeSelected
                                                      .value)
                                              ? controller
                                                  .deliveryModeSelected.value
                                              : null
                                          : null,
                                      validator: validateDeliveryMode,
                                      onChanged: (value) {
                                        controller.setDeliveryMode = value;
                                      },
                                      enabled: !readOnly,
                                    ),
                                    // MyTextFormField(
                                    //   titleText: AppStrings.consignorPickUpAddress,
                                    //   labelText: AppStrings.pickUpAddress,
                                    //   hintText: AppStrings.pickUpAddressHint,
                                    //   controller: controller
                                    //       .consignorPickUpAddressController,
                                    //   validator: validateConsignorPickUpAddress,
                                    // ),
                                  ],
                                  isSmallScreen: isSmallScreen,
                                  isMediumScreen: isMediumScreen,
                                ),
                                // Distance Management

                                SectionWidget(
                                  title: "Location & Distance Management",
                                  fields: [
                                    Obx(() => MyDropdownFormField(
                                          titletext: "District",
                                          hinttext: "Select District",
                                          items: controller.districts
                                              .map((d) => d.districtName)
                                              .toList(),
                                          initalValue: controller
                                              .selectedDistrict
                                              .value
                                              ?.districtName,
                                          onChanged: (value) {
                                            if (value != null) {
                                              final district = controller
                                                  .districts
                                                  .firstWhere(
                                                (d) => d.districtName == value,
                                              );
                                              controller
                                                  .onDistrictSelected(district);
                                            }
                                          },
                                          validator: (value) =>
                                              value == null || value.isEmpty
                                                  ? 'Please select a district'
                                                  : null,
                                          enabled: !readOnly,
                                        )),
                                    Obx(() => MyDropdownFormField(
                                          titletext: "Station",
                                          hinttext: "Select Station",
                                          items: controller.stations
                                              .map((s) => s.stationName)
                                              .toList(),
                                          initalValue: controller
                                              .selectedStation
                                              .value
                                              ?.stationName,
                                          onChanged: (value) {
                                            if (value != null) {
                                              final station = controller
                                                  .stations
                                                  .firstWhere(
                                                (s) => s.stationName == value,
                                              );
                                              controller
                                                  .onStationSelected(station);
                                            }
                                          },
                                          validator: (value) =>
                                              value == null || value.isEmpty
                                                  ? 'Please select a station'
                                                  : null,
                                          enabled: !readOnly,
                                        )),
                                    Obx(() => MyDropdownFormField(
                                          titletext: "From Place",
                                          hinttext: "Select From Place",
                                          items: controller.places
                                              .map((p) => p.fromPlace)
                                              .toList(),
                                          initalValue: controller
                                              .selectedPlace.value?.fromPlace,
                                          onChanged: (value) {
                                            if (value != null) {
                                              final place =
                                                  controller.places.firstWhere(
                                                (p) => p.fromPlace == value,
                                              );
                                              controller.onPlaceSelected(place);
                                            }
                                          },
                                          validator: (value) =>
                                              value == null || value.isEmpty
                                                  ? 'Please select from place'
                                                  : null,
                                          enabled: !readOnly,
                                        )),
                                    Obx(() => MyTextFormField(
                                          titleText: "Distance (km)",
                                          labelText: controller.distance.value
                                              .toString(),
                                          hintText: "Auto-calculated",
                                          enabled: false,
                                          readOnly: readOnly,
                                        )),
                                  ],
                                  isSmallScreen: isSmallScreen,
                                  isMediumScreen: isMediumScreen,
                                ),
                              ],
                            ));
                      },
                    ),
                  ),
                ),

                // Footer Buttons
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(
                        color: notifier.text.withAlpha(10),
                        width: 1,
                      ),
                    ),
                    gradient: LinearGradient(
                      begin: Alignment.bottomLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        notifier.getBgColor,
                        notifier.getBgColor.withAlpha(80),
                      ],
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      CustomButton.danger(
                        onPressed: () => Get.back(),
                        text: AppStrings.cancel,
                        minimumSize: Size(isSmallScreen ? 100 : 130, 50),
                      ),
                      if (!readOnly) ...[
                        const SizedBox(width: 16),
                        CustomButton(
                          text: "Fill Dummy",
                          onPressed: () => controller.fillDummyDetails(),
                          backgroundColor: Colors.amber,
                          minimumSize: Size(isSmallScreen ? 100 : 130, 50),
                        ),
                        const SizedBox(width: 16),
                        CustomButton.primary(
                          onPressed: () => controller.saveInvoice(),
                          text: AppStrings.save,
                          minimumSize: Size(isSmallScreen ? 100 : 130, 50),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
