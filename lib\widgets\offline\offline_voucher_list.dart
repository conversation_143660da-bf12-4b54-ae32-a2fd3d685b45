import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/offline/offline_voucher_model.dart';
import '../../models/offline/offline_transaction_state.dart';
import '../../core/services/offline_voucher_service.dart';

/// Widget that displays a list of offline vouchers with their sync status
class OfflineVoucherList extends StatelessWidget {
  final bool showSyncedVouchers;
  final VoidCallback? onRefresh;
  
  const OfflineVoucherList({
    super.key,
    this.showSyncedVouchers = false,
    this.onRefresh,
  });
  
  @override
  Widget build(BuildContext context) {
    return GetBuilder<OfflineVoucherService>(
      builder: (offlineVoucherService) {
        if (!offlineVoucherService.isInitialized) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        final vouchers = _filterVouchers(offlineVoucherService.offlineVouchers);
        
        if (vouchers.isEmpty) {
          return _buildEmptyState(context);
        }
        
        return RefreshIndicator(
          onRefresh: () async {
            onRefresh?.call();
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: vouchers.length,
            itemBuilder: (context, index) {
              final voucher = vouchers[index];
              return OfflineVoucherCard(
                voucher: voucher,
                onTap: () => _showVoucherDetails(context, voucher),
              );
            },
          ),
        );
      },
    );
  }
  
  List<OfflineVoucherModel> _filterVouchers(List<OfflineVoucherModel> vouchers) {
    if (showSyncedVouchers) {
      return vouchers;
    } else {
      return vouchers.where((v) => v.syncState != OfflineTransactionState.synced).toList();
    }
  }
  
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            showSyncedVouchers ? 'No offline vouchers' : 'No pending vouchers',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            showSyncedVouchers 
                ? 'All vouchers have been synced'
                : 'All vouchers are up to date',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
  
  void _showVoucherDetails(BuildContext context, OfflineVoucherModel voucher) {
    showDialog(
      context: context,
      builder: (context) => OfflineVoucherDetailsDialog(voucher: voucher),
    );
  }
}

/// Card widget for displaying an offline voucher
class OfflineVoucherCard extends StatelessWidget {
  final OfflineVoucherModel voucher;
  final VoidCallback? onTap;
  
  const OfflineVoucherCard({
    super.key,
    required this.voucher,
    this.onTap,
  });
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Voucher ${voucher.voucherNumber}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          voucher.driverName,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildSyncStatusChip(),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'Truck',
                      voucher.truckNumber,
                      Icons.local_shipping,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'Freight',
                      '₹${voucher.totalFreight.toStringAsFixed(0)}',
                      Icons.currency_rupee,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'Date',
                      voucher.departureDate,
                      Icons.calendar_today,
                    ),
                  ),
                ],
              ),
              if (voucher.hasConflicts) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.warning,
                        color: Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          voucher.conflictDetails ?? 'Sync conflict detected',
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildSyncStatusChip() {
    final color = _getSyncStatusColor();
    final icon = _getSyncStatusIcon();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 14,
          ),
          const SizedBox(width: 4),
          Text(
            voucher.syncStatusDescription,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  Color _getSyncStatusColor() {
    switch (voucher.syncState) {
      case OfflineTransactionState.pending:
        return Colors.orange;
      case OfflineTransactionState.syncing:
        return Colors.blue;
      case OfflineTransactionState.synced:
        return Colors.green;
      case OfflineTransactionState.conflict:
        return Colors.red;
      case OfflineTransactionState.failed:
        return Colors.red;
      case OfflineTransactionState.interrupted:
        return Colors.amber;
    }
  }
  
  IconData _getSyncStatusIcon() {
    switch (voucher.syncState) {
      case OfflineTransactionState.pending:
        return Icons.schedule;
      case OfflineTransactionState.syncing:
        return Icons.sync;
      case OfflineTransactionState.synced:
        return Icons.check_circle;
      case OfflineTransactionState.conflict:
        return Icons.error;
      case OfflineTransactionState.failed:
        return Icons.error_outline;
      case OfflineTransactionState.interrupted:
        return Icons.pause_circle;
    }
  }
  
  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 14,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}

/// Dialog for showing detailed offline voucher information
class OfflineVoucherDetailsDialog extends StatelessWidget {
  final OfflineVoucherModel voucher;
  
  const OfflineVoucherDetailsDialog({
    super.key,
    required this.voucher,
  });
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Voucher ${voucher.voucherNumber}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Driver', voucher.driverName),
            _buildDetailRow('Truck Number', voucher.truckNumber),
            _buildDetailRow('Product', voucher.productName),
            _buildDetailRow('Total Freight', '₹${voucher.totalFreight.toStringAsFixed(2)}'),
            _buildDetailRow('Departure Date', voucher.departureDate),
            _buildDetailRow('Sync Status', voucher.syncStatusDescription),
            _buildDetailRow('Created Locally', _formatDateTime(voucher.localCreatedAt)),
            if (voucher.lastSyncAttempt != null)
              _buildDetailRow('Last Sync Attempt', _formatDateTime(voucher.lastSyncAttempt!)),
            if (voucher.syncAttempts > 0)
              _buildDetailRow('Sync Attempts', '${voucher.syncAttempts}'),
            if (voucher.localJournalEntryIds.isNotEmpty)
              _buildDetailRow('Local Journal Entries', '${voucher.localJournalEntryIds.length}'),
            if (voucher.localBalanceChanges.isNotEmpty)
              _buildDetailRow('Balance Changes', '${voucher.localBalanceChanges.length} accounts'),
            if (voucher.hasConflicts && voucher.conflictDetails != null) ...[
              const SizedBox(height: 16),
              Text(
                'Conflict Details:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Text(
                  voucher.conflictDetails!,
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
  
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 13,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
