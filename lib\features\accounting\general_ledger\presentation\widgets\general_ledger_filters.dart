import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../controllers/general_ledger_controller.dart';

class GeneralLedgerFilters extends StatelessWidget {
  final GeneralLedgerController controller;

  const GeneralLedgerFilters({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.6,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Text(
                  'Filter General Ledger',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: 24),

            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Account Type Filter
                    _buildSectionHeader(context, 'Account Type'),
                    const SizedBox(height: 12),
                    Obx(() => DropdownButtonFormField<AccountType?>(
                          value: controller.selectedAccountType.value,
                          decoration: const InputDecoration(
                            labelText: 'Select Account Type',
                            border: OutlineInputBorder(),
                            hintText: 'All Account Types',
                          ),
                          items: [
                            const DropdownMenuItem<AccountType?>(
                              value: null,
                              child: Text('All Account Types'),
                            ),
                            ...AccountType.values
                                .map((type) => DropdownMenuItem(
                                      value: type,
                                      child: Row(
                                        children: [
                                          Icon(
                                            _getAccountTypeIcon(type),
                                            size: 16,
                                            color: _getAccountTypeColor(type),
                                          ),
                                          const SizedBox(width: 8),
                                          Text(type.displayName),
                                        ],
                                      ),
                                    )),
                          ],
                          onChanged: (value) =>
                              controller.selectedAccountType.value = value,
                        )),

                    const SizedBox(height: 24),

                    // Account Status Filter
                    _buildSectionHeader(context, 'Account Status'),
                    const SizedBox(height: 12),
                    Obx(() => DropdownButtonFormField<bool?>(
                          value: controller.selectedAccountStatus.value,
                          decoration: const InputDecoration(
                            labelText: 'Select Account Status',
                            border: OutlineInputBorder(),
                            hintText: 'All Statuses',
                          ),
                          items: [
                            const DropdownMenuItem<bool?>(
                              value: null,
                              child: Text('All Statuses'),
                            ),
                            const DropdownMenuItem<bool?>(
                              value: true,
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.check_circle,
                                    size: 16,
                                    color: Colors.green,
                                  ),
                                  SizedBox(width: 8),
                                  Text('Active'),
                                ],
                              ),
                            ),
                            const DropdownMenuItem<bool?>(
                              value: false,
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.pause_circle,
                                    size: 16,
                                    color: Colors.grey,
                                  ),
                                  SizedBox(width: 8),
                                  Text('Inactive'),
                                ],
                              ),
                            ),
                          ],
                          onChanged: (value) =>
                              controller.selectedAccountStatus.value = value,
                        )),

                    const SizedBox(height: 24),

                    // Date Range Filter
                    _buildSectionHeader(context, 'Date Range'),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: Obx(() => InkWell(
                                onTap: () => _selectStartDate(context),
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'Start Date',
                                    border: OutlineInputBorder(),
                                    suffixIcon: Icon(Icons.calendar_today),
                                  ),
                                  child: Text(
                                    controller.startDate.value
                                            ?.toString()
                                            .split(' ')[0] ??
                                        'Select Date',
                                    style: TextStyle(
                                      color: controller.startDate.value != null
                                          ? Colors.black
                                          : Colors.grey[600],
                                    ),
                                  ),
                                ),
                              )),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Obx(() => InkWell(
                                onTap: () => _selectEndDate(context),
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'End Date',
                                    border: OutlineInputBorder(),
                                    suffixIcon: Icon(Icons.calendar_today),
                                  ),
                                  child: Text(
                                    controller.endDate.value
                                            ?.toString()
                                            .split(' ')[0] ??
                                        'Select Date',
                                    style: TextStyle(
                                      color: controller.endDate.value != null
                                          ? Colors.black
                                          : Colors.grey[600],
                                    ),
                                  ),
                                ),
                              )),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Quick Date Filters
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildQuickDateFilter(context, 'Today', () {
                          final today = DateTime.now();
                          controller.startDate.value = today;
                          controller.endDate.value = today;
                        }),
                        _buildQuickDateFilter(context, 'This Week', () {
                          final now = DateTime.now();
                          final startOfWeek =
                              now.subtract(Duration(days: now.weekday - 1));
                          controller.startDate.value = startOfWeek;
                          controller.endDate.value = now;
                        }),
                        _buildQuickDateFilter(context, 'This Month', () {
                          final now = DateTime.now();
                          final startOfMonth = DateTime(now.year, now.month, 1);
                          controller.startDate.value = startOfMonth;
                          controller.endDate.value = now;
                        }),
                        _buildQuickDateFilter(context, 'Last 30 Days', () {
                          final now = DateTime.now();
                          final thirtyDaysAgo =
                              now.subtract(const Duration(days: 30));
                          controller.startDate.value = thirtyDaysAgo;
                          controller.endDate.value = now;
                        }),
                        _buildQuickDateFilter(context, 'This Year', () {
                          final now = DateTime.now();
                          final startOfYear = DateTime(now.year, 1, 1);
                          controller.startDate.value = startOfYear;
                          controller.endDate.value = now;
                        }),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Balance Filters
                    _buildSectionHeader(context, 'Balance Filters'),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildBalanceFilter(context, 'All Accounts', null),
                        _buildBalanceFilter(
                            context, 'With Balances', 'non-zero'),
                        _buildBalanceFilter(context, 'Zero Balances', 'zero'),
                        _buildBalanceFilter(
                            context, 'Positive Balances', 'positive'),
                        _buildBalanceFilter(
                            context, 'Negative Balances', 'negative'),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Current Filters Summary
                    _buildCurrentFilters(context),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                TextButton(
                  onPressed: () {
                    controller.clearFilters();
                    Navigator.of(context).pop();
                  },
                  child: const Text('Clear All'),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    // Filters are applied automatically through reactive programming
                    Navigator.of(context).pop();
                  },
                  child: const Text('Apply Filters'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
    );
  }

  Widget _buildQuickDateFilter(
      BuildContext context, String label, VoidCallback onTap) {
    return OutlinedButton(
      onPressed: onTap,
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      child: Text(label),
    );
  }

  Widget _buildBalanceFilter(
      BuildContext context, String label, String? filterType) {
    return FilterChip(
      label: Text(label),
      selected: false, // TODO: Implement balance filter state
      onSelected: (selected) {
        // TODO: Implement balance filtering logic
      },
    );
  }

  Widget _buildCurrentFilters(BuildContext context) {
    return Obx(() {
      final filters = <String>[];

      if (controller.selectedAccountType.value != null) {
        filters
            .add('Type: ${controller.selectedAccountType.value!.displayName}');
      }

      if (controller.selectedAccountStatus.value != null) {
        filters.add(
            'Status: ${controller.selectedAccountStatus.value! ? 'Active' : 'Inactive'}');
      }

      if (controller.startDate.value != null) {
        filters.add(
            'From: ${controller.startDate.value!.toString().split(' ')[0]}');
      }

      if (controller.endDate.value != null) {
        filters
            .add('To: ${controller.endDate.value!.toString().split(' ')[0]}');
      }

      if (filters.isEmpty) {
        return const SizedBox.shrink();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(context, 'Active Filters'),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: filters
                .map((filter) => Chip(
                      label: Text(filter),
                      deleteIcon: const Icon(Icons.close, size: 16),
                      onDeleted: () {
                        // TODO: Implement individual filter removal
                      },
                    ))
                .toList(),
          ),
        ],
      );
    });
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      controller.startDate.value = picked;
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      controller.endDate.value = picked;
    }
  }

  IconData _getAccountTypeIcon(AccountType type) {
    return type.category.icon;
  }

  Color _getAccountTypeColor(AccountType type) {
    switch (type.category) {
      case AccountCategory.assets:
        return Colors.green;
      case AccountCategory.liabilities:
        return Colors.red;
      case AccountCategory.equity:
        return Colors.blue;
      case AccountCategory.revenue:
        return Colors.purple;
      case AccountCategory.expenses:
        return Colors.orange;
    }
  }
}
