import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/features/asset_management/presentation/controllers/maintenance_history_controller.dart';
import 'package:logestics/features/asset_management/presentation/views/maintenance_form_dialog.dart';
import 'package:logestics/models/asset/asset_maintenance_model.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

class MaintenanceHistoryWidget extends StatefulWidget {
  final String assetId;
  final String assetName;

  const MaintenanceHistoryWidget({
    super.key,
    required this.assetId,
    required this.assetName,
  });

  @override
  State<MaintenanceHistoryWidget> createState() =>
      _MaintenanceHistoryWidgetState();
}

class _MaintenanceHistoryWidgetState extends State<MaintenanceHistoryWidget> {
  late MaintenanceHistoryController controller;
  late ColorNotifier notifier;

  @override
  void initState() {
    super.initState();
    controller = Get.put(
      MaintenanceHistoryController(),
      tag: widget.assetId, // Use asset ID as tag for unique instances
    );
    controller.initializeForAsset(widget.assetId);
  }

  @override
  void dispose() {
    Get.delete<MaintenanceHistoryController>(tag: widget.assetId);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of<ColorNotifier>(context, listen: true);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildSummaryCards(),
          const SizedBox(height: 16),
          _buildMaintenanceList(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.build,
          color: notifier.text,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Maintenance History',
          style: TextStyle(
            color: notifier.text,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        ElevatedButton.icon(
          onPressed: _addMaintenance,
          icon: const Icon(Icons.add, size: 16),
          label: const Text('Add Maintenance'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF0165FC),
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCards() {
    return Obx(() => Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Records',
                controller.maintenanceRecords.length.toString(),
                Icons.list_alt,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Total Cost',
                'PKR ${controller.formatCurrency(controller.totalMaintenanceCost)}',
                Icons.attach_money,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Last Maintenance',
                controller.lastMaintenanceDate,
                Icons.schedule,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Avg Cost',
                'PKR ${controller.formatCurrency(controller.averageMaintenanceCost)}',
                Icons.trending_up,
                Colors.purple,
              ),
            ),
          ],
        ));
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: notifier.text.withValues(alpha: 0.7),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: notifier.text,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceList() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(32),
            child: CircularProgressIndicator(),
          ),
        );
      }

      if (controller.maintenanceRecords.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                Icons.build_outlined,
                size: 48,
                color: notifier.text.withValues(alpha: 0.3),
              ),
              const SizedBox(height: 16),
              Text(
                'No maintenance records found',
                style: TextStyle(
                  color: notifier.text.withValues(alpha: 0.6),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Add your first maintenance record to track asset upkeep',
                style: TextStyle(
                  color: notifier.text.withValues(alpha: 0.4),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );
      }

      return Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: notifier.text.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'Date',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Type',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Description',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Cost',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Performed By',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Actions',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          ...controller.maintenanceRecords
              .map((maintenance) => _buildMaintenanceRow(maintenance)),
        ],
      );
    });
  }

  Widget _buildMaintenanceRow(AssetMaintenanceModel maintenance) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '${maintenance.maintenanceDate.day}/${maintenance.maintenanceDate.month}/${maintenance.maintenanceDate.year}',
              style: TextStyle(color: notifier.text, fontSize: 12),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getMaintenanceTypeColor(maintenance.maintenanceType)
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _getMaintenanceTypeColor(maintenance.maintenanceType),
                  width: 1,
                ),
              ),
              child: Text(
                maintenance.maintenanceType,
                style: TextStyle(
                  color: _getMaintenanceTypeColor(maintenance.maintenanceType),
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              maintenance.description,
              style: TextStyle(color: notifier.text, fontSize: 12),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'PKR ${controller.formatCurrency(maintenance.cost)}',
              style: TextStyle(
                color: notifier.text,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              maintenance.performedBy,
              style: TextStyle(color: notifier.text, fontSize: 12),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            flex: 1,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, size: 16),
                  onPressed: () => _editMaintenance(maintenance),
                  tooltip: 'Edit',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
                const SizedBox(width: 4),
                IconButton(
                  icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                  onPressed: () => _deleteMaintenance(maintenance),
                  tooltip: 'Delete',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getMaintenanceTypeColor(String type) {
    switch (type) {
      case 'Preventive':
        return Colors.green;
      case 'Corrective':
        return Colors.orange;
      case 'Emergency':
        return Colors.red;
      case 'Routine':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _addMaintenance() async {
    final result = await Get.dialog<bool>(
      MaintenanceFormDialog(
        assetId: widget.assetId,
        assetName: widget.assetName,
      ),
    );
    if (result == true) {
      controller.loadMaintenanceRecords();
    }
  }

  void _editMaintenance(AssetMaintenanceModel maintenance) async {
    final result = await Get.dialog<bool>(
      MaintenanceFormDialog(
        assetId: widget.assetId,
        assetName: widget.assetName,
        maintenance: maintenance,
      ),
    );
    if (result == true) {
      controller.loadMaintenanceRecords();
    }
  }

  void _deleteMaintenance(AssetMaintenanceModel maintenance) {
    Get.dialog(
      AlertDialog(
        backgroundColor: notifier.getBgColor,
        title: Text('Delete Maintenance Record',
            style: TextStyle(color: notifier.text)),
        content: Text(
          'Are you sure you want to delete this maintenance record? This action cannot be undone.',
          style: TextStyle(color: notifier.text),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteMaintenance(maintenance.id);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
