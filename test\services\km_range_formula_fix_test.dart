import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';
import 'package:logestics/services/slab_rate_calculation_service.dart';
import 'package:logestics/core/utils/constants/constants.dart';

void main() {
  group('KM Range Formula Fix Tests', () {
    late InvoiceModel testInvoice175km;
    late InvoiceModel testInvoice50km;
    late InvoiceModel testInvoice250km;

    setUp(() {
      // Invoice with 175 KM distance (should fall in 161-200 range)
      testInvoice175km = InvoiceModel(
        invoiceNumber: 1122,
        invoiceStatus: 'Active',
        tasNumber: 'TAS1122',
        productName: 'Test Product',
        numberOfBags: 400,
        weightPerBag: 50.0,
        customerName: 'Test Customer',
        truckNumber: 'TRK001',
        conveyNoteNumber: 'CN001',
        biltyNumber: 'BLT001',
        consignorName: 'Test Consignor',
        deliveryMode: 'Road',
        districtId: 'DIST001',
        districtName: 'Test District',
        stationId: 'STN001',
        stationName: 'Test Station',
        fromPlaceId: 'PLC001',
        fromPlaceName: 'Test Place',
        distanceInKilometers: 175.0,
        consignorPickUpAddress: 'Test Address',
      );

      // Invoice with 50 KM distance (should fall in 41-80 range)
      testInvoice50km = InvoiceModel(
        invoiceNumber: 1123,
        invoiceStatus: 'Active',
        tasNumber: 'TAS1123',
        productName: 'Test Product',
        numberOfBags: 400,
        weightPerBag: 50.0,
        customerName: 'Test Customer',
        truckNumber: 'TRK001',
        conveyNoteNumber: 'CN001',
        biltyNumber: 'BLT001',
        consignorName: 'Test Consignor',
        deliveryMode: 'Road',
        districtId: 'DIST001',
        districtName: 'Test District',
        stationId: 'STN001',
        stationName: 'Test Station',
        fromPlaceId: 'PLC001',
        fromPlaceName: 'Test Place',
        distanceInKilometers: 50.0,
        consignorPickUpAddress: 'Test Address',
      );

      // Invoice with 250 KM distance (should fall in ELSE clause)
      testInvoice250km = InvoiceModel(
        invoiceNumber: 1124,
        invoiceStatus: 'Active',
        tasNumber: 'TAS1124',
        productName: 'Test Product',
        numberOfBags: 400,
        weightPerBag: 50.0,
        customerName: 'Test Customer',
        truckNumber: 'TRK001',
        conveyNoteNumber: 'CN001',
        biltyNumber: 'BLT001',
        consignorName: 'Test Consignor',
        deliveryMode: 'Road',
        districtId: 'DIST001',
        districtName: 'Test District',
        stationId: 'STN001',
        stationName: 'Test Station',
        fromPlaceId: 'PLC001',
        fromPlaceName: 'Test Place',
        distanceInKilometers: 250.0,
        consignorPickUpAddress: 'Test Address',
      );
    });

    test('should correctly parse and execute KM range formula for 175 KM', () {
      // Create the exact KM range formula that was failing
      final kmRangeFormula =
          'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 1196.38 ELSE IF distanceInKilometers >= 121 AND distanceInKilometers <= 160 THEN 7.68 ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 7.68';

      final formula = CalculationFormulaModel(
        formulaId: 'km_range_fix_test',
        formulaName: 'KM Range Fix Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Determine Effective Rate Based on Distance',
            formula: kmRangeFormula,
            resultVariable: 'effectiveRate',
            description: 'KM-based rate calculation',
          ),
          FormulaStepModel(
            stepId: 'step2',
            stepName: 'Calculate Final Amount',
            formula: 'totalWeightTons × effectiveRate',
            resultVariable: 'finalAmount',
            description: 'Apply effective rate to weight',
          ),
        ],
        finalResultVariable: 'finalAmount',
      );

      // Test the formula execution
      final result =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice175km,
        formula: formula,
        customColumnValues: {},
      );

      expect(result.isSuccess, isTrue,
          reason: 'Formula execution should succeed');
      expect(result.finalAmount, isNotNull,
          reason: 'Final amount should not be null');

      // For 175 KM, should use rate 7.68 (falls in 161-200 range)
      // Expected: 20 tons × 7.68 = 153.6
      expect(result.finalAmount, equals(153.6),
          reason: 'Should calculate 20 tons × 7.68 = 153.6');
    });

    test('should correctly handle different KM ranges', () {
      final kmRangeFormula =
          'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 1196.38 ELSE IF distanceInKilometers >= 121 AND distanceInKilometers <= 160 THEN 7.68 ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 7.68';

      final formula = CalculationFormulaModel(
        formulaId: 'km_range_test',
        formulaName: 'KM Range Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Determine Rate',
            formula: kmRangeFormula,
            resultVariable: 'rate',
            description: 'KM-based rate',
          ),
        ],
        finalResultVariable: 'rate',
      );

      // Test 50 KM (should use 1196.38 - falls in 41-80 range)
      final result50 =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice50km,
        formula: formula,
        customColumnValues: {},
      );

      expect(result50.isSuccess, isTrue);
      expect(result50.finalAmount, equals(1196.38));

      // Test 175 KM (should use 7.68 - falls in 161-200 range)
      final result175 =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice175km,
        formula: formula,
        customColumnValues: {},
      );

      expect(result175.isSuccess, isTrue);
      expect(result175.finalAmount, equals(7.68));

      // Test 250 KM (should use 7.68 - falls in ELSE clause)
      final result250 =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice250km,
        formula: formula,
        customColumnValues: {},
      );

      expect(result250.isSuccess, isTrue);
      expect(result250.finalAmount, equals(7.68));
    });

    test('should work with slab rate calculation service', () {
      // Create a slab with KM range formula
      final slab = SlabModel(
        slabId: 'km_range_slab',
        slabName: 'KM Range Test Slab',
        startDate: DateTime.now().subtract(const Duration(days: 1)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now(),
        rates: [
          SlabRateModel(
            regionId: 'REG001',
            regionName: 'Test Region',
            districtId: 'DIST001',
            districtName: 'Test District',
            hmtRate: 0.0, // Not used with custom formula
            nonFuelRate: 0.0, // Not used with custom formula
            customColumns: {},
          ),
        ],
        calculationFormula: CalculationFormulaModel(
          formulaId: 'km_range_slab_formula',
          formulaName: 'KM Range Slab Formula',
          steps: [
            FormulaStepModel(
              stepId: 'step1',
              stepName: 'Determine Effective Rate Based on Distance',
              formula:
                  'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 1196.38 ELSE IF distanceInKilometers >= 121 AND distanceInKilometers <= 160 THEN 7.68 ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 7.68',
              resultVariable: 'effectiveRate',
              description: 'KM-based effective rate calculation',
            ),
            FormulaStepModel(
              stepId: 'step2',
              stepName: 'Calculate Final Amount',
              formula: 'totalWeightTons × effectiveRate',
              resultVariable: 'finalAmount',
              description: 'Apply effective rate to weight',
            ),
          ],
          finalResultVariable: 'finalAmount',
        ),
      );

      // Test with the slab rate calculation service
      final result =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice175km,
        formula: slab.calculationFormula!,
        customColumnValues: {},
      );

      expect(result.isSuccess, isTrue,
          reason: 'Slab formula should execute successfully');
      expect(result.finalAmount, isNotNull,
          reason: 'Should return a calculated amount');

      // For 175 KM distance with 20 tons:
      // Step 1: effectiveRate = 7.68 (175 falls in 161-200 range)
      // Step 2: finalAmount = 20 × 7.68 = 153.6
      expect(result.finalAmount, equals(153.6),
          reason: 'Should calculate correct final amount');
    });

    test('should handle the original problematic scenario', () {
      // This recreates the exact scenario from the debug logs
      final problematicFormula =
          'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 1196.38 ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 7.68';

      // Test direct evaluation
      final directResult = FlexibleFormulaCalculationService.evaluateExpression(
          problematicFormula.replaceAll('distanceInKilometers', '175.0'));

      expect(directResult, isNotNull, reason: 'Direct evaluation should work');
      expect(directResult, equals(7.68),
          reason: 'Should return correct rate for 175 KM');

      // Test with formula step
      final formula = CalculationFormulaModel(
        formulaId: 'problematic_test',
        formulaName: 'Problematic Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Determine Effective Rate Based on Distance',
            formula: problematicFormula,
            resultVariable: 'effectiveRate',
            description: 'Test the problematic formula',
          ),
        ],
        finalResultVariable: 'effectiveRate',
      );

      final stepResult =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice175km,
        formula: formula,
        customColumnValues: {},
      );

      expect(stepResult.isSuccess, isTrue,
          reason: 'Step execution should succeed');
      expect(stepResult.finalAmount, equals(7.68),
          reason: 'Should return correct effective rate');
    });
  });
}
