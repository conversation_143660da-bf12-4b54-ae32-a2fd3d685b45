import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/services/slab_rate_calculation_service.dart';
import 'package:logestics/core/utils/constants/constants.dart';

void main() {
  group('Slab Billing Fix Tests', () {
    late InvoiceModel testInvoice;

    setUp(() {
      testInvoice = InvoiceModel(
        invoiceNumber: 1001,
        invoiceStatus: 'Active',
        tasNumber: 'TAS001',
        productName: 'Test Product',
        numberOfBags: 100,
        weightPerBag: 50.0,
        customerName: 'Test Customer',
        truckNumber: 'TRK001',
        conveyNoteNumber: 'CN001',
        biltyNumber: 'BLT001',
        consignorName: 'Test Consignor',
        deliveryMode: 'Road',
        districtId: 'DIST001',
        districtName: 'Test District',
        stationId: 'STN001',
        stationName: 'Test Station',
        fromPlaceId: 'PLC001',
        fromPlaceName: 'Test Place',
        distanceInKilometers: 150.0,
        consignorPickUpAddress: 'Test Address',
      );
    });

    test('should calculate correct amount for newly created slab with HMT rate', () {
      // Create a new slab with HMT rate (simulating newly created slab)
      final slab = SlabModel(
        slabId: 'new_slab_1',
        slabName: 'New Test Slab',
        startDate: DateTime.now().subtract(const Duration(days: 1)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now(),
        rates: [
          SlabRateModel(
            regionId: 'REG001',
            regionName: 'Test Region',
            districtId: 'DIST001',
            districtName: 'Test District',
            hmtRate: 2.5, // Non-zero HMT rate
            nonFuelRate: 2.0, // Non-zero Non-Fuel rate
            customColumns: {}, // Empty custom columns (new slab)
          ),
        ],
      );

      // Test HMT rate calculation
      final hmtRate = slab.getRateForDistrict('DIST001')?.hmtRate ?? 0.0;
      expect(hmtRate, equals(2.5));

      // Calculate expected amount: (100 bags × 50 kg × 150 km × 2.5 rate) / 1000
      final totalWeightKg = testInvoice.numberOfBags * testInvoice.weightPerBag;
      final totalWeightTons = totalWeightKg / 1000;
      final expectedAmount = totalWeightTons * testInvoice.distanceInKilometers * hmtRate;
      final roundedExpectedAmount = MonetaryRounding.roundHalfUp(expectedAmount);

      expect(roundedExpectedAmount, equals(1875.0)); // 5 tons × 150 km × 2.5 rate = 1875
    });

    test('should calculate correct amount for newly created slab with custom column', () {
      // Create a new slab with custom column rate (simulating newly created slab with custom columns)
      final slab = SlabModel(
        slabId: 'new_slab_2',
        slabName: 'New Test Slab with Custom Column',
        startDate: DateTime.now().subtract(const Duration(days: 1)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now(),
        rates: [
          SlabRateModel(
            regionId: 'REG001',
            regionName: 'Test Region',
            districtId: 'DIST001',
            districtName: 'Test District',
            hmtRate: 0.0, // Zero legacy rates
            nonFuelRate: 0.0, // Zero legacy rates
            customColumns: {
              'customRate': 3.0, // Non-zero custom rate
            },
          ),
        ],
      );

      // Test custom column rate
      final customRate = slab.getRateForDistrict('DIST001')?.getCustomValue('customRate') ?? 0.0;
      expect(customRate, equals(3.0));

      // Calculate expected amount: (100 bags × 50 kg × 150 km × 3.0 rate) / 1000
      final totalWeightKg = testInvoice.numberOfBags * testInvoice.weightPerBag;
      final totalWeightTons = totalWeightKg / 1000;
      final expectedAmount = totalWeightTons * testInvoice.distanceInKilometers * customRate;
      final roundedExpectedAmount = MonetaryRounding.roundHalfUp(expectedAmount);

      expect(roundedExpectedAmount, equals(2250.0)); // 5 tons × 150 km × 3.0 rate = 2250
    });

    test('should handle slab with zero rates correctly', () {
      // Create a slab with all zero rates (problematic case)
      final slab = SlabModel(
        slabId: 'zero_slab',
        slabName: 'Zero Rate Slab',
        startDate: DateTime.now().subtract(const Duration(days: 1)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now(),
        rates: [
          SlabRateModel(
            regionId: 'REG001',
            regionName: 'Test Region',
            districtId: 'DIST001',
            districtName: 'Test District',
            hmtRate: 0.0, // Zero HMT rate
            nonFuelRate: 0.0, // Zero Non-Fuel rate
            customColumns: {}, // Empty custom columns
          ),
        ],
      );

      // All rates should be zero
      final rate = slab.getRateForDistrict('DIST001');
      expect(rate?.hmtRate, equals(0.0));
      expect(rate?.nonFuelRate, equals(0.0));
      expect(rate?.customColumns.isEmpty, isTrue);

      // This should result in zero calculation (the problem we're fixing)
      final totalWeightKg = testInvoice.numberOfBags * testInvoice.weightPerBag;
      final totalWeightTons = totalWeightKg / 1000;
      final calculatedAmount = totalWeightTons * testInvoice.distanceInKilometers * 0.0;

      expect(calculatedAmount, equals(0.0));
    });

    test('should preserve backward compatibility with old slabs', () {
      // Create an old slab with legacy rates (simulating slabs created before the feature implementation)
      final oldSlab = SlabModel(
        slabId: 'old_slab_1',
        slabName: 'Old Test Slab',
        startDate: DateTime.now().subtract(const Duration(days: 30)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now().subtract(const Duration(days: 30)), // Created 30 days ago
        rates: [
          SlabRateModel(
            regionId: 'REG001',
            regionName: 'Test Region',
            districtId: 'DIST001',
            districtName: 'Test District',
            hmtRate: 2.8, // Non-zero legacy HMT rate
            nonFuelRate: 2.3, // Non-zero legacy Non-Fuel rate
            customColumns: {
              'oldCustomRate': 2.6, // Some custom columns from old system
            },
          ),
        ],
      );

      // Test that old slab rates are preserved
      final rate = oldSlab.getRateForDistrict('DIST001');
      expect(rate?.hmtRate, equals(2.8));
      expect(rate?.nonFuelRate, equals(2.3));
      expect(rate?.getCustomValue('oldCustomRate'), equals(2.6));

      // Calculate expected amount using HMT rate
      final totalWeightKg = testInvoice.numberOfBags * testInvoice.weightPerBag;
      final totalWeightTons = totalWeightKg / 1000;
      final expectedAmount = totalWeightTons * testInvoice.distanceInKilometers * 2.8;
      final roundedExpectedAmount = MonetaryRounding.roundHalfUp(expectedAmount);

      expect(roundedExpectedAmount, equals(2100.0)); // 5 tons × 150 km × 2.8 rate = 2100
    });
  });
}
