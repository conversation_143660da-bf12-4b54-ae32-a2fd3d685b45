import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/features/slab/presentation/widgets/formula_builder_widget.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/models/slab/formula_variables.dart';
import 'package:logestics/main.dart';

// Mock theme notifier for testing
class MockThemeNotifier extends ChangeNotifier {
  Color get getBgColor => Colors.white;
  Color get getHoverColor => Colors.grey[100]!;
  Color get getfillborder => Colors.grey[300]!;
  Color get text => Colors.black;
}

void main() {
  group('FormulaBuilderWidget', () {
    late MockThemeNotifier mockNotifier;

    setUp(() {
      mockNotifier = MockThemeNotifier();
      // Initialize GetX
      Get.testMode = true;
    });

    tearDown(() {
      Get.reset();
    });

    Widget createTestWidget({
      CalculationFormulaModel? initialFormula,
      required Function(CalculationFormulaModel?) onFormulaChanged,
      bool readOnly = false,
    }) {
      return GetMaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<MockThemeNotifier>.value(
            value: mockNotifier,
            child: FormulaBuilderWidget(
              initialFormula: initialFormula,
              onFormulaChanged: onFormulaChanged,
              readOnly: readOnly,
            ),
          ),
        ),
      );
    }

    testWidgets('should render with default formula', (WidgetTester tester) async {
      CalculationFormulaModel? capturedFormula;

      await tester.pumpWidget(createTestWidget(
        onFormulaChanged: (formula) => capturedFormula = formula,
      ));

      // Wait for widget to build
      await tester.pumpAndSettle();

      // Check if the widget renders
      expect(find.text('Calculation Formula'), findsOneWidget);
      expect(find.text('Standard Calculation'), findsOneWidget);
      expect(find.text('Calculate Final Amount'), findsOneWidget);

      // Check if formula name field exists
      expect(find.byType(TextFormField), findsWidgets);
    });

    testWidgets('should load initial formula correctly', (WidgetTester tester) async {
      final initialFormula = CalculationFormulaModel.fromJson(FormulaTemplates.standardFormula);
      CalculationFormulaModel? capturedFormula;

      await tester.pumpWidget(createTestWidget(
        initialFormula: initialFormula,
        onFormulaChanged: (formula) => capturedFormula = formula,
      ));

      await tester.pumpAndSettle();

      // Check if initial formula is loaded
      expect(find.text('Standard Calculation'), findsOneWidget);
      expect(find.text('Calculate Final Amount'), findsOneWidget);
    });

    testWidgets('should add new step when add button is pressed', (WidgetTester tester) async {
      CalculationFormulaModel? capturedFormula;

      await tester.pumpWidget(createTestWidget(
        onFormulaChanged: (formula) => capturedFormula = formula,
      ));

      await tester.pumpAndSettle();

      // Find and tap the add step button
      final addButton = find.text('Add Step');
      expect(addButton, findsOneWidget);

      await tester.tap(addButton);
      await tester.pumpAndSettle();

      // Check if new step is added
      expect(find.text('Step 1'), findsOneWidget);
      expect(find.text('Step 2'), findsOneWidget);
    });

    testWidgets('should show test formula button', (WidgetTester tester) async {
      CalculationFormulaModel? capturedFormula;

      await tester.pumpWidget(createTestWidget(
        onFormulaChanged: (formula) => capturedFormula = formula,
      ));

      await tester.pumpAndSettle();

      // Check if test formula button exists
      expect(find.text('Test Formula'), findsOneWidget);
    });

    testWidgets('should show variable reference button', (WidgetTester tester) async {
      CalculationFormulaModel? capturedFormula;

      await tester.pumpWidget(createTestWidget(
        onFormulaChanged: (formula) => capturedFormula = formula,
      ));

      await tester.pumpAndSettle();

      // Check if variable reference button exists
      expect(find.text('Variable Reference'), findsOneWidget);
    });

    testWidgets('should be read-only when readOnly is true', (WidgetTester tester) async {
      final initialFormula = CalculationFormulaModel.fromJson(FormulaTemplates.standardFormula);
      CalculationFormulaModel? capturedFormula;

      await tester.pumpWidget(createTestWidget(
        initialFormula: initialFormula,
        onFormulaChanged: (formula) => capturedFormula = formula,
        readOnly: true,
      ));

      await tester.pumpAndSettle();

      // Check that add step button is not present in read-only mode
      expect(find.text('Add Step'), findsNothing);
      
      // Check that test formula button is not present in read-only mode
      expect(find.text('Test Formula'), findsNothing);
    });

    testWidgets('should show variable chips for formula building', (WidgetTester tester) async {
      CalculationFormulaModel? capturedFormula;

      await tester.pumpWidget(createTestWidget(
        onFormulaChanged: (formula) => capturedFormula = formula,
      ));

      await tester.pumpAndSettle();

      // Check if variable chips are present
      expect(find.text('Available Variables:'), findsOneWidget);
      
      // Check for some specific variable chips
      expect(find.text('Total Weight (tons)'), findsOneWidget);
      expect(find.text('Distance (km)'), findsOneWidget);
      expect(find.text('Rate Value'), findsOneWidget);
    });

    testWidgets('should update formula when text fields change', (WidgetTester tester) async {
      CalculationFormulaModel? capturedFormula;
      int formulaChangeCount = 0;

      await tester.pumpWidget(createTestWidget(
        onFormulaChanged: (formula) {
          capturedFormula = formula;
          formulaChangeCount++;
        },
      ));

      await tester.pumpAndSettle();

      // Find the formula name field and change its value
      final formulaNameField = find.byType(TextFormField).first;
      await tester.enterText(formulaNameField, 'Custom Formula Name');
      await tester.pumpAndSettle();

      // Check if formula was updated
      expect(formulaChangeCount, greaterThan(0));
    });

    testWidgets('should show final result dropdown', (WidgetTester tester) async {
      CalculationFormulaModel? capturedFormula;

      await tester.pumpWidget(createTestWidget(
        onFormulaChanged: (formula) => capturedFormula = formula,
      ));

      await tester.pumpAndSettle();

      // Check if final result section exists
      expect(find.text('Final Result Variable:'), findsOneWidget);
      expect(find.byType(DropdownButtonFormField<String>), findsOneWidget);
    });
  });
}
