import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/core/services/account_type_helper_service.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/models/voucher_model.dart';

void main() {
  group('Voucher Debit/Credit Behavior Tests', () {
    group('Account Type Helper Service Tests', () {
      test('should correctly identify debit accounts', () {
        // Asset accounts should be debit accounts
        expect(AccountTypeHelperService.isDebitAccount(AccountType.cash), true);
        expect(AccountTypeHelperService.isDebitAccount(AccountType.bank), true);
        expect(
            AccountTypeHelperService.isDebitAccount(
                AccountType.accountsReceivable),
            true);
        expect(
            AccountTypeHelperService.isDebitAccount(AccountType.currentAssets),
            true);

        // Expense accounts should be debit accounts
        expect(
            AccountTypeHelperService.isDebitAccount(
                AccountType.operatingExpenses),
            true);
        expect(
            AccountTypeHelperService.isDebitAccount(
                AccountType.administrativeExpenses),
            true);
      });

      test('should correctly identify credit accounts', () {
        // Liability accounts should be credit accounts
        expect(
            AccountTypeHelperService.isDebitAccount(
                AccountType.accountsPayable),
            false);
        expect(
            AccountTypeHelperService.isDebitAccount(
                AccountType.currentLiabilities),
            false);
        expect(
            AccountTypeHelperService.isDebitAccount(
                AccountType.longTermLiabilities),
            false);

        // Equity accounts should be credit accounts
        expect(
            AccountTypeHelperService.isDebitAccount(AccountType.ownersEquity),
            false);
        expect(
            AccountTypeHelperService.isDebitAccount(
                AccountType.retainedEarnings),
            false);
        expect(
            AccountTypeHelperService.isDebitAccount(
                AccountType.equityServiceRevenue),
            false);

        // Revenue accounts should be credit accounts
        expect(
            AccountTypeHelperService.isDebitAccount(AccountType.salesRevenue),
            false);
        expect(
            AccountTypeHelperService.isDebitAccount(AccountType.serviceRevenue),
            false);
      });

      test('should calculate correct balance changes for asset accounts', () {
        // Asset accounts: Debit increases, Credit decreases
        final assetBalanceIncrease =
            AccountTypeHelperService.calculateBalanceChange(
          accountType: AccountType.cash,
          debitAmount: 1000.0,
          creditAmount: 0.0,
        );
        expect(assetBalanceIncrease, 1000.0); // Debit increases asset

        final assetBalanceDecrease =
            AccountTypeHelperService.calculateBalanceChange(
          accountType: AccountType.cash,
          debitAmount: 0.0,
          creditAmount: 500.0,
        );
        expect(assetBalanceDecrease, -500.0); // Credit decreases asset
      });

      test('should calculate correct balance changes for liability accounts',
          () {
        // Liability accounts: Credit increases, Debit decreases
        final liabilityBalanceIncrease =
            AccountTypeHelperService.calculateBalanceChange(
          accountType: AccountType.accountsPayable,
          debitAmount: 0.0,
          creditAmount: 1000.0,
        );
        expect(liabilityBalanceIncrease, 1000.0); // Credit increases liability

        final liabilityBalanceDecrease =
            AccountTypeHelperService.calculateBalanceChange(
          accountType: AccountType.accountsPayable,
          debitAmount: 500.0,
          creditAmount: 0.0,
        );
        expect(liabilityBalanceDecrease, -500.0); // Debit decreases liability
      });

      test('should calculate correct balance changes for equity accounts', () {
        // Equity accounts: Credit increases, Debit decreases
        final equityBalanceIncrease =
            AccountTypeHelperService.calculateBalanceChange(
          accountType: AccountType.retainedEarnings,
          debitAmount: 0.0,
          creditAmount: 1000.0,
        );
        expect(equityBalanceIncrease, 1000.0); // Credit increases equity

        final equityBalanceDecrease =
            AccountTypeHelperService.calculateBalanceChange(
          accountType: AccountType.retainedEarnings,
          debitAmount: 500.0,
          creditAmount: 0.0,
        );
        expect(equityBalanceDecrease, -500.0); // Debit decreases equity
      });
    });

    group('Voucher Account Type Mapping Tests', () {
      test('should map voucher components to correct account types', () {
        // Create test accounts for each voucher component
        final testAccounts = _createTestAccountsForVoucher();

        // Verify account types match requirements
        expect(testAccounts['nlcReceivable']!.accountType.category,
            AccountCategory.assets);
        expect(testAccounts['brokerFees']!.accountType.category,
            AccountCategory.liabilities);
        expect(testAccounts['munshiana']!.accountType.category,
            AccountCategory.equity);
        expect(testAccounts['truckFare']!.accountType.category,
            AccountCategory.liabilities);
        expect(testAccounts['salesTaxReceivable']!.accountType.category,
            AccountCategory.assets);
        expect(testAccounts['freightTaxPayable']!.accountType.category,
            AccountCategory.liabilities);
        expect(testAccounts['netProfit']!.accountType.category,
            AccountCategory.equity);
      });
    });

    group('Journal Entry Generation Tests', () {
      test('should generate journal entries with correct debit/credit behavior',
          () {
        final testVoucher = _createTestVoucher();
        final testAccounts = _createTestAccountsForVoucher();

        // Simulate journal entry generation
        final journalEntries =
            _simulateJournalEntryGeneration(testVoucher, testAccounts);

        // Calculate totals
        final totalDebits = journalEntries.fold(
            0.0, (sum, entry) => sum + entry['debitAmount'] as double);
        final totalCredits = journalEntries.fold(
            0.0, (sum, entry) => sum + entry['creditAmount'] as double);

        // Note: Voucher creation journal entries are NOT balanced by themselves
        // since truck freight is now recorded only with payments.
        // The entries will be balanced when payment journal entries are added.
        print(
            'Voucher creation journal entries - Debits: \$${totalDebits.toStringAsFixed(2)}, Credits: \$${totalCredits.toStringAsFixed(2)}');
        print(
            'Note: These entries are not balanced by design - truck freight is recorded with payments');

        // Verify that we have the expected number of entries
        expect(
            journalEntries.length,
            equals(
                6)); // NLC, Broker, Munshiana, Sales Tax, Freight Tax, Net Profit

        // Verify specific amounts
        expect(totalDebits, equals(57500.0)); // NLC (50000) + Sales Tax (7500)
        expect(
            totalCredits,
            equals(
                16650.0)); // Broker (5000) + Munshiana (3000) + Freight Tax (3450) + Net Profit (5200)
      });

      test('should apply correct debit/credit behavior for each account type',
          () {
        final testVoucher = _createTestVoucher();
        final testAccounts = _createTestAccountsForVoucher();
        final journalEntries =
            _simulateJournalEntryGeneration(testVoucher, testAccounts);

        // Verify specific account behaviors
        for (final entry in journalEntries) {
          final accountType = entry['accountType'] as AccountType;
          final debitAmount = entry['debitAmount'] as double;
          final creditAmount = entry['creditAmount'] as double;
          final description = entry['description'] as String;

          print('Entry: $description');
          print(
              '  Account Type: ${accountType.displayName} (${accountType.category.name})');
          print(
              '  Debit: \$${debitAmount.toStringAsFixed(2)}, Credit: \$${creditAmount.toStringAsFixed(2)}');

          // Verify that only one of debit or credit is non-zero
          expect(
              (debitAmount > 0 && creditAmount == 0) ||
                  (debitAmount == 0 && creditAmount > 0),
              true);

          // Verify correct behavior based on account type
          if (AccountTypeHelperService.isDebitAccount(accountType)) {
            // For asset/expense accounts increasing: should be debited
            if (description.contains('NLC Amount') ||
                description.contains('Sales Tax')) {
              expect(debitAmount > 0, true,
                  reason: 'Asset accounts should be debited when increasing');
              expect(creditAmount, 0.0);
            }
          } else {
            // For liability/equity/revenue accounts increasing: should be credited
            if (description.contains('Broker Fees') ||
                description.contains('Munshiana') ||
                description.contains('Truck') ||
                description.contains('Freight Tax') ||
                description.contains('Net Profit')) {
              expect(creditAmount > 0, true,
                  reason:
                      'Liability/Equity accounts should be credited when increasing');
              expect(debitAmount, 0.0);
            }
          }
        }
      });
    });
  });
}

/// Create test accounts for all voucher components
Map<String, ChartOfAccountsModel> _createTestAccountsForVoucher() {
  return {
    'nlcReceivable': ChartOfAccountsModel(
      id: 'nlc_receivable_001',
      accountNumber: 'AR-001',
      accountName: 'NLC Receivable',
      category: AccountCategory.assets,
      accountType: AccountType.accountsReceivable,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    'brokerFees': ChartOfAccountsModel(
      id: 'broker_fees_001',
      accountNumber: 'AP-001',
      accountName: 'Broker Fees Payable',
      category: AccountCategory.liabilities,
      accountType: AccountType.accountsPayable,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    'munshiana': ChartOfAccountsModel(
      id: 'munshiana_001',
      accountNumber: 'EQ-001',
      accountName: 'Munshiana Service Revenue',
      category: AccountCategory.equity,
      accountType: AccountType.equityServiceRevenue,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    'truckFare': ChartOfAccountsModel(
      id: 'truck_fare_001',
      accountNumber: 'AP-002',
      accountName: 'Truck Fare Payable',
      category: AccountCategory.liabilities,
      accountType: AccountType.accountsPayable,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    'salesTaxReceivable': ChartOfAccountsModel(
      id: 'sales_tax_001',
      accountNumber: 'AR-002',
      accountName: '15% Tax Receivable',
      category: AccountCategory.assets,
      accountType: AccountType.accountsReceivable,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    'freightTaxPayable': ChartOfAccountsModel(
      id: 'freight_tax_001',
      accountNumber: 'CL-001',
      accountName: '6.9% Tax Payable',
      category: AccountCategory.liabilities,
      accountType: AccountType.currentLiabilities,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    'netProfit': ChartOfAccountsModel(
      id: 'net_profit_001',
      accountNumber: 'EQ-002',
      accountName: 'Retained Earnings',
      category: AccountCategory.equity,
      accountType: AccountType.retainedEarnings,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
  };
}

/// Create a test voucher with all components
VoucherModel _createTestVoucher() {
  return VoucherModel(
    voucherNumber: 'TEST-001',
    voucherStatus: 'Active',
    departureDate: '2024-01-15',
    driverName: 'Test Driver',
    invoiceTasNumberList: ['TAS-001'],
    invoiceBiltyNumberList: ['BILTY-001'],
    weightInTons: 25,
    productName: 'Test Product',
    totalNumberOfBags: 500,
    brokerType: 'Outsource',
    brokerName: 'Test Broker',
    brokerFees: 5000.0,
    munshianaFees: 3000.0,
    brokerAccount: 'Test Broker Account',
    munshianaAccount: 'Test Munshiana Account',
    driverPhoneNumber: '**********',
    truckNumber: 'TRK-001',
    conveyNoteNumber: 'CN-001',
    totalFreight: 50000.0,
    companyFreight: 50000.0,
    calculatedTax: 7500.0, // 15% of 50000
    calculatedFreightTax: 3450.0, // 6.9% of 50000
    calculatedProfit: 5200.0, // Adjusted to balance the equation
    // Chart of Accounts IDs
    companyFreightAccountId: 'nlc_receivable_001',
    brokerAccountId: 'broker_fees_001',
    munshianaAccountId: 'munshiana_001',
    truckFreightAccountId: 'truck_fare_001',
    salesTaxAccountId: 'sales_tax_001',
    freightTaxAccountId: 'freight_tax_001',
    profitAccountId: 'net_profit_001',
  );
}

/// Simulate journal entry generation for testing
List<Map<String, dynamic>> _simulateJournalEntryGeneration(
  VoucherModel voucher,
  Map<String, ChartOfAccountsModel> accounts,
) {
  final entries = <Map<String, dynamic>>[];

  // Helper function to determine debit/credit amounts
  Map<String, double> getDebitCreditAmounts(
      ChartOfAccountsModel account, double amount, bool isIncrease) {
    final increasesWithDebit =
        AccountTypeHelperService.isDebitAccount(account.accountType);

    if ((increasesWithDebit && isIncrease) ||
        (!increasesWithDebit && !isIncrease)) {
      return {'debit': amount, 'credit': 0.0};
    } else {
      return {'debit': 0.0, 'credit': amount};
    }
  }

  // 1. NLC Amount (Company Freight) - Asset Account
  if (voucher.companyFreight > 0) {
    final account = accounts['nlcReceivable']!;
    final amounts =
        getDebitCreditAmounts(account, voucher.companyFreight, true);
    entries.add({
      'accountType': account.accountType,
      'debitAmount': amounts['debit']!,
      'creditAmount': amounts['credit']!,
      'description': 'NLC Amount - Voucher #${voucher.voucherNumber}',
    });
  }

  // 2. Broker Fees - Liability Account
  if (voucher.brokerFees > 0) {
    final account = accounts['brokerFees']!;
    final amounts = getDebitCreditAmounts(account, voucher.brokerFees, true);
    entries.add({
      'accountType': account.accountType,
      'debitAmount': amounts['debit']!,
      'creditAmount': amounts['credit']!,
      'description': 'Broker Fees - Voucher #${voucher.voucherNumber}',
    });
  }

  // 3. Munshiana Fees - Equity Account
  if (voucher.munshianaFees > 0) {
    final account = accounts['munshiana']!;
    final amounts = getDebitCreditAmounts(account, voucher.munshianaFees, true);
    entries.add({
      'accountType': account.accountType,
      'debitAmount': amounts['debit']!,
      'creditAmount': amounts['credit']!,
      'description': 'Munshiana Fees - Voucher #${voucher.voucherNumber}',
    });
  }

  // 4. Truck Freight - REMOVED: Now recorded only when payments are made
  // Note: Truck freight journal entries are now created during payment processing
  // to ensure accounting entries reflect actual payments rather than expected amounts
  // This aligns with the new business logic in AutomaticJournalEntryService

  // 5. Sales Tax (15%) - Asset Account
  if (voucher.calculatedTax > 0) {
    final account = accounts['salesTaxReceivable']!;
    final amounts = getDebitCreditAmounts(account, voucher.calculatedTax, true);
    entries.add({
      'accountType': account.accountType,
      'debitAmount': amounts['debit']!,
      'creditAmount': amounts['credit']!,
      'description': '15% Sales Tax - Voucher #${voucher.voucherNumber}',
    });
  }

  // 6. Freight Tax (4.6%) - Liability Account
  if (voucher.calculatedFreightTax > 0) {
    final account = accounts['freightTaxPayable']!;
    final amounts =
        getDebitCreditAmounts(account, voucher.calculatedFreightTax, true);
    entries.add({
      'accountType': account.accountType,
      'debitAmount': amounts['debit']!,
      'creditAmount': amounts['credit']!,
      'description': '6.9% Freight Tax - Voucher #${voucher.voucherNumber}',
    });
  }

  // 7. Net Profit - Equity Account
  if (voucher.calculatedProfit > 0) {
    final account = accounts['netProfit']!;
    final amounts =
        getDebitCreditAmounts(account, voucher.calculatedProfit, true);
    entries.add({
      'accountType': account.accountType,
      'debitAmount': amounts['debit']!,
      'creditAmount': amounts['credit']!,
      'description': 'Net Profit - Voucher #${voucher.voucherNumber}',
    });
  }

  return entries;
}
