import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/services/broker_financial_service.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/models/finance/broker_model.dart';

class BrokerDetailController extends GetxController
    with GetTickerProviderStateMixin {
  final BrokerFinancialService brokerFinancialService;

  BrokerDetailController({
    required this.brokerFinancialService,
  });

  // Observable data
  final Rx<BrokerModel?> broker = Rx<BrokerModel?>(null);
  final RxList<BrokerTransactionModel> transactions =
      <BrokerTransactionModel>[].obs;
  final RxList<BrokerPaymentModel> payments = <BrokerPaymentModel>[].obs;
  final Rx<Map<String, dynamic>> financialSummary =
      Rx<Map<String, dynamic>>({});
  final RxBool isLoading = false.obs;
  final RxBool isLoadingTransactions = false.obs;
  final RxBool isLoadingPayments = false.obs;
  final RxBool isRecordingPayment = false.obs;

  // Tab controller
  late TabController tabController;

  // Payment form controllers
  final amountController = TextEditingController();
  final notesController = TextEditingController();
  final checkNumberController = TextEditingController();
  final bankNameController = TextEditingController();
  final referenceNumberController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // Payment form data
  final Rx<BrokerPaymentMethod> selectedPaymentMethod =
      BrokerPaymentMethod.cash.obs;
  final Rx<DateTime> selectedPaymentDate = DateTime.now().obs;
  final paymentAccountController = TextEditingController();
  final Rx<DateTime?> checkIssueDate = Rx<DateTime?>(null);
  final Rx<DateTime?> checkExpiryDate = Rx<DateTime?>(null);

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 3, vsync: this);
  }

  @override
  void onClose() {
    tabController.dispose();
    amountController.dispose();
    notesController.dispose();
    checkNumberController.dispose();
    bankNameController.dispose();
    referenceNumberController.dispose();
    super.onClose();
  }

  /// Initialize with broker data
  void initializeBroker(BrokerModel brokerModel) {
    broker.value = brokerModel;
    _loadBrokerFinancialData();
  }

  /// Load broker financial data
  Future<void> _loadBrokerFinancialData() async {
    if (broker.value == null) return;

    isLoading.value = true;
    try {
      await Future.wait([
        _loadFinancialSummary(),
        _loadTransactionHistory(),
        _loadPaymentHistory(),
      ]);
    } catch (e) {
      log('Error loading broker financial data: $e');
      SnackbarUtils.showError('Error', 'Failed to load broker financial data');
    } finally {
      isLoading.value = false;
    }
  }

  /// Load financial summary
  Future<void> _loadFinancialSummary() async {
    if (broker.value == null) return;

    final result = await brokerFinancialService
        .getBrokerFinancialSummary(broker.value!.id);
    result.fold(
      (failure) {
        log('Failed to load financial summary: ${failure.message}');
        SnackbarUtils.showError('Error', 'Failed to load financial summary');
      },
      (summary) {
        financialSummary.value = summary;
      },
    );
  }

  /// Load transaction history
  Future<void> _loadTransactionHistory() async {
    if (broker.value == null) return;

    isLoadingTransactions.value = true;
    final result = await brokerFinancialService
        .getBrokerTransactionHistory(broker.value!.id);
    result.fold(
      (failure) {
        log('Failed to load transaction history: ${failure.message}');
        SnackbarUtils.showError('Error', 'Failed to load transaction history');
      },
      (transactionList) {
        transactions.value = transactionList;
      },
    );
    isLoadingTransactions.value = false;
  }

  /// Load payment history
  Future<void> _loadPaymentHistory() async {
    if (broker.value == null) return;

    isLoadingPayments.value = true;
    final result =
        await brokerFinancialService.getBrokerPaymentHistory(broker.value!.id);
    result.fold(
      (failure) {
        log('Failed to load payment history: ${failure.message}');
        SnackbarUtils.showError('Error', 'Failed to load payment history');
      },
      (paymentList) {
        payments.value = paymentList;
      },
    );
    isLoadingPayments.value = false;
  }

  /// Record a new payment (tracking only)
  Future<void> recordPayment() async {
    if (!formKey.currentState!.validate()) return;
    if (broker.value == null) return;
    if (paymentAccountController.text.trim().isEmpty) {
      SnackbarUtils.showError('Error', 'Please enter a payment account name');
      return;
    }

    isRecordingPayment.value = true;
    try {
      final amount = double.parse(amountController.text);

      final result = await brokerFinancialService.recordBrokerPayment(
        brokerId: broker.value!.id,
        brokerName: broker.value!.name,
        amount: amount,
        method: selectedPaymentMethod.value,
        paymentDate: selectedPaymentDate.value,
        paymentAccountName: paymentAccountController.text.trim(),
        notes: notesController.text.trim().isEmpty
            ? null
            : notesController.text.trim(),
        checkNumber: selectedPaymentMethod.value == BrokerPaymentMethod.check
            ? checkNumberController.text.trim()
            : null,
        bankName: selectedPaymentMethod.value == BrokerPaymentMethod.check
            ? bankNameController.text.trim()
            : null,
        checkIssueDate: checkIssueDate.value,
        checkExpiryDate: checkExpiryDate.value,
        referenceNumber: referenceNumberController.text.trim().isEmpty
            ? null
            : referenceNumberController.text.trim(),
      );

      result.fold(
        (failure) {
          SnackbarUtils.showError(
              'Error', 'Failed to record payment: ${failure.message}');
        },
        (payment) {
          SnackbarUtils.showSuccess('Success', 'Payment recorded successfully');
          _clearPaymentForm();
          Get.back(); // Close payment dialog
          _loadBrokerFinancialData(); // Refresh data
        },
      );
    } catch (e) {
      SnackbarUtils.showError('Error', 'Invalid amount entered');
    } finally {
      isRecordingPayment.value = false;
    }
  }

  /// Clear payment form
  void _clearPaymentForm() {
    amountController.clear();
    notesController.clear();
    checkNumberController.clear();
    bankNameController.clear();
    referenceNumberController.clear();
    paymentAccountController.clear();
    selectedPaymentMethod.value = BrokerPaymentMethod.cash;
    selectedPaymentDate.value = DateTime.now();
    checkIssueDate.value = null;
    checkExpiryDate.value = null;
  }

  /// Refresh all data
  Future<void> refreshData() async {
    await _loadBrokerFinancialData();
  }

  /// Get balance color based on amount
  Color getBalanceColor(double balance) {
    if (balance > 0) {
      return Colors.red; // Money owed to broker
    } else if (balance < 0) {
      return Colors.green; // Overpaid
    } else {
      return Colors.grey; // Balanced
    }
  }

  /// Get balance text
  String getBalanceText(double balance) {
    if (balance > 0) {
      return 'Owed: \$${balance.toStringAsFixed(2)}';
    } else if (balance < 0) {
      return 'Overpaid: \$${(-balance).toStringAsFixed(2)}';
    } else {
      return 'Balanced';
    }
  }

  /// Format currency
  String formatCurrency(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }

  /// Format date
  String formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Get transaction type icon
  IconData getTransactionTypeIcon(BrokerTransactionType type) {
    switch (type) {
      case BrokerTransactionType.fee:
        return Icons.add_circle_outline;
      case BrokerTransactionType.payment:
        return Icons.remove_circle_outline;
    }
  }

  /// Get transaction type color
  Color getTransactionTypeColor(BrokerTransactionType type) {
    switch (type) {
      case BrokerTransactionType.fee:
        return Colors.red; // Money owed
      case BrokerTransactionType.payment:
        return Colors.green; // Money paid
    }
  }
}
