import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for individual account ledger entries
/// Each entry represents a single debit or credit transaction for an account
class AccountLedgerModel {
  final String id;
  final String accountId; // Reference to Chart of Accounts
  final String journalEntryId; // Reference to parent journal entry
  final String journalEntryLineId; // Reference to specific journal entry line
  final DateTime transactionDate;
  final String description;
  final String referenceNumber; // Voucher number, invoice number, etc.
  final String referenceType; // voucher, invoice, expense, etc.
  final double debitAmount;
  final double creditAmount;
  final double runningBalance; // Running balance after this transaction
  final DateTime createdAt;
  final String uid; // Company isolation

  AccountLedgerModel({
    required this.id,
    required this.accountId,
    required this.journalEntryId,
    required this.journalEntryLineId,
    required this.transactionDate,
    required this.description,
    required this.referenceNumber,
    required this.referenceType,
    required this.debitAmount,
    required this.creditAmount,
    required this.runningBalance,
    required this.createdAt,
    required this.uid,
  });

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'accountId': accountId,
      'journalEntryId': journalEntryId,
      'journalEntryLineId': journalEntryLineId,
      'transactionDate': Timestamp.fromDate(transactionDate),
      'description': description,
      'referenceNumber': referenceNumber,
      'referenceType': referenceType,
      'debitAmount': debitAmount,
      'creditAmount': creditAmount,
      'runningBalance': runningBalance,
      'createdAt': Timestamp.fromDate(createdAt),
      'uid': uid,
    };
  }

  /// Create from Firestore document
  factory AccountLedgerModel.fromFirestore(Map<String, dynamic> data) {
    return AccountLedgerModel(
      id: data['id'] ?? '',
      accountId: data['accountId'] ?? '',
      journalEntryId: data['journalEntryId'] ?? '',
      journalEntryLineId: data['journalEntryLineId'] ?? '',
      transactionDate: _parseDateTime(data['transactionDate']),
      description: data['description'] ?? '',
      referenceNumber: data['referenceNumber'] ?? '',
      referenceType: data['referenceType'] ?? '',
      debitAmount: (data['debitAmount'] ?? 0.0).toDouble(),
      creditAmount: (data['creditAmount'] ?? 0.0).toDouble(),
      runningBalance: (data['runningBalance'] ?? 0.0).toDouble(),
      createdAt: _parseDateTime(data['createdAt']),
      uid: data['uid'] ?? '',
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'journalEntryId': journalEntryId,
      'journalEntryLineId': journalEntryLineId,
      'transactionDate': transactionDate.toIso8601String(),
      'description': description,
      'referenceNumber': referenceNumber,
      'referenceType': referenceType,
      'debitAmount': debitAmount,
      'creditAmount': creditAmount,
      'runningBalance': runningBalance,
      'createdAt': createdAt.toIso8601String(),
      'uid': uid,
    };
  }

  /// Create from JSON
  factory AccountLedgerModel.fromJson(Map<String, dynamic> json) {
    return AccountLedgerModel(
      id: json['id'] ?? '',
      accountId: json['accountId'] ?? '',
      journalEntryId: json['journalEntryId'] ?? '',
      journalEntryLineId: json['journalEntryLineId'] ?? '',
      transactionDate: DateTime.parse(json['transactionDate'] ?? DateTime.now().toIso8601String()),
      description: json['description'] ?? '',
      referenceNumber: json['referenceNumber'] ?? '',
      referenceType: json['referenceType'] ?? '',
      debitAmount: (json['debitAmount'] ?? 0.0).toDouble(),
      creditAmount: (json['creditAmount'] ?? 0.0).toDouble(),
      runningBalance: (json['runningBalance'] ?? 0.0).toDouble(),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      uid: json['uid'] ?? '',
    );
  }

  /// Create a copy with updated fields
  AccountLedgerModel copyWith({
    String? id,
    String? accountId,
    String? journalEntryId,
    String? journalEntryLineId,
    DateTime? transactionDate,
    String? description,
    String? referenceNumber,
    String? referenceType,
    double? debitAmount,
    double? creditAmount,
    double? runningBalance,
    DateTime? createdAt,
    String? uid,
  }) {
    return AccountLedgerModel(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      journalEntryLineId: journalEntryLineId ?? this.journalEntryLineId,
      transactionDate: transactionDate ?? this.transactionDate,
      description: description ?? this.description,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      referenceType: referenceType ?? this.referenceType,
      debitAmount: debitAmount ?? this.debitAmount,
      creditAmount: creditAmount ?? this.creditAmount,
      runningBalance: runningBalance ?? this.runningBalance,
      createdAt: createdAt ?? this.createdAt,
      uid: uid ?? this.uid,
    );
  }

  /// Helper methods
  bool get isDebit => debitAmount > 0;
  bool get isCredit => creditAmount > 0;
  double get amount => debitAmount > 0 ? debitAmount : creditAmount;
  String get transactionType => debitAmount > 0 ? 'Debit' : 'Credit';

  /// Format reference for display
  String get formattedReference {
    if (referenceNumber.isNotEmpty) {
      return '$referenceType #$referenceNumber';
    }
    return referenceType;
  }

  @override
  String toString() {
    return 'AccountLedgerModel(id: $id, accountId: $accountId, description: $description, amount: $amount, type: $transactionType, balance: $runningBalance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AccountLedgerModel &&
        other.id == id &&
        other.accountId == accountId &&
        other.journalEntryId == journalEntryId &&
        other.journalEntryLineId == journalEntryLineId;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        accountId.hashCode ^
        journalEntryId.hashCode ^
        journalEntryLineId.hashCode;
  }

  /// Helper method to parse DateTime from Firestore Timestamp or milliseconds
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    
    if (value is Timestamp) {
      return value.toDate();
    } else if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    } else if (value is String) {
      return DateTime.tryParse(value) ?? DateTime.now();
    }
    
    return DateTime.now();
  }
}

/// Pagination result for account ledger entries
class AccountLedgerPaginationResult {
  final List<AccountLedgerModel> entries;
  final bool hasMore;
  final DocumentSnapshot? lastDocument;
  final int totalCount;

  AccountLedgerPaginationResult({
    required this.entries,
    required this.hasMore,
    this.lastDocument,
    required this.totalCount,
  });
}

/// Filter options for account ledger queries
class AccountLedgerFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? referenceType;
  final String? searchTerm;
  final bool? showDebitsOnly;
  final bool? showCreditsOnly;

  AccountLedgerFilter({
    this.startDate,
    this.endDate,
    this.referenceType,
    this.searchTerm,
    this.showDebitsOnly,
    this.showCreditsOnly,
  });

  bool get hasFilters =>
      startDate != null ||
      endDate != null ||
      referenceType != null ||
      searchTerm != null ||
      showDebitsOnly == true ||
      showCreditsOnly == true;
}
