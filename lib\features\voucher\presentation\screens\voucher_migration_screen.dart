import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/voucher_migration_controller.dart';

/// Screen for managing voucher migration to Chart of Accounts
class VoucherMigrationScreen extends StatelessWidget {
  const VoucherMigrationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(VoucherMigrationController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('Voucher Migration'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.account_tree, color: Colors.blue, size: 32),
                        const SizedBox(width: 12),
                        const Text(
                          'Chart of Accounts Migration',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'Migrate existing vouchers from legacy account references to Chart of Accounts structure.',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Migration Controls
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Migration Actions',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Dry Run Button
                    SizedBox(
                      width: double.infinity,
                      child: Obx(() => ElevatedButton.icon(
                            onPressed: controller.isMigrationInProgress.value
                                ? null
                                : controller.runDryRun,
                            icon: const Icon(Icons.preview),
                            label: const Text('Run Dry Run (Preview)'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          )),
                    ),

                    const SizedBox(height: 12),

                    // Migration Button
                    SizedBox(
                      width: double.infinity,
                      child: Obx(() => ElevatedButton.icon(
                            onPressed: controller.isMigrationInProgress.value
                                ? null
                                : controller.runMigration,
                            icon: const Icon(Icons.sync),
                            label: const Text('Start Migration'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          )),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Progress Section
            Obx(() {
              if (!controller.isMigrationInProgress.value &&
                  !controller.migrationCompleted.value) {
                return const SizedBox.shrink();
              }

              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          if (controller.isMigrationInProgress.value)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          else
                            Icon(
                              Icons.check_circle,
                              color: controller.migrationStatusColor,
                              size: 20,
                            ),
                          const SizedBox(width: 8),
                          const Text(
                            'Migration Progress',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        controller.migrationProgress.value,
                        style: const TextStyle(fontSize: 14),
                      ),
                      if (controller.migrationCompleted.value) ...[
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: controller.migrationStatusColor
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: controller.migrationStatusColor
                                  .withValues(alpha: 0.3),
                            ),
                          ),
                          child: Text(
                            controller.migrationStatusSummary,
                            style: const TextStyle(
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            }),

            // Migration Report Section
            Obx(() {
              if (controller.migrationResult.value == null) {
                return const SizedBox.shrink();
              }

              return Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Migration Report',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            TextButton.icon(
                              onPressed: controller.toggleMigrationReport,
                              icon: Icon(
                                controller.showMigrationReport.value
                                    ? Icons.expand_less
                                    : Icons.expand_more,
                              ),
                              label: Text(
                                controller.showMigrationReport.value
                                    ? 'Hide Details'
                                    : 'Show Details',
                              ),
                            ),
                          ],
                        ),
                        if (controller.showMigrationReport.value) ...[
                          const SizedBox(height: 12),
                          Expanded(
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey[50],
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey[300]!),
                              ),
                              child: SingleChildScrollView(
                                child: Text(
                                  controller.migrationReportText,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontFamily: 'monospace',
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
