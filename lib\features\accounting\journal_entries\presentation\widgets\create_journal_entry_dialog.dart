import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../controllers/journal_entry_controller.dart';
import 'validation_summary_widget.dart';

class CreateJournalEntryDialog extends StatelessWidget {
  const CreateJournalEntryDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<JournalEntryController>();

    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Text(
                  'Create Journal Entry',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {
                    controller.clearForm();
                    Navigator.of(context).pop();
                  },
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            Expanded(
              child: Form(
                key: controller.formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Basic Information Section
                    _buildSectionHeader(context, 'Basic Information'),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: TextFormField(
                            controller: controller.descriptionController,
                            decoration: const InputDecoration(
                              labelText: 'Description *',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Description is required';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: controller.referenceController,
                            decoration: const InputDecoration(
                              labelText: 'Reference',
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Obx(() => InkWell(
                                onTap: () => _selectDate(context, controller),
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'Entry Date',
                                    border: OutlineInputBorder(),
                                    suffixIcon: Icon(Icons.calendar_today),
                                  ),
                                  child: Text(
                                    controller.entryDate.value
                                        .toString()
                                        .split(' ')[0],
                                  ),
                                ),
                              )),
                        ),
                      ],
                    ),

                    const SizedBox(height: 32),

                    // Journal Lines Section
                    _buildSectionHeader(context, 'Journal Lines'),
                    const SizedBox(height: 16),

                    // Add Line Form
                    _buildAddLineForm(context, controller),

                    const SizedBox(height: 24),

                    // Lines List
                    Expanded(
                      child: Obx(() {
                        if (controller.journalLines.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.list_alt_outlined,
                                  size: 48,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No journal lines added yet',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Add at least one debit and one credit line',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: Colors.grey[500],
                                      ),
                                ),
                              ],
                            ),
                          );
                        }

                        return Column(
                          children: [
                            // Balance Summary
                            _buildBalanceSummary(context, controller),
                            const SizedBox(height: 8),

                            // Validation Summary
                            Obx(() {
                              final validationResult =
                                  controller.validateJournalEntry();
                              return JournalEntryValidationStatus(
                                validationResult: validationResult,
                                showDetails: false,
                                onToggleDetails: () {
                                  // Could implement toggle logic here if needed
                                },
                              );
                            }),
                            const SizedBox(height: 16),

                            // Lines List
                            Expanded(
                              child: ListView.builder(
                                itemCount: controller.journalLines.length,
                                itemBuilder: (context, index) {
                                  final line = controller.journalLines[index];
                                  return Card(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    child: ListTile(
                                      title: Text(
                                          '${line.accountNumber} - ${line.accountName}'),
                                      subtitle: Text(line.description),
                                      trailing: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              Text(
                                                'Dr: \$${line.debitAmount.toStringAsFixed(2)}',
                                                style: TextStyle(
                                                  color: line.debitAmount > 0
                                                      ? Colors.green
                                                      : Colors.grey,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              Text(
                                                'Cr: \$${line.creditAmount.toStringAsFixed(2)}',
                                                style: TextStyle(
                                                  color: line.creditAmount > 0
                                                      ? Colors.red
                                                      : Colors.grey,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(width: 8),
                                          PopupMenuButton(
                                            itemBuilder: (context) => [
                                              PopupMenuItem(
                                                value: 'edit',
                                                child: const Row(
                                                  children: [
                                                    Icon(Icons.edit, size: 16),
                                                    SizedBox(width: 8),
                                                    Text('Edit'),
                                                  ],
                                                ),
                                              ),
                                              PopupMenuItem(
                                                value: 'delete',
                                                child: const Row(
                                                  children: [
                                                    Icon(Icons.delete,
                                                        size: 16,
                                                        color: Colors.red),
                                                    SizedBox(width: 8),
                                                    Text('Delete',
                                                        style: TextStyle(
                                                            color: Colors.red)),
                                                  ],
                                                ),
                                              ),
                                            ],
                                            onSelected: (value) {
                                              if (value == 'edit') {
                                                controller
                                                    .editJournalLine(index);
                                              } else if (value == 'delete') {
                                                controller
                                                    .removeJournalLine(index);
                                              }
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        );
                      }),
                    ),

                    const SizedBox(height: 24),

                    // Action Buttons
                    Row(
                      children: [
                        TextButton(
                          onPressed: () {
                            controller.clearForm();
                            Navigator.of(context).pop();
                          },
                          child: const Text('Cancel'),
                        ),
                        const Spacer(),
                        Obx(() => ElevatedButton(
                              onPressed: controller.isCreating.value ||
                                      controller.journalLines.isEmpty ||
                                      !controller.isBalanced
                                  ? null
                                  : controller.createJournalEntry,
                              child: controller.isCreating.value
                                  ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2),
                                    )
                                  : const Text('Create Journal Entry'),
                            )),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
    );
  }

  Widget _buildAddLineForm(
      BuildContext context, JournalEntryController controller) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add Journal Line',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // Account Selection
                Expanded(
                  flex: 2,
                  child:
                      Obx(() => DropdownButtonFormField<ChartOfAccountsModel>(
                            value: controller.selectedAccount.value,
                            decoration: const InputDecoration(
                              labelText: 'Account *',
                              border: OutlineInputBorder(),
                            ),
                            items: controller.accounts.map((account) {
                              return DropdownMenuItem(
                                value: account,
                                child: Text(
                                    '${account.accountNumber} - ${account.accountName}'),
                              );
                            }).toList(),
                            onChanged: (account) =>
                                controller.selectedAccount.value = account,
                          )),
                ),
                const SizedBox(width: 12),

                // Description
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    controller: controller.lineDescriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description *',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Debit Amount
                Expanded(
                  child: TextFormField(
                    controller: controller.debitAmountController,
                    decoration: const InputDecoration(
                      labelText: 'Debit',
                      border: OutlineInputBorder(),
                      prefixText: '\$ ',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d{0,2}')),
                    ],
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        controller.creditAmountController.clear();
                      }
                    },
                  ),
                ),
                const SizedBox(width: 12),

                // Credit Amount
                Expanded(
                  child: TextFormField(
                    controller: controller.creditAmountController,
                    decoration: const InputDecoration(
                      labelText: 'Credit',
                      border: OutlineInputBorder(),
                      prefixText: '\$ ',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d{0,2}')),
                    ],
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        controller.debitAmountController.clear();
                      }
                    },
                  ),
                ),
                const SizedBox(width: 12),

                // Add/Update Button
                Obx(() => ElevatedButton(
                      onPressed: controller.addJournalLine,
                      child: Text(controller.editingLineIndex.value >= 0
                          ? 'Update'
                          : 'Add'),
                    )),
              ],
            ),
            if (controller.editingLineIndex.value >= 0) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.info, size: 16, color: Colors.blue),
                  const SizedBox(width: 8),
                  const Text('Editing line. Click "Update" to save changes.'),
                  const Spacer(),
                  TextButton(
                    onPressed: controller.clearLineForm,
                    child: const Text('Cancel Edit'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceSummary(
      BuildContext context, JournalEntryController controller) {
    return Card(
      color: controller.isBalanced
          ? Colors.green.withValues(alpha: 0.1)
          : Colors.red.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              controller.isBalanced ? Icons.check_circle : Icons.warning,
              color: controller.isBalanced ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    controller.isBalanced
                        ? 'Journal Entry is Balanced'
                        : 'Journal Entry is NOT Balanced',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: controller.isBalanced
                          ? Colors.green[700]
                          : Colors.red[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                          'Total Debits: \$${controller.totalDebits.toStringAsFixed(2)}'),
                      const SizedBox(width: 24),
                      Text(
                          'Total Credits: \$${controller.totalCredits.toStringAsFixed(2)}'),
                      if (!controller.isBalanced) ...[
                        const SizedBox(width: 24),
                        Text(
                          'Difference: \$${controller.balanceDifference.abs().toStringAsFixed(2)}',
                          style: const TextStyle(
                              color: Colors.red, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate(
      BuildContext context, JournalEntryController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.entryDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      controller.entryDate.value = picked;
    }
  }
}
