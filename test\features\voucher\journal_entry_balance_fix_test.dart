import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/payment_transaction_model.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/models/voucher_model.dart';

/// Test to verify the journal entry balance fix and sales tax inclusion
/// This test ensures payment journal entries are properly balanced and include sales tax
void main() {
  group('Journal Entry Balance Fix Tests', () {
    late VoucherModel testVoucher;
    late PaymentTransactionModel testPayment;
    late ChartOfAccountsModel bankAccount;
    late ChartOfAccountsModel truckFareAccount;
    late ChartOfAccountsModel salesTaxAccount;

    setUpAll(() {
      // Create test accounts
      bankAccount = ChartOfAccountsModel(
        id: 'cash_001',
        accountName: 'Cash Account',
        accountNumber: '1002',
        category: AccountCategory.assets,
        accountType: AccountType.cash,
        description: 'Cash account',
        isActive: true,
        balance: 10000.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'VjeIBkQWgMUNegsWpMKwcPOAfdF3',
      );

      truckFareAccount = ChartOfAccountsModel(
        id: 'truck_fare_001',
        accountName: 'Truck Fare Payable',
        accountNumber: '2100',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        description: 'Truck fare liability',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'VjeIBkQWgMUNegsWpMKwcPOAfdF3',
      );

      salesTaxAccount = ChartOfAccountsModel(
        id: 'sales_tax_001',
        accountName: '6.9% Sales Tax Payable',
        accountNumber: '2200',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        description: '6.9% sales tax liability',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'VjeIBkQWgMUNegsWpMKwcPOAfdF3',
      );

      // Create test voucher that matches the debug scenario
      testVoucher = VoucherModel(
        voucherNumber: 'V-TEST-BALANCE',
        voucherStatus: 'Active',
        departureDate: DateTime.now().toIso8601String(),
        driverName: 'Test Driver',
        invoiceTasNumberList: ['TAS001'],
        invoiceBiltyNumberList: ['BILTY001'],
        weightInTons: 1,
        productName: 'Test Product',
        totalNumberOfBags: 10,
        brokerType: 'External',
        brokerName: 'Test Broker',
        brokerFees: 0.0,
        munshianaFees: 0.0,
        brokerAccount: 'Test Broker Account',
        munshianaAccount: 'Test Munshiana Account',
        driverPhoneNumber: '**********',
        truckNumber: 'TEST-123',
        conveyNoteNumber: 'CN001',
        totalFreight:
            52.*********, // Total freight that would result in 5 payment with 0.474375 tax
        calculatedFreightTax: 3.625, // 6.9% of total freight (approximately)
        salesTaxAccountId: null, // Uses settings fallback
      );

      // Create test payment that matches the debug scenario
      testPayment = PaymentTransactionModel(
        id: '51d2dff3-df16-448f-ad11-c448516061d2',
        voucherId: testVoucher.voucherNumber,
        method: PaymentMethod.check,
        status: PaymentStatus.paid,
        amount: 5.0, // Payment amount from debug scenario
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: bankAccount.id,
        accountName: bankAccount.accountName,
      );
    });

    test('should calculate correct payment split for balance', () {
      // This test verifies the payment split calculation that fixes the balance issue

      // Arrange: Calculate expected split
      final totalPayment = testPayment.amount; // 5.0
      final proportionalSalesTax = testVoucher.totalFreight > 0
          ? (totalPayment / testVoucher.totalFreight) *
              testVoucher.calculatedFreightTax
          : 0.0;
      final freightPortion = totalPayment - proportionalSalesTax;

      print('🧮 Payment Split Calculation:');
      print('  - Total Payment: $totalPayment');
      print('  - Total Freight: ${testVoucher.totalFreight}');
      print('  - Total Sales Tax: ${testVoucher.calculatedFreightTax}');
      print(
          '  - Proportional Sales Tax: ${proportionalSalesTax.toStringAsFixed(6)}');
      print('  - Freight Portion: ${freightPortion.toStringAsFixed(6)}');

      // Act & Assert: Verify the split adds up to the total payment
      final totalSplit = freightPortion + proportionalSalesTax;
      expect(totalSplit, closeTo(totalPayment, 0.000001),
          reason:
              'Freight portion + sales tax portion should equal total payment');

      // Verify the proportional calculation is reasonable (should be positive and less than total payment)
      expect(proportionalSalesTax, greaterThan(0),
          reason: 'Proportional sales tax should be positive');
      expect(proportionalSalesTax, lessThan(totalPayment),
          reason: 'Proportional sales tax should be less than total payment');
    });

    test('should create balanced journal entries with sales tax', () {
      // This test demonstrates the fixed journal entry structure

      // Arrange: Calculate payment split
      final totalPayment = testPayment.amount;
      final proportionalSalesTax = testVoucher.totalFreight > 0
          ? (totalPayment / testVoucher.totalFreight) *
              testVoucher.calculatedFreightTax
          : 0.0;
      final freightPortion = totalPayment - proportionalSalesTax;

      // Create journal entries (simulating the fixed logic)
      final journalEntries = <Map<String, dynamic>>[];

      // 1. Source account (credit - money going out)
      journalEntries.add({
        'type': 'Source Account',
        'accountId': bankAccount.id,
        'accountName': bankAccount.accountName,
        'debitAmount': 0.0,
        'creditAmount': totalPayment, // Full payment amount
        'description':
            'Payment - Voucher #${testPayment.voucherId} - ${testPayment.method.name}',
      });

      // 2. Truck fare account (debit - freight portion only)
      journalEntries.add({
        'type': 'Truck Fare',
        'accountId': truckFareAccount.id,
        'accountName': truckFareAccount.accountName,
        'debitAmount': freightPortion, // Freight portion only
        'creditAmount': 0.0,
        'description':
            'Truck Freight Payment - Voucher #${testPayment.voucherId} - ${testPayment.method.name}',
      });

      // 3. Sales tax account (debit - sales tax portion)
      if (proportionalSalesTax > 0) {
        journalEntries.add({
          'type': 'Sales Tax (6.9%)',
          'accountId': salesTaxAccount.id,
          'accountName': salesTaxAccount.accountName,
          'debitAmount': proportionalSalesTax, // Sales tax portion
          'creditAmount': 0.0,
          'description':
              'Sales Tax Payment (6.9%) - Voucher #${testPayment.voucherId} - ${testPayment.method.name}',
        });
      }

      // Calculate totals
      final totalDebits = journalEntries.fold<double>(
          0, (sum, entry) => sum + (entry['debitAmount'] as double));
      final totalCredits = journalEntries.fold<double>(
          0, (sum, entry) => sum + (entry['creditAmount'] as double));

      print('\n📊 Journal Entry Structure:');
      for (int i = 0; i < journalEntries.length; i++) {
        final entry = journalEntries[i];
        print('  ${i + 1}. ${entry['type']}:');
        print('     - Account: ${entry['accountName']}');
        print(
            '     - Debit: ${(entry['debitAmount'] as double).toStringAsFixed(6)}');
        print(
            '     - Credit: ${(entry['creditAmount'] as double).toStringAsFixed(6)}');
      }

      print('\n💰 Balance Check:');
      print('  - Total Debits: ${totalDebits.toStringAsFixed(6)}');
      print('  - Total Credits: ${totalCredits.toStringAsFixed(6)}');
      print(
          '  - Difference: ${(totalDebits - totalCredits).abs().toStringAsFixed(6)}');

      // Assert: Journal entries should be balanced
      expect(totalDebits, closeTo(totalCredits, 0.000001),
          reason: 'Total debits should equal total credits');

      // Assert: Should have 3 journal entries
      expect(journalEntries.length, equals(3),
          reason:
              'Should create 3 journal entries: source, truck fare, and sales tax');

      // Assert: Sales tax entry should be included
      final salesTaxEntry = journalEntries.firstWhere(
          (entry) => entry['type'] == 'Sales Tax (6.9%)',
          orElse: () => <String, dynamic>{});
      expect(salesTaxEntry.isNotEmpty, isTrue,
          reason: 'Sales tax entry should be included');

      // Assert: Sales tax amount should be correct
      expect(
          salesTaxEntry['debitAmount'], closeTo(proportionalSalesTax, 0.000001),
          reason: 'Sales tax debit amount should match calculated proportion');

      print('\n✅ Journal entry balance fix verified!');
      print('   - Entries are perfectly balanced');
      print('   - Sales tax entry is included');
      print('   - Payment is properly split between freight and tax');
    });

    test('should handle edge cases correctly', () {
      // Test edge cases for the balance fix

      // Case 1: Zero sales tax
      final voucherNoTax = VoucherModel(
        voucherNumber: 'V-NO-TAX',
        voucherStatus: 'Active',
        departureDate: DateTime.now().toIso8601String(),
        driverName: 'Test Driver',
        invoiceTasNumberList: ['TAS002'],
        invoiceBiltyNumberList: ['BILTY002'],
        weightInTons: 1,
        productName: 'Test Product',
        totalNumberOfBags: 10,
        brokerType: 'External',
        brokerName: 'Test Broker',
        brokerFees: 0.0,
        munshianaFees: 0.0,
        brokerAccount: 'Test Broker Account',
        munshianaAccount: 'Test Munshiana Account',
        driverPhoneNumber: '**********',
        truckNumber: 'TEST-456',
        conveyNoteNumber: 'CN002',
        totalFreight: 100.0,
        calculatedFreightTax: 0.0, // No sales tax
        salesTaxAccountId: null,
      );

      final paymentNoTax = PaymentTransactionModel(
        id: 'payment_no_tax',
        voucherId: voucherNoTax.voucherNumber,
        method: PaymentMethod.accountTransfer,
        status: PaymentStatus.paid,
        amount: 50.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: bankAccount.id,
        accountName: bankAccount.accountName,
      );

      // Calculate split for no-tax scenario
      final salesTaxAmount = voucherNoTax.totalFreight > 0
          ? (paymentNoTax.amount / voucherNoTax.totalFreight) *
              voucherNoTax.calculatedFreightTax
          : 0.0;
      final freightAmount = paymentNoTax.amount - salesTaxAmount;

      expect(salesTaxAmount, equals(0.0),
          reason: 'Sales tax amount should be zero when voucher has no tax');

      expect(freightAmount, equals(paymentNoTax.amount),
          reason: 'Freight amount should equal full payment when no tax');

      // Case 2: Very small payment
      final smallPayment = PaymentTransactionModel(
        id: 'payment_small',
        voucherId: testVoucher.voucherNumber,
        method: PaymentMethod.cash,
        status: PaymentStatus.paid,
        amount: 0.01, // Very small payment
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: bankAccount.id,
        accountName: bankAccount.accountName,
      );

      final smallSalesTax = testVoucher.totalFreight > 0
          ? (smallPayment.amount / testVoucher.totalFreight) *
              testVoucher.calculatedFreightTax
          : 0.0;
      final smallFreight = smallPayment.amount - smallSalesTax;

      expect(
          smallFreight + smallSalesTax, closeTo(smallPayment.amount, 0.000001),
          reason: 'Small payment should still split correctly');

      print('\n✅ Edge cases handled correctly!');
    });
  });
}
