import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/firebase_service/finance/account_transaction_firebase_service.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';
import 'package:logestics/models/finance/expense_model.dart';
import 'package:uuid/uuid.dart';

class ExpenseFirebaseService {
  late FirebaseFirestore _firestore;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AccountTransactionFirebaseService _transactionService =
      AccountTransactionFirebaseService();

  ExpenseFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  // Get current user UID or return a default value if not authenticated
  String get _uid => _auth.currentUser?.uid ?? 'anonymous';

  Future<void> createExpense(ExpenseModel expense,
      {File? file, Uint8List? fileBytes, String? fileName}) async {
    log('Creating expense for account: ${expense.accountName}');
    try {
      // Check if this expense uses Chart of Accounts
      if (expense.usesChartOfAccounts) {
        log('Expense uses Chart of Accounts, skipping legacy account validation');
        await _createExpenseWithChartOfAccounts(expense,
            file: file, fileBytes: fileBytes, fileName: fileName);
        return;
      }

      // Legacy account validation for backward compatibility
      final accountRef = _firestore
          .collection(AppCollection.accountsCollection)
          .doc(expense.accountId);

      final accountDoc = await accountRef.get();
      if (!accountDoc.exists) {
        throw Exception('Account not found');
      }

      // Generate a new ID for the expense
      final expenseRef =
          _firestore.collection(AppCollection.expensesCollection).doc();
      final expenseId = expenseRef.id;

      // Prepare expense data
      final expenseData = expense.toJson();
      expenseData['id'] = expenseId;
      expenseData['uid'] = _uid;

      // Store file name if provided

      // Create the expense
      await expenseRef.set(expenseData);

      // Create account transaction
      try {
        final transaction = AccountTransactionModel(
          id: const Uuid().v4(),
          uid: _uid,
          accountId: expense.accountId,
          accountName: expense.accountName,
          amount: -expense.amount, // Negative amount for expense
          transactionDate: expense.createdAt,
          type: TransactionType.expense, // Changed from expense to withdrawal
          description: 'Expense: ${expense.title}',
          payeeId: expense.payeeId,
          payeeName: expense.payeeName,
          referenceId: expenseId,
          referenceName: expense.referenceNumber,
          category: expense.categoryName,
          metadata: {
            'expenseId': expenseId,
            'categoryId': expense.categoryId,
            'categoryName': expense.categoryName,
            'notes': expense.notes,
          },
        );

        await _transactionService.createTransaction(transaction);
        log('Created account transaction for expense: $expenseId');
      } catch (e) {
        log('Error creating account transaction for expense: $e');
        // Don't throw error as the main expense was already created
      }

      log('Successfully created expense: $expenseId');
    } catch (e) {
      log('Error creating expense: $e');
      rethrow;
    }
  }

  /// Create expense using Chart of Accounts (no legacy account validation needed)
  Future<void> _createExpenseWithChartOfAccounts(ExpenseModel expense,
      {File? file, Uint8List? fileBytes, String? fileName}) async {
    log('Creating Chart of Accounts expense: ${expense.amount}');
    try {
      // Generate a new ID for the expense
      final expenseRef =
          _firestore.collection(AppCollection.expensesCollection).doc();
      final expenseId = expenseRef.id;

      // Prepare expense data
      final expenseData = expense.toJson();
      expenseData['id'] = expenseId;
      expenseData['uid'] = _uid;

      // Create the expense document (no account balance update needed for Chart of Accounts)
      await expenseRef.set(expenseData);

      log('Successfully created Chart of Accounts expense: $expenseId');
    } catch (e) {
      log('Error creating Chart of Accounts expense: $e');
      rethrow;
    }
  }

  /// Create expense for external company (cross-company operations)
  Future<void> createExpenseForExternalCompany(
    ExpenseModel expense,
    String externalCompanyUid, {
    File? file,
    Uint8List? fileBytes,
    String? fileName,
  }) async {
    log('Creating cross-company expense for account: ${expense.accountName}, company: $externalCompanyUid');
    try {
      if (externalCompanyUid.isEmpty) {
        throw ArgumentError('External company UID cannot be empty');
      }

      // Validate the account exists for the external company
      final accountRef = _firestore
          .collection(AppCollection.accountsCollection)
          .doc(expense.accountId);

      final accountDoc = await accountRef.get();
      if (!accountDoc.exists) {
        throw Exception('Account not found for external company');
      }

      // Verify the account belongs to the external company
      final accountData = accountDoc.data() as Map<String, dynamic>;
      if (accountData['uid'] != externalCompanyUid) {
        throw Exception(
            'Account does not belong to the specified external company');
      }

      // Generate a new ID for the expense
      final expenseRef =
          _firestore.collection(AppCollection.expensesCollection).doc();
      final expenseId = expenseRef.id;

      // Prepare expense data with external company UID
      final expenseData = expense.toJson();
      expenseData['id'] = expenseId;
      expenseData['uid'] = externalCompanyUid; // Set to external company's UID

      // Create the expense document
      await expenseRef.set(expenseData);

      // Update account balance for external company
      await _updateExternalAccountBalance(
          expense.accountId, expense.amount, externalCompanyUid);

      // Create account transaction for external company
      try {
        final transaction = AccountTransactionModel(
          id: '',
          accountId: expense.accountId,
          accountName: expense.accountName,
          amount:
              -expense.amount, // Negative amount for expense (money going out)
          transactionDate: expense.createdAt,
          type: TransactionType.expense,
          description: expense.title,
          referenceId: expense.referenceNumber,
          referenceName: expense.title,
          payeeId: expense.payeeId,
          payeeName: expense.payeeName,
          category: expense.categoryName,
          uid: externalCompanyUid, // Set to external company's UID
        );

        await _transactionService.createTransactionForExternalCompany(
            transaction, externalCompanyUid);
        log('Created cross-company account transaction for expense: $expenseId');
      } catch (e) {
        log('Error creating cross-company account transaction for expense: $e');
        // Don't throw error as the main expense was already created
      }

      log('Successfully created cross-company expense: $expenseId for company: $externalCompanyUid');
    } catch (e) {
      log('Error creating cross-company expense: $e');
      rethrow;
    }
  }

  /// Update account balance for external company
  Future<void> _updateExternalAccountBalance(
    String accountId,
    double expenseAmount,
    String externalCompanyUid,
  ) async {
    try {
      final accountRef = _firestore
          .collection(AppCollection.accountsCollection)
          .doc(accountId);

      await _firestore.runTransaction((transaction) async {
        final accountDoc = await transaction.get(accountRef);

        if (!accountDoc.exists) {
          throw Exception('Account not found');
        }

        final accountData = accountDoc.data() as Map<String, dynamic>;

        // Verify account belongs to external company
        if (accountData['uid'] != externalCompanyUid) {
          throw Exception(
              'Account does not belong to the specified external company');
        }

        final currentBalance =
            (accountData['availableBalance'] as num).toDouble();
        final newBalance =
            currentBalance - expenseAmount; // Subtract expense amount

        transaction.update(accountRef, {'availableBalance': newBalance});

        log('Updated external company account balance: $accountId, old: $currentBalance, new: $newBalance');
      });
    } catch (e) {
      log('Error updating external company account balance: $e');
      rethrow;
    }
  }

  Future<List<ExpenseModel>> getExpenses() async {
    log('Fetching expenses from Firestore');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.expensesCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .get();

      final expenses = snapshot.docs
          .map((doc) => ExpenseModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${expenses.length} expenses');
      return expenses;
    } catch (e) {
      log('Error fetching expenses: $e');
      rethrow;
    }
  }

  Future<List<ExpenseModel>> getExpensesByAccount(String accountId) async {
    log('Fetching expenses for account: $accountId');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.expensesCollection)
          .where('accountId', isEqualTo: accountId)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .get();

      final expenses = snapshot.docs
          .map((doc) => ExpenseModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${expenses.length} expenses for account: $accountId');
      return expenses;
    } catch (e) {
      log('Error fetching expenses by account: $e');
      rethrow;
    }
  }

  Future<List<ExpenseModel>> getExpensesByCategory(String categoryId) async {
    log('Fetching expenses for category: $categoryId');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.expensesCollection)
          .where('categoryId', isEqualTo: categoryId)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .get();

      final expenses = snapshot.docs
          .map((doc) => ExpenseModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${expenses.length} expenses for category: $categoryId');
      return expenses;
    } catch (e) {
      log('Error fetching expenses by category: $e');
      rethrow;
    }
  }

  Future<List<ExpenseModel>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    log('Fetching expenses between ${startDate.toIso8601String()} and ${endDate.toIso8601String()}');
    try {
      final startDateTIme = startDate.millisecondsSinceEpoch;
      final endDateTIme = endDate.millisecondsSinceEpoch;

      final snapshot = await _firestore
          .collection(AppCollection.expensesCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .where('dateTime', isGreaterThanOrEqualTo: startDateTIme)
          .where('dateTime', isLessThanOrEqualTo: endDateTIme)
          .orderBy('dateTime', descending: true)
          .get();

      final expenses = snapshot.docs
          .map((doc) => ExpenseModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${expenses.length} expenses in date range');
      return expenses;
    } catch (e) {
      log('Error fetching expenses by date range: $e');
      rethrow;
    }
  }

  Future<void> updateExpense(
    ExpenseModel expense, {
    Uint8List? fileBytes,
    String? fileName,
    dynamic file,
  }) async {
    log('Updating expense: ${expense.id}');
    try {
      if (expense.id.isEmpty) {
        throw ArgumentError('Expense ID cannot be empty');
      }

      // First check if the expense belongs to the current user
      final expenseDoc = await _firestore
          .collection(AppCollection.expensesCollection)
          .doc(expense.id)
          .get();

      if (!expenseDoc.exists) {
        throw Exception('Expense not found');
      }

      final expenseData = expenseDoc.data() as Map<String, dynamic>;
      if (expenseData['uid'] != _uid) {
        throw Exception('You do not have permission to update this expense');
      }

      final expenseRef = _firestore
          .collection(AppCollection.expensesCollection)
          .doc(expense.id);
      final updatedExpenseData = expense.toJson();

      // Remove file upload code since we're not using Firebase Storage anymore
      if (fileBytes != null || file != null) {
        log('File upload not supported: Firebase Storage has been removed');
      }

      // Preserve the original UID
      updatedExpenseData['uid'] = expenseData['uid'];

      await expenseRef.update(updatedExpenseData);
      log('Successfully updated expense: ${expense.id}');
    } catch (e) {
      log('Error updating expense: $e');
      rethrow;
    }
  }

  Future<ExpenseModel?> getExpenseById(String expenseId) async {
    log('Fetching expense by ID: $expenseId');
    try {
      if (expenseId.isEmpty) {
        throw ArgumentError('Expense ID cannot be empty');
      }

      final expenseDoc = await _firestore
          .collection(AppCollection.expensesCollection)
          .doc(expenseId)
          .get();

      if (!expenseDoc.exists) {
        log('Expense not found: $expenseId');
        return null;
      }

      final expenseData = expenseDoc.data() as Map<String, dynamic>;
      if (expenseData['uid'] != _uid) {
        log('Expense does not belong to current user: $expenseId');
        return null;
      }

      return ExpenseModel.fromJson(expenseData);
    } catch (e) {
      log('Error fetching expense by ID: $e');
      rethrow;
    }
  }

  Future<void> deleteExpense(String expenseId) async {
    log('Deleting expense: $expenseId');
    try {
      if (expenseId.isEmpty) {
        throw ArgumentError('Expense ID cannot be empty');
      }

      // First check if the expense belongs to the current user
      final expenseDoc = await _firestore
          .collection(AppCollection.expensesCollection)
          .doc(expenseId)
          .get();

      if (!expenseDoc.exists) {
        throw Exception('Expense not found');
      }

      final expenseData = expenseDoc.data() as Map<String, dynamic>;
      if (expenseData['uid'] != _uid) {
        throw Exception('You do not have permission to delete this expense');
      }

      // Delete the expense document
      await _firestore
          .collection(AppCollection.expensesCollection)
          .doc(expenseId)
          .delete();

      // Delete associated account transactions
      try {
        final transactionsSnapshot = await _firestore
            .collection(AppCollection.transactionsCollection)
            .where('referenceId', isEqualTo: expenseId)
            .get();

        for (final doc in transactionsSnapshot.docs) {
          await doc.reference.delete();
        }
        log('Deleted associated account transactions for expense: $expenseId');
      } catch (e) {
        log('Error deleting account transactions for expense: $e');
        // Don't throw error as the main expense was already deleted
      }

      log('Successfully deleted expense: $expenseId');
    } catch (e) {
      log('Error deleting expense: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to expenses
  Stream<List<ExpenseModel>> listenToExpenses() {
    try {
      return _firestore
          .collection(AppCollection.expensesCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => ExpenseModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to expenses: $e', error: e);
      return Stream.value([]);
    }
  }
}
