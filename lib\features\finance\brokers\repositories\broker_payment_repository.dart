import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/firebase_service/finance/broker_payment_firebase_service.dart';
import 'package:logestics/models/finance/broker_model.dart';

class BrokerPaymentRepository {
  final BrokerPaymentFirebaseService _firebaseService;

  BrokerPaymentRepository(this._firebaseService);

  Future<Either<FailureObj, void>> createBrokerPayment(
      BrokerPaymentModel payment) async {
    try {
      await _firebaseService.createBrokerPayment(payment);
      return const Right(null);
    } catch (e) {
      return Left(FailureObj(
          code: 'broker-payment-creation-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, List<BrokerPaymentModel>>>
      getBrokerPayments() async {
    try {
      final payments = await _firebaseService.getBrokerPayments();
      return Right(payments);
    } catch (e) {
      return Left(FailureObj(
          code: 'broker-payment-fetch-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, List<BrokerPaymentModel>>>
      getBrokerPaymentsByBrokerId(String brokerId) async {
    try {
      final payments =
          await _firebaseService.getBrokerPaymentsByBrokerId(brokerId);
      return Right(payments);
    } catch (e) {
      return Left(FailureObj(
          code: 'broker-payment-fetch-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, void>> updateBrokerPayment(
      BrokerPaymentModel payment) async {
    try {
      await _firebaseService.updateBrokerPayment(payment);
      return const Right(null);
    } catch (e) {
      return Left(FailureObj(
          code: 'broker-payment-update-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, void>> deleteBrokerPayment(String paymentId) async {
    try {
      await _firebaseService.deleteBrokerPayment(paymentId);
      return const Right(null);
    } catch (e) {
      return Left(FailureObj(
          code: 'broker-payment-delete-error', message: e.toString()));
    }
  }

  Stream<List<BrokerPaymentModel>> listenToBrokerPayments() {
    return _firebaseService.listenToBrokerPayments();
  }

  Stream<List<BrokerPaymentModel>> listenToBrokerPaymentsByBrokerId(
      String brokerId) {
    return _firebaseService.listenToBrokerPaymentsByBrokerId(brokerId);
  }

  Future<Either<FailureObj, void>> createBrokerTransaction(
      BrokerTransactionModel transaction) async {
    try {
      await _firebaseService.createBrokerTransaction(transaction);
      return const Right(null);
    } catch (e) {
      return Left(FailureObj(
          code: 'broker-transaction-creation-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, List<BrokerTransactionModel>>>
      getBrokerTransactionsByBrokerId(String brokerId) async {
    try {
      final transactions =
          await _firebaseService.getBrokerTransactionsByBrokerId(brokerId);
      return Right(transactions);
    } catch (e) {
      return Left(FailureObj(
          code: 'broker-transaction-fetch-error', message: e.toString()));
    }
  }

  Stream<List<BrokerTransactionModel>> listenToBrokerTransactionsByBrokerId(
      String brokerId) {
    return _firebaseService.listenToBrokerTransactionsByBrokerId(brokerId);
  }

  Future<Either<FailureObj, double>> calculateBrokerBalance(
      String brokerId) async {
    try {
      final balance = await _firebaseService.calculateBrokerBalance(brokerId);
      return Right(balance);
    } catch (e) {
      return Left(FailureObj(
          code: 'broker-balance-calculation-error', message: e.toString()));
    }
  }

  Future<Either<FailureObj, Map<String, dynamic>>> getBrokerFinancialSummary(
      String brokerId) async {
    try {
      final summary =
          await _firebaseService.getBrokerFinancialSummary(brokerId);
      return Right(summary);
    } catch (e) {
      return Left(FailureObj(
          code: 'broker-summary-fetch-error', message: e.toString()));
    }
  }
}
