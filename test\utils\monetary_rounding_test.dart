import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/core/utils/constants/constants.dart';

void main() {
  group('MonetaryRounding Tests', () {
    group('roundHalfUp', () {
      test('should round down when decimal < 0.50', () {
        expect(MonetaryRounding.roundHalfUp(123.49), equals(123.0));
        expect(MonetaryRounding.roundHalfUp(123.25), equals(123.0));
        expect(MonetaryRounding.roundHalfUp(123.01), equals(123.0));
        expect(MonetaryRounding.roundHalfUp(123.499), equals(123.0));
      });

      test('should round down when decimal = 0.50', () {
        expect(MonetaryRounding.roundHalfUp(123.50), equals(123.0));
        expect(MonetaryRounding.roundHalfUp(0.50), equals(0.0));
        expect(MonetaryRounding.roundHalfUp(999.50), equals(999.0));
      });

      test('should round up when decimal > 0.50', () {
        expect(MonetaryRounding.roundHalfUp(123.75), equals(124.0));
        expect(MonetaryRounding.roundHalfUp(123.51), equals(124.0));
        expect(MonetaryRounding.roundHalfUp(123.99), equals(124.0));
        expect(MonetaryRounding.roundHalfUp(123.501), equals(124.0));
      });

      test('should handle whole numbers correctly', () {
        expect(MonetaryRounding.roundHalfUp(123.00), equals(123.0));
        expect(MonetaryRounding.roundHalfUp(0.0), equals(0.0));
        expect(MonetaryRounding.roundHalfUp(999.0), equals(999.0));
      });

      test('should handle negative numbers correctly', () {
        expect(MonetaryRounding.roundHalfUp(-123.49), equals(-123.0));
        expect(MonetaryRounding.roundHalfUp(-123.50), equals(-123.0));
        expect(MonetaryRounding.roundHalfUp(-123.75), equals(-124.0));
        expect(MonetaryRounding.roundHalfUp(-123.00), equals(-123.0));
      });

      test('should handle edge cases', () {
        expect(MonetaryRounding.roundHalfUp(0.0), equals(0.0));
        expect(MonetaryRounding.roundHalfUp(double.nan), equals(0.0));
        expect(MonetaryRounding.roundHalfUp(double.infinity), equals(0.0));
        expect(MonetaryRounding.roundHalfUp(double.negativeInfinity), equals(0.0));
      });

      test('should match the specified examples exactly', () {
        // Examples from requirements
        expect(MonetaryRounding.roundHalfUp(123.49), equals(123.0));
        expect(MonetaryRounding.roundHalfUp(123.50), equals(123.0));
        expect(MonetaryRounding.roundHalfUp(123.75), equals(124.0));
        expect(MonetaryRounding.roundHalfUp(123.00), equals(123.0));
      });

      test('should handle large numbers', () {
        expect(MonetaryRounding.roundHalfUp(1234567.49), equals(1234567.0));
        expect(MonetaryRounding.roundHalfUp(1234567.50), equals(1234567.0));
        expect(MonetaryRounding.roundHalfUp(1234567.75), equals(1234568.0));
      });

      test('should handle small decimal numbers', () {
        expect(MonetaryRounding.roundHalfUp(0.49), equals(0.0));
        expect(MonetaryRounding.roundHalfUp(0.50), equals(0.0));
        expect(MonetaryRounding.roundHalfUp(0.75), equals(1.0));
        expect(MonetaryRounding.roundHalfUp(0.99), equals(1.0));
      });
    });

    group('roundHalfUpToInt', () {
      test('should return integer values', () {
        expect(MonetaryRounding.roundHalfUpToInt(123.49), equals(123));
        expect(MonetaryRounding.roundHalfUpToInt(123.50), equals(123));
        expect(MonetaryRounding.roundHalfUpToInt(123.75), equals(124));
        expect(MonetaryRounding.roundHalfUpToInt(123.00), equals(123));
      });

      test('should handle negative integers', () {
        expect(MonetaryRounding.roundHalfUpToInt(-123.49), equals(-123));
        expect(MonetaryRounding.roundHalfUpToInt(-123.50), equals(-123));
        expect(MonetaryRounding.roundHalfUpToInt(-123.75), equals(-124));
      });
    });

    group('roundFinancialAmounts', () {
      test('should round all amounts in a map', () {
        final amounts = {
          'amount1': 123.49,
          'amount2': 123.50,
          'amount3': 123.75,
          'amount4': 123.00,
        };

        final rounded = MonetaryRounding.roundFinancialAmounts(amounts);

        expect(rounded['amount1'], equals(123.0));
        expect(rounded['amount2'], equals(123.0));
        expect(rounded['amount3'], equals(124.0));
        expect(rounded['amount4'], equals(123.0));
      });

      test('should handle empty map', () {
        final amounts = <String, double>{};
        final rounded = MonetaryRounding.roundFinancialAmounts(amounts);
        expect(rounded, isEmpty);
      });

      test('should handle mixed positive and negative amounts', () {
        final amounts = {
          'positive': 123.75,
          'negative': -123.75,
          'zero': 0.0,
          'halfDown': 123.50,
        };

        final rounded = MonetaryRounding.roundFinancialAmounts(amounts);

        expect(rounded['positive'], equals(124.0));
        expect(rounded['negative'], equals(-124.0));
        expect(rounded['zero'], equals(0.0));
        expect(rounded['halfDown'], equals(123.0));
      });
    });

    group('Real-world billing scenarios', () {
      test('should handle typical invoice calculations', () {
        // Simulate typical slab billing calculations
        final invoiceAmount1 = 5.0 * 150.0 * 2.5; // 1875.0 (exact)
        final invoiceAmount2 = 5.0 * 150.0 * 2.333; // 1749.75
        final invoiceAmount3 = 5.0 * 150.0 * 2.334; // 1750.5

        expect(MonetaryRounding.roundHalfUp(invoiceAmount1), equals(1875.0));
        expect(MonetaryRounding.roundHalfUp(invoiceAmount2), equals(1750.0));
        expect(MonetaryRounding.roundHalfUp(invoiceAmount3), equals(1750.0)); // 0.5 rounds down
      });

      test('should handle batch calculations with rounding', () {
        final invoiceAmounts = [1234.49, 567.50, 890.75, 123.00];
        final totalBeforeRounding = invoiceAmounts.reduce((a, b) => a + b); // 2815.74
        
        // Round individual amounts first, then sum
        final roundedAmounts = invoiceAmounts.map(MonetaryRounding.roundHalfUp).toList();
        final totalAfterIndividualRounding = roundedAmounts.reduce((a, b) => a + b);
        
        // Round the total
        final roundedTotal = MonetaryRounding.roundHalfUp(totalBeforeRounding);

        expect(roundedAmounts, equals([1234.0, 567.0, 891.0, 123.0]));
        expect(totalAfterIndividualRounding, equals(2815.0));
        expect(roundedTotal, equals(2816.0)); // 2815.74 rounds up
      });
    });
  });
}
