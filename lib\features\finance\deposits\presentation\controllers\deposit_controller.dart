// ignore_for_file: overridden_fields

import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
import 'package:logestics/features/finance/deposit_categories/repositories/deposit_category_repository.dart';
import 'package:logestics/features/finance/deposits/repository/deposit_repository.dart';
import 'package:logestics/features/finance/payers/repository/payer_repository.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';
import 'package:logestics/models/finance/deposit_category_model.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/models/finance/payer_model.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import 'package:uuid/uuid.dart';

class DepositController extends GetxController
    with PaginationMixin, AutoRefreshMixin {
  final DepositRepository depositRepository;
  final AccountRepository accountRepository;
  final PayerRepository payerRepository;
  final DepositCategoryRepository categoryRepository;
  final ChartOfAccountsRepository chartOfAccountsRepository;

  // Observable lists
  final deposits = <DepositModel>[].obs;
  final filteredDeposits = <DepositModel>[].obs;
  final accounts = <AccountModel>[].obs;
  final payers = <PayerModel>[].obs;
  final categories = <DepositCategoryModel>[].obs;
  final chartOfAccounts = <ChartOfAccountsModel>[].obs;

  // Selected items
  final selectedAccount = Rxn<AccountModel>();
  final selectedPayer = Rxn<PayerModel>();
  final selectedCategory = Rxn<DepositCategoryModel>();

  // Chart of Accounts selections
  final selectedSourceAccount = Rxn<ChartOfAccountsModel>();
  final selectedDestinationAccount = Rxn<ChartOfAccountsModel>();
  final selectedDate = Rx<DateTime>(DateTime.now());

  // Form controllers
  final amountController = TextEditingController();
  final referenceController = TextEditingController();
  final notesController = TextEditingController();
  final searchController = TextEditingController();

  // UI state
  final isLoading = false.obs;
  final isLoadingAccounts = false.obs;
  final isLoadingPayers = false.obs;
  final isLoadingCategories = false.obs;
  final isDeleting = false.obs;
  final isDrawerOpen = false.obs;
  final isAddAccountOpen = false.obs;
  final isAddPayerOpen = false.obs;
  final isAddCategoryOpen = false.obs;

  @override
  final currentPage = 1.obs;
  @override
  final itemsPerPage = 10.obs;
  final searchQuery = ''.obs;

  // File upload
  // final selectedFile = Rx<dynamic>(null);
  // final selectedFileBytes = Rx<List<int>?>(null);
  // final selectedFileName = ''.obs;
  // final selectedFileSize = 0.obs;
  // final isImage = false.obs;
  // final isWebFile = false.obs;

  // Form key
  final formKey = GlobalKey<FormState>();

  // Additional form controllers for dialog forms
  final accountNameController = TextEditingController();
  final payerNameController = TextEditingController();
  final categoryNameController = TextEditingController();

  // Balance validation
  final balanceValidationMessage = ''.obs;
  final hasInsufficientBalance = false.obs;

  // Stream subscription for real-time updates
  StreamSubscription<List<DepositModel>>? _depositsSubscription;

  // Constants
  // static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  // static const List<String> allowedFileTypes = [
  //   'pdf',
  //   'doc',
  //   'docx',
  //   'xls',
  //   'xlsx',
  //   'jpg',
  //   'jpeg',
  //   'png'
  // ];

  DepositController({
    required this.depositRepository,
    required this.accountRepository,
    required this.payerRepository,
    required this.categoryRepository,
    required this.chartOfAccountsRepository,
  });

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    amountController.addListener(_validateBalance);
    // Set up real-time deposit updates
    _setupRealTimeUpdates();
    // AutoRefreshMixin will handle calling refreshData for other data
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterDeposits();
  }

  void _validateBalance() {
    final amount = double.tryParse(amountController.text);
    if (amount != null && amount <= 0) {
      hasInsufficientBalance.value = true;
      balanceValidationMessage.value = 'Amount must be greater than 0';
    } else {
      hasInsufficientBalance.value = false;
      balanceValidationMessage.value = '';
    }
  }

  /// Implementation of AutoRefreshMixin.refreshData
  @override
  Future<void> refreshData() async {
    await Future.wait([
      fetchAccounts(),
      fetchPayers(),
      fetchCategories(),
    ]);
    // Deposits are handled by real-time stream, no need to fetch manually
  }

  /// Set up real-time deposit updates
  void _setupRealTimeUpdates() {
    // Cancel existing subscription
    _depositsSubscription?.cancel();

    // Set up real-time listener for deposits
    _depositsSubscription = depositRepository.listenToDeposits().listen(
      (depositsList) {
        log('Real-time update: received ${depositsList.length} deposits');
        deposits.assignAll(depositsList);
        _filterDeposits();
      },
      onError: (error) {
        log('Error in real-time deposits stream: $error');
        // Fallback to manual fetch on error
        fetchDeposits();
      },
    );

    // Listen for changes in deposits and update filtered list
    ever(deposits, (_) => _filterDeposits());
    ever(searchQuery, (_) => _filterDeposits());
  }

  // CRUD Operations

  Future<void> fetchDeposits() async {
    try {
      isLoading.value = true;
      final result = await depositRepository.getDeposits();
      result.fold(
        (failure) => {
          log('Failed to fetch deposits: ${failure.message}'),
          SnackbarUtils.showError(
              AppStrings.errorS, 'Failed to load deposits: ${failure.message}'),
        },
        (depositsList) {
          deposits.assignAll(depositsList);
          _filterDeposits();
        },
      );
    } catch (e) {
      log('Error loading deposits: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load deposits');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAccounts() async {
    try {
      isLoadingAccounts.value = true;
      final result = await accountRepository.getAccounts();
      result.fold(
        (failure) => {
          log('Failed to fetch accounts: ${failure.message}'),
          SnackbarUtils.showError(
              AppStrings.errorS, 'Failed to load accounts: ${failure.message}'),
        },
        (accountsList) {
          // Remove duplicates and validate selected account
          final uniqueAccounts = _removeDuplicateAccounts(accountsList);

          if (selectedAccount.value != null) {
            final stillExists = uniqueAccounts
                .any((acc) => acc.id == selectedAccount.value!.id);
            if (!stillExists) {
              selectedAccount.value = null;
              _validateBalance(); // Clear balance validation
            } else {
              // Update selected account with fresh data to ensure same object reference
              selectedAccount.value = uniqueAccounts
                  .firstWhere((acc) => acc.id == selectedAccount.value!.id);
              _validateBalance(); // Re-validate with updated balance
            }
          }
          accounts.assignAll(uniqueAccounts);
          log('Successfully fetched ${uniqueAccounts.length} unique accounts');
        },
      );
    } catch (e) {
      log('Error loading accounts: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load accounts');
    } finally {
      isLoadingAccounts.value = false;
    }
  }

  Future<void> fetchPayers() async {
    try {
      isLoadingPayers.value = true;
      final result = await payerRepository.getPayers();
      result.fold(
        (failure) => {
          log('Failed to fetch payers: ${failure.message}'),
          SnackbarUtils.showError(
              AppStrings.errorS, 'Failed to load payers: ${failure.message}'),
        },
        (payersList) {
          // Remove duplicates and validate selected payer
          final uniquePayers = _removeDuplicatePayers(payersList);

          if (selectedPayer.value != null) {
            final stillExists = uniquePayers
                .any((payer) => payer.id == selectedPayer.value!.id);
            if (!stillExists) {
              selectedPayer.value = null;
            } else {
              // Update with fresh data from the list to ensure same object reference
              selectedPayer.value = uniquePayers
                  .firstWhere((payer) => payer.id == selectedPayer.value!.id);
            }
          }
          payers.assignAll(uniquePayers);
          log('Successfully fetched ${uniquePayers.length} unique payers');
        },
      );
    } catch (e) {
      log('Error loading payers: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load payers');
    } finally {
      isLoadingPayers.value = false;
    }
  }

  Future<void> fetchCategories() async {
    try {
      isLoadingCategories.value = true;
      final result = await categoryRepository.getCategories();
      result.fold(
        (failure) => {
          log('Failed to fetch categories: ${failure.message}'),
          SnackbarUtils.showError(AppStrings.errorS,
              'Failed to load categories: ${failure.message}'),
        },
        (categoriesList) {
          // Remove duplicates and validate selected category
          final uniqueCategories = _removeDuplicateCategories(categoriesList);

          if (selectedCategory.value != null) {
            final stillExists = uniqueCategories
                .any((cat) => cat.id == selectedCategory.value!.id);
            if (!stillExists) {
              selectedCategory.value = null;
            } else {
              // Update with fresh data from the list to ensure same object reference
              selectedCategory.value = uniqueCategories
                  .firstWhere((cat) => cat.id == selectedCategory.value!.id);
            }
          }
          categories.assignAll(uniqueCategories);
          log('Successfully fetched ${uniqueCategories.length} unique categories');
        },
      );
    } catch (e) {
      log('Error loading categories: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load categories');
    } finally {
      isLoadingCategories.value = false;
    }
  }

  // Helper methods to remove duplicates
  List<DepositCategoryModel> _removeDuplicateCategories(
      List<DepositCategoryModel> categories) {
    final Map<String, DepositCategoryModel> uniqueMap = {};
    for (final category in categories) {
      uniqueMap[category.id] = category;
    }
    return uniqueMap.values.toList();
  }

  List<PayerModel> _removeDuplicatePayers(List<PayerModel> payers) {
    final Map<String, PayerModel> uniqueMap = {};
    for (final payer in payers) {
      uniqueMap[payer.id] = payer;
    }
    return uniqueMap.values.toList();
  }

  List<AccountModel> _removeDuplicateAccounts(List<AccountModel> accounts) {
    final Map<String, AccountModel> uniqueMap = {};
    for (final account in accounts) {
      uniqueMap[account.id] = account;
    }
    return uniqueMap.values.toList();
  }

  // Enhanced setter methods to ensure proper object references
  void setSelectedAccount(AccountModel? account) {
    if (account != null) {
      // Find the exact object from the current list to ensure proper reference
      final existingAccount =
          accounts.firstWhereOrNull((acc) => acc.id == account.id);
      selectedAccount.value = existingAccount ?? account;
    } else {
      selectedAccount.value = null;
    }
    _validateBalance();
  }

  void setSelectedCategory(DepositCategoryModel? category) {
    if (category != null) {
      // Find the exact object from the current list to ensure proper reference
      final existingCategory =
          categories.firstWhereOrNull((cat) => cat.id == category.id);
      selectedCategory.value = existingCategory ?? category;
    } else {
      selectedCategory.value = null;
    }
  }

  void setSelectedPayer(PayerModel? payer) {
    if (payer != null) {
      // Find the exact object from the current list to ensure proper reference
      final existingPayer = payers.firstWhereOrNull((p) => p.id == payer.id);
      selectedPayer.value = existingPayer ?? payer;
    } else {
      selectedPayer.value = null;
    }
  }

  void setDate(DateTime date) {
    selectedDate.value = date;
  }

  void _filterDeposits() {
    if (searchQuery.value.isEmpty) {
      filteredDeposits.assignAll(deposits);
    } else {
      final query = searchQuery.value.toLowerCase();
      filteredDeposits.assignAll(deposits.where((deposit) {
        return deposit.accountName.toLowerCase().contains(query) ||
            deposit.payerName.toLowerCase().contains(query) ||
            deposit.referenceNumber.toLowerCase().contains(query) ||
            deposit.categoryName.toLowerCase().contains(query);
      }).toList());
    }
    setTotalItems(filteredDeposits.length);
  }

  List<DepositModel> get paginatedDeposits => paginateList(filteredDeposits);

  void openDrawer() {
    // Refresh all form data when opening drawer
    refreshFormData();
    isDrawerOpen.value = true;
  }

  void closeDrawer() {
    isDrawerOpen.value = false;
    clearForm();
  }

  void openAddAccount() {
    isAddAccountOpen.value = true;
  }

  void closeAddAccount() {
    isAddAccountOpen.value = false;
  }

  void openAddPayer() {
    isAddPayerOpen.value = true;
  }

  void closeAddPayer() {
    isAddPayerOpen.value = false;
  }

  void openAddCategory() {
    isAddCategoryOpen.value = true;
  }

  void closeAddCategory() {
    isAddCategoryOpen.value = false;
  }

  void clearForm() {
    amountController.clear();
    referenceController.clear();
    notesController.clear();
    selectedAccount.value = null;
    selectedPayer.value = null;
    selectedCategory.value = null;
    selectedDate.value = DateTime.now();
    // selectedFile.value = null;
    // selectedFileName.value = '';
    // selectedFileSize.value = 0;
    // selectedFileBytes.value = null;
    // isWebFile.value = false;
    // isImage.value = false;
    balanceValidationMessage.value = '';
    hasInsufficientBalance.value = false;
    // Clear Chart of Accounts selections
    selectedSourceAccount.value = null;
    selectedDestinationAccount.value = null;
  }

  // Future<void> pickFile() async {
  //   try {
  //     FilePickerResult? result = await FilePicker.platform.pickFiles(
  //       type: FileType.custom,
  //       allowedExtensions: allowedFileTypes,
  //       withData: kIsWeb,
  //       compressionQuality: 50,
  //     );

  //     if (result != null && result.files.isNotEmpty) {
  //       final file = result.files.single;

  //       if (kIsWeb) {
  //         if (file.bytes == null) {
  //           SnackbarUtils.showError(
  //               AppStrings.error, 'Could not read the file data');
  //           return;
  //         }

  //         final fileSize = file.size;

  //         if (fileSize > maxFileSize) {
  //           SnackbarUtils.showError(
  //               AppStrings.error, 'File size exceeds 10MB limit');
  //           return;
  //         }

  //         selectedFile.value = null;
  //         selectedFileName.value = file.name;
  //         selectedFileSize.value = fileSize;
  //         selectedFileBytes.value = file.bytes;
  //         isWebFile.value = true;
  //         isImage.value = isImageFile(file.name);

  //         log('Web file selected: ${file.name}, size: ${fileSize / 1024 / 1024} MB, isImage: ${isImage.value}');
  //       } else {
  //         final path = file.path;
  //         if (path != null) {
  //           final fileSize = await File(path).length();

  //           if (fileSize > maxFileSize) {
  //             SnackbarUtils.showError(
  //                 AppStrings.error, 'File size exceeds 10MB limit');
  //             return;
  //           }

  //           selectedFile.value = File(path);
  //           selectedFileName.value = file.name;
  //           selectedFileSize.value = fileSize;
  //           selectedFileBytes.value = null;
  //           isWebFile.value = false;
  //           isImage.value = isImageFile(file.name);
  //         }
  //       }

  //       SnackbarUtils.showSuccess(
  //           AppStrings.success, 'File selected: ${selectedFileName.value}');
  //     }
  //   } catch (e) {
  //     log('Error picking file: $e');
  //     SnackbarUtils.showError(AppStrings.error, 'Failed to select file: $e');
  //   }
  // }

  // void removeFile() {
  //   selectedFile.value = null;
  //   selectedFileName.value = '';
  //   selectedFileSize.value = 0;
  //   selectedFileBytes.value = null;
  //   isImage.value = false;
  // }

  // bool isImageFile(String fileName) {
  //   final extension = getFileExtension(fileName).toLowerCase();
  //   return ['jpg', 'jpeg', 'png'].contains(extension);
  // }

  // String getFileExtension(String fileName) {
  //   return fileName.split('.').last.toLowerCase();
  // }

  String? validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Amount is required';
    }
    if (double.tryParse(value) == null) {
      return 'Please enter a valid number';
    }
    final amount = double.parse(value);
    if (amount <= 0) {
      return 'Amount must be greater than 0';
    }
    return null;
  }

  String? validateAccount(AccountModel? account) {
    if (account == null) {
      return 'Please select an account';
    }
    if (accounts.isEmpty) {
      return 'No accounts available. Please add an account first.';
    }
    return null;
  }

  String? validatePayer(PayerModel? payer) {
    if (payer == null) {
      return 'Please select a payer';
    }
    if (payers.isEmpty) {
      return 'No payers available. Please add a payer first.';
    }
    return null;
  }

  String? validateCategory(DepositCategoryModel? category) {
    if (category == null) {
      return 'Please select a category';
    }
    if (categories.isEmpty) {
      return 'No categories available. Please add a category first.';
    }
    return null;
  }

  String? validateReference(String? value) {
    if (value == null || value.isEmpty) {
      return 'Reference number is required';
    }
    return null;
  }

  Future<void> createDeposit() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    // Validate Chart of Accounts selections
    if (selectedSourceAccount.value == null) {
      SnackbarUtils.showError(
          AppStrings.errorS, 'Please select a source account');
      return;
    }

    if (selectedDestinationAccount.value == null) {
      SnackbarUtils.showError(
          AppStrings.errorS, 'Please select a destination account');
      return;
    }

    if (selectedPayer.value == null) {
      SnackbarUtils.showError(AppStrings.errorS, 'Please select a payer');
      return;
    }

    if (selectedCategory.value == null) {
      SnackbarUtils.showError(AppStrings.errorS, 'Please select a category');
      return;
    }

    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      final amount = double.parse(amountController.text);

      final newDeposit = DepositModel(
        id: '', // ID will be set by the Firebase API
        // Legacy account fields (for backward compatibility)
        accountId: selectedDestinationAccount.value!.id,
        accountName: selectedDestinationAccount.value!.accountName,
        // Chart of Accounts fields
        sourceAccountId: selectedSourceAccount.value!.id,
        sourceAccountName: selectedSourceAccount.value!.accountName,
        destinationAccountId: selectedDestinationAccount.value!.id,
        destinationAccountName: selectedDestinationAccount.value!.accountName,
        amount: amount,
        createdAt: selectedDate.value,
        categoryId: selectedCategory.value!.id,
        categoryName: selectedCategory.value!.name,
        payerId: selectedPayer.value!.id,
        payerName: selectedPayer.value!.name,
        referenceNumber: referenceController.text,
        notes: notesController.text,
      );

      final result = await depositRepository.createDeposit(
        newDeposit,
        // file: isWebFile.value ? null : selectedFile.value,
        // fileBytes: isWebFile.value
        //     ? (selectedFileBytes.value != null
        //         ? Uint8List.fromList(selectedFileBytes.value!)
        //         : null)
        //     : null,
        // fileName:
        //     selectedFileName.value.isNotEmpty ? selectedFileName.value : null,
      );

      result.fold(
        (failure) {
          log('Failed to create deposit: ${failure.message}');
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (success) {
          log('Deposit created successfully');

          // Close drawer and reset form
          clearForm();
          closeDrawer();

          // Fetch updated deposits
          fetchDeposits();

          SnackbarUtils.showSuccess(AppStrings.success, success.message);

          // Force refresh to ensure pagination updates immediately
          _refreshDepositData();
        },
      );
    } catch (e) {
      log('Error creating deposit: $e');
      SnackbarUtils.showError(AppStrings.error, 'Failed to create deposit: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteDeposit(DepositModel deposit) async {
    if (isDeleting.value) {
      return;
    }

    isDeleting.value = true;

    try {
      log('Deleting deposit: ${deposit.id}');

      // Optimistically remove from UI immediately for better UX
      final originalIndex = deposits.indexOf(deposit);
      deposits.removeWhere((item) => item.id == deposit.id);
      _filterDeposits();

      final result = await depositRepository.deleteDeposit(
        deposit.id,
        deposit.accountId,
        deposit.amount,
      );

      result.fold(
        (failure) {
          log('Failed to delete deposit: ${failure.message}');
          // Restore the item if deletion failed
          if (originalIndex >= 0) {
            deposits.insert(originalIndex, deposit);
            _filterDeposits();
          }
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (success) async {
          log('Deposit deleted successfully');

          // Force refresh to ensure pagination updates immediately
          _refreshDepositData();

          SnackbarUtils.showSuccess(AppStrings.success, success.message);
        },
      );
    } catch (e) {
      log('Error deleting deposit: $e');
      // Restore the item if deletion failed due to exception
      if (!deposits.any((item) => item.id == deposit.id)) {
        deposits.add(deposit);
        _filterDeposits();
      }
      SnackbarUtils.showError(AppStrings.error, 'Failed to delete deposit: $e');
    } finally {
      isDeleting.value = false;
    }
  }

  // Add a method to create a deposit record from an account transaction
  Future<bool> createDepositFromTransaction(
      AccountTransactionModel transaction) async {
    if (transaction.amount <= 0) {
      log('Cannot create deposit from a negative or zero amount transaction');
      return false;
    }

    try {
      isLoading.value = true;

      // Determine category details based on transaction type
      String categoryId = '1'; // Default
      String categoryName = 'Account Deposit';

      // Get a better category name based on transaction type
      if (transaction.type == TransactionType.brokerFees) {
        categoryName = 'Broker Fees Income';
        if (transaction.category != null && transaction.category!.isNotEmpty) {
          categoryName = transaction.category!;
        }
      } else if (transaction.type == TransactionType.munshiana) {
        categoryName = 'Munshiana Income';
        if (transaction.category != null && transaction.category!.isNotEmpty) {
          categoryName = transaction.category!;
        }
      } else if (transaction.type == TransactionType.voucherPayment) {
        categoryName = 'Voucher Payment Income';
      }

      final deposit = DepositModel(
        id: const Uuid().v4(),
        accountId: transaction.accountId,
        accountName: transaction.accountName,
        amount: transaction.amount,
        createdAt: transaction.transactionDate,
        categoryId: categoryId,
        categoryName: categoryName,
        payerId: transaction.payerId ?? '',
        payerName: transaction.payerName ?? 'Unknown Payer',
        referenceNumber: transaction.voucherNumber != null &&
                transaction.voucherNumber!.isNotEmpty
            ? transaction.voucherNumber!
            : 'TRX-${transaction.id.substring(0, 8)}',
        notes: transaction.description,
      );

      final result = await depositRepository.createDeposit(deposit);

      result.fold(
        (failure) {
          log('Failed to create deposit: ${failure.message}');
          return false;
        },
        (success) {
          log('Deposit created successfully: ${deposit.id}');
          deposits.add(deposit);
          _filterDeposits();
          return true;
        },
      );

      return true;
    } catch (e) {
      log('Error creating deposit from transaction: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  void _refreshDepositData() {
    // Immediately refresh the filtered list and pagination
    _filterDeposits();

    // Add a small delay to ensure any Firestore listeners have processed the change
    Future.delayed(const Duration(milliseconds: 500), () {
      // Force refresh the data from Firestore
      fetchDeposits();
    });
  }

  @override
  void onClose() {
    amountController.dispose();
    referenceController.dispose();
    notesController.dispose();
    accountNameController.dispose();
    payerNameController.dispose();
    categoryNameController.dispose();
    searchController.dispose();
    _depositsSubscription?.cancel();
    super.onClose();
  }

  /// Refresh form data (categories, payers, accounts) when opening forms
  Future<void> refreshFormData() async {
    isLoading.value = true;
    try {
      log('Refreshing form data for deposits');

      // Refresh all form dependencies in parallel
      await Future.wait([
        fetchAccounts(),
        fetchCategories(),
        fetchPayers(),
      ]);

      log('Form data refreshed successfully');
    } catch (e) {
      log('Error refreshing form data: $e');
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to refresh form data: $e',
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Chart of Accounts methods
  void setSelectedSourceAccount(ChartOfAccountsModel? account) {
    selectedSourceAccount.value = account;
  }

  void setSelectedDestinationAccount(ChartOfAccountsModel? account) {
    selectedDestinationAccount.value = account;
  }

  String? validateSourceAccount(ChartOfAccountsModel? account) {
    if (account == null) {
      return 'Please select a source account';
    }

    // Validate account is active
    if (!account.isActive) {
      return 'Selected source account is inactive';
    }

    // Validate account category for deposits (should be Revenue or Liability)
    if (account.category != AccountCategory.revenue &&
        account.category != AccountCategory.liabilities) {
      return 'Source account should be a Revenue or Liability account (Customer, Sales Revenue, etc.)';
    }

    return null;
  }

  String? validateDestinationAccount(ChartOfAccountsModel? account) {
    if (account == null) {
      return 'Please select a destination account';
    }

    // Validate account is active
    if (!account.isActive) {
      return 'Selected destination account is inactive';
    }

    // Validate account category for deposits (should be Asset)
    if (account.category != AccountCategory.assets) {
      return 'Destination account should be an Asset account (Cash, Bank, etc.)';
    }

    return null;
  }

  /// Validate the complete deposit transaction
  String? validateDepositTransaction() {
    // Validate amount
    if (amountController.text.isEmpty) {
      return 'Please enter an amount';
    }

    final amount = double.tryParse(amountController.text);
    if (amount == null || amount <= 0) {
      return 'Please enter a valid amount greater than zero';
    }

    // Validate Chart of Accounts selections
    final sourceValidation = validateSourceAccount(selectedSourceAccount.value);
    if (sourceValidation != null) {
      return sourceValidation;
    }

    final destinationValidation =
        validateDestinationAccount(selectedDestinationAccount.value);
    if (destinationValidation != null) {
      return destinationValidation;
    }

    // Additional business logic validation can be added here
    // For deposits, we typically don't check source account balance
    // since money is coming from external sources

    return null;
  }

  /// Force refresh the deposits data
  @override
  Future<void> forceRefresh() async {
    await fetchDeposits();
  }
}
