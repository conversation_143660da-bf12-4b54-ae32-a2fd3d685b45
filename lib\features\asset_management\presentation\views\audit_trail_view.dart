import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/features/asset_management/presentation/controllers/audit_trail_controller.dart';
import 'package:logestics/features/asset_management/presentation/controllers/audit_trail_export_controller.dart';
import 'package:logestics/models/asset/asset_audit_model.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

class AuditTrailView extends StatefulWidget {
  final String? assetId; // Optional: for asset-specific audit trail

  const AuditTrailView({super.key, this.assetId});

  @override
  State<AuditTrailView> createState() => _AuditTrailViewState();
}

class _AuditTrailViewState extends State<AuditTrailView> {
  late ColorNotifier notifier;
  late AuditTrailController controller;
  late AuditTrailExportController exportController;

  @override
  void initState() {
    super.initState();

    // Ensure controller is available - create if not found
    try {
      controller = Get.find<AuditTrailController>();
    } catch (e) {
      // Controller not found, this can happen after app refresh
      log('AuditTrailView: Controller not found, will be initialized by parent');
      // Don't create here, let the parent handle it
      return;
    }

    exportController = Get.put(AuditTrailExportController());

    // Load specific asset audit trail if assetId is provided
    if (widget.assetId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Double-check controller is still available
        try {
          final ctrl = Get.find<AuditTrailController>();
          ctrl.loadAssetAuditTrail(widget.assetId!);
        } catch (e) {
          log('AuditTrailView: Controller not available for loading asset audit trail');
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of<ColorNotifier>(context, listen: true);
    var width = Get.width;

    // Check if controller is available
    try {
      controller = Get.find<AuditTrailController>();
    } catch (e) {
      // Controller not available, show loading or error state
      return Scaffold(
        backgroundColor: notifier.getBgColor,
        appBar: AppBar(
          backgroundColor: notifier.getBgColor,
          elevation: 0,
          title: Text(
            'Audit Trail',
            style: TextStyle(
              color: notifier.text,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: notifier.text),
            onPressed: () => Get.back(),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                'Initializing audit trail...',
                style: TextStyle(color: notifier.text),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      appBar: AppBar(
        backgroundColor: notifier.getBgColor,
        elevation: 0,
        title: Text(
          widget.assetId != null ? 'Asset Audit Trail' : 'Audit Trail',
          style: TextStyle(
            color: notifier.text,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: notifier.text),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: notifier.text),
            onPressed: () => controller.refreshAuditTrail(),
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: Icon(Icons.file_download, color: notifier.text),
            onPressed: _exportAuditTrail,
            tooltip: 'Export Audit Trail',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilters(),
          const SizedBox(height: 16),
          _buildSummaryCards(),
          const SizedBox(height: 16),
          Expanded(child: _buildAuditTable(width)),
          _buildPagination(),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Card(
        color: notifier.getBgColor,
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Search and quick date filters row
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextField(
                      controller: controller.searchController,
                      decoration: InputDecoration(
                        hintText: 'Search audit entries...',
                        hintStyle: TextStyle(
                            color: notifier.text.withValues(alpha: 0.6)),
                        prefixIcon: Icon(Icons.search, color: notifier.text),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        fillColor: notifier.textFileColor,
                      ),
                      style: TextStyle(color: notifier.text),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Quick date range filters
                  Expanded(
                    child: Wrap(
                      spacing: 8,
                      children: [
                        'Today',
                        'This Week',
                        'This Month',
                        'Last 30 Days',
                        'Clear'
                      ]
                          .map((range) => Obx(() => FilterChip(
                                label: Text(range),
                                selected:
                                    controller.selectedDateRange.value == range,
                                onSelected: (_) =>
                                    controller.setQuickDateRange(range),
                                selectedColor: const Color(0xFF0165FC)
                                    .withValues(alpha: 0.2),
                                labelStyle: TextStyle(
                                  color: controller.selectedDateRange.value ==
                                          range
                                      ? const Color(0xFF0165FC)
                                      : notifier.text,
                                ),
                              )))
                          .toList(),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Filter dropdowns row
              Row(
                children: [
                  Expanded(
                    child: Obx(() => DropdownButtonFormField<String>(
                          value: controller.selectedActionFilter.value.isEmpty
                              ? null
                              : controller.selectedActionFilter.value,
                          decoration: InputDecoration(
                            labelText: 'Action',
                            labelStyle: TextStyle(color: notifier.text),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            filled: true,
                            fillColor: notifier.textFileColor,
                          ),
                          dropdownColor: notifier.getBgColor,
                          style: TextStyle(color: notifier.text),
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('All Actions'),
                            ),
                            ...controller.availableActions
                                .map((action) => DropdownMenuItem<String>(
                                      value: action,
                                      child: Row(
                                        children: [
                                          Icon(
                                            controller.getActionIcon(action),
                                            color: controller
                                                .getActionColor(action),
                                            size: 16,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(AssetAuditModel
                                              .getActionDisplayText(action)),
                                        ],
                                      ),
                                    )),
                          ],
                          onChanged: controller.setActionFilter,
                        )),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Obx(() => DropdownButtonFormField<String>(
                          value: controller.selectedUserFilter.value.isEmpty
                              ? null
                              : controller.selectedUserFilter.value,
                          decoration: InputDecoration(
                            labelText: 'User',
                            labelStyle: TextStyle(color: notifier.text),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            filled: true,
                            fillColor: notifier.textFileColor,
                          ),
                          dropdownColor: notifier.getBgColor,
                          style: TextStyle(color: notifier.text),
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('All Users'),
                            ),
                            ...controller.availableUsers
                                .map((user) => DropdownMenuItem<String>(
                                      value: user,
                                      child: Text(user),
                                    )),
                          ],
                          onChanged: controller.setUserFilter,
                        )),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Obx(() => DropdownButtonFormField<String>(
                          value: controller.selectedAssetFilter.value.isEmpty
                              ? null
                              : controller.selectedAssetFilter.value,
                          decoration: InputDecoration(
                            labelText: 'Asset',
                            labelStyle: TextStyle(color: notifier.text),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            filled: true,
                            fillColor: notifier.textFileColor,
                          ),
                          dropdownColor: notifier.getBgColor,
                          style: TextStyle(color: notifier.text),
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('All Assets'),
                            ),
                            ...controller.availableAssets
                                .map((asset) => DropdownMenuItem<String>(
                                      value: asset,
                                      child: Text(asset),
                                    )),
                          ],
                          onChanged: controller.setAssetFilter,
                        )),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.icon(
                    onPressed: controller.clearFilters,
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear Filters'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Obx(() {
        final totalEntries = controller.filteredAuditEntries.length;
        final todayEntries = controller.filteredAuditEntries
            .where((entry) => _isToday(entry.timestamp))
            .length;
        final thisWeekEntries = controller.filteredAuditEntries
            .where((entry) => _isThisWeek(entry.timestamp))
            .length;

        return Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Entries',
                totalEntries.toString(),
                Icons.list_alt,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Today',
                todayEntries.toString(),
                Icons.today,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'This Week',
                thisWeekEntries.toString(),
                Icons.date_range,
                Colors.orange,
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      color: notifier.getBgColor,
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: notifier.text.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    color: notifier.text,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAuditTable(double width) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Card(
        color: notifier.getBgColor,
        elevation: 2,
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (controller.paginatedAuditEntries.isEmpty) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.history,
                      size: 64,
                      color: notifier.text.withValues(alpha: 0.3),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No audit entries found',
                      style: TextStyle(
                        color: notifier.text.withValues(alpha: 0.7),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              columnSpacing: 20,
              headingRowColor: WidgetStateProperty.all(
                notifier.text.withValues(alpha: 0.05),
              ),
              columns: [
                DataColumn(
                  label: Text(
                    'Timestamp',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Action',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Asset',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'User',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Changes',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Notes',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
              rows: controller.paginatedAuditEntries.map((entry) {
                return DataRow(
                  cells: [
                    DataCell(
                      Text(
                        DateFormat('MMM dd, yyyy HH:mm')
                            .format(entry.timestamp),
                        style: TextStyle(color: notifier.text),
                      ),
                    ),
                    DataCell(
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            controller.getActionIcon(entry.action),
                            color: controller.getActionColor(entry.action),
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            AssetAuditModel.getActionDisplayText(entry.action),
                            style: TextStyle(
                              color: controller.getActionColor(entry.action),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    DataCell(
                      Text(
                        entry.assetName,
                        style: TextStyle(color: notifier.text),
                      ),
                    ),
                    DataCell(
                      Text(
                        entry.userName,
                        style: TextStyle(color: notifier.text),
                      ),
                    ),
                    DataCell(
                      entry.fieldChanges.isNotEmpty
                          ? Tooltip(
                              message: _formatFieldChanges(entry.fieldChanges),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.blue.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  '${entry.fieldChanges.length} changes',
                                  style: TextStyle(
                                    color: Colors.blue,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            )
                          : Text(
                              '-',
                              style: TextStyle(
                                  color: notifier.text.withValues(alpha: 0.5)),
                            ),
                    ),
                    DataCell(
                      Text(
                        entry.notes.isNotEmpty ? entry.notes : '-',
                        style: TextStyle(color: notifier.text),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildPagination() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Obx(() => PaginationWidget(
            currentPage: controller.currentPage.value,
            totalPages: controller.totalPages,
            itemsPerPage: controller.itemsPerPage.value,
            onPageChanged: controller.setCurrentPage,
            onItemsPerPageChanged: controller.setItemsPerPage,
          )),
    );
  }

  void _exportAuditTrail() {
    // Export filtered audit trail with current filters
    exportController.exportFilteredAuditTrail(
      auditEntries: controller.filteredAuditEntries,
      actionFilter: controller.selectedActionFilter.value.isNotEmpty
          ? controller.selectedActionFilter.value
          : null,
      userFilter: controller.selectedUserFilter.value.isNotEmpty
          ? controller.selectedUserFilter.value
          : null,
      assetFilter: controller.selectedAssetFilter.value.isNotEmpty
          ? controller.selectedAssetFilter.value
          : null,
      startDate: controller.startDateFilter.value,
      endDate: controller.endDateFilter.value,
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  bool _isThisWeek(DateTime date) {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
        date.isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  String _formatFieldChanges(Map<String, dynamic> fieldChanges) {
    if (fieldChanges.isEmpty) return 'No changes';

    return fieldChanges.entries.map((entry) {
      final field = entry.key;
      final change = entry.value;

      if (change is Map<String, dynamic>) {
        final oldValue = change['old']?.toString() ?? 'null';
        final newValue = change['new']?.toString() ?? 'null';
        return '$field: $oldValue → $newValue';
      } else {
        return '$field: ${change.toString()}';
      }
    }).join('\n');
  }
}
