import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import '../core/services/voucher_accounting_hook_service.dart';
import '../core/services/voucher_account_setup_service.dart';
import '../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../models/voucher_model.dart';

/// Comprehensive test for complete voucher integration workflow
class TestCompleteVoucherIntegration {
  static Future<void> runCompleteTest() async {
    log('🧪 ========== COMPLETE VOUCHER INTEGRATION TEST START ==========');

    try {
      // Check authentication
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        log('❌ No authenticated user. Please login first.');
        return;
      }

      final uid = user.uid;
      log('👤 Testing complete integration for user: $uid');

      // Step 1: Ensure required accounts exist
      log('🔍 Step 1: Ensuring required accounts exist...');
      final accountsExist =
          await VoucherAccountSetupService.ensureRequiredAccountsExist();
      if (!accountsExist) {
        log('❌ Failed to ensure required accounts exist');
        return;
      }
      log('✅ Required accounts verified/created');

      // Step 2: Create test voucher data
      log('🔍 Step 2: Creating test voucher data...');
      final testVoucherData = _createTestVoucherData(uid);
      log('📋 Test voucher created: ${testVoucherData['voucherNumber']}');

      // Step 3: Test VoucherModel conversion
      log('🔍 Step 3: Testing VoucherModel conversion...');
      VoucherModel voucher;
      try {
        voucher = VoucherModel.fromJson(testVoucherData);
        log('✅ VoucherModel conversion successful');
        log('  - Voucher Number: ${voucher.voucherNumber}');
        log('  - Total Freight: ${voucher.totalFreight}');
        log('  - Broker Fees: ${voucher.brokerFees}');
        log('  - Munshiana Fees: ${voucher.munshianaFees}');
      } catch (e) {
        log('❌ VoucherModel conversion failed: $e');
        return;
      }

      // Step 4: Get initial account balances
      log('🔍 Step 4: Getting initial account balances...');
      final chartService = ChartOfAccountsFirebaseService();
      final journalService = JournalEntryFirebaseService();

      final allAccounts = await chartService.getAllAccounts();
      final Map<String, double> initialBalances = {};

      for (final account in allAccounts) {
        try {
          final balance =
              await journalService.calculateAccountBalance(account.id);
          initialBalances[account.id] = balance;
          log('📊 Initial balance for ${account.accountName}: $balance');
        } catch (e) {
          log('⚠️ Could not get balance for ${account.accountName}: $e');
          initialBalances[account.id] = 0.0;
        }
      }

      // Step 5: Test hook service call (create journal entries)
      log('🔍 Step 5: Testing hook service call...');
      final hookService = VoucherAccountingHookService();
      await hookService.onVoucherCreated(testVoucherData, uid);
      log('✅ Hook service call completed');

      // Step 6: Verify journal entries were created
      log('🔍 Step 6: Verifying journal entries were created...');
      await Future.delayed(
          const Duration(seconds: 2)); // Allow time for Firebase operations

      // Check for journal entries with the voucher number
      List<dynamic> voucherEntries = [];
      try {
        final allJournalEntries = await journalService.getJournalEntries();
        voucherEntries = allJournalEntries
            .where((entry) =>
                entry.sourceTransactionType == 'voucher' &&
                entry.sourceTransactionId == voucher.voucherNumber)
            .toList();

        if (voucherEntries.isEmpty) {
          log('❌ No journal entries found for voucher: ${voucher.voucherNumber}');
        } else {
          log('✅ Found ${voucherEntries.length} journal entries for voucher');
          for (final entry in voucherEntries) {
            log('  - Entry: ${entry.entryNumber} with ${entry.lines.length} lines');
            log('  - Description: ${entry.description}');
            log('  - Total Debits: ${entry.totalDebits}');
            log('  - Total Credits: ${entry.totalCredits}');
            log('  - Status: ${entry.status.displayName}');

            // Log each line
            for (final line in entry.lines) {
              log('    * ${line.accountName}: Debit=${line.debitAmount}, Credit=${line.creditAmount}');
            }
          }
        }
      } catch (e) {
        log('❌ Error checking journal entries: $e');
      }

      // Step 7: Verify account balances were updated
      log('🔍 Step 7: Verifying account balances were updated...');
      bool balancesUpdated = false;

      for (final account in allAccounts) {
        try {
          final newBalance =
              await journalService.calculateAccountBalance(account.id);
          final initialBalance = initialBalances[account.id] ?? 0.0;

          if ((newBalance - initialBalance).abs() > 0.01) {
            log('✅ Balance updated for ${account.accountName}: $initialBalance → $newBalance');
            balancesUpdated = true;
          } else {
            log('📊 No change for ${account.accountName}: $newBalance');
          }
        } catch (e) {
          log('⚠️ Could not check updated balance for ${account.accountName}: $e');
        }
      }

      if (balancesUpdated) {
        log('✅ Account balances were successfully updated');
      } else {
        log('⚠️ No account balance changes detected');
      }

      // Step 8: Summary
      log('🔍 Step 8: Test Summary...');
      log('✅ Account setup: Complete');
      log('✅ Voucher model conversion: Complete');
      log('✅ Hook service execution: Complete');
      log('✅ Journal entry creation: ${voucherEntries.isNotEmpty ? 'Complete' : 'Failed'}');
      log('✅ Account balance updates: ${balancesUpdated ? 'Complete' : 'No changes'}');

      log('🎉 ========== COMPLETE VOUCHER INTEGRATION TEST COMPLETED ==========');

      if (voucherEntries.isNotEmpty) {
        log('🎉 SUCCESS: Voucher integration is working correctly!');
      } else {
        log('⚠️ PARTIAL SUCCESS: Hook service ran but journal entries may not be visible');
      }
    } catch (e) {
      log('❌ Complete integration test failed with error: $e');
      log('📋 Stack trace: ${StackTrace.current}');
    }
  }

  static Map<String, dynamic> _createTestVoucherData(String uid) {
    final now = DateTime.now();
    final voucherNumber = 'COMPLETE-TEST-${now.millisecondsSinceEpoch}';

    return {
      'voucherNumber': voucherNumber,
      'voucherStatus': 'Completed',
      'driverName': 'Test Driver',
      'invoiceTasNumberList': ['TEST-001'],
      'invoiceBiltyNumberList': ['BILTY-001'],
      'weightInTons': 10,
      'departureDate': now.toIso8601String().split('T')[0],
      'productName': 'Test Product',
      'totalNumberOfBags': 100,
      'brokerType': 'Internal',
      'brokerName': 'Test Broker',
      'selectedBroker': 'test-broker-id',
      'brokerFees': 5000.0,
      'munshianaFees': 2000.0,
      'brokerAccount': 'test-broker-account',
      'munshianaAccount': 'test-munshiana-account',
      'driverPhoneNumber': '**********',
      'truckNumber': 'TEST-123',
      'conveyNoteNumber': 'CN-001',
      'totalFreight': 50000.0,
      'companyFreight': 43000.0,
      'settledFreight': 43000.0,
      'paymentTransactions': [],
      // Chart of Accounts fields (null to test fallback mechanism)
      'brokerAccountId': null,
      'munshianaAccountId': null,
      'salesTaxAccountId': null,
      'freightTaxAccountId': null,
      'profitAccountId': null,
      'truckFreightAccountId': null,
      'companyFreightAccountId': null,
      // Tax and profit fields
      'calculatedProfit': 0.0,
      'calculatedTax': 0.0,
      'calculatedFreightTax': 0.0,
      // Required fields
      'brokerList': [],
      'selectedTaxAuthorities': [],
      'uid': uid,
      'createdAt': now.millisecondsSinceEpoch,
    };
  }
}
