import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../core/services/accounting_validation_service.dart';
import '../../../../../core/services/account_type_helper_service.dart';

import '../../../../../models/finance/journal_entry_model.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../repositories/journal_entry_repository.dart';
import '../../../chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import '../../../chart_of_accounts/presentation/controllers/chart_of_accounts_controller.dart';
import '../../../general_ledger/presentation/controllers/general_ledger_controller.dart';

class JournalEntryController extends GetxController {
  final JournalEntryRepository repository;
  final ChartOfAccountsRepository chartOfAccountsRepository;

  JournalEntryController({
    required this.repository,
    required this.chartOfAccountsRepository,
  }) {
    log('🔍 [CONTROLLER DEBUG] JournalEntryController constructor called');
  }

  // Observable variables
  final RxList<JournalEntryModel> journalEntries = <JournalEntryModel>[].obs;
  final RxList<ChartOfAccountsModel> accounts = <ChartOfAccountsModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isCreating = false.obs;

  // Form controllers for manual journal entry
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController referenceController = TextEditingController();
  final Rx<DateTime> entryDate = DateTime.now().obs;
  final RxList<JournalEntryLineModel> journalLines =
      <JournalEntryLineModel>[].obs;

  // Current editing line
  final RxInt editingLineIndex = (-1).obs;
  final TextEditingController lineDescriptionController =
      TextEditingController();
  final TextEditingController debitAmountController = TextEditingController();
  final TextEditingController creditAmountController = TextEditingController();
  final Rx<ChartOfAccountsModel?> selectedAccount =
      Rx<ChartOfAccountsModel?>(null);

  @override
  void onInit() {
    super.onInit();
    log('🔍 [CONTROLLER DEBUG] JournalEntryController onInit() called');

    // Debug authentication status
    final currentUser = FirebaseAuth.instance.currentUser;
    log('🔍 [CONTROLLER DEBUG] Current user: ${currentUser?.email ?? 'null'}');
    log('🔍 [CONTROLLER DEBUG] Current UID: ${currentUser?.uid ?? 'anonymous'}');

    loadAccounts();
    loadJournalEntries();
  }

  @override
  void onClose() {
    descriptionController.dispose();
    referenceController.dispose();
    lineDescriptionController.dispose();
    debitAmountController.dispose();
    creditAmountController.dispose();
    super.onClose();
  }

  /// Test method to check if debug logs work and diagnose journal entry display issues
  void testDebugLogs() {
    log('🔍 [CONTROLLER DEBUG] Test method called - log works!');
    log('🔍 [CONTROLLER DEBUG] === JOURNAL ENTRY DISPLAY DIAGNOSTIC ===');
    log('🔍 [CONTROLLER DEBUG] Current journalEntries.length: ${journalEntries.length}');
    log('🔍 [CONTROLLER DEBUG] isLoading.value: ${isLoading.value}');
    log('🔍 [CONTROLLER DEBUG] Repository type: ${repository.runtimeType}');

    // Log first few entries if any exist
    if (journalEntries.isNotEmpty) {
      log('🔍 [CONTROLLER DEBUG] First few entries:');
      for (int i = 0; i < journalEntries.length && i < 5; i++) {
        final entry = journalEntries[i];
        log('🔍 [CONTROLLER DEBUG] Entry $i: ${entry.entryNumber} - ${entry.description} (Ref: ${entry.referenceNumber})');
      }
    } else {
      log('🔍 [CONTROLLER DEBUG] No journal entries found in controller');
    }

    // Check for voucher ******** specifically
    final voucher********Entries = journalEntries
        .where((entry) =>
            entry.referenceNumber == '********' ||
            entry.description.contains('********') ||
            entry.sourceTransactionId == '********')
        .toList();

    log('🔍 [CONTROLLER DEBUG] Entries for voucher ********: ${voucher********Entries.length}');
    for (final entry in voucher********Entries) {
      log('🔍 [CONTROLLER DEBUG] Found voucher entry: ${entry.entryNumber} - ${entry.description}');
    }

    // Trigger a fresh load
    log('🔍 [CONTROLLER DEBUG] Triggering fresh load of journal entries...');
    loadJournalEntries();

    // Test color coding logic
    _testColorCodingLogic();
  }

  /// Test the color coding logic for different account types
  void _testColorCodingLogic() {
    log('🎨 [COLOR TEST] Testing Chart of Accounts color coding logic...');

    // Test Assets (should be GREEN for debits, RED for credits)
    final assetDebitColor =
        AccountTypeHelperService.getBalanceChangeColorByCategory(
      accountCategory: AccountCategory.assets,
      debitAmount: 100.0,
      creditAmount: 0.0,
    );
    final assetCreditColor =
        AccountTypeHelperService.getBalanceChangeColorByCategory(
      accountCategory: AccountCategory.assets,
      debitAmount: 0.0,
      creditAmount: 100.0,
    );

    log('🎨 Assets - Debit (increase): ${assetDebitColor.toString()} (should be green)');
    log('🎨 Assets - Credit (decrease): ${assetCreditColor.toString()} (should be red)');

    // Test Liabilities (should be RED for debits, GREEN for credits)
    final liabilityDebitColor =
        AccountTypeHelperService.getBalanceChangeColorByCategory(
      accountCategory: AccountCategory.liabilities,
      debitAmount: 100.0,
      creditAmount: 0.0,
    );
    final liabilityCreditColor =
        AccountTypeHelperService.getBalanceChangeColorByCategory(
      accountCategory: AccountCategory.liabilities,
      debitAmount: 0.0,
      creditAmount: 100.0,
    );

    log('🎨 Liabilities - Debit (decrease): ${liabilityDebitColor.toString()} (should be red)');
    log('🎨 Liabilities - Credit (increase): ${liabilityCreditColor.toString()} (should be green)');

    log('🎨 [COLOR TEST] Color coding test completed');
  }

  /// Load all active accounts for selection
  Future<void> loadAccounts() async {
    try {
      log('🔍 [CONTROLLER DEBUG] Starting to load accounts');
      isLoading.value = true;
      log('🔍 [CONTROLLER DEBUG] Set loading to true (accounts)');
      final result = await chartOfAccountsRepository.getAccounts();
      result.fold(
        (failure) {
          log('Error loading accounts: ${failure.message}');
          SnackbarUtils.showError(
              'Error', 'Error loading accounts: ${failure.message}');
        },
        (accountsList) {
          accounts.value = accountsList;
          log('Loaded ${accountsList.length} accounts');
        },
      );
    } catch (e) {
      log('Exception loading accounts: $e');
      SnackbarUtils.showError('Error', 'Error loading accounts');
    } finally {
      isLoading.value = false;
    }
  }

  /// Load journal entries
  Future<void> loadJournalEntries() async {
    try {
      log('🔍 [CONTROLLER DEBUG] Starting to load journal entries');
      isLoading.value = true;
      log('🔍 [CONTROLLER DEBUG] Set loading to true');

      final result = await repository.getJournalEntries();
      log('🔍 [CONTROLLER DEBUG] Repository call completed');

      result.fold(
        (failure) {
          log('❌ [CONTROLLER DEBUG] Error loading journal entries: ${failure.message}');
          SnackbarUtils.showError(
              'Error', 'Error loading journal entries: ${failure.message}');
        },
        (entriesList) {
          log('🔍 [CONTROLLER DEBUG] Repository returned ${entriesList.length} entries');

          // Log first few entries for debugging with detailed info
          log('🔍 [CONTROLLER DEBUG] Entries received from repository (should be newest first):');
          for (int i = 0; i < entriesList.length && i < 5; i++) {
            final entry = entriesList[i];
            log('🔍 [CONTROLLER DEBUG] Entry $i: ${entry.entryNumber} - ${entry.description} - Created: ${entry.createdAt} - Type: ${entry.entryType.name} - Source: ${entry.sourceTransactionType ?? 'manual'}');
          }

          journalEntries.value = entriesList;
          log('🔍 [CONTROLLER DEBUG] Updated observable with ${entriesList.length} entries');
          log('🔍 [CONTROLLER DEBUG] Current journalEntries.length: ${journalEntries.length}');
        },
      );
    } catch (e) {
      log('❌ [CONTROLLER DEBUG] Exception loading journal entries: $e');
      SnackbarUtils.showError('Error', 'Error loading journal entries');
    } finally {
      isLoading.value = false;
      log('🔍 [CONTROLLER DEBUG] Set loading to false');
    }
  }

  /// Add a new line to the journal entry
  void addJournalLine() {
    if (!validateCurrentLine()) return;

    final debitAmount = double.tryParse(debitAmountController.text) ?? 0.0;
    final creditAmount = double.tryParse(creditAmountController.text) ?? 0.0;

    final newLine = JournalEntryLineModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      journalEntryId: '',
      accountId: selectedAccount.value!.id,
      accountNumber: selectedAccount.value!.accountNumber,
      accountName: selectedAccount.value!.accountName,
      debitAmount: debitAmount,
      creditAmount: creditAmount,
      description: lineDescriptionController.text.trim(),
      createdAt: DateTime.now(),
    );

    if (editingLineIndex.value >= 0) {
      // Update existing line
      journalLines[editingLineIndex.value] = newLine;
      editingLineIndex.value = -1;
    } else {
      // Add new line
      journalLines.add(newLine);
    }

    clearLineForm();
  }

  /// Edit an existing journal line
  void editJournalLine(int index) {
    final line = journalLines[index];
    editingLineIndex.value = index;

    selectedAccount.value = accounts.firstWhereOrNull(
      (account) => account.id == line.accountId,
    );
    lineDescriptionController.text = line.description;
    debitAmountController.text =
        line.debitAmount > 0 ? line.debitAmount.toString() : '';
    creditAmountController.text =
        line.creditAmount > 0 ? line.creditAmount.toString() : '';
  }

  /// Remove a journal line
  void removeJournalLine(int index) {
    journalLines.removeAt(index);
    if (editingLineIndex.value == index) {
      editingLineIndex.value = -1;
      clearLineForm();
    } else if (editingLineIndex.value > index) {
      editingLineIndex.value--;
    }
  }

  /// Clear the line form
  void clearLineForm() {
    selectedAccount.value = null;
    lineDescriptionController.clear();
    debitAmountController.clear();
    creditAmountController.clear();
    editingLineIndex.value = -1;
  }

  /// Validate current line input
  bool validateCurrentLine() {
    if (selectedAccount.value == null) {
      SnackbarUtils.showError('Error', 'Please select an account');
      return false;
    }

    if (lineDescriptionController.text.trim().isEmpty) {
      SnackbarUtils.showError(
          'Error', 'Please enter a description for the line');
      return false;
    }

    final debitAmount = double.tryParse(debitAmountController.text) ?? 0.0;
    final creditAmount = double.tryParse(creditAmountController.text) ?? 0.0;

    if (debitAmount == 0.0 && creditAmount == 0.0) {
      SnackbarUtils.showError(
          'Error', 'Please enter either a debit or credit amount');
      return false;
    }

    if (debitAmount > 0.0 && creditAmount > 0.0) {
      SnackbarUtils.showError(
          'Error', 'A line cannot have both debit and credit amounts');
      return false;
    }

    return true;
  }

  /// Calculate total debits
  double get totalDebits {
    return journalLines.fold(0.0, (sum, line) => sum + line.debitAmount);
  }

  /// Calculate total credits
  double get totalCredits {
    return journalLines.fold(0.0, (sum, line) => sum + line.creditAmount);
  }

  /// Check if the journal entry is balanced
  bool get isBalanced {
    return (totalDebits - totalCredits).abs() < 0.01;
  }

  /// Get balance difference
  double get balanceDifference {
    return totalDebits - totalCredits;
  }

  /// Validate journal entry using comprehensive validation
  ValidationResult validateJournalEntry() {
    // Create a temporary journal entry for validation
    final tempEntry = JournalEntryModel(
      id: '',
      entryNumber: '',
      description: descriptionController.text.trim(),
      entryDate: entryDate.value,
      entryType: JournalEntryType.manual,
      status: JournalEntryStatus.draft,
      lines: journalLines.toList(),
      totalDebits: totalDebits,
      totalCredits: totalCredits,
      referenceNumber: referenceController.text.trim().isNotEmpty
          ? referenceController.text.trim()
          : null,
      createdAt: DateTime.now(),
      createdBy: '',
      uid: '',
    );

    return AccountingValidationService.validateJournalEntry(tempEntry);
  }

  /// Create journal entry
  Future<void> createJournalEntry() async {
    if (!formKey.currentState!.validate()) return;

    // Comprehensive validation
    final validationResult = validateJournalEntry();
    if (!validationResult.isValid) {
      final errorMessage = validationResult.errors.join('\n');
      SnackbarUtils.showError('Validation Failed', errorMessage);
      return;
    }

    // Show warnings if any
    if (validationResult.hasWarnings) {
      final warningMessage = validationResult.warnings.join('\n');
      SnackbarUtils.showWarning('Validation Warnings', warningMessage);
    }

    try {
      isCreating.value = true;

      // Get next journal entry number
      final numberResult = await repository.getNextJournalEntryNumber();
      final entryNumber = numberResult.fold(
        (failure) => 'JE${DateTime.now().millisecondsSinceEpoch}',
        (number) => number,
      );

      final journalEntry = JournalEntryModel(
        id: '',
        entryNumber: entryNumber,
        entryDate: entryDate.value,
        description: descriptionController.text.trim(),
        entryType: JournalEntryType.manual,
        status: JournalEntryStatus.draft,
        lines: journalLines.toList(),
        totalDebits: totalDebits,
        totalCredits: totalCredits,
        referenceNumber: referenceController.text.trim().isNotEmpty
            ? referenceController.text.trim()
            : null,
        createdAt: DateTime.now(),
        createdBy: FirebaseAuth.instance.currentUser?.displayName ??
            FirebaseAuth.instance.currentUser?.email ??
            'Unknown User',
        uid: FirebaseAuth.instance.currentUser?.uid ?? 'anonymous',
      );

      final result = await repository.createJournalEntry(journalEntry);
      result.fold(
        (failure) {
          log('Error creating journal entry: ${failure.message}');
          SnackbarUtils.showError(
              'Error', 'Failed to create journal entry: ${failure.message}');
        },
        (success) {
          log('Journal entry created successfully');
          SnackbarUtils.showSuccess(
              'Success', 'Journal entry created successfully');
          clearForm();
          loadJournalEntries();
          Get.back(); // Close the form dialog/screen
        },
      );
    } catch (e) {
      log('Exception creating journal entry: $e');
      SnackbarUtils.showError('Error', 'Failed to create journal entry');
    } finally {
      isCreating.value = false;
    }
  }

  /// Clear the entire form
  void clearForm() {
    descriptionController.clear();
    referenceController.clear();
    entryDate.value = DateTime.now();
    journalLines.clear();
    clearLineForm();
  }

  /// Post a journal entry (change status from draft to posted)
  Future<void> postJournalEntry(String entryId) async {
    try {
      isLoading.value = true;
      final result = await repository.postJournalEntry(entryId);
      result.fold(
        (failure) {
          log('Error posting journal entry: ${failure.message}');
          SnackbarUtils.showError(
              'Error', 'Failed to post journal entry: ${failure.message}');
        },
        (success) {
          log('Journal entry posted successfully');
          SnackbarUtils.showSuccess(
              'Success', 'Journal entry posted successfully');
          loadJournalEntries();

          // Refresh Chart of Accounts data to reflect updated balances
          try {
            final chartOfAccountsController =
                Get.find<ChartOfAccountsController>();
            chartOfAccountsController.refreshAccounts();
          } catch (e) {
            // Chart of Accounts controller might not be loaded, that's okay
            log('Chart of Accounts controller not found, skipping refresh: $e');
          }

          // Refresh General Ledger data if controller exists
          try {
            final generalLedgerController = Get.find<GeneralLedgerController>();
            generalLedgerController.refreshAllBalances();
          } catch (e) {
            // General Ledger controller might not be loaded, that's okay
            log('General Ledger controller not found, skipping refresh: $e');
          }
        },
      );
    } catch (e) {
      log('Exception posting journal entry: $e');
      SnackbarUtils.showError('Error', 'Failed to post journal entry');
    } finally {
      isLoading.value = false;
    }
  }

  /// Delete a journal entry (only drafts)
  Future<void> deleteJournalEntry(String entryId) async {
    try {
      isLoading.value = true;
      final result = await repository.deleteJournalEntry(entryId);
      result.fold(
        (failure) {
          log('Error deleting journal entry: ${failure.message}');
          SnackbarUtils.showError(
              'Error', 'Failed to delete journal entry: ${failure.message}');
        },
        (success) {
          log('Journal entry deleted successfully');
          SnackbarUtils.showSuccess(
              'Success', 'Journal entry deleted successfully');
          loadJournalEntries();
        },
      );
    } catch (e) {
      log('Exception deleting journal entry: $e');
      SnackbarUtils.showError('Error', 'Failed to delete journal entry');
    } finally {
      isLoading.value = false;
    }
  }
}
