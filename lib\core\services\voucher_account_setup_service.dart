import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';

/// Service to ensure required accounts exist for voucher integration
class VoucherAccountSetupService {
  static final ChartOfAccountsFirebaseService _chartService =
      ChartOfAccountsFirebaseService();

  /// Check if all required accounts exist for voucher integration
  /// DOES NOT create accounts automatically - users must create accounts manually
  static Future<bool> ensureRequiredAccountsExist() async {
    try {
      final uid = FirebaseAuth.instance.currentUser?.uid ?? '';
      if (uid.isEmpty) {
        log('❌ VoucherAccountSetupService: No authenticated user found');
        return false;
      }

      log('🔍 VoucherAccountSetupService: Checking required accounts for voucher integration...');

      // Check if required accounts exist
      final requiredAccounts = [
        {'type': AccountType.operatingExpenses, 'name': 'Broker Fees'},
        {'type': AccountType.operatingExpenses, 'name': 'Munshiana'},
        {'type': AccountType.salesRevenue, 'name': 'Freight Revenue'},
        {'type': AccountType.cash, 'name': 'Cash'},
      ];

      bool allAccountsExist = true;
      final missingAccounts = <String>[];

      for (final accountInfo in requiredAccounts) {
        final accountType = accountInfo['type'] as AccountType;
        final accountName = accountInfo['name'] as String;

        final accounts = await _chartService.getAccountsByType(accountType);
        final accountExists = accounts.any((account) => account.accountName
            .toLowerCase()
            .contains(accountName.toLowerCase()));

        if (!accountExists) {
          allAccountsExist = false;
          missingAccounts.add(accountName);
          log('❌ VoucherAccountSetupService: Missing account: $accountName (${accountType.name})');
        } else {
          log('✅ VoucherAccountSetupService: Found account: $accountName');
        }
      }

      if (!allAccountsExist) {
        log('❌ VoucherAccountSetupService: Missing required accounts: ${missingAccounts.join(', ')}');
        log('⚠️ VoucherAccountSetupService: Users must create these accounts manually before voucher processing');
        return false; // Return false instead of creating accounts
      } else {
        log('✅ VoucherAccountSetupService: All required accounts exist');
      }

      return true;
    } catch (e) {
      log('❌ VoucherAccountSetupService: Error checking required accounts: $e');
      return false;
    }
  }

  /// Get setup status for UI display
  static Future<VoucherAccountSetupStatus> getSetupStatus() async {
    try {
      final uid = FirebaseAuth.instance.currentUser?.uid ?? '';
      if (uid.isEmpty) {
        return VoucherAccountSetupStatus(
          isSetup: false,
          missingAccounts: ['Authentication required'],
          message: 'Please login to check account setup',
        );
      }

      // Check required accounts
      final requiredAccounts = [
        {'type': AccountType.operatingExpenses, 'name': 'Broker Fees'},
        {'type': AccountType.operatingExpenses, 'name': 'Munshiana'},
        {'type': AccountType.salesRevenue, 'name': 'Freight Revenue'},
        {'type': AccountType.cash, 'name': 'Cash'},
      ];

      final missingAccounts = <String>[];

      for (final accountInfo in requiredAccounts) {
        final accountType = accountInfo['type'] as AccountType;
        final accountName = accountInfo['name'] as String;

        final accounts = await _chartService.getAccountsByType(accountType);
        final accountExists = accounts.any((account) => account.accountName
            .toLowerCase()
            .contains(accountName.toLowerCase()));

        if (!accountExists) {
          missingAccounts.add(accountName);
        }
      }

      return VoucherAccountSetupStatus(
        isSetup: missingAccounts.isEmpty,
        missingAccounts: missingAccounts,
        message: missingAccounts.isEmpty
            ? 'All required accounts are configured'
            : 'Missing required accounts: ${missingAccounts.join(', ')}',
      );
    } catch (e) {
      return VoucherAccountSetupStatus(
        isSetup: false,
        missingAccounts: ['Error checking accounts'],
        message: 'Error checking account setup: $e',
      );
    }
  }
}

/// Status class for voucher account setup
class VoucherAccountSetupStatus {
  final bool isSetup;
  final List<String> missingAccounts;
  final String message;

  VoucherAccountSetupStatus({
    required this.isSetup,
    required this.missingAccounts,
    required this.message,
  });
}
