import 'dart:developer';
import 'package:dartz/dartz.dart';
import 'package:logestics/models/finance/year_end_closing_model.dart';
import 'package:logestics/models/finance/fiscal_period_model.dart';

import 'package:logestics/models/finance/journal_entry_model.dart';
import 'package:logestics/firebase_service/accounting/year_end_closing_firebase_service.dart';
import 'package:logestics/firebase_service/accounting/fiscal_period_firebase_service.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';

/// Repository interface for year-end closing operations
abstract class YearEndClosingRepository {
  Future<Either<FailureObj, YearEndClosingModel>> performYearEndClosing({
    required FiscalYearModel fiscalYear,
    required String performedBy,
    String? notes,
  });

  Future<Either<FailureObj, List<YearEndClosingModel>>> getYearEndClosings();

  Future<Either<FailureObj, YearEndClosingSummary>> getYearEndClosingSummary();

  Stream<List<YearEndClosingModel>> listenToYearEndClosings();

  Future<Either<FailureObj, SuccessObj>> validateFiscalYearForClosing(
    FiscalYearModel fiscalYear,
  );
}

/// Implementation of year-end closing repository
class YearEndClosingRepositoryImpl implements YearEndClosingRepository {
  final YearEndClosingFirebaseService _firebaseService;
  final FiscalPeriodFirebaseService _fiscalPeriodService;

  YearEndClosingRepositoryImpl(
      this._firebaseService, this._fiscalPeriodService);

  @override
  Future<Either<FailureObj, YearEndClosingModel>> performYearEndClosing({
    required FiscalYearModel fiscalYear,
    required String performedBy,
    String? notes,
  }) async {
    log('Performing year-end closing for fiscal year: ${fiscalYear.yearName}');

    try {
      // Step 1: Validate fiscal year for closing
      final validation = await validateFiscalYearForClosing(fiscalYear);
      if (validation.isLeft()) {
        return Left(validation.fold((l) => l,
            (r) => FailureObj(code: 'ERROR', message: 'Validation failed')));
      }

      // Step 2: Start year-end closing process
      var closing = await _firebaseService.startYearEndClosing(
        fiscalYear: fiscalYear,
        startedBy: performedBy,
        notes: notes,
      );

      // Step 3: Get revenue accounts with balances
      final revenueAccounts =
          await _firebaseService.getRevenueAccountsForClosing();
      final totalRevenues = revenueAccounts.fold<double>(
        0.0,
        (sum, account) => sum + account.balance,
      );

      // Step 4: Get expense accounts with balances
      final expenseAccounts =
          await _firebaseService.getExpenseAccountsForClosing();
      final totalExpenses = expenseAccounts.fold<double>(
        0.0,
        (sum, account) => sum + account.balance,
      );

      // Step 5: Get or create retained earnings account
      final retainedEarningsAccount =
          await _firebaseService.getOrCreateRetainedEarningsAccount();

      // Step 6: Calculate net income
      final netIncome = totalRevenues - totalExpenses;

      // Step 7: Create closing journal entries
      final closingEntries = <JournalEntryModel>[];

      // Close revenue accounts
      if (revenueAccounts.isNotEmpty) {
        final revenueClosingEntry =
            await _firebaseService.createRevenueClosingEntry(
          revenueAccounts: revenueAccounts,
          createdBy: performedBy,
          closingId: closing.id,
        );
        closingEntries.add(revenueClosingEntry);
      }

      // Close expense accounts
      if (expenseAccounts.isNotEmpty) {
        final expenseClosingEntry =
            await _firebaseService.createExpenseClosingEntry(
          expenseAccounts: expenseAccounts,
          createdBy: performedBy,
          closingId: closing.id,
        );
        closingEntries.add(expenseClosingEntry);
      }

      // Transfer net income to retained earnings
      if (netIncome != 0.0) {
        final retainedEarningsEntry =
            await _firebaseService.createRetainedEarningsTransferEntry(
          netIncome: netIncome,
          retainedEarningsAccount: retainedEarningsAccount,
          createdBy: performedBy,
          closingId: closing.id,
        );
        closingEntries.add(retainedEarningsEntry);
      }

      // Step 8: Update account balances
      await _firebaseService.updateAccountBalances(
        revenueAccounts: revenueAccounts,
        expenseAccounts: expenseAccounts,
        retainedEarningsAccount: retainedEarningsAccount,
        netIncome: netIncome,
      );

      // Step 9: Update closing record with completion details
      closing = closing.copyWith(
        status: YearEndClosingStatus.completed,
        completedAt: DateTime.now(),
        completedBy: performedBy,
        closingJournalEntryIds: closingEntries.map((e) => e.id).toList(),
        totalRevenuesClosed: totalRevenues,
        totalExpensesClosed: totalExpenses,
        netIncomeTransferred: netIncome,
        retainedEarningsAccountId: retainedEarningsAccount.id,
      );

      await _firebaseService.updateYearEndClosing(closing);

      log('Year-end closing completed successfully for ${fiscalYear.yearName}');
      return Right(closing);
    } catch (e) {
      log('Error performing year-end closing: $e');
      return Left(FailureObj(code: 'ERROR', message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<YearEndClosingModel>>>
      getYearEndClosings() async {
    try {
      log('Fetching year-end closings from repository');
      final closings = await _firebaseService.getYearEndClosings();
      return Right(closings);
    } catch (e) {
      log('Error fetching year-end closings: $e');
      return Left(FailureObj(code: 'ERROR', message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, YearEndClosingSummary>>
      getYearEndClosingSummary() async {
    try {
      log('Generating year-end closing summary');
      final closings = await _firebaseService.getYearEndClosings();
      final summary = YearEndClosingSummary.fromClosings(closings);
      return Right(summary);
    } catch (e) {
      log('Error generating year-end closing summary: $e');
      return Left(FailureObj(code: 'ERROR', message: e.toString()));
    }
  }

  @override
  Stream<List<YearEndClosingModel>> listenToYearEndClosings() {
    log('Setting up year-end closings stream in repository');
    return _firebaseService.listenToYearEndClosings();
  }

  @override
  Future<Either<FailureObj, SuccessObj>> validateFiscalYearForClosing(
    FiscalYearModel fiscalYear,
  ) async {
    try {
      log('Validating fiscal year for closing: ${fiscalYear.yearName}');

      // Check if fiscal year is already closed
      if (fiscalYear.status == FiscalPeriodStatus.closed) {
        return Left(FailureObj(
          code: 'ERROR',
          message: 'Fiscal year ${fiscalYear.yearName} is already closed',
        ));
      }

      // Check if fiscal year has ended
      if (fiscalYear.endDate.isAfter(DateTime.now())) {
        return Left(FailureObj(
          code: 'ERROR',
          message: 'Fiscal year ${fiscalYear.yearName} has not ended yet',
        ));
      }

      // Check if all periods in the fiscal year are closed
      final allPeriods =
          await _fiscalPeriodService.getFiscalPeriods(fiscalYear.id);
      final openPeriods = allPeriods
          .where((period) => period.status == FiscalPeriodStatus.open)
          .toList();

      if (openPeriods.isNotEmpty) {
        return Left(FailureObj(
          code: 'ERROR',
          message: 'All fiscal periods must be closed before year-end closing. '
              '${openPeriods.length} period(s) are still open.',
        ));
      }

      // Check if year-end closing has already been performed
      final existingClosings = await _firebaseService.getYearEndClosings();
      final existingClosing = existingClosings
          .where((closing) => closing.fiscalYearId == fiscalYear.id)
          .where((closing) => closing.isCompleted)
          .firstOrNull;

      if (existingClosing != null) {
        return Left(FailureObj(
          code: 'ERROR',
          message:
              'Year-end closing has already been completed for fiscal year ${fiscalYear.yearName}',
        ));
      }

      log('Fiscal year validation passed for ${fiscalYear.yearName}');
      return Right(
          SuccessObj(message: 'Fiscal year is ready for year-end closing'));
    } catch (e) {
      log('Error validating fiscal year for closing: $e');
      return Left(FailureObj(code: 'ERROR', message: e.toString()));
    }
  }
}
