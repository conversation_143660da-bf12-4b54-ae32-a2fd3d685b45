import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'voucher_integration_diagnostic.dart';

/// Simple script to run voucher integration diagnostic
class RunVoucherDiagnostic {
  static Future<void> execute() async {
    log('🚀 Starting Voucher Integration Diagnostic...');
    
    // Check if user is authenticated
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      log('❌ No authenticated user. Please login first.');
      return;
    }
    
    log('👤 Running diagnostic for user: ${user.uid}');
    
    // Run the full diagnostic
    await VoucherIntegrationDiagnostic.runFullDiagnostic();
    
    log('✅ Diagnostic completed. Check logs above for detailed results.');
  }
}
