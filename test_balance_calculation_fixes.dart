import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'lib/models/finance/chart_of_accounts_model.dart';
import 'lib/core/services/account_type_helper_service.dart';

/// Comprehensive test suite for balance calculation fixes
/// Tests the critical bug fix where credits and debits were being applied incorrectly
void main() {
  runApp(const BalanceCalculationTestApp());
}

class BalanceCalculationTestApp extends StatelessWidget {
  const BalanceCalculationTestApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Balance Calculation Test Suite',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const BalanceCalculationTestScreen(),
    );
  }
}

class BalanceCalculationTestScreen extends StatefulWidget {
  const BalanceCalculationTestScreen({Key? key}) : super(key: key);

  @override
  State<BalanceCalculationTestScreen> createState() =>
      _BalanceCalculationTestScreenState();
}

class _BalanceCalculationTestScreenState
    extends State<BalanceCalculationTestScreen> {
  final List<TestResult> _testResults = [];
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Balance Calculation Test Suite'),
        backgroundColor: Colors.blue[700],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Balance Calculation Bug Fix Test Suite',
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This test suite verifies the fix for the critical balance calculation bug where '
                      'credits and debits were being applied incorrectly. The specific issue was: '
                      'when an account had a balance of 35,000 and a credit entry of 300 was added, '
                      'the system incorrectly calculated the new balance as +35,300 instead of the expected -34,700.',
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isRunning ? null : _runAllTests,
                      child: _isRunning
                          ? const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                ),
                                SizedBox(width: 8),
                                Text('Running Tests...'),
                              ],
                            )
                          : const Text('Run All Tests'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _testResults.isEmpty
                  ? const Center(
                      child: Text(
                        'Click "Run All Tests" to start the test suite',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _testResults.length,
                      itemBuilder: (context, index) {
                        final result = _testResults[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: Icon(
                              result.passed ? Icons.check_circle : Icons.error,
                              color: result.passed ? Colors.green : Colors.red,
                            ),
                            title: Text(result.testName),
                            subtitle: Text(result.description),
                            trailing: result.passed
                                ? const Text('PASS',
                                    style: TextStyle(
                                        color: Colors.green,
                                        fontWeight: FontWeight.bold))
                                : const Text('FAIL',
                                    style: TextStyle(
                                        color: Colors.red,
                                        fontWeight: FontWeight.bold)),
                            onTap: () => _showTestDetails(result),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunning = true;
      _testResults.clear();
    });

    dev.log('🧪 Starting comprehensive balance calculation test suite...');

    // Test 1: Asset Account Balance Calculation
    await _testAssetAccountBalanceCalculation();

    // Test 2: Liability Account Balance Calculation
    await _testLiabilityAccountBalanceCalculation();

    // Test 3: Equity Account Balance Calculation
    await _testEquityAccountBalanceCalculation();

    // Test 4: Revenue Account Balance Calculation
    await _testRevenueAccountBalanceCalculation();

    // Test 5: Expense Account Balance Calculation
    await _testExpenseAccountBalanceCalculation();

    // Test 6: The Specific Bug Scenario
    await _testSpecificBugScenario();

    // Test 7: Multiple Transaction Scenarios
    await _testMultipleTransactionScenarios();

    // Test 8: Edge Cases
    await _testEdgeCases();

    setState(() {
      _isRunning = false;
    });

    dev.log(
        '✅ Test suite completed. Results: ${_testResults.where((r) => r.passed).length}/${_testResults.length} passed');
  }

  Future<void> _testAssetAccountBalanceCalculation() async {
    final testName = 'Asset Account Balance Calculation';
    dev.log('🧪 Running test: $testName');

    try {
      // Test Asset account (should increase with debits, decrease with credits)
      const accountType = AccountType.cash;
      double currentBalance = 1000.0;

      // Test debit (should increase balance)
      double balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 500.0,
        creditAmount: 0.0,
      );
      double newBalance = currentBalance + balanceChange;

      bool debitTest = newBalance == 1500.0; // 1000 + 500

      // Test credit (should decrease balance)
      balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 0.0,
        creditAmount: 300.0,
      );
      newBalance = currentBalance + balanceChange;

      bool creditTest = newBalance == 700.0; // 1000 - 300

      final passed = debitTest && creditTest;
      _testResults.add(TestResult(
        testName: testName,
        description:
            'Asset accounts should increase with debits and decrease with credits',
        passed: passed,
        details:
            'Debit test: ${debitTest ? 'PASS' : 'FAIL'} (1000 + 500 = $newBalance)\n'
            'Credit test: ${creditTest ? 'PASS' : 'FAIL'} (1000 - 300 = $newBalance)',
      ));

      dev.log(
          '${passed ? '✅' : '❌'} $testName: ${passed ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      _testResults.add(TestResult(
        testName: testName,
        description: 'Asset account balance calculation test',
        passed: false,
        details: 'Error: $e',
      ));
      dev.log('❌ $testName: ERROR - $e');
    }
  }

  Future<void> _testLiabilityAccountBalanceCalculation() async {
    final testName = 'Liability Account Balance Calculation';
    dev.log('🧪 Running test: $testName');

    try {
      // Test Liability account (should increase with credits, decrease with debits)
      const accountType = AccountType.accountsPayable;
      double currentBalance = 2000.0;

      // Test credit (should increase balance)
      double balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 0.0,
        creditAmount: 800.0,
      );
      double newBalance = currentBalance + balanceChange;

      bool creditTest = newBalance == 2800.0; // 2000 + 800

      // Test debit (should decrease balance)
      balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 500.0,
        creditAmount: 0.0,
      );
      newBalance = currentBalance + balanceChange;

      bool debitTest = newBalance == 1500.0; // 2000 - 500

      final passed = creditTest && debitTest;
      _testResults.add(TestResult(
        testName: testName,
        description:
            'Liability accounts should increase with credits and decrease with debits',
        passed: passed,
        details:
            'Credit test: ${creditTest ? 'PASS' : 'FAIL'} (2000 + 800 = ${currentBalance + AccountTypeHelperService.calculateBalanceChange(accountType: accountType, debitAmount: 0.0, creditAmount: 800.0)})\n'
            'Debit test: ${debitTest ? 'PASS' : 'FAIL'} (2000 - 500 = ${currentBalance + AccountTypeHelperService.calculateBalanceChange(accountType: accountType, debitAmount: 500.0, creditAmount: 0.0)})',
      ));

      dev.log(
          '${passed ? '✅' : '❌'} $testName: ${passed ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      _testResults.add(TestResult(
        testName: testName,
        description: 'Liability account balance calculation test',
        passed: false,
        details: 'Error: $e',
      ));
      dev.log('❌ $testName: ERROR - $e');
    }
  }

  Future<void> _testEquityAccountBalanceCalculation() async {
    final testName = 'Equity Account Balance Calculation';
    dev.log('🧪 Running test: $testName');

    try {
      // Test Equity account (should increase with credits, decrease with debits)
      const accountType = AccountType.ownersEquity;
      double currentBalance = 10000.0;

      // Test credit (should increase balance)
      double balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 0.0,
        creditAmount: 1500.0,
      );
      double newBalance = currentBalance + balanceChange;

      bool creditTest = newBalance == 11500.0; // 10000 + 1500

      // Test debit (should decrease balance)
      balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 2000.0,
        creditAmount: 0.0,
      );
      newBalance = currentBalance + balanceChange;

      bool debitTest = newBalance == 8000.0; // 10000 - 2000

      final passed = creditTest && debitTest;
      _testResults.add(TestResult(
        testName: testName,
        description:
            'Equity accounts should increase with credits and decrease with debits',
        passed: passed,
        details:
            'Credit test: ${creditTest ? 'PASS' : 'FAIL'}\nDebit test: ${debitTest ? 'PASS' : 'FAIL'}',
      ));

      dev.log(
          '${passed ? '✅' : '❌'} $testName: ${passed ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      _testResults.add(TestResult(
        testName: testName,
        description: 'Equity account balance calculation test',
        passed: false,
        details: 'Error: $e',
      ));
      dev.log('❌ $testName: ERROR - $e');
    }
  }

  Future<void> _testRevenueAccountBalanceCalculation() async {
    final testName = 'Revenue Account Balance Calculation';
    dev.log('🧪 Running test: $testName');

    try {
      // Test Revenue account (should increase with credits, decrease with debits)
      const accountType = AccountType.salesRevenue;
      double currentBalance = 5000.0;

      // Test credit (should increase balance)
      double balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 0.0,
        creditAmount: 1200.0,
      );
      double newBalance = currentBalance + balanceChange;

      bool creditTest = newBalance == 6200.0; // 5000 + 1200

      // Test debit (should decrease balance)
      balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 800.0,
        creditAmount: 0.0,
      );
      newBalance = currentBalance + balanceChange;

      bool debitTest = newBalance == 4200.0; // 5000 - 800

      final passed = creditTest && debitTest;
      _testResults.add(TestResult(
        testName: testName,
        description:
            'Revenue accounts should increase with credits and decrease with debits',
        passed: passed,
        details:
            'Credit test: ${creditTest ? 'PASS' : 'FAIL'}\nDebit test: ${debitTest ? 'PASS' : 'FAIL'}',
      ));

      dev.log(
          '${passed ? '✅' : '❌'} $testName: ${passed ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      _testResults.add(TestResult(
        testName: testName,
        description: 'Revenue account balance calculation test',
        passed: false,
        details: 'Error: $e',
      ));
      dev.log('❌ $testName: ERROR - $e');
    }
  }

  Future<void> _testExpenseAccountBalanceCalculation() async {
    final testName = 'Expense Account Balance Calculation';
    dev.log('🧪 Running test: $testName');

    try {
      // Test Expense account (should increase with debits, decrease with credits)
      const accountType = AccountType.operatingExpenses;
      double currentBalance = 3000.0;

      // Test debit (should increase balance)
      double balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 600.0,
        creditAmount: 0.0,
      );
      double newBalance = currentBalance + balanceChange;

      bool debitTest = newBalance == 3600.0; // 3000 + 600

      // Test credit (should decrease balance)
      balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 0.0,
        creditAmount: 400.0,
      );
      newBalance = currentBalance + balanceChange;

      bool creditTest = newBalance == 2600.0; // 3000 - 400

      final passed = debitTest && creditTest;
      _testResults.add(TestResult(
        testName: testName,
        description:
            'Expense accounts should increase with debits and decrease with credits',
        passed: passed,
        details:
            'Debit test: ${debitTest ? 'PASS' : 'FAIL'}\nCredit test: ${creditTest ? 'PASS' : 'FAIL'}',
      ));

      dev.log(
          '${passed ? '✅' : '❌'} $testName: ${passed ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      _testResults.add(TestResult(
        testName: testName,
        description: 'Expense account balance calculation test',
        passed: false,
        details: 'Error: $e',
      ));
      dev.log('❌ $testName: ERROR - $e');
    }
  }

  Future<void> _testSpecificBugScenario() async {
    final testName = 'Specific Bug Scenario Test';
    dev.log('🧪 Running test: $testName');

    try {
      // Test the exact scenario described in the bug report:
      // Account balance: 35,000, Credit entry: 300
      // Expected result for Asset account: 35,000 - 300 = 34,700

      const accountType = AccountType.cash; // Asset account
      double currentBalance = 35000.0;
      double creditAmount = 300.0;

      double balanceChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 0.0,
        creditAmount: creditAmount,
      );
      double newBalance = currentBalance + balanceChange;

      // For asset accounts, credits should decrease the balance
      // So: 35,000 - 300 = 34,700
      bool passed = newBalance == 34700.0;

      _testResults.add(TestResult(
        testName: testName,
        description:
            'Tests the exact bug scenario: Asset account with 35,000 balance + 300 credit should = 34,700',
        passed: passed,
        details:
            'Starting balance: 35,000\nCredit amount: 300\nExpected result: 34,700\nActual result: $newBalance\n'
            'Balance change calculated: $balanceChange\n'
            'Test result: ${passed ? 'CORRECT - Bug is FIXED!' : 'INCORRECT - Bug still exists!'}',
      ));

      dev.log(
          '${passed ? '✅' : '❌'} $testName: ${passed ? 'PASSED - Bug is FIXED!' : 'FAILED - Bug still exists!'}');
    } catch (e) {
      _testResults.add(TestResult(
        testName: testName,
        description: 'Specific bug scenario test',
        passed: false,
        details: 'Error: $e',
      ));
      dev.log('❌ $testName: ERROR - $e');
    }
  }

  Future<void> _testMultipleTransactionScenarios() async {
    final testName = 'Multiple Transaction Scenarios';
    dev.log('🧪 Running test: $testName');

    try {
      // Test multiple transactions on the same account
      const accountType = AccountType.bank; // Asset account
      double balance = 10000.0;

      // Transaction 1: Debit 2000 (should increase to 12000)
      balance += AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 2000.0,
        creditAmount: 0.0,
      );
      bool test1 = balance == 12000.0;

      // Transaction 2: Credit 1500 (should decrease to 10500)
      balance += AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 0.0,
        creditAmount: 1500.0,
      );
      bool test2 = balance == 10500.0;

      // Transaction 3: Debit 500 (should increase to 11000)
      balance += AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 500.0,
        creditAmount: 0.0,
      );
      bool test3 = balance == 11000.0;

      final passed = test1 && test2 && test3;
      _testResults.add(TestResult(
        testName: testName,
        description:
            'Tests multiple sequential transactions on the same account',
        passed: passed,
        details: 'Test 1 (Debit 2000): ${test1 ? 'PASS' : 'FAIL'}\n'
            'Test 2 (Credit 1500): ${test2 ? 'PASS' : 'FAIL'}\n'
            'Test 3 (Debit 500): ${test3 ? 'PASS' : 'FAIL'}\n'
            'Final balance: $balance (expected: 11000)',
      ));

      dev.log(
          '${passed ? '✅' : '❌'} $testName: ${passed ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      _testResults.add(TestResult(
        testName: testName,
        description: 'Multiple transaction scenarios test',
        passed: false,
        details: 'Error: $e',
      ));
      dev.log('❌ $testName: ERROR - $e');
    }
  }

  Future<void> _testEdgeCases() async {
    final testName = 'Edge Cases Test';
    dev.log('🧪 Running test: $testName');

    try {
      // Test zero amounts
      double balanceChange1 = AccountTypeHelperService.calculateBalanceChange(
        accountType: AccountType.cash,
        debitAmount: 0.0,
        creditAmount: 0.0,
      );
      bool zeroTest = balanceChange1 == 0.0;

      // Test negative balance scenarios
      const accountType = AccountType.cash;
      double balance = 100.0;
      balance += AccountTypeHelperService.calculateBalanceChange(
        accountType: accountType,
        debitAmount: 0.0,
        creditAmount: 200.0,
      );
      bool negativeTest = balance == -100.0; // Asset can go negative

      // Test large amounts
      double largeChange = AccountTypeHelperService.calculateBalanceChange(
        accountType: AccountType.cash,
        debitAmount: 1000000.0,
        creditAmount: 0.0,
      );
      bool largeTest = largeChange == 1000000.0;

      final passed = zeroTest && negativeTest && largeTest;
      _testResults.add(TestResult(
        testName: testName,
        description:
            'Tests edge cases like zero amounts, negative balances, and large amounts',
        passed: passed,
        details: 'Zero amounts test: ${zeroTest ? 'PASS' : 'FAIL'}\n'
            'Negative balance test: ${negativeTest ? 'PASS' : 'FAIL'}\n'
            'Large amounts test: ${largeTest ? 'PASS' : 'FAIL'}',
      ));

      dev.log(
          '${passed ? '✅' : '❌'} $testName: ${passed ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      _testResults.add(TestResult(
        testName: testName,
        description: 'Edge cases test',
        passed: false,
        details: 'Error: $e',
      ));
      dev.log('❌ $testName: ERROR - $e');
    }
  }

  void _showTestDetails(TestResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(result.testName),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Description:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(result.description),
              const SizedBox(height: 16),
              Text(
                'Details:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(result.details),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

class TestResult {
  final String testName;
  final String description;
  final bool passed;
  final String details;

  TestResult({
    required this.testName,
    required this.description,
    required this.passed,
    required this.details,
  });
}
