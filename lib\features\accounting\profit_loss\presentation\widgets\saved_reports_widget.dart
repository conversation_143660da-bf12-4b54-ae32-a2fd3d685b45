import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profit_loss_controller.dart';

/// Widget for displaying and managing saved Profit & Loss reports
class SavedReportsWidget extends StatelessWidget {
  const SavedReportsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfitLossController>();

    return Obx(() {
      if (controller.savedReports.isEmpty) {
        return _buildEmptyState(context);
      }

      return Column(
        children: [
          // Reports List
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.savedReports.length,
            separatorBuilder: (context, index) => const SizedBox(height: 8),
            itemBuilder: (context, index) {
              final report = controller.savedReports[index];
              return _buildReportCard(context, report, controller);
            },
          ),
        ],
      );
    });
  }

  /// Build empty state when no saved reports
  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        children: [
          Icon(
            Icons.folder_open,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Saved Reports',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Generate and save reports to see them here.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
        ],
      ),
    );
  }

  /// Build individual report card
  Widget _buildReportCard(
    BuildContext context,
    dynamic report,
    ProfitLossController controller,
  ) {
    final isProfitable = report.isProfitable;
    final profitColor = isProfitable ? Colors.green : Colors.red;

    return Card(
      elevation: 1,
      child: InkWell(
        onTap: () => controller.loadReport(report.reportId),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Company Name and Date Range
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          report.companyName,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_formatDate(report.startDate)} - ${_formatDate(report.endDate)}',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                      ],
                    ),
                  ),
                  // Actions
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Profit/Loss Indicator
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: profitColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                              color: profitColor.withValues(alpha: 0.3)),
                        ),
                        child: Text(
                          isProfitable ? 'Profit' : 'Loss',
                          style: TextStyle(
                            color: profitColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Delete Button
                      IconButton(
                        icon: const Icon(Icons.delete_outline),
                        onPressed: () => _showDeleteConfirmation(
                            context, report, controller),
                        tooltip: 'Delete Report',
                        iconSize: 20,
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Financial Summary Row
              Row(
                children: [
                  // Revenue
                  Expanded(
                    child: _buildMetricColumn(
                      context,
                      'Revenue',
                      report.totalRevenue,
                      Colors.green,
                    ),
                  ),
                  // Expenses
                  Expanded(
                    child: _buildMetricColumn(
                      context,
                      'Expenses',
                      report.totalExpenses,
                      Colors.orange,
                    ),
                  ),
                  // Net Income
                  Expanded(
                    child: _buildMetricColumn(
                      context,
                      'Net Income',
                      report.netIncome,
                      profitColor,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Footer Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Generated Date
                  Text(
                    'Generated: ${_formatDateTime(report.generatedAt)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                  // Load Button
                  TextButton.icon(
                    onPressed: () => controller.loadReport(report.reportId),
                    icon: const Icon(Icons.open_in_new, size: 16),
                    label: const Text('Load'),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 4),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build metric column for financial data
  Widget _buildMetricColumn(
    BuildContext context,
    String label,
    double amount,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        const SizedBox(height: 2),
        Text(
          _formatCurrency(amount),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
        ),
      ],
    );
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(
    BuildContext context,
    dynamic report,
    ProfitLossController controller,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Report'),
          content: Text(
            'Are you sure you want to delete the P&L report for "${report.companyName}" (${_formatDate(report.startDate)} - ${_formatDate(report.endDate)})?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                controller.deleteReport(report.reportId);
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Format date and time for display
  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Format currency amount
  String _formatCurrency(double amount) {
    final isNegative = amount < 0;
    final absAmount = amount.abs();

    String formatted;
    if (absAmount >= 1000000) {
      formatted = '${(absAmount / 1000000).toStringAsFixed(1)}M';
    } else if (absAmount >= 1000) {
      formatted = '${(absAmount / 1000).toStringAsFixed(0)}K';
    } else {
      formatted = absAmount.toStringAsFixed(0);
    }

    return isNegative ? '($formatted)' : formatted;
  }
}
