import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';

import '../controllers/general_ledger_controller.dart';
import '../widgets/account_balance_card.dart';
import '../widgets/account_transactions_list.dart';
import '../widgets/general_ledger_filters.dart';

class GeneralLedgerScreen extends StatelessWidget {
  const GeneralLedgerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<GeneralLedgerController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('General Ledger'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFiltersDialog(context, controller),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.loadData(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Summary Section
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            child: Column(
              children: [
                // Quick Stats
                Row(
                  children: [
                    Expanded(
                      child: _buildSummaryCard(
                        context,
                        'Total Accounts',
                        controller.accounts.length.toString(),
                        Icons.account_balance,
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Obx(() {
                        final activeAccounts = controller.accounts
                            .where((account) => account.isActive)
                            .length;
                        return _buildSummaryCard(
                          context,
                          'Active Accounts',
                          activeAccounts.toString(),
                          Icons.check_circle,
                          Colors.green,
                        );
                      }),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Obx(() {
                        final totalBalance = controller.accountBalances.values
                            .fold(0.0, (sum, balance) => sum + balance.abs());
                        return _buildSummaryCard(
                          context,
                          'Total Balance',
                          '\$${totalBalance.toStringAsFixed(2)}',
                          Icons.account_balance_wallet,
                          Colors.purple,
                        );
                      }),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Search Bar
                TextField(
                  controller: controller.searchController,
                  decoration: InputDecoration(
                    hintText: 'Search accounts by name or number...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon:
                        Obx(() => controller.searchQuery.value.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: controller.clearSearch,
                              )
                            : const SizedBox.shrink()),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: controller.updateSearchQuery,
                ),
              ],
            ),
          ),

          // Accounts List
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              final filteredAccounts = controller.filteredAccounts;

              if (filteredAccounts.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.account_balance_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.searchQuery.value.isNotEmpty
                            ? 'No accounts found matching "${controller.searchQuery.value}"'
                            : 'No accounts found',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        controller.searchQuery.value.isNotEmpty
                            ? 'Try adjusting your search criteria'
                            : 'Set up your chart of accounts to get started',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[500],
                            ),
                      ),
                      if (controller.searchQuery.value.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: controller.clearSearch,
                          child: const Text('Clear Search'),
                        ),
                      ],
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: controller.loadData,
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredAccounts.length,
                  itemBuilder: (context, index) {
                    final account = filteredAccounts[index];
                    final balance =
                        controller.accountBalances[account.id] ?? 0.0;

                    return AccountBalanceCard(
                      account: account,
                      balance: balance,
                      onTap: () =>
                          _showAccountDetails(context, account, balance),
                      isLoading:
                          controller.loadingBalances.contains(account.id),
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAccountTransactionsDialog(context),
        icon: const Icon(Icons.list_alt),
        label: const Text('View Transactions'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFiltersDialog(
      BuildContext context, GeneralLedgerController controller) {
    showDialog(
      context: context,
      builder: (context) => GeneralLedgerFilters(controller: controller),
    );
  }

  void _showAccountDetails(
      BuildContext context, ChartOfAccountsModel account, double balance) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${account.accountNumber} - ${account.accountName}',
                          style: Theme.of(context)
                              .textTheme
                              .headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Account Type: ${account.accountType.displayName}',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Balance Card
              Card(
                color: balance >= 0
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.red.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        balance >= 0 ? Icons.trending_up : Icons.trending_down,
                        color: balance >= 0 ? Colors.green : Colors.red,
                        size: 32,
                      ),
                      const SizedBox(width: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Current Balance',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: Colors.grey[600],
                                ),
                          ),
                          Text(
                            '\$${balance.toStringAsFixed(2)}',
                            style: Theme.of(context)
                                .textTheme
                                .headlineMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: balance >= 0
                                      ? Colors.green[700]
                                      : Colors.red[700],
                                ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Transactions List
              Text(
                'Recent Transactions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 16),

              Expanded(
                child: AccountTransactionsList(
                  accountId: account.id,
                  accountName: account.accountName,
                  accountCategory: account.category,
                  accountBalance: account.balance,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAccountTransactionsDialog(BuildContext context) {
    final controller = Get.find<GeneralLedgerController>();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Account Transactions',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Account Selection
              Obx(() => DropdownButtonFormField<ChartOfAccountsModel>(
                    value: controller.selectedAccount.value,
                    decoration: const InputDecoration(
                      labelText: 'Select Account',
                      border: OutlineInputBorder(),
                    ),
                    items: controller.accounts.map((account) {
                      return DropdownMenuItem(
                        value: account,
                        child: Text(
                            '${account.accountNumber} - ${account.accountName}'),
                      );
                    }).toList(),
                    onChanged: (account) =>
                        controller.selectedAccount.value = account,
                  )),

              const SizedBox(height: 24),

              Expanded(
                child: Obx(() {
                  if (controller.selectedAccount.value == null) {
                    return const Center(
                      child:
                          Text('Please select an account to view transactions'),
                    );
                  }

                  return AccountTransactionsList(
                    accountId: controller.selectedAccount.value!.id,
                    accountName: controller.selectedAccount.value!.accountName,
                    accountCategory: controller.selectedAccount.value!.category,
                    accountBalance: controller.selectedAccount.value!.balance,
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
