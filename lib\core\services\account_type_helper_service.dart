import 'package:flutter/material.dart';
import '../../models/finance/chart_of_accounts_model.dart';

/// Service to handle account type-specific behavior according to standard accounting principles
///
/// Standard Accounting Rules:
/// - Assets & Expenses: Debit increases balance, Credit decreases balance
/// - Revenue, Liabilities & Equity: Credit increases balance, Debit decreases balance
class AccountTypeHelperService {
  /// Determine if an account type increases with debit entries
  static bool isDebitAccount(AccountType accountType) {
    switch (accountType.category) {
      case AccountCategory.assets:
      case AccountCategory.expenses:
        return true;
      case AccountCategory.liabilities:
      case AccountCategory.equity:
      case AccountCategory.revenue:
        return false;
    }
  }

  /// Determine if an account type increases with credit entries
  static bool isCreditAccount(AccountType accountType) {
    return !isDebitAccount(accountType);
  }

  /// Calculate balance change for an account based on account type and transaction type
  /// Returns positive value for balance increase, negative for balance decrease
  static double calculateBalanceChange({
    required AccountType accountType,
    required double debitAmount,
    required double creditAmount,
  }) {
    if (isDebitAccount(accountType)) {
      // Assets & Expenses: Debit increases, Credit decreases
      return debitAmount - creditAmount;
    } else {
      // Revenue, Liabilities & Equity: Credit increases, Debit decreases
      return creditAmount - debitAmount;
    }
  }

  /// Calculate new balance for an account after a transaction
  static double calculateNewBalance({
    required double currentBalance,
    required AccountType accountType,
    required double debitAmount,
    required double creditAmount,
  }) {
    final balanceChange = calculateBalanceChange(
      accountType: accountType,
      debitAmount: debitAmount,
      creditAmount: creditAmount,
    );
    return currentBalance + balanceChange;
  }

  /// Calculate balance change using AccountCategory (for backward compatibility)
  static double calculateBalanceChangeByCategory({
    required AccountCategory accountCategory,
    required double debitAmount,
    required double creditAmount,
  }) {
    switch (accountCategory) {
      case AccountCategory.assets:
      case AccountCategory.expenses:
        // Debit increases, Credit decreases
        return debitAmount - creditAmount;
      case AccountCategory.liabilities:
      case AccountCategory.equity:
      case AccountCategory.revenue:
        // Credit increases, Debit decreases
        return creditAmount - debitAmount;
    }
  }

  /// Calculate new balance using AccountCategory (for backward compatibility)
  static double calculateNewBalanceByCategory({
    required double currentBalance,
    required AccountCategory accountCategory,
    required double debitAmount,
    required double creditAmount,
  }) {
    final balanceChange = calculateBalanceChangeByCategory(
      accountCategory: accountCategory,
      debitAmount: debitAmount,
      creditAmount: creditAmount,
    );
    return currentBalance + balanceChange;
  }

  /// Get account type behavior description for UI display
  static String getAccountBehaviorDescription(AccountType accountType) {
    if (isDebitAccount(accountType)) {
      return 'Debit increases balance, Credit decreases balance';
    } else {
      return 'Credit increases balance, Debit decreases balance';
    }
  }

  /// Get account category behavior description for UI display
  static String getAccountCategoryBehaviorDescription(
      AccountCategory accountCategory) {
    switch (accountCategory) {
      case AccountCategory.assets:
      case AccountCategory.expenses:
        return 'Debit increases balance, Credit decreases balance';
      case AccountCategory.liabilities:
      case AccountCategory.equity:
      case AccountCategory.revenue:
        return 'Credit increases balance, Debit decreases balance';
    }
  }

  /// Determine if a balance change represents an increase for the account type
  static bool isBalanceIncrease({
    required AccountType accountType,
    required double debitAmount,
    required double creditAmount,
  }) {
    final balanceChange = calculateBalanceChange(
      accountType: accountType,
      debitAmount: debitAmount,
      creditAmount: creditAmount,
    );
    return balanceChange > 0;
  }

  /// Determine if a balance change represents an increase for the account category
  static bool isBalanceIncreaseByCategory({
    required AccountCategory accountCategory,
    required double debitAmount,
    required double creditAmount,
  }) {
    final balanceChange = calculateBalanceChangeByCategory(
      accountCategory: accountCategory,
      debitAmount: debitAmount,
      creditAmount: creditAmount,
    );
    return balanceChange > 0;
  }

  /// Get color for balance change based on whether it increases or decreases the balance
  /// Green for increases, Red for decreases, Grey for no change
  static Color getBalanceChangeColor({
    required AccountType accountType,
    required double debitAmount,
    required double creditAmount,
  }) {
    final isIncrease = isBalanceIncrease(
      accountType: accountType,
      debitAmount: debitAmount,
      creditAmount: creditAmount,
    );

    final balanceChange = calculateBalanceChange(
      accountType: accountType,
      debitAmount: debitAmount,
      creditAmount: creditAmount,
    );

    if (balanceChange == 0) {
      return Colors.grey[600]!;
    } else if (isIncrease) {
      return Colors.green[700]!;
    } else {
      return Colors.red[700]!;
    }
  }

  /// Get color for balance change by category
  static Color getBalanceChangeColorByCategory({
    required AccountCategory accountCategory,
    required double debitAmount,
    required double creditAmount,
  }) {
    final isIncrease = isBalanceIncreaseByCategory(
      accountCategory: accountCategory,
      debitAmount: debitAmount,
      creditAmount: creditAmount,
    );

    final balanceChange = calculateBalanceChangeByCategory(
      accountCategory: accountCategory,
      debitAmount: debitAmount,
      creditAmount: creditAmount,
    );

    if (balanceChange == 0) {
      return Colors.grey[600]!;
    } else if (isIncrease) {
      return Colors.green[700]!;
    } else {
      return Colors.red[700]!;
    }
  }
}
