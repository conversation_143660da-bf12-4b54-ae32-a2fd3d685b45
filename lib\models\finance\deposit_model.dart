class DepositModel {
  final String id;

  // Legacy account fields (maintained for backward compatibility)
  final String accountId;
  final String accountName;

  // Chart of Accounts fields for two-account system
  final String? sourceAccountId; // Account where money decreases from
  final String? sourceAccountName;
  final String? destinationAccountId; // Account where money increases to
  final String? destinationAccountName;

  final double amount;

  final String categoryId;
  final String categoryName;
  final String payerId;
  final String payerName;
  final String referenceNumber;
  final String notes;
  final DateTime createdAt;
  final String uid; // User ID who owns this deposit

  DepositModel({
    required this.id,
    required this.accountId,
    required this.accountName,
    this.sourceAccountId,
    this.sourceAccountName,
    this.destinationAccountId,
    this.destinationAccountName,
    required this.amount,
    required this.categoryId,
    required this.categoryName,
    required this.payerId,
    required this.payerName,
    required this.referenceNumber,
    this.notes = '',
    required this.createdAt,
    this.uid = '', // Default empty string for backward compatibility
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      // Legacy account fields
      'accountId': accountId,
      'accountName': accountName,
      // Chart of Accounts fields
      'sourceAccountId': sourceAccountId,
      'sourceAccountName': sourceAccountName,
      'destinationAccountId': destinationAccountId,
      'destinationAccountName': destinationAccountName,
      'amount': amount,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'payerId': payerId,
      'payerName': payerName,
      'referenceNumber': referenceNumber,
      'notes': notes,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'uid': uid, // Include UID in JSON
    };
  }

  factory DepositModel.fromJson(Map<String, dynamic> json) {
    return DepositModel(
      id: json['id'] ?? '',
      // Legacy account fields
      accountId: json['accountId'] ?? '',
      accountName: json['accountName'] ?? '',
      // Chart of Accounts fields
      sourceAccountId: json['sourceAccountId'],
      sourceAccountName: json['sourceAccountName'],
      destinationAccountId: json['destinationAccountId'],
      destinationAccountName: json['destinationAccountName'],
      amount: (json['amount'] ?? 0).toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      categoryId: json['categoryId'] ?? '',
      categoryName: json['categoryName'] ?? '',
      payerId: json['payerId'] ?? '',
      payerName: json['payerName'] ?? '',
      referenceNumber: json['referenceNumber'] ?? '',
      notes: json['notes'] ?? '',
      uid: json['uid'] ?? '', // Extract UID from JSON
    );
  }

  // Create a copy of this model with updated fields
  DepositModel copyWith({
    String? id,
    String? accountId,
    String? accountName,
    String? sourceAccountId,
    String? sourceAccountName,
    String? destinationAccountId,
    String? destinationAccountName,
    double? amount,
    String? categoryId,
    String? categoryName,
    String? payerId,
    String? payerName,
    String? referenceNumber,
    String? notes,
    DateTime? createdAt,
    String? uid,
  }) {
    return DepositModel(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      accountName: accountName ?? this.accountName,
      sourceAccountId: sourceAccountId ?? this.sourceAccountId,
      sourceAccountName: sourceAccountName ?? this.sourceAccountName,
      destinationAccountId: destinationAccountId ?? this.destinationAccountId,
      destinationAccountName:
          destinationAccountName ?? this.destinationAccountName,
      amount: amount ?? this.amount,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      payerId: payerId ?? this.payerId,
      payerName: payerName ?? this.payerName,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      uid: uid ?? this.uid,
    );
  }

  // Helper methods for Chart of Accounts integration

  /// Check if this deposit uses Chart of Accounts (has source and destination accounts)
  bool get usesChartOfAccounts =>
      sourceAccountId != null &&
      sourceAccountId!.isNotEmpty &&
      destinationAccountId != null &&
      destinationAccountId!.isNotEmpty;

  /// Check if this deposit uses legacy account system
  bool get usesLegacyAccount => accountId.isNotEmpty && !usesChartOfAccounts;

  /// Get the effective destination account ID (Chart of Accounts or legacy)
  String get effectiveDestinationAccountId =>
      usesChartOfAccounts ? destinationAccountId! : accountId;

  /// Get the effective destination account name (Chart of Accounts or legacy)
  String get effectiveDestinationAccountName =>
      usesChartOfAccounts ? destinationAccountName! : accountName;

  /// Get a display-friendly description of the account setup
  String get accountSetupDescription {
    if (usesChartOfAccounts) {
      return 'From: $sourceAccountName → To: $destinationAccountName';
    } else {
      return 'Legacy Account: $accountName';
    }
  }

  @override
  String toString() {
    return 'DepositModel(id: $id, amount: $amount, '
        'usesChartOfAccounts: $usesChartOfAccounts, '
        'accountSetup: $accountSetupDescription)';
  }
}
