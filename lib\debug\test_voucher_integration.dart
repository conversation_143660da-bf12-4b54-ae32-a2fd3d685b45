import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/services/voucher_accounting_hook_service.dart';
import 'package:logestics/features/voucher/repositories/voucher_repository.dart';
import 'package:logestics/firebase_service/voucher/voucher_crud_firebase_service.dart';

/// Test script to verify voucher-to-journal entry integration
class TestVoucherIntegration {
  static Future<void> runTest() async {
    try {
      final uid = FirebaseAuth.instance.currentUser?.uid ?? '';
      if (uid.isEmpty) {
        log('❌ No authenticated user found');
        return;
      }

      log('🧪 Starting voucher integration test...');

      // Create test voucher data with Chart of Accounts integration
      final testVoucherData = _createTestVoucherData(uid);

      // Test 1: Direct hook service call
      log('🔗 Test 1: Testing direct hook service call...');
      final hookService = VoucherAccountingHookService();
      await hookService.onVoucherCreated(testVoucherData, uid);

      // Test 2: Repository integration
      log('🔗 Test 2: Testing repository integration...');
      final repository = VoucherRepositoryImp(VoucherCrudFirebaseService());
      final result = await repository.createVoucher(
        uid: uid,
        voucher: testVoucherData,
      );

      result.fold(
        (failure) => log('❌ Repository test failed: ${failure.message}'),
        (success) => log('✅ Repository test passed: ${success.message}'),
      );

      log('✅ Voucher integration test completed');
    } catch (e) {
      log('❌ Test failed with error: $e');
    }
  }

  static Map<String, dynamic> _createTestVoucherData(String uid) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return {
      'voucherNumber': 'TEST-$timestamp',
      'voucherStatus': 'Active',
      'driverName': 'Test Driver',
      'departureDate': DateTime.now().toIso8601String(),
      'productName': 'Test Product',
      'totalNumberOfBags': 100,
      'brokerType': 'outsource',
      'brokerName': 'Test Broker',
      'selectedBroker': 'test-broker-id',
      'brokerFees': 5000.0,
      'munshianaFees': 2000.0,
      'brokerAccount': '',
      'munshianaAccount': '',
      'driverPhoneNumber': '**********',
      'truckNumber': 'ABC-123',
      'conveyNoteNumber': 'CN-123',
      'totalFreight': 50000.0,
      'companyFreight': 45000.0,
      'settledFreight': 45000.0,
      'weightInTons': 10,
      'invoiceTasNumberList': ['TAS-001'],
      'invoiceBiltyNumberList': ['BILTY-001'],
      'paymentTransactions': [],
      'belongsToDate': timestamp,
      'createdAt': timestamp,
      'uid': uid,

      // Chart of Accounts fields - these are critical for integration
      'brokerAccountId': 'test-broker-account-id',
      'munshianaAccountId': 'test-munshiana-account-id',
      'salesTaxAccountId': 'test-sales-tax-account-id',
      'freightTaxAccountId': 'test-freight-tax-account-id',
      'profitAccountId': 'test-profit-account-id',
      'nlcAccountId': 'test-nlc-account-id',
      'truckFreightAccountId': 'test-truck-freight-account-id',

      // Tax amounts
      'salesTaxAmount': 750.0, // 15% of 5000
      'freightTaxAmount': 2070.0, // 4.6% of 45000
      'netProfitAmount': 37180.0, // Calculated net profit
    };
  }
}

/// Widget to add test button to UI
class VoucherIntegrationTestButton extends StatelessWidget {
  const VoucherIntegrationTestButton({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () async {
        await TestVoucherIntegration.runTest();
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      child: const Text('Test Voucher Integration'),
    );
  }
}
