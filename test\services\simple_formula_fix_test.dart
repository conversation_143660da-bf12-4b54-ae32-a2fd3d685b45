import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';

void main() {
  group('Simple Formula Fix Tests', () {
    late InvoiceModel testInvoice;

    setUp(() {
      testInvoice = InvoiceModel(
        invoiceNumber: 1122,
        invoiceStatus: 'Active',
        tasNumber: 'TAS1122',
        productName: 'Test Product',
        numberOfBags: 400,
        weightPerBag: 50.0,
        customerName: 'Test Customer',
        truckNumber: 'TRK001',
        conveyNoteNumber: 'CN001',
        biltyNumber: 'BLT001',
        consignorName: 'Test Consignor',
        deliveryMode: 'Road',
        districtId: 'DIST001',
        districtName: 'Test District',
        stationId: 'STN001',
        stationName: 'Test Station',
        fromPlaceId: 'PLC001',
        fromPlaceName: 'Test Place',
        distanceInKilometers: 175.0,
        consignorPickUpAddress: 'Test Address',
      );
    });

    test('should parse simple nested IF-ELSE correctly', () {
      // Test the simplest nested IF-ELSE that was failing
      final simpleFormula = 'IF 175.0 > 120 THEN 7.68 ELSE 1196.38';
      final result =
          FlexibleFormulaCalculationService.evaluateExpression(simpleFormula);

      print('Simple formula result: $result');
      expect(result, isNotNull);
      expect(result, equals(7.68));
    });

    test('should parse two-level nested IF-ELSE correctly', () {
      // Test two-level nesting
      final twoLevelFormula =
          'IF 175.0 <= 40 THEN 1196.38 ELSE IF 175.0 > 120 THEN 7.68 ELSE 1196.38';
      final result =
          FlexibleFormulaCalculationService.evaluateExpression(twoLevelFormula);

      print('Two-level formula result: $result');
      expect(result, isNotNull);
      expect(result, equals(7.68));
    });

    test('should parse three-level nested IF-ELSE correctly', () {
      // Test three-level nesting
      final threeLevelFormula =
          'IF 175.0 <= 40 THEN 1196.38 ELSE IF 175.0 <= 80 THEN 1196.38 ELSE IF 175.0 > 120 THEN 7.68 ELSE 1196.38';
      final result = FlexibleFormulaCalculationService.evaluateExpression(
          threeLevelFormula);

      print('Three-level formula result: $result');
      expect(result, isNotNull);
      expect(result, equals(7.68));
    });

    test('should work with formula step execution', () {
      // Test with actual formula step execution
      final formula = CalculationFormulaModel(
        formulaId: 'simple_test',
        formulaName: 'Simple Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Simple IF Test',
            formula: 'IF distanceInKilometers > 120 THEN 7.68 ELSE 1196.38',
            resultVariable: 'rate',
            description: 'Simple test',
          ),
        ],
        finalResultVariable: 'rate',
      );

      final result =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice,
        formula: formula,
        customColumnValues: {},
      );

      print('Step execution success: ${result.isSuccess}');
      print('Step execution result: ${result.finalAmount}');
      print('Step execution error: ${result.errorMessage}');

      expect(result.isSuccess, isTrue);
      expect(result.finalAmount, equals(7.68));
    });

    test('should work with two-step formula execution', () {
      // Test with two-step formula execution
      final formula = CalculationFormulaModel(
        formulaId: 'two_step_test',
        formulaName: 'Two Step Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Determine Rate',
            formula: 'IF distanceInKilometers > 120 THEN 7.68 ELSE 1196.38',
            resultVariable: 'rate',
            description: 'Determine rate',
          ),
          FormulaStepModel(
            stepId: 'step2',
            stepName: 'Calculate Amount',
            formula: 'totalWeightTons × rate',
            resultVariable: 'finalAmount',
            description: 'Calculate final amount',
          ),
        ],
        finalResultVariable: 'finalAmount',
      );

      final result =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice,
        formula: formula,
        customColumnValues: {},
      );

      print('Two-step execution success: ${result.isSuccess}');
      print('Two-step execution result: ${result.finalAmount}');
      print('Two-step execution error: ${result.errorMessage}');

      expect(result.isSuccess, isTrue);
      // Expected: 20 tons × 7.68 = 153.6, rounded to 154.0 (round half up rule)
      expect(result.finalAmount, equals(154.0));
    });

    test('should debug the exact problematic formula', () {
      // Test the exact formula that was causing issues
      final problematicFormula =
          'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 1196.38 ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 7.68';

      print('Problematic formula length: ${problematicFormula.length}');

      // First test direct evaluation
      final directResult = FlexibleFormulaCalculationService.evaluateExpression(
          problematicFormula.replaceAll('distanceInKilometers', '175.0'));
      print('Direct evaluation result: $directResult');

      final formula = CalculationFormulaModel(
        formulaId: 'problematic_test',
        formulaName: 'Problematic Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Determine Effective Rate Based on Distance',
            formula: problematicFormula,
            resultVariable: 'effectiveRate',
            description: 'Test problematic formula',
          ),
        ],
        finalResultVariable: 'effectiveRate',
      );

      final result =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice,
        formula: formula,
        customColumnValues: {},
      );

      print('Problematic formula success: ${result.isSuccess}');
      print('Problematic formula result: ${result.finalAmount}');
      print('Problematic formula error: ${result.errorMessage}');
      print('Step results: ${result.stepResults}');

      // If direct evaluation works but step execution doesn't, there's an issue in step execution
      if (directResult != null && !result.isSuccess) {
        print(
            'Direct evaluation works but step execution fails - issue in step execution');
      }

      // This should now work with our fix
      expect(result.isSuccess, isTrue,
          reason: 'Problematic formula should now work');
      expect(result.finalAmount, equals(7.68),
          reason: 'Should return correct rate for 175 KM');
    });

    test('should test a shorter version of the problematic formula', () {
      // Test a shorter version to see if it's a length issue
      final shorterFormula =
          'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 7.68';

      final formula = CalculationFormulaModel(
        formulaId: 'shorter_test',
        formulaName: 'Shorter Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Shorter Formula Test',
            formula: shorterFormula,
            resultVariable: 'rate',
            description: 'Test shorter formula',
          ),
        ],
        finalResultVariable: 'rate',
      );

      final result =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice,
        formula: formula,
        customColumnValues: {},
      );

      print('Shorter formula success: ${result.isSuccess}');
      print('Shorter formula result: ${result.finalAmount}');
      print('Shorter formula error: ${result.errorMessage}');

      expect(result.isSuccess, isTrue, reason: 'Shorter formula should work');
      expect(result.finalAmount, equals(7.68),
          reason: 'Should return correct rate for 175 KM');
    });
  });
}
