import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import 'package:logestics/features/authentication/presentation/controllers/signup_controller.dart';
import 'package:logestics/features/authentication/presentation/screens/login_screen/components/account_button.dart';
import 'package:logestics/features/authentication/presentation/screens/login_screen/components/input_field.dart';
import 'package:logestics/core/utils/widgets/app_error_container.dart';

class SignupComponent extends GetView<SignupController> {
  const SignupComponent({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Form(
          key: controller.signupFormStateKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              // Sign up header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Register Company',
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Text(
                    'Already have an account?',
                    style: TextStyle(color: Colors.black87),
                  ),
                  SizedBox(width: 10),
                  InkWell(
                    onTap: controller.navigateToLogin,
                    child: const Text(
                      'Login here',
                      style: TextStyle(color: AppColors.primary),
                    ),
                  )
                ],
              ),
              const SizedBox(height: 20),

              // Full name field
              const Text(
                '  Company Name',
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  fontSize: 17,
                ),
              ),
              const SizedBox(height: 10),
              Obx(() => InputField(
                    onTap: () => controller.onFocusCompanyName(),
                    focus: controller.companyNameFocus.value,
                    onTapOutSide: () => controller.onTapOutside(context),
                    hint: "Enter your full name",
                    controller: controller.companyNameController,
                  )),
              const SizedBox(height: 15),

              // Email field
              const Text(
                '  Email',
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  fontSize: 17,
                ),
              ),
              const SizedBox(height: 10),
              Obx(() => InputField(
                    onTap: () => controller.onFocusEmail(),
                    focus: controller.emailFocus.value,
                    onTapOutSide: () => controller.onTapOutside(context),
                    hint: "Enter your email",
                    controller: controller.emailController,
                  )),
              const SizedBox(height: 15),

              // Phone number field
              const Text(
                '  Phone Number',
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  fontSize: 17,
                ),
              ),
              const SizedBox(height: 10),
              Obx(() => InputField(
                    onTap: () => controller.onFocusPhone(),
                    focus: controller.phoneFocus.value,
                    onTapOutSide: () => controller.onTapOutside(context),
                    hint: "Enter phone number",
                    controller: controller.phoneController,
                  )),
              const SizedBox(height: 15),

              // Password field
              const Text(
                '  Password',
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  fontSize: 17,
                ),
              ),
              const SizedBox(height: 10),
              Obx(
                () => InputField(
                  onTap: () => controller.onFocusPassword(),
                  focus: controller.passwordFocus.value,
                  onTapOutSide: () => controller.onTapOutside(context),
                  hint: "Enter password",
                  controller: controller.passwordController,
                  hideText: !controller.isShowPassword.value,
                  onChanged: (p0) => controller.password.value = p0,
                  suffix: IconButton(
                    onPressed: controller.toggleShowPassword,
                    icon: controller.password.isNotEmpty
                        ? Icon(
                            controller.isShowPassword.value == false
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: AppColors.primary,
                            size: 18,
                          )
                        : SizedBox.shrink(),
                  ),
                ),
              ),
              const SizedBox(height: 15),

              // Confirm password field
              const Text(
                '  Confirm Password',
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  fontSize: 17,
                ),
              ),
              const SizedBox(height: 10),
              Obx(
                () => InputField(
                  onTap: () => controller.onFocusConfirmPassword(),
                  focus: controller.confirmPasswordFocus.value,
                  onTapOutSide: () => controller.onTapOutside(context),
                  hint: "Confirm your password",
                  controller: controller.confirmPasswordController,
                  hideText: !controller.isShowConfirmPassword.value,
                  onChanged: (p0) => controller.confirmPassword.value = p0,
                  suffix: IconButton(
                    onPressed: controller.toggleShowConfirmPassword,
                    icon: controller.confirmPassword.isNotEmpty
                        ? Icon(
                            controller.isShowConfirmPassword.value == false
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: AppColors.primary,
                            size: 18,
                          )
                        : SizedBox.shrink(),
                  ),
                ),
              ),
              const SizedBox(height: 15),

              // Error message
              Obx(() => controller.errorMessages['general'] == null
                  ? const SizedBox()
                  : controller.errorMessages['general']!.isEmpty
                      ? const SizedBox()
                      : AppErrorContainer(
                          errorMessage:
                              controller.errorMessages['general'] ?? '')),
              const SizedBox(height: 15),

              // Submit button
              Obx(() => AccountButton(
                    text: "Register Company",
                    loading: controller.isLoading.value,
                    onTap: controller.isLoading.value
                        ? () {}
                        : () => controller.register(),
                  )),

              // Clear Cache button (only show when there's an email already in use error)
              Obx(() {
                final hasEmailError = controller.errorMessages['email']
                            ?.contains('already registered') ==
                        true ||
                    controller.errorMessages['email']
                            ?.contains('already in use') ==
                        true;

                if (hasEmailError && !controller.isLoading.value) {
                  return Column(
                    children: [
                      const SizedBox(height: 10),
                      TextButton.icon(
                        onPressed: () => controller.clearCacheAndRetry(),
                        icon: const Icon(Icons.refresh, size: 18),
                        label: const Text(
                          'Clear Cache & Retry',
                          style: TextStyle(fontSize: 14),
                        ),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.orange,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                        ),
                      ),
                      const SizedBox(height: 10),
                    ],
                  );
                }
                return const SizedBox(height: 20);
              }),
            ],
          ),
        ),
      ),
    );
  }
}
