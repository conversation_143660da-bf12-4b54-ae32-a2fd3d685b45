import 'package:flutter/material.dart';

import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/main.dart';
import 'package:logestics/models/finance/broker_model.dart';
import 'package:provider/provider.dart';

class BrokerFinancialSummaryWidget extends StatelessWidget {
  final BrokerModel broker;
  final Map<String, dynamic> financialSummary;
  final VoidCallback? onViewDetails;
  final VoidCallback? onRecordPayment;

  const BrokerFinancialSummaryWidget({
    super.key,
    required this.broker,
    required this.financialSummary,
    this.onViewDetails,
    this.onRecordPayment,
  });

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    final balance = financialSummary['balance']?.toDouble() ?? 0.0;
    final totalFees = financialSummary['totalFees']?.toDouble() ?? 0.0;
    final totalPayments = financialSummary['totalPayments']?.toDouble() ?? 0.0;
    final transactionCount = financialSummary['totalTransactionCount'] ?? 0;

    return Card(
      color: notifier.getcardColor,
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Broker header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        broker.name,
                        style: AppTextStyles.titleStyle.copyWith(
                          color: notifier.text,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (broker.phoneNumber?.isNotEmpty == true)
                        Text(
                          broker.phoneNumber!,
                          style: AppTextStyles.subtitleStyle.copyWith(
                            color: notifier.text.withOpacity(0.7),
                          ),
                        ),
                    ],
                  ),
                ),

                // Balance indicator
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getBalanceColor(balance).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: _getBalanceColor(balance)),
                  ),
                  child: Text(
                    _getBalanceText(balance),
                    style: AppTextStyles.subtitleStyle.copyWith(
                      color: _getBalanceColor(balance),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Financial metrics
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Total Fees',
                    _formatCurrency(totalFees),
                    Icons.add_circle_outline,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    'Total Payments',
                    _formatCurrency(totalPayments),
                    Icons.remove_circle_outline,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    'Transactions',
                    transactionCount.toString(),
                    Icons.receipt_long,
                    Colors.blue,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (onViewDetails != null)
                  TextButton.icon(
                    onPressed: onViewDetails,
                    icon: Icon(Icons.visibility, size: 16),
                    label: Text('View Details'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.blue,
                    ),
                  ),
                const SizedBox(width: 8),
                if (onRecordPayment != null && balance > 0)
                  ElevatedButton.icon(
                    onPressed: onRecordPayment,
                    icon: Icon(Icons.payment, size: 16),
                    label: Text('Pay'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: notifier.getHoverColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: notifier.getfillborder),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  title,
                  style: AppTextStyles.subtitleStyle.copyWith(
                    color: notifier.text.withOpacity(0.7),
                    fontSize: 12,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTextStyles.titleStyle.copyWith(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getBalanceColor(double balance) {
    if (balance > 0) {
      return Colors.red; // Money owed to broker
    } else if (balance < 0) {
      return Colors.green; // Overpaid
    } else {
      return Colors.grey; // Balanced
    }
  }

  String _getBalanceText(double balance) {
    if (balance > 0) {
      return 'Owed: ${_formatCurrency(balance)}';
    } else if (balance < 0) {
      return 'Overpaid: ${_formatCurrency(-balance)}';
    } else {
      return 'Balanced';
    }
  }

  String _formatCurrency(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }
}
