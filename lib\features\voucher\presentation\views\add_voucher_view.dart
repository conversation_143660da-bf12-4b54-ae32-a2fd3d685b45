import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/widgets/section_widget.dart';
import 'package:logestics/models/finance/broker_model.dart';
import 'package:logestics/models/user_model.dart';
import 'package:logestics/core/utils/widgets/my_dropdown_field.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/core/utils/widgets/tax_authority_checkbox_widget.dart';
import 'package:logestics/features/voucher/presentation/controllers/add_voucher_controller.dart';

import 'package:logestics/features/voucher/presentation/widgets/voucher_payment_settings_widget.dart';
// DEBUG IMPORTS - COMMENTED OUT FOR PRODUCTION
// import 'package:logestics/features/voucher/presentation/widgets/voucher_settings_test_widget.dart';
// import 'package:logestics/debug/test_cross_company_loan_date_fix.dart';
import 'package:logestics/features/accounting/chart_of_accounts/presentation/controllers/chart_of_accounts_controller.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:textfield_tags/textfield_tags.dart';
import '../../../../core/enums/broker_enums.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/custom_button.dart';
import 'package:logestics/core/utils/widgets/payment_transaction_table.dart';
//todp is old code

class AddVoucherView extends StatelessWidget {
  const AddVoucherView({super.key});

  @override
  Widget build(BuildContext context) {
    AddVoucherController controller = Get.find<AddVoucherController>();
    // Ensure the tag controller is initialized properly
    controller.ensureTagControllerInitialized();

    // Ensure Chart of Accounts controller is available
    try {
      Get.find<ChartOfAccountsController>();
    } catch (e) {
      // If not found, this will trigger the lazy loading
      Get.lazyPut(() => ChartOfAccountsController(repository: Get.find()));
    }

    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;
    final isMediumScreen = size.width < 1200 && size.width >= 600;
    notifier = Provider.of(context, listen: false);
    var width = Get.width;
    return Stack(
      children: [
        Form(
          key: controller.addVoucherFormStateKey,
          child: Scaffold(
              backgroundColor: notifier.getBgColor,
              body: AlertDialog(
                backgroundColor: notifier.getBgColor,
                insetPadding: const EdgeInsets.symmetric(horizontal: 3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 2,
                title: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppStrings.addNewVoucherButton,
                      overflow: TextOverflow.ellipsis,
                      style: AppTextStyles.voucherTitleStyle.copyWith(
                        color: notifier.text,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Icon(
                        Icons.close,
                        color: notifier.text,
                      ),
                    ),
                  ],
                ),
                content: SizedBox(
                  width: width,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 10,
                      children: [
                        // Basic Information
                        SectionWidget(
                          title: "Voucher Details",
                          isSmallScreen: isSmallScreen,
                          isMediumScreen: isMediumScreen,
                          fields: [
                            MyTextFormField(
                              titleText: AppStrings.voucherNumberLabel,
                              labelText: AppStrings.voucherNumberLabel,
                              hintText: AppStrings.voucherNumberHint,
                              controller: controller.voucherNumberController,
                              focusNode: controller
                                  .voucherNumberFocusNode, // Focus node for tab navigation
                              textInputFormatter: [
                                controller.voucherNumberFormatter
                              ],
                              validator: controller.validateVoucherNumber,
                              onFieldSubmitted: controller
                                  .onVoucherNumberSubmitted, // Tab navigation
                            ),
                            MyTextFormField(
                              titleText: AppStrings.departureDateLabel,
                              hintText: AppStrings.departureDateHint,
                              labelText: AppStrings.departureDateLabel,
                              controller: controller.departureDateController,
                              validator: controller.validateDepartureDate,
                              suffixIcon: IconButton(
                                onPressed: () {
                                  // showDatePickers();
                                  controller.selectDepartureDate(context);
                                },
                                icon: Icon(
                                  Icons.calendar_today_rounded,
                                  color: notifier.text,
                                ),
                              ),
                            ),
                            Obx(
                              () => MyDropdownFormField(
                                titletext: AppStrings.voucherStatusLabel,
                                hinttext: AppStrings.voucherStatusHint,
                                items: VoucherStatus.allValues,
                                initalValue:
                                    controller.voucherStatusSelected.value,
                                onChanged: (value) {
                                  controller.setVoucherStatus(
                                      value ?? VoucherStatus.pending.value);
                                },
                                validator: controller.validateVoucherStatus,
                                validatorMode:
                                    AutovalidateMode.onUserInteraction,
                              ),
                            ),
                          ],
                        ),
                        // Convey Note & Invoice Details
                        SectionWidget(
                          title: "Convey Note & Invoice Details",
                          isSmallScreen: isSmallScreen,
                          isMediumScreen: isMediumScreen,
                          fields: [
                            // TextFieldTags for convey note numbers
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                GetBuilder<AddVoucherController>(
                                  id: 'tags-section',
                                  builder: (ctrl) {
                                    // Ensure tag controller is initialized
                                    ctrl.ensureTagControllerInitialized();

                                    return Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          "Convey Note Numbers",
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        TextFieldTags<String>(
                                          textfieldTagsController:
                                              ctrl.stringTagController,
                                          initialTags:
                                              ctrl.invoiceTasNumberList,
                                          letterCase: LetterCase.normal,
                                          // Add key to force rebuild when tags change
                                          key: ValueKey(
                                              "tags-${ctrl.invoiceTasNumberList.hashCode}"),
                                          inputFieldBuilder:
                                              (context, inputFieldValues) {
                                            // Store the focus node reference immediately when the widget is built
                                            WidgetsBinding.instance
                                                .addPostFrameCallback((_) {
                                              ctrl.setConveyNoteFocusNode(
                                                  inputFieldValues.focusNode);
                                            });

                                            return Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                TextField(
                                                  controller: inputFieldValues
                                                      .textEditingController,
                                                  focusNode: inputFieldValues
                                                      .focusNode,
                                                  decoration: InputDecoration(
                                                    border:
                                                        const OutlineInputBorder(),
                                                    hintText:
                                                        'Enter Convey Note Numbers (press Enter after each)',
                                                    helperText:
                                                        'Add multiple Convey Note Numbers',
                                                    errorText:
                                                        inputFieldValues.error,
                                                  ),
                                                  onChanged: inputFieldValues
                                                      .onTagChanged,
                                                  onSubmitted: (value) {
                                                    // Validate the convey note before adding it as a tag
                                                    if (value.isNotEmpty &&
                                                        ctrl.validateConveyNoteTag(
                                                            value)) {
                                                      // Add the tag to the UI
                                                      inputFieldValues
                                                          .onTagSubmitted(
                                                              value);

                                                      // Also add to the invoiceTasNumberList to keep in sync
                                                      ctrl.invoiceTasNumberList
                                                          .add(value);

                                                      // Update product, bags and tons after adding
                                                      ctrl.updateProductDetails();

                                                      // Navigate to broker fees field after adding convey note
                                                      ctrl.onConveyNumberSubmitted(
                                                          value);
                                                    }
                                                  },
                                                ),
                                                const SizedBox(height: 12),
                                                // Display chips for the entered convey notes
                                                Wrap(
                                                    spacing: 8,
                                                    runSpacing: 4,
                                                    children: inputFieldValues
                                                        .tags
                                                        .map((String tag) {
                                                      // Find the invoice related to this convey note
                                                      final relatedInvoice =
                                                          ctrl.companyController
                                                              .company.invoices
                                                              .firstWhereOrNull(
                                                        (invoice) =>
                                                            invoice
                                                                .conveyNoteNumber ==
                                                            tag,
                                                      );

                                                      // Create tooltip text with invoice details if found
                                                      String tooltipText = relatedInvoice !=
                                                              null
                                                          ? "Convey Note: $tag\n"
                                                              "Product: ${relatedInvoice.productName}\n"
                                                              "Bags: ${relatedInvoice.numberOfBags}\n"
                                                              "TAS Number: ${relatedInvoice.tasNumber}\n"
                                                              "Bilty Number: ${relatedInvoice.biltyNumber.isNotEmpty ? relatedInvoice.biltyNumber : 'Not Set'}"
                                                          : "Convey Note: $tag";

                                                      return Tooltip(
                                                        message: tooltipText,
                                                        padding:
                                                            const EdgeInsets
                                                                .all(12),
                                                        showDuration:
                                                            const Duration(
                                                                seconds: 2),
                                                        textStyle:
                                                            const TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 12,
                                                        ),
                                                        child: Chip(
                                                          backgroundColor:
                                                              Colors.blue
                                                                  .shade100,
                                                          avatar: Icon(
                                                            Icons.receipt,
                                                            size: 16,
                                                            color: Colors
                                                                .blue.shade700,
                                                          ),
                                                          label: Text(tag),
                                                          deleteIcon:
                                                              const Icon(
                                                            Icons.cancel,
                                                            size: 18,
                                                          ),
                                                          onDeleted: () {
                                                            // First, add the tag to a temporary list to avoid reference issues
                                                            final String
                                                                tagToRemove =
                                                                tag;

                                                            // Remove from the list first to prevent re-addition
                                                            if (ctrl
                                                                .invoiceTasNumberList
                                                                .contains(
                                                                    tagToRemove)) {
                                                              ctrl.invoiceTasNumberList
                                                                  .remove(
                                                                      tagToRemove);
                                                            }

                                                            // Explicitly clear the tags then reconstruct them
                                                            ctrl.stringTagController
                                                                .clearTags();

                                                            // Delay to ensure UI updates
                                                            Future.delayed(
                                                                const Duration(
                                                                    milliseconds:
                                                                        100),
                                                                () {
                                                              // Re-add all the remaining tags to the controller
                                                              for (var t in ctrl
                                                                  .invoiceTasNumberList) {
                                                                if (t !=
                                                                    tagToRemove) {
                                                                  ctrl.stringTagController
                                                                      .onTagSubmitted(
                                                                          t);
                                                                }
                                                              }

                                                              // Update product details
                                                              ctrl.updateProductDetails();

                                                              // Force update
                                                              ctrl.update();
                                                            });
                                                          },
                                                        ),
                                                      );
                                                    }).toList()),

                                                // Display TAS numbers section if available
                                                GetBuilder<
                                                    AddVoucherController>(
                                                  builder: (ctrl) {
                                                    // Check if there are related TAS numbers to display
                                                    final tasList = ctrl
                                                        .companyController
                                                        .company
                                                        .invoices
                                                        .where((invoice) => ctrl
                                                            .invoiceTasNumberList
                                                            .contains(invoice
                                                                .conveyNoteNumber))
                                                        .map((invoice) =>
                                                            invoice.tasNumber)
                                                        .where((tas) =>
                                                            tas.isNotEmpty)
                                                        .toList();

                                                    if (tasList.isNotEmpty) {
                                                      return Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          const SizedBox(
                                                              height: 20),
                                                          const Text(
                                                            "Related TAS Numbers",
                                                            style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              fontSize: 16,
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                              height: 8),
                                                          Wrap(
                                                            spacing: 8,
                                                            runSpacing: 4,
                                                            children: tasList
                                                                .map((tas) {
                                                              // Find the associated convey note for this TAS
                                                              final relatedInvoice = ctrl
                                                                  .companyController
                                                                  .company
                                                                  .invoices
                                                                  .firstWhereOrNull(
                                                                (invoice) =>
                                                                    invoice
                                                                        .tasNumber ==
                                                                    tas,
                                                              );

                                                              String tooltipText = relatedInvoice !=
                                                                      null
                                                                  ? "TAS: ${relatedInvoice.tasNumber}\n"
                                                                      "Convey Note: ${relatedInvoice.conveyNoteNumber}\n"
                                                                      "Bilty Number: ${relatedInvoice.biltyNumber.isNotEmpty ? relatedInvoice.biltyNumber : 'Not Set'}\n"
                                                                      "Product: ${relatedInvoice.productName}\n"
                                                                      "Bags: ${relatedInvoice.numberOfBags}\n"
                                                                      "Weight/Bag: ${relatedInvoice.weightPerBag}kg\n"
                                                                      "Total Weight: ${(relatedInvoice.numberOfBags * relatedInvoice.weightPerBag).toStringAsFixed(2)}kg"
                                                                  : "TAS Number";

                                                              return Tooltip(
                                                                message:
                                                                    tooltipText,
                                                                padding:
                                                                    const EdgeInsets
                                                                        .all(
                                                                        12),
                                                                showDuration:
                                                                    const Duration(
                                                                        seconds:
                                                                            3),
                                                                textStyle:
                                                                    const TextStyle(
                                                                  color: Colors
                                                                      .white,
                                                                  fontSize: 12,
                                                                ),
                                                                child: Chip(
                                                                  backgroundColor:
                                                                      Colors
                                                                          .green
                                                                          .shade100,
                                                                  label:
                                                                      Text(tas),
                                                                  avatar: Icon(
                                                                    Icons
                                                                        .info_outline,
                                                                    size: 16,
                                                                    color: Colors
                                                                        .green
                                                                        .shade700,
                                                                  ),
                                                                  // No delete option for TAS chips
                                                                ),
                                                              );
                                                            }).toList(),
                                                          ),
                                                        ],
                                                      );
                                                    }

                                                    return const SizedBox
                                                        .shrink();
                                                  },
                                                ),

                                                // Bilty Numbers Section
                                                GetBuilder<
                                                    AddVoucherController>(
                                                  builder: (ctrl) {
                                                    final biltyList = ctrl
                                                        .invoiceBiltyNumberList;

                                                    if (biltyList.isNotEmpty) {
                                                      return Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          const SizedBox(
                                                              height: 16),
                                                          const Text(
                                                            "Linked Bilty Numbers",
                                                            style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              fontSize: 14,
                                                              color:
                                                                  Colors.purple,
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                              height: 8),
                                                          Wrap(
                                                            spacing: 8,
                                                            runSpacing: 4,
                                                            children: biltyList
                                                                .map((bilty) {
                                                              // Find the associated invoice for this bilty number
                                                              final relatedInvoice = ctrl
                                                                  .companyController
                                                                  .company
                                                                  .invoices
                                                                  .firstWhereOrNull(
                                                                (invoice) =>
                                                                    invoice
                                                                        .biltyNumber ==
                                                                    bilty,
                                                              );

                                                              String tooltipText = relatedInvoice !=
                                                                      null
                                                                  ? "Bilty: ${relatedInvoice.biltyNumber}\n"
                                                                      "Invoice #: ${relatedInvoice.invoiceNumber}\n"
                                                                      "Convey Note: ${relatedInvoice.conveyNoteNumber}\n"
                                                                      "TAS: ${relatedInvoice.tasNumber}\n"
                                                                      "Product: ${relatedInvoice.productName}\n"
                                                                      "Customer: ${relatedInvoice.customerName}"
                                                                  : "Bilty Number";

                                                              return Tooltip(
                                                                message:
                                                                    tooltipText,
                                                                padding:
                                                                    const EdgeInsets
                                                                        .all(
                                                                        12),
                                                                showDuration:
                                                                    const Duration(
                                                                        seconds:
                                                                            3),
                                                                textStyle:
                                                                    const TextStyle(
                                                                  color: Colors
                                                                      .white,
                                                                  fontSize: 12,
                                                                ),
                                                                child: Chip(
                                                                  backgroundColor:
                                                                      Colors
                                                                          .purple
                                                                          .shade100,
                                                                  label: Text(
                                                                      bilty),
                                                                  avatar: Icon(
                                                                    Icons
                                                                        .receipt_long,
                                                                    size: 16,
                                                                    color: Colors
                                                                        .purple
                                                                        .shade700,
                                                                  ),
                                                                  // No delete option for bilty chips
                                                                ),
                                                              );
                                                            }).toList(),
                                                          ),
                                                        ],
                                                      );
                                                    }

                                                    return const SizedBox
                                                        .shrink();
                                                  },
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ],
                            ),

                            // Product Details - Changed to read-only
                            GetBuilder<AddVoucherController>(
                              builder: (ctrl) {
                                // Check if the product name contains multiple products
                                final productNames = ctrl
                                    .productNameController.text
                                    .split(", ")
                                    .where((name) => name.isNotEmpty)
                                    .toList();

                                // Get linked invoices for additional data
                                final List<String> conveyNotes =
                                    ctrl.stringTagController.getTags;
                                final linkedInvoices = ctrl
                                    .companyController.company.invoices
                                    .where((invoice) => conveyNotes
                                        .contains(invoice.conveyNoteNumber))
                                    .toList();

                                // Calculate total weight for summary display
                                final totalWeightKg =
                                    linkedInvoices.fold<double>(
                                        0,
                                        (sum, invoice) =>
                                            sum +
                                            (invoice.numberOfBags *
                                                invoice.weightPerBag));

                                // Convert to tons for display
                                final totalWeightTons =
                                    (totalWeightKg / 1000).toStringAsFixed(2);

                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    // Product summary chips
                                    if (linkedInvoices.isNotEmpty) ...[
                                      Text(
                                        "Summary",
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Wrap(
                                        spacing: 8,
                                        runSpacing: 4,
                                        children: [
                                          // Product count chip
                                          Chip(
                                            backgroundColor:
                                                Colors.blue.shade50,
                                            avatar: Icon(Icons.inventory_2,
                                                color: Colors.blue.shade700,
                                                size: 16),
                                            label: Text(
                                                "${productNames.length} Product${productNames.length > 1 ? 's' : ''}"),
                                          ),

                                          // Total bags chip
                                          Chip(
                                            backgroundColor:
                                                Colors.amber.shade50,
                                            avatar: Icon(Icons.shopping_bag,
                                                color: Colors.amber.shade700,
                                                size: 16),
                                            label: Text(
                                                "${ctrl.totalNumberOfBagsController.text} Bags"),
                                          ),

                                          // Total weight chip
                                          Chip(
                                            backgroundColor:
                                                Colors.green.shade50,
                                            avatar: Icon(Icons.scale,
                                                color: Colors.green.shade700,
                                                size: 16),
                                            label:
                                                Text("$totalWeightTons Tons"),
                                          ),

                                          // Invoice count chip
                                          Chip(
                                            backgroundColor:
                                                Colors.purple.shade50,
                                            avatar: Icon(Icons.receipt_long,
                                                color: Colors.purple.shade700,
                                                size: 16),
                                            label: Text(
                                                "${linkedInvoices.length} Invoice${linkedInvoices.length > 1 ? 's' : ''}"),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                    ],
                                    Text(
                                      AppStrings.productNameLabel,
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    // If there are multiple products, show product chips
                                    if (productNames.length > 1)
                                      Wrap(
                                        spacing: 8,
                                        runSpacing: 4,
                                        children: productNames.map((product) {
                                          return Chip(
                                            backgroundColor:
                                                Colors.green.shade100,
                                            avatar: Icon(Icons.local_shipping,
                                                color: Colors.green.shade700,
                                                size: 16),
                                            label: Text(product),
                                          );
                                        }).toList(),
                                      )
                                    // Otherwise, show a standard read-only text field
                                    else
                                      MyTextFormField(
                                        titleText: "",
                                        labelText: AppStrings.productNameLabel,
                                        hintText:
                                            "Auto-filled from linked invoices",
                                        controller: ctrl.productNameController,
                                        readOnly:
                                            true, // Make read-only as it will be auto-filled
                                      ),
                                  ],
                                );
                              },
                            ),

                            MyTextFormField(
                              titleText: AppStrings.totalNumberOfBagsLabel,
                              labelText: AppStrings.totalBagsLabel,
                              hintText: "Auto-calculated from linked invoices",
                              controller:
                                  controller.totalNumberOfBagsController,
                              readOnly: true, // Make read-only
                            ),

                            MyTextFormField(
                              titleText: AppStrings.weightInTonsLabel,
                              labelText: AppStrings.weightInTonsLabel,
                              hintText: "Auto-calculated from linked invoices",
                              controller: controller.weightInTonsController,
                              readOnly: true, // Make read-only
                            ),
                          ],
                        ),

                        /// Driver & Transport Details
                        SectionWidget(
                          title: "Driver & Transport Details",
                          isSmallScreen: isSmallScreen,
                          isMediumScreen: isMediumScreen,
                          fields: [
                            MyTextFormField(
                              titleText: AppStrings.driverNameLabel,
                              labelText: AppStrings.driverNameLabel,
                              hintText: AppStrings.driverNameHint,
                              controller: controller.driverNameController,
                              validator: controller.validateDriverName,
                            ),
                            MyTextFormField(
                              titleText: AppStrings.driverPhoneNumberLabel,
                              labelText: AppStrings.driverPhoneLabel,
                              hintText: AppStrings.driverPhoneHint,
                              controller:
                                  controller.driverPhoneNumberController,
                              validator: controller.validateDriverPhoneNumber,
                              textInputFormatter: [
                                controller.phoneNumberFormatter
                              ],
                            ),
                            MyTextFormField(
                              titleText: AppStrings.truckWagonNumberLabel,
                              labelText: AppStrings.truckWagonNumberLabel,
                              hintText: AppStrings.voucherTruckNumberHint,
                              controller: controller.truckNumberController,
                              validator: controller.validateTruckNumber,
                              textInputFormatter: [
                                controller.truckNumberFormatter
                              ],
                            ),
                          ],
                        ),

                        // Broker Information
                        SectionWidget(
                          title: "Broker Information",
                          isSmallScreen: isSmallScreen,
                          isMediumScreen: isMediumScreen,
                          fields: [
                            Obx(
                              () => MyDropdownFormField(
                                titletext: "Broker Type",
                                items: BrokerType.allValues,
                                hinttext: "Select Broker Type",
                                initalValue: controller.brokerType.value,
                                onChanged: (value) => controller.setBrokerType(
                                    value ?? BrokerType.outsource.value),
                              ),
                            ),
                            Obx(
                              () => controller.brokerType.value ==
                                      BrokerType.own.value
                                  ? Column(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        // Broker Selection Dropdown for Own
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Obx(
                                                () => DropdownButtonFormField<
                                                    BrokerModel>(
                                                  decoration: InputDecoration(
                                                    labelText: 'Select Broker',
                                                    border: OutlineInputBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    filled: true,
                                                    fillColor:
                                                        Colors.transparent,
                                                  ),
                                                  hint: Text('Select Broker'),
                                                  value: controller
                                                      .selectedBrokerModel
                                                      .value,
                                                  onChanged:
                                                      (BrokerModel? broker) {
                                                    controller
                                                        .setSelectedBroker(
                                                            broker);
                                                  },
                                                  items: controller
                                                      .availableBrokers
                                                      .map(
                                                          (BrokerModel broker) {
                                                    return DropdownMenuItem<
                                                        BrokerModel>(
                                                      value: broker,
                                                      child: Text(broker.name),
                                                    );
                                                  }).toList(),
                                                ),
                                              ),
                                            ),
                                            IconButton(
                                              icon: Icon(Icons.add_circle,
                                                  color: Colors.blue),
                                              onPressed: () =>
                                                  controller.openBrokerDialog(),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                        // Company Selection for Own Broker
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Obx(
                                                () => DropdownButtonFormField<
                                                    UserModel>(
                                                  decoration: InputDecoration(
                                                    labelText: 'Select Company',
                                                    hintText:
                                                        'Choose company for this broker',
                                                    border: OutlineInputBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    filled: true,
                                                    fillColor:
                                                        Colors.transparent,
                                                  ),
                                                  hint: Text(
                                                      'Select Company (Optional)'),
                                                  value: controller
                                                      .selectedBrokerCompany
                                                      .value,
                                                  onChanged:
                                                      (UserModel? company) {
                                                    controller
                                                        .setSelectedBrokerCompany(
                                                            company);
                                                  },
                                                  items: controller
                                                      .availableCompanies
                                                      .map((UserModel company) {
                                                    return DropdownMenuItem<
                                                        UserModel>(
                                                      value: company,
                                                      child: Text(
                                                          company.companyName),
                                                    );
                                                  }).toList(),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    )
                                  : Column(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        // Broker Selection Dropdown
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Obx(
                                                () => DropdownButtonFormField<
                                                    BrokerModel>(
                                                  decoration: InputDecoration(
                                                    labelText: 'Select Broker',
                                                    border: OutlineInputBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    filled: true,
                                                    fillColor:
                                                        Colors.transparent,
                                                  ),
                                                  hint: Text('Select Broker'),
                                                  value: controller
                                                      .selectedBrokerModel
                                                      .value,
                                                  onChanged:
                                                      (BrokerModel? broker) {
                                                    controller
                                                        .setSelectedBroker(
                                                            broker);
                                                  },
                                                  items: controller
                                                      .availableBrokers
                                                      .map(
                                                          (BrokerModel broker) {
                                                    return DropdownMenuItem<
                                                        BrokerModel>(
                                                      value: broker,
                                                      child: Text(broker.name),
                                                    );
                                                  }).toList(),
                                                ),
                                              ),
                                            ),
                                            IconButton(
                                              icon: Icon(Icons.add_circle,
                                                  color: Colors.blue),
                                              onPressed: () =>
                                                  controller.openBrokerDialog(),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                            ),
                            MyTextFormField(
                              titleText: AppStrings.brokerFeesTitleLabel,
                              labelText: AppStrings.brokerFeesLabel,
                              hintText: AppStrings.brokerFeesHint,
                              controller: controller.brokerFeesController,
                              focusNode: controller
                                  .brokerFeesFocusNode, // Focus node for tab navigation
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                // Trigger calculation updates when broker fees change
                                controller.updateTruckFreight();
                              },
                              onFieldSubmitted: controller
                                  .onBrokerFeesSubmitted, // Tab navigation
                            ),
                          ],
                        ),

                        // Munshiana Information (Separate Section)
                        SectionWidget(
                          title: "Munshiana Information",
                          isSmallScreen: isSmallScreen,
                          isMediumScreen: isMediumScreen,
                          fields: [
                            MyTextFormField(
                              titleText: AppStrings.munshianaTitleLabel,
                              labelText: AppStrings.munshianaLabel,
                              hintText: AppStrings.munshianaHint,
                              controller: controller.munshianaFeesController,
                              focusNode: controller
                                  .munshianaFeesFocusNode, // Focus node for tab navigation
                              keyboardType: TextInputType.number,
                              validator: controller
                                  .validateMunshianaFees, // Added required validation
                              onChanged: (value) {
                                // Trigger calculation updates when munshiana fees change
                                controller.updateTruckFreight();
                              },
                              onFieldSubmitted: controller
                                  .onMunshianaFeesSubmitted, // Tab navigation
                            ),
                          ],
                        ),
                        SectionWidget(
                          title: "Total Payment",
                          isSmallScreen: isSmallScreen,
                          isMediumScreen: isMediumScreen,
                          fields: [
                            MyTextFormField(
                              titleText: AppStrings.totalFreight,
                              labelText: AppStrings.totalFreightLabel,
                              hintText: AppStrings.totalFreightHint,
                              controller: controller.totalFreightController,
                              focusNode: controller
                                  .totalFreightFocusNode, // Focus node for tab navigation
                              keyboardType: TextInputType.number,
                              validator: controller.validateTotalFreight,
                              textInputFormatter: [
                                controller.totalFreightFormatter
                              ],
                              onTap: controller
                                  .onTotalFreightFieldTapped, // Auto-clear "0" when tapped
                              onChanged: (value) {
                                // Trigger validation and calculation updates when total freight changes
                                controller.addVoucherFormStateKey.currentState
                                    ?.validate();
                                controller.updateTruckFreight();
                              },
                              onFieldSubmitted: controller
                                  .onTotalFreightSubmitted, // Tab navigation
                            ),
                          ],
                        ),
                        SectionWidget(
                          title: "Truck/Wagon Payment Details",
                          isSmallScreen: isSmallScreen,
                          isMediumScreen: isMediumScreen,
                          fields: [
                            // Use GetBuilder for better performance on calculated fields
                            GetBuilder<AddVoucherController>(
                              id: 'truck_freight_calculation',
                              builder: (controller) {
                                return MyTextFormField(
                                  titleText: AppStrings.totalFreightTitleLabel,
                                  labelText: AppStrings.totalFreightLabel,
                                  hintText: AppStrings.totalTruckFreightHint,
                                  readOnly: true,
                                  controller: controller.totalTruckFreight,
                                  validator: controller.validateTotalFreight,
                                  textInputFormatter: [
                                    controller.totalFreightFormatter
                                  ],
                                  onChanged: (value) {
                                    // Trigger validation when total freight changes
                                    controller
                                        .addVoucherFormStateKey.currentState
                                        ?.validate();
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                        SectionWidget(
                          title: "Financial Summary",
                          isSmallScreen: isSmallScreen,
                          isMediumScreen: isMediumScreen,
                          fields: [
                            MyTextFormField(
                              titleText: AppStrings.companyFreightTitleLabel,
                              labelText: AppStrings.companyFreightLabel,
                              hintText: AppStrings.companyFreightHint,
                              controller: controller.companyFreightController,
                              focusNode: controller
                                  .companyFreightFocusNode, // Focus node for tab navigation
                              keyboardType: TextInputType.number,
                              validator: controller
                                  .validateCompanyFreight, // Added required validation
                              onChanged: (value) {
                                // Trigger profit/loss calculation when company freight changes
                                controller.calculateProfitLoss();
                                // Trigger auto-calculations for reactive updates
                                controller.performAutoCalculations();
                              },
                              // No onFieldSubmitted needed as this is the last field in the tab sequence
                            ),

                            // Tax Authority Selection Widget (15% Tax Options) - Ultra-fast
                            GetBuilder<AddVoucherController>(
                              id: 'tax_authorities',
                              builder: (controller) =>
                                  TaxAuthorityCheckboxWidget(
                                availableAuthorities: AddVoucherController
                                    .availableTaxAuthorities,
                                selectedAuthorities:
                                    controller.selectedTaxAuthorities,
                                onToggle: controller.toggleTaxAuthority,
                                errorMessage:
                                    controller.taxAuthorityError.value,
                                onErrorClear: controller.clearTaxAuthorityError,
                              ),
                            ),

                            // Auto-calculation breakdown panel - optimized with GetBuilder
                            GetBuilder<AddVoucherController>(
                                id: 'financial_breakdown',
                                builder: (controller) => Container(
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Colors.blue.shade300,
                                          width: 2,
                                        ),
                                        borderRadius: BorderRadius.circular(12),
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Colors.blue.shade50,
                                            Colors.white,
                                          ],
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(Icons.calculate,
                                                  color: Colors.blue.shade700),
                                              const SizedBox(width: 8),
                                              Text(
                                                'Financial Breakdown',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 18,
                                                  color: Colors.blue.shade700,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 16),
                                          // Financial calculations table with borders
                                          _buildFinancialCalculationsTable(
                                              controller),
                                        ],
                                      ),
                                    )),
                          ],
                        ),

                        // Payment Tracking Section
                        SectionWidget(
                          title: "Payment Tracking",
                          isSmallScreen: isSmallScreen,
                          isMediumScreen: isMediumScreen,
                          fields: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "Payment Transactions",
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: notifier.text,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(width: 8),
                                ElevatedButton.icon(
                                  icon: const Icon(Icons.add),
                                  label: const Text("Add Payment"),
                                  onPressed: () => controller
                                      .showPaymentTransactionDialog(context),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                        SizedBox(
                          width: double.infinity,
                          child: Obx(
                            () => controller.allPaymentTransactions.isEmpty
                                ? Container(
                                    padding: const EdgeInsets.all(20),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: Colors.grey.shade300),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Text(
                                      'No payment transactions yet. Click "Add Payment" to add a transaction.',
                                      style: TextStyle(
                                        color: Colors.grey,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  )
                                : PaymentTransactionTable(
                                    transactions:
                                        controller.allPaymentTransactions,
                                    totalFreight: double.tryParse(controller
                                            .totalTruckFreight.text) ??
                                        0.0,
                                    settledFreight:
                                        controller.settledFreight.value,
                                    canEdit: controller.canEditTransaction,
                                    canDelete: controller.canDeleteTransaction,
                                    onEdit: (transaction) => controller
                                        .showPaymentTransactionDialog(context,
                                            transaction: transaction),
                                    onDelete: (transaction) {
                                      showDialog(
                                        context: context,
                                        builder: (context) => AlertDialog(
                                          title: const Text("Confirm Delete"),
                                          content: const Text(
                                              "Are you sure you want to delete this payment transaction?"),
                                          actions: [
                                            TextButton(
                                              onPressed: () =>
                                                  Navigator.of(context).pop(),
                                              child: const Text("CANCEL"),
                                            ),
                                            TextButton(
                                              onPressed: () {
                                                controller
                                                    .deleteLocalPaymentTransaction(
                                                        transaction);
                                                Navigator.of(context).pop();
                                              },
                                              child: const Text("DELETE"),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                          ),
                        ),

                        // Voucher Payment Settings Widget
                        const VoucherPaymentSettingsWidget(),

                        // DEBUG WIDGET - COMMENTED OUT FOR PRODUCTION
                        // Test Widget (for development/testing only)
                        // const VoucherSettingsTestWidget(),

                        // DEBUG WIDGET - COMMENTED OUT FOR PRODUCTION
                        // Cross-Company Loan Date Fix Test Widget
                        // Container(
                        //   margin: const EdgeInsets.symmetric(vertical: 8),
                        //   padding: const EdgeInsets.all(12),
                        //   decoration: BoxDecoration(
                        //     border: Border.all(color: Colors.orange.shade300),
                        //     borderRadius: BorderRadius.circular(8),
                        //     color: Colors.orange.shade50,
                        //   ),
                        //   child: Column(
                        //     crossAxisAlignment: CrossAxisAlignment.start,
                        //     children: [
                        //       Text(
                        //         'Cross-Company Loan Date Fix Test',
                        //         style: TextStyle(
                        //           fontWeight: FontWeight.bold,
                        //           color: Colors.orange.shade800,
                        //         ),
                        //       ),
                        //       const SizedBox(height: 8),
                        //       Text(
                        //         'Test the fix for cross-company loan approval journal entry dates',
                        //         style: TextStyle(
                        //           fontSize: 12,
                        //           color: Colors.orange.shade700,
                        //         ),
                        //       ),
                        //       const SizedBox(height: 8),
                        //       Row(
                        //         children: [
                        //           ElevatedButton(
                        //             onPressed: () async {
                        //               await TestCrossCompanyLoanDateFix
                        //                   .runTest();
                        //             },
                        //             style: ElevatedButton.styleFrom(
                        //               backgroundColor: Colors.orange.shade600,
                        //               foregroundColor: Colors.white,
                        //               padding: const EdgeInsets.symmetric(
                        //                   horizontal: 12, vertical: 6),
                        //             ),
                        //             child: const Text('Test Date Fix',
                        //                 style: TextStyle(fontSize: 12)),
                        //           ),
                        //           const SizedBox(width: 8),
                        //           ElevatedButton(
                        //             onPressed: () async {
                        //               await TestCrossCompanyLoanDateFix
                        //                   .testVoucherPaymentDateRetrieval();
                        //             },
                        //             style: ElevatedButton.styleFrom(
                        //               backgroundColor: Colors.blue.shade600,
                        //               foregroundColor: Colors.white,
                        //               padding: const EdgeInsets.symmetric(
                        //                   horizontal: 12, vertical: 6),
                        //             ),
                        //             child: const Text('Test Payment Retrieval',
                        //                 style: TextStyle(fontSize: 12)),
                        //           ),
                        //         ],
                        //       ),
                        //     ],
                        //   ),
                        // ),
                      ],
                    ),
                  ),
                ),
                actions: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomButton.danger(
                        text: AppStrings.cancel,
                        onPressed: () => Get.back(),
                        minimumSize: const Size(100, 50),
                      ),
                      Row(
                        children: [
                          // DEBUG BUTTON - COMMENTED OUT FOR PRODUCTION
                          // CustomButton(
                          //   text: "Fill Dummy",
                          //   onPressed: () => controller.fillDummyDetails(),
                          //   backgroundColor: Colors.amber,
                          //   minimumSize: const Size(100, 50),
                          // ),
                          // const SizedBox(width: 16),
                          Obx(() => CustomButton.primary(
                                text: AppStrings.save,
                                onPressed: () => controller.saveVoucher(),
                                minimumSize: const Size(100, 50),
                                isLoading: controller.isSaving.value,
                                isDisabled: controller.isSaving.value,
                              )),
                        ],
                      ),
                    ],
                  ),
                ],
              )),
        ),
        PayeeDialogWidget(controller: controller),
        BrokerDialogWidget(controller: controller),
        // REMOVED: Account creation dialog - users must create accounts in Chart of Accounts section
      ],
    );
  }

  /// Build financial calculations table with bordered layout matching reference image
  Widget _buildFinancialCalculationsTable(AddVoucherController controller) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400, width: 1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Company Freight row
          _buildTableRow(
            'Company Freight',
            'PKR ${(double.tryParse(controller.companyFreightController.text) ?? 0.0).toStringAsFixed(0)}',
            Colors.green.shade600,
            isFirst: true,
          ),
          // Sales Tax row
          _buildTableRow(
            'Sales Tax (6.9%)',
            'PKR ${controller.calculatedSalesTax.value.toStringAsFixed(0)}',
            Colors.red.shade600,
          ),
          // Truck Freight row
          _buildTableRow(
            'Truck Freight',
            'PKR ${controller.calculatedTotalFreight.value.toStringAsFixed(0)}',
            Colors.orange.shade600,
          ),
          // Company Freight Tax row (Info only)
          _buildTableRow(
            'Company Freight Tax (6.9%) - Info Only',
            'PKR ${controller.calculatedFreightTax.value.toStringAsFixed(0)}',
            Colors.grey.shade600,
          ),
          // Net Profit/Loss row (Total)
          _buildTableRow(
            'Net Profit/Loss',
            'PKR ${controller.calculatedProfit.value.toStringAsFixed(0)}',
            controller.calculatedProfit.value >= 0
                ? Colors.green.shade700
                : Colors.red.shade700,
            isTotal: true,
            isLast: true,
          ),
        ],
      ),
    );
  }

  /// Build individual table row with borders
  Widget _buildTableRow(
    String label,
    String value,
    Color valueColor, {
    bool isFirst = false,
    bool isLast = false,
    bool isTotal = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: isLast
              ? BorderSide.none
              : BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        color: isTotal ? Colors.grey.shade50 : Colors.white,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
                fontSize: isTotal ? 16 : 14,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Container(
            width: 1,
            height: 20,
            color: Colors.grey.shade300,
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: Text(
              value,
              textAlign: TextAlign.right,
              style: TextStyle(
                color: valueColor,
                fontWeight: FontWeight.bold,
                fontSize: isTotal ? 18 : 15,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBreakdownRow(
      String label, String value, Color color, IconData icon,
      {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
                fontSize: isTotal ? 16 : 14,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 18 : 15,
            ),
          ),
        ],
      ),
    );
  }
}

// Add payee dialog widget
class PayeeDialogWidget extends StatelessWidget {
  final AddVoucherController controller;

  const PayeeDialogWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!controller.showPayeeDialog.value) {
        return const SizedBox.shrink();
      }

      return Dialog(
        child: Container(
          padding: const EdgeInsets.all(16),
          width: 400,
          child: Form(
            key: controller.payeeFormKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Add New Payee',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close),
                      onPressed: controller.closePayeeDialog,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: controller.payeeNameController,
                  decoration: InputDecoration(
                    labelText: 'Name',
                    border: OutlineInputBorder(),
                  ),
                  validator: controller.validatePayeeName,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: controller.payeePhoneController,
                  decoration: InputDecoration(
                    labelText: 'Phone Number',
                    border: OutlineInputBorder(),
                    hintText: '03123456789',
                  ),
                  keyboardType: TextInputType.phone,
                  validator: controller.validatePayeePhone,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: Obx(() => ElevatedButton(
                        onPressed: controller.isAddingPayee.value
                            ? null
                            : controller.addPayee,
                        child: controller.isAddingPayee.value
                            ? CircularProgressIndicator()
                            : Text('Add Payee'),
                      )),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}

// Add broker dialog widget
class BrokerDialogWidget extends StatefulWidget {
  final AddVoucherController controller;

  const BrokerDialogWidget({super.key, required this.controller});

  @override
  State<BrokerDialogWidget> createState() => _BrokerDialogWidgetState();
}

class _BrokerDialogWidgetState extends State<BrokerDialogWidget> {
  String _brokerName = '';
  String _description = '';
  String _phoneNumber = '';
  String _email = '';
  String _address = '';

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!widget.controller.showBrokerDialog.value) {
        return const SizedBox.shrink();
      }

      return Dialog(
        child: Container(
          padding: const EdgeInsets.all(16),
          width: 400,
          child: Form(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Add New Broker',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close),
                      onPressed: widget.controller.closeBrokerDialog,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Broker Name',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) => _brokerName = value,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Description (Optional)',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) => _description = value,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Phone Number (Optional)',
                    border: OutlineInputBorder(),
                    hintText: '03123456789',
                  ),
                  keyboardType: TextInputType.phone,
                  onChanged: (value) => _phoneNumber = value,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Email (Optional)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.emailAddress,
                  onChanged: (value) => _email = value,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Address (Optional)',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) => _address = value,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: Obx(() => ElevatedButton(
                        onPressed: widget.controller.isAddingBroker.value
                            ? null
                            : () => _createBroker(),
                        child: widget.controller.isAddingBroker.value
                            ? CircularProgressIndicator()
                            : Text('Add Broker'),
                      )),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  void _createBroker() {
    if (_brokerName.trim().isEmpty) {
      Get.snackbar('Error', 'Broker name is required');
      return;
    }

    widget.controller.createBroker(
      name: _brokerName.trim(),
      description: _description.trim().isEmpty ? null : _description.trim(),
      phoneNumber: _phoneNumber.trim().isEmpty ? null : _phoneNumber.trim(),
      email: _email.trim().isEmpty ? null : _email.trim(),
      address: _address.trim().isEmpty ? null : _address.trim(),
    );
  }
}

// REMOVED: Account creation dialog removed from voucher system
// Users must create accounts through the Chart of Accounts section
// This ensures proper separation of concerns and prevents automatic account creation
