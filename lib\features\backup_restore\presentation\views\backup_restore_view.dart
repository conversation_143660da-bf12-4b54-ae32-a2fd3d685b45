import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/widgets/loading_indicator.dart';
import 'package:logestics/features/backup_restore/presentation/controllers/backup_restore_controller.dart';
import 'package:logestics/features/home/<USER>/drawer_controllers.dart';

import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/assets/app_assets.dart';
import 'package:logestics/features/home/<USER>/dashbord/e_commerece/e_commerce.dart';

class BackupRestoreView extends StatefulWidget {
  const BackupRestoreView({super.key});

  @override
  State<BackupRestoreView> createState() => _BackupRestoreViewState();
}

class _BackupRestoreViewState extends State<BackupRestoreView> {
  bool _isInitialized = false;
  bool _isLoading = true;
  String? _errorMessage;
  BackupRestoreController? _controller;
  Timer? _timeoutTimer;

  @override
  void initState() {
    super.initState();
    // Use post-frame callback to ensure UI is ready before initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeComponents();
    });

    // Set up timeout timer to prevent indefinite loading
    _timeoutTimer = Timer(const Duration(seconds: 10), () {
      if (_isLoading && mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Initialization timeout. Please try again.';
        });
      }
    });
  }

  @override
  void dispose() {
    // Cancel timeout timer
    _timeoutTimer?.cancel();

    // Controller is managed by GetX dependency injection, no need to dispose manually
    if (kDebugMode) {
      print('BackupRestoreView: Disposing view, controller managed by GetX');
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print(
          'BackupRestoreView: build() called - isLoading: $_isLoading, isInitialized: $_isInitialized');
    }

    // Critical safety checks with detailed logging
    if (!mounted) {
      if (kDebugMode) {
        print('BackupRestoreView: Widget not mounted, returning empty widget');
      }
      return const SizedBox.shrink();
    }

    // Loading state with timeout protection
    if (_isLoading) {
      if (kDebugMode) {
        print('BackupRestoreView: Showing loading screen');
      }
      return _buildLoadingScreen(context);
    }

    // Error state with recovery options
    if (_errorMessage != null) {
      if (kDebugMode) {
        print('BackupRestoreView: Showing error screen: $_errorMessage');
      }
      return _buildErrorScreen(context, _errorMessage!);
    }

    // Initialization validation
    if (!_isInitialized || _controller == null) {
      if (kDebugMode) {
        print('BackupRestoreView: Components not initialized properly');
      }
      return _buildErrorScreen(context, 'Failed to initialize components');
    }

    // Controller validation with comprehensive checks
    try {
      if (!Get.isRegistered<BackupRestoreController>()) {
        if (kDebugMode) {
          print('BackupRestoreView: Controller not registered in GetX');
        }
        return _buildErrorScreen(context, 'Controller not properly registered');
      }

      // Validate controller state
      _controller!; // Just verify it's accessible
      if (kDebugMode) {
        print('BackupRestoreView: Controller validated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('BackupRestoreView: Controller validation failed: $e');
      }
      return _buildErrorScreen(context, 'Controller validation failed: $e');
    }

    // Layout readiness validation with comprehensive constraints checking
    return LayoutBuilder(
      builder: (context, constraints) {
        if (kDebugMode) {
          print(
              'BackupRestoreView: LayoutBuilder constraints: ${constraints.maxWidth}x${constraints.maxHeight}');
        }

        if (constraints.maxWidth <= 0 || constraints.maxHeight <= 0) {
          if (kDebugMode) {
            print(
                'BackupRestoreView: Invalid layout constraints, showing loading');
          }
          return _buildLoadingScreen(context);
        }

        // Post-frame callback for layout completion verification
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && _isInitialized && _controller != null) {
            if (kDebugMode) {
              print(
                  'BackupRestoreView: Post-frame callback - Layout established, UI ready');
            }
          }
        });

        return _buildMainContent(context);
      },
    );
  }

  Future<void> _initializeComponents() async {
    if (kDebugMode) {
      print('BackupRestoreView: Starting component initialization');
    }

    try {
      // Get the controller from dependency injection (should be registered in AppBindings)
      _controller = Get.find<BackupRestoreController>();

      if (kDebugMode) {
        print('BackupRestoreView: Controller found successfully');
      }

      // Cancel timeout timer since initialization was successful
      _timeoutTimer?.cancel();

      // Use post-frame callback to ensure widget tree is ready
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isInitialized = true;
            _isLoading = false;
            _errorMessage = null;
          });
        }
      });

      if (kDebugMode) {
        print('BackupRestoreView: Components initialized successfully');
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('BackupRestoreView: Component initialization failed: $e');
        print('Stack trace: $stackTrace');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage =
              'Failed to load backup and restore - controller not properly registered: $e';
        });
      }
    }
  }

  Widget _buildLoadingScreen(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const LoadingIndicator(
              type: LoadingIndicatorType.circular,
              size: LoadingIndicatorSize.medium,
            ),
            const SizedBox(height: 16),
            Text(
              'Loading Backup & Restore...',
              style: TextStyle(
                color: notifier.text,
                fontSize: 16,
                fontFamily: AppTextStyles.outfitFontFamily,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Initializing components',
              style: TextStyle(
                color: notifier.text.withOpacity(0.6),
                fontSize: 14,
                fontFamily: AppTextStyles.outfitFontFamily,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(BuildContext context, String error) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red.shade400,
              ),
              const SizedBox(height: 24),
              Text(
                'Failed to Load Backup & Restore',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                  fontFamily: AppTextStyles.outfitFontFamily,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Text(
                  error,
                  style: TextStyle(
                    color: Colors.red.shade700,
                    fontFamily: AppTextStyles.outfitFontFamily,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _isLoading = true;
                        _errorMessage = null;
                        _isInitialized = false;
                      });
                      _initializeComponents();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2196F3),
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.icon(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('Go Back'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent(BuildContext context) {
    try {
      final notifier = Provider.of<ColorNotifier>(context, listen: true);
      final controller = _controller!; // Use the controller from state
      final mainDrawerController = Get.find<MainDrawerController>();

      return Scaffold(
        backgroundColor: notifier.getBgColor,
        body: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight - 48, // Account for padding
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Navigation breadcrumb
                    _buildNavigationRow(notifier, mainDrawerController),
                    const SizedBox(height: 24),

                    // Page title
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Data Backup & Restore',
                                style: AppTextStyles.titleLargeStyle.copyWith(
                                  color: notifier.text,
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Backup your company data or restore from a previous backup',
                                style: TextStyle(
                                  color: notifier.text.withOpacity(0.7),
                                  fontSize: 16,
                                  fontFamily: AppTextStyles.outfitFontFamily,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // DEBUG BUTTONS - COMMENTED OUT FOR PRODUCTION
                        // Test button (only show in debug mode)
                        // if (kDebugMode) ...[
                        //   const SizedBox(width: 16),
                        //   ElevatedButton.icon(
                        //     onPressed: () => _runSystemTests(controller),
                        //     icon: const Icon(Icons.bug_report),
                        //     label: const Text('Run Tests'),
                        //     style: ElevatedButton.styleFrom(
                        //       backgroundColor: Colors.orange,
                        //       foregroundColor: Colors.white,
                        //     ),
                        //   ),
                        //   const SizedBox(width: 8),
                        //   ElevatedButton.icon(
                        //     onPressed: () {
                        //       if (kDebugMode) {
                        //         print('Direct navigation test button pressed');
                        //       }
                        //       // Test direct navigation
                        //       Get.toNamed('/backup-restore');
                        //     },
                        //     icon: const Icon(Icons.navigation),
                        //     label: const Text('Direct Nav'),
                        //     style: ElevatedButton.styleFrom(
                        //       backgroundColor: Colors.blue,
                        //       foregroundColor: Colors.white,
                        //     ),
                        //   ),
                        // ],
                        // DEBUG BUTTONS - COMMENTED OUT FOR PRODUCTION
                        // const SizedBox(width: 8),
                        // ElevatedButton.icon(
                        //   onPressed: () => _runPerformanceTest(controller),
                        //   icon: const Icon(Icons.speed),
                        //   label: const Text('Perf Test'),
                        //   style: ElevatedButton.styleFrom(
                        //     backgroundColor: Colors.purple,
                        //     foregroundColor: Colors.white,
                        //   ),
                        // ),
                        // const SizedBox(width: 8),
                        // ElevatedButton.icon(
                        //   onPressed: () => _testFunctionality(controller),
                        //   icon: const Icon(Icons.play_arrow),
                        //   label: const Text('Test Functions'),
                        //   style: ElevatedButton.styleFrom(
                        //     backgroundColor: Colors.teal,
                        //     foregroundColor: Colors.white,
                        //   ),
                        // ),
                        // const SizedBox(width: 8),
                        // ElevatedButton.icon(
                        //   onPressed: () => _runComprehensiveTest(controller),
                        //   icon: const Icon(Icons.verified),
                        //   label: const Text('Full Test'),
                        //   style: ElevatedButton.styleFrom(
                        //     backgroundColor: Colors.indigo,
                        //     foregroundColor: Colors.white,
                        //   ),
                        // ),
                        // const SizedBox(width: 8),
                        // ElevatedButton.icon(
                        //   onPressed: () => _testRenderingFix(controller),
                        //   icon: const Icon(Icons.widgets),
                        //   label: const Text('Render Test'),
                        //   style: ElevatedButton.styleFrom(
                        //     backgroundColor: Colors.deepOrange,
                        //     foregroundColor: Colors.white,
                        //   ),
                        // ),
                        // const SizedBox(width: 8),
                        // ElevatedButton.icon(
                        //   onPressed: () =>
                        //       _validateAllFunctionality(controller),
                        //   icon: const Icon(Icons.check_circle_outline),
                        //   label: const Text('Validate All'),
                        //   style: ElevatedButton.styleFrom(
                        //     backgroundColor: Colors.green.shade700,
                        //     foregroundColor: Colors.white,
                        //   ),
                        // ),
                        // DEBUG BUTTONS - COMMENTED OUT FOR PRODUCTION
                        // const SizedBox(width: 8),
                        // ElevatedButton.icon(
                        //   onPressed: () => _testUIElements(controller),
                        //   icon: const Icon(Icons.visibility),
                        //   label: const Text('Test UI'),
                        //   style: ElevatedButton.styleFrom(
                        //     backgroundColor: Colors.cyan,
                        //     foregroundColor: Colors.white,
                        //   ),
                        // ),
                        // const SizedBox(width: 8),
                        // ElevatedButton.icon(
                        //   onPressed: () => _testCompleteWorkflow(controller),
                        //   icon: const Icon(Icons.play_circle_filled),
                        //   label: const Text('Test Workflow'),
                        //   style: ElevatedButton.styleFrom(
                        //     backgroundColor: Colors.deepPurple,
                        //     foregroundColor: Colors.white,
                        //   ),
                        // ),
                        // const SizedBox(width: 8),
                        // ElevatedButton.icon(
                        //   onPressed: () => _validateLayoutAndRendering(),
                        //   icon: const Icon(Icons.architecture),
                        //   label: const Text('Layout Test'),
                        //   style: ElevatedButton.styleFrom(
                        //     backgroundColor: Colors.teal,
                        //     foregroundColor: Colors.white,
                        //   ),
                        // ),
                      ],
                    ),
                    // DEBUG SECTION - COMMENTED OUT FOR PRODUCTION
                    // const SizedBox(height: 8),
                    // Second row of test buttons
                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.center,
                    //   children: [
                    //     ElevatedButton.icon(
                    //       onPressed: () => _validateNavigationAndRendering(),
                    //       icon: const Icon(Icons.navigation),
                    //       label: const Text('Nav Test'),
                    //       style: ElevatedButton.styleFrom(
                    //         backgroundColor: const Color(0xFF3F51B5),
                    //         foregroundColor: Colors.white,
                    //       ),
                    //     ),
                    //     const SizedBox(width: 8),
                    //     ElevatedButton.icon(
                    //       onPressed: () => _validateCompleteLayoutSystem(),
                    //       icon: const Icon(Icons.check_circle),
                    //       label: const Text('System Check'),
                    //       style: ElevatedButton.styleFrom(
                    //         backgroundColor: const Color(0xFF4CAF50),
                    //         foregroundColor: Colors.white,
                    //       ),
                    //     ),
                    //     const SizedBox(width: 8),
                    //     ElevatedButton.icon(
                    //       onPressed: () => _validateCriticalFixes(),
                    //       icon: const Icon(Icons.bug_report),
                    //       label: const Text('Critical Fix Test'),
                    //       style: ElevatedButton.styleFrom(
                    //         backgroundColor: const Color(0xFFE91E63),
                    //         foregroundColor: Colors.white,
                    //       ),
                    //     ),
                    //   ],
                    // ),
                    const SizedBox(height: 32),

                    // Main content with error boundary
                    LayoutBuilder(
                      builder: (context, constraints) {
                        // Ensure we have valid constraints
                        if (constraints.maxWidth <= 0 ||
                            constraints.maxHeight <= 0) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }

                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Backup section
                            Expanded(
                              child: _buildBackupSection(notifier, controller),
                            ),
                            const SizedBox(width: 24),
                            // Restore section
                            Expanded(
                              child: _buildRestoreSection(notifier, controller),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print(
            'BackupRestoreView: Error in _buildMainContent(): $e'); // Debug output
        print('BackupRestoreView: Stack trace: $stackTrace'); // Debug output
      }

      return _buildErrorScreen(context, e.toString());
    }
  }

  Widget _buildBackupProgressIndicator(
      ColorNotifier notifier, BackupRestoreController controller) {
    return Obx(() {
      if (kDebugMode) {
        print(
            'BackupRestoreView: Building backup progress indicator - isCreating: ${controller.isCreatingBackup.value}');
      }

      if (!controller.isCreatingBackup.value) {
        return const SizedBox.shrink();
      }

      if (kDebugMode) {
        print(
            'BackupRestoreView: Showing backup progress - ${controller.backupProgressPercent.value}%');
      }

      return Column(
        mainAxisSize: MainAxisSize.min, // Prevent unbounded height
        children: [
          // Circular progress indicator with percentage
          _buildCircularProgressWithText(notifier, controller),
          const SizedBox(height: 12),
          // Progress message
          _buildProgressMessage(notifier, controller),
          // Collections count
          _buildCollectionsCount(notifier, controller),
        ],
      );
    });
  }

  Widget _buildCircularProgressWithText(
      ColorNotifier notifier, BackupRestoreController controller) {
    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(
          width: 60,
          height: 60,
          child: CircularProgressIndicator(
            value: controller.backupProgressPercent.value / 100,
            strokeWidth: 4,
            backgroundColor: notifier.text.withOpacity(0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(
              Color(0xFF4CAF50),
            ),
          ),
        ),
        Text(
          '${controller.backupProgressPercent.value.round()}%',
          style: TextStyle(
            color: notifier.text,
            fontWeight: FontWeight.bold,
            fontSize: 12,
            fontFamily: AppTextStyles.outfitFontFamily,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressMessage(
      ColorNotifier notifier, BackupRestoreController controller) {
    return Text(
      controller.backupProgress.value,
      style: TextStyle(
        color: notifier.text.withOpacity(0.7),
        fontFamily: AppTextStyles.outfitFontFamily,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildCollectionsCount(
      ColorNotifier notifier, BackupRestoreController controller) {
    if (controller.processedCollections.value <= 0) {
      return const SizedBox.shrink();
    }
    return Column(
      children: [
        const SizedBox(height: 8),
        Text(
          'Processed ${controller.processedCollections.value} collections',
          style: TextStyle(
            color: notifier.text.withOpacity(0.5),
            fontSize: 12,
            fontFamily: AppTextStyles.outfitFontFamily,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildRestoreProgressIndicator(
      ColorNotifier notifier, BackupRestoreController controller) {
    return Obx(() {
      if (kDebugMode) {
        print(
            'BackupRestoreView: Building restore progress indicator - isRestoring: ${controller.isRestoringBackup.value}');
      }

      if (!controller.isRestoringBackup.value) {
        return const SizedBox.shrink();
      }

      if (kDebugMode) {
        print('BackupRestoreView: Showing restore progress');
      }

      return Column(
        mainAxisSize: MainAxisSize.min, // Prevent unbounded height
        children: [
          const LoadingIndicator(
            type: LoadingIndicatorType.circular,
            size: LoadingIndicatorSize.medium,
          ),
          const SizedBox(height: 12),
          _buildRestoreProgressMessage(notifier, controller),
        ],
      );
    });
  }

  Widget _buildRestoreProgressMessage(
      ColorNotifier notifier, BackupRestoreController controller) {
    return Text(
      controller.restoreProgress.value,
      style: TextStyle(
        color: notifier.text.withOpacity(0.7),
        fontFamily: AppTextStyles.outfitFontFamily,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildNavigationRow(
      ColorNotifier notifier, MainDrawerController mainDrawerController) {
    return Row(
      children: [
        InkWell(
          onTap: () => mainDrawerController.updateSelectedScreen(
              const ECommercePageView(), "main/dashboard"),
          child: Row(
            children: [
              Image.asset(
                AppAssets.homeIcon,
                height: 15,
                color: const Color(0xFF0f7bf4),
              ),
              const Text(
                AppStrings.dashboard,
                style: AppTextStyles.navigationTextStyle,
              ),
            ],
          ),
        ),
        _buildNavigationDot(),
        const Text(
          AppStrings.system,
          style: AppTextStyles.navigationTextStyle,
        ),
        _buildNavigationDot(),
        Text(
          "Backup & Restore",
          overflow: TextOverflow.ellipsis,
          style: AppTextStyles.activeNavigationTextStyle.copyWith(
            color: notifier.text,
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationDot() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      height: 5,
      width: 5,
      decoration: const BoxDecoration(
        color: Colors.grey,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildBackupSection(
      ColorNotifier notifier, BackupRestoreController controller) {
    try {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: notifier.getcardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: notifier.getfillborder),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // Prevent unbounded height
          children: [
            // Section header
            Row(
              children: [
                Icon(
                  Icons.backup,
                  color: const Color(0xFF4CAF50),
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Create Backup',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Description
            Text(
              'Export all your company data to a secure backup file. This includes invoices, vouchers, accounts, transactions, and all other business data.',
              style: TextStyle(
                color: notifier.text.withOpacity(0.8),
                height: 1.5,
                fontFamily: AppTextStyles.outfitFontFamily,
              ),
            ),
            const SizedBox(height: 24),

            // Backup features
            _buildFeatureList(notifier, [
              'Complete data export',
              'Secure JSON format',
              'Automatic file download',
              'Data integrity preserved',
            ]),
            const SizedBox(height: 24),

            // Progress indicator
            _buildBackupProgressIndicator(notifier, controller),

            // Create backup button
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton.icon(
                    onPressed: controller.isCreatingBackup.value
                        ? null
                        : () => controller.createBackup(),
                    icon: controller.isCreatingBackup.value
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: LoadingIndicator(
                              type: LoadingIndicatorType.circular,
                              size: LoadingIndicatorSize.small,
                            ),
                          )
                        : const Icon(Icons.backup),
                    label: Text(
                      controller.isCreatingBackup.value
                          ? 'Creating Backup...'
                          : 'Create Backup',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4CAF50),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  )),
            ),
          ],
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in _buildBackupSection: $e');
      }
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: notifier.getcardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: notifier.getfillborder),
        ),
        child: Center(
          child: Text(
            'Error loading backup section',
            style: TextStyle(color: notifier.text),
          ),
        ),
      );
    }
  }

  Widget _buildRestoreSection(
      ColorNotifier notifier, BackupRestoreController controller) {
    try {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: notifier.getcardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: notifier.getfillborder),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // Prevent unbounded height
          children: [
            // Section header
            Row(
              children: [
                Icon(
                  Icons.restore,
                  color: const Color(0xFF2196F3),
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Restore Data',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Description
            Text(
              'Restore your company data from a previously created backup file. This will replace all current data with the backup data.',
              style: TextStyle(
                color: notifier.text.withOpacity(0.8),
                height: 1.5,
                fontFamily: AppTextStyles.outfitFontFamily,
              ),
            ),
            const SizedBox(height: 24),

            // Restore features
            _buildFeatureList(notifier, [
              'Complete data restoration',
              'Validates backup integrity',
              'Preserves data relationships',
              'Confirmation before restore',
            ]),
            const SizedBox(height: 24),

            // File selection
            _buildFileSelectionArea(notifier, controller),
            const SizedBox(height: 16),

            // Progress indicator
            _buildRestoreProgressIndicator(notifier, controller),

            // Restore button
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton.icon(
                    onPressed: (controller.isRestoringBackup.value ||
                            !controller.hasSelectedFile.value)
                        ? null
                        : () => controller.restoreFromBackup(),
                    icon: controller.isRestoringBackup.value
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: LoadingIndicator(
                              type: LoadingIndicatorType.circular,
                              size: LoadingIndicatorSize.small,
                            ),
                          )
                        : const Icon(Icons.restore),
                    label: Text(
                      controller.isRestoringBackup.value
                          ? 'Restoring Data...'
                          : 'Restore Data',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2196F3),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  )),
            ),
          ],
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in _buildRestoreSection: $e');
      }
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: notifier.getcardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: notifier.getfillborder),
        ),
        child: Center(
          child: Text(
            'Error loading restore section',
            style: TextStyle(color: notifier.text),
          ),
        ),
      );
    }
  }

  Widget _buildFeatureList(ColorNotifier notifier, List<String> features) {
    return Column(
      children: features
          .map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: const Color(0xFF4CAF50),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        feature,
                        style: TextStyle(
                          color: notifier.text.withOpacity(0.8),
                          fontFamily: AppTextStyles.outfitFontFamily,
                        ),
                      ),
                    ),
                  ],
                ),
              ))
          .toList(),
    );
  }

  Widget _buildFileSelectionArea(
      ColorNotifier notifier, BackupRestoreController controller) {
    return Obx(() {
      final hasFile = controller.hasSelectedFile.value;

      if (kDebugMode) {
        print(
            'BackupRestoreView: Building file selection area - hasFile: $hasFile');
        if (hasFile) {
          print(
              'BackupRestoreView: Selected file: ${controller.selectedFileName.value}');
        }
      }

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: notifier.getBgColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: hasFile ? const Color(0xFF4CAF50) : notifier.getfillborder,
            width: 2,
          ),
        ),
        child: hasFile
            ? Column(
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Color(0xFF4CAF50),
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'File Selected',
                    style: TextStyle(
                      color: const Color(0xFF4CAF50),
                      fontWeight: FontWeight.w600,
                      fontFamily: AppTextStyles.outfitFontFamily,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    controller.selectedFileName.value,
                    style: TextStyle(
                      color: notifier.text.withOpacity(0.8),
                      fontFamily: AppTextStyles.outfitFontFamily,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  TextButton.icon(
                    onPressed: () => controller.pickBackupFile(),
                    icon: const Icon(Icons.folder_open),
                    label: const Text('Choose Different File'),
                  ),
                ],
              )
            : InkWell(
                onTap: () => controller.pickBackupFile(),
                child: Column(
                  children: [
                    Icon(
                      Icons.cloud_upload,
                      color: notifier.text.withOpacity(0.5),
                      size: 48,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Select Backup File',
                      style: TextStyle(
                        color: notifier.text,
                        fontWeight: FontWeight.w600,
                        fontFamily: AppTextStyles.outfitFontFamily,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Click to browse and select a .json backup file',
                      style: TextStyle(
                        color: notifier.text.withOpacity(0.6),
                        fontSize: 14,
                        fontFamily: AppTextStyles.outfitFontFamily,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
      );
    });
  }
}
