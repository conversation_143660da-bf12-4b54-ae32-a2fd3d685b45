import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:either_dart/either.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/models/auth_failer.dart';
import 'package:logestics/features/authentication/domain/repositories/auth_repository.dart';
import 'package:logestics/models/user_model.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';

class SignupUseCase {
  final AuthRepository authRepository;
  final FirebaseAuth firebaseAuth;

  SignupUseCase({
    required this.authRepository,
    FirebaseAuth? firebaseAuth,
  }) : firebaseAuth = firebaseAuth ?? FirebaseAuth.instance;

  Future<Either<AuthFailure, UserCredential>> execute(
      String email, String password, UserModel userModel) async {
    try {
      // 1. Create the user account with Firebase Auth
      final userCredential = await firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // 2. Update the user model with the created user ID
      final updatedUserModel = userModel.copyWith(
        uid: userCredential.user!.uid,
      );

      // 3. Save the user details to Firestore using UserModel pattern
      await _createUser(updatedUserModel);

      return Right(userCredential);
    } on FirebaseAuthException catch (e) {
      // Handle Firebase Auth-specific errors
      switch (e.code) {
        case 'email-already-in-use':
          // Try to handle the case where user was deleted from Firebase but cache still exists
          try {
            // Sign out any existing user to clear cache
            await firebaseAuth.signOut();

            // Wait a moment for cache to clear
            await Future.delayed(const Duration(milliseconds: 500));

            // Try registration again
            final retryCredential =
                await firebaseAuth.createUserWithEmailAndPassword(
              email: email,
              password: password,
            );

            // Update the user model with the created user ID
            final updatedUserModel = userModel.copyWith(
              uid: retryCredential.user!.uid,
            );

            // Save the user details to Firestore
            await _createUser(updatedUserModel);

            return Right(retryCredential);
          } catch (retryError) {
            return Left(
              AuthFailure(
                field: 'email',
                code: e.code,
                message:
                    'This email is already registered. If you recently deleted your account, please try again in a few minutes or contact support.',
              ),
            );
          }

        case 'invalid-email':
          return Left(
            AuthFailure(
              field: 'email',
              code: e.code,
              message: 'Please enter a valid email address.',
            ),
          );
        case 'weak-password':
          return Left(
            AuthFailure(
              field: 'password',
              code: e.code,
              message: 'The password provided is too weak.',
            ),
          );
        default:
          return Left(
            AuthFailure(
              code: e.code,
              message: e.message ?? 'An authentication error occurred.',
            ),
          );
      }
    } catch (e) {
      return Left(
        AuthFailure(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  // Create user in Firestore according to UserModel pattern
  Future<void> _createUser(UserModel user) async {
    try {
      // Create a batch operation for transaction safety
      final firestore = FirebaseFirestore.instance;
      final batch = firestore.batch();

      // Set the user document with the user's UID as the document ID
      final userRef =
          firestore.collection(AppCollection.usersCollection).doc(user.uid);

      batch.set(userRef, user.toJson());

      // Commit the batch
      await batch.commit();
    } catch (e) {
      // Let the error propagate up so it can be handled by the execute method
      rethrow;
    }
  }
}
