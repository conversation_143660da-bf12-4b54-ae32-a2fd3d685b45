import 'dart:async';
import 'dart:developer' as dev;
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:dartz/dartz.dart';
import '../../models/offline/sync_queue_entry.dart';
import '../../models/offline/offline_transaction_state.dart';
import '../../core/shared_services/failure_obj.dart';
import '../../core/shared_services/success_obj.dart';
import 'connectivity_service.dart';
import 'offline_state_service.dart';
import '../../features/voucher/repositories/voucher_repository.dart';

/// Comprehensive sync service for offline operations
class SyncService extends GetxController {
  static SyncService get instance => Get.find<SyncService>();

  // Hive boxes
  Box<Map>? _syncQueueBox;
  Box<Map>? _conflictResolutionBox;
  Box<Map>? _syncMetadataBox;

  // Services
  late final ConnectivityService _connectivityService;
  late final OfflineStateService _offlineStateService;
  late final VoucherRepository _voucherRepository;

  // Sync state
  final RxBool _isSyncing = false.obs;
  final RxDouble _syncProgress = 0.0.obs;
  final RxString _syncStatus = 'idle'.obs;
  final RxInt _totalOperations = 0.obs;
  final RxInt _completedOperations = 0.obs;
  final RxInt _failedOperations = 0.obs;

  // Sync configuration
  static const int maxRetryAttempts = 5;
  static const Duration baseSyncDelay = Duration(seconds: 30);
  static const Duration maxSyncDelay = Duration(minutes: 30);
  static const int batchSize = 10;

  // Public getters
  bool get isSyncing => _isSyncing.value;
  double get syncProgress => _syncProgress.value;
  String get syncStatus => _syncStatus.value;
  int get totalOperations => _totalOperations.value;
  int get completedOperations => _completedOperations.value;
  int get failedOperations => _failedOperations.value;

  Timer? _periodicSyncTimer;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _initializeSyncStorage();
    _startPeriodicSync();
  }

  @override
  void onClose() {
    _periodicSyncTimer?.cancel();
    _syncQueueBox?.close();
    _conflictResolutionBox?.close();
    _syncMetadataBox?.close();
    super.onClose();
  }

  /// Initialize required services
  void _initializeServices() {
    _connectivityService = Get.find<ConnectivityService>();
    _offlineStateService = Get.find<OfflineStateService>();
    _voucherRepository = Get.find<VoucherRepository>();
  }

  /// Initialize sync storage
  Future<void> _initializeSyncStorage() async {
    try {
      _syncQueueBox = await Hive.openBox<Map>('sync_queue');
      _conflictResolutionBox = await Hive.openBox<Map>('conflict_resolution');
      _syncMetadataBox = await Hive.openBox<Map>('sync_metadata');

      dev.log('SyncService: Initialized successfully');
    } catch (e) {
      dev.log('SyncService: Failed to initialize: $e');
    }
  }

  /// Start periodic sync when online
  void _startPeriodicSync() {
    _periodicSyncTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      if (_connectivityService.isOnline && !_isSyncing.value) {
        triggerSync();
      }
    });
  }

  /// Trigger sync operation
  Future<void> triggerSync() async {
    if (_isSyncing.value || _connectivityService.isOffline) {
      dev.log('SyncService: Sync already in progress or offline');
      return;
    }

    try {
      await syncPendingOperations();
    } catch (e) {
      dev.log('SyncService: Failed to trigger sync: $e');
    }
  }

  /// Sync all pending operations
  Future<Either<FailureObj, SuccessObj>> syncPendingOperations() async {
    if (_isSyncing.value) {
      return Left(FailureObj(
        code: 'sync-in-progress',
        message: 'Sync operation already in progress',
      ));
    }

    if (_connectivityService.isOffline) {
      return Left(FailureObj(
        code: 'offline',
        message: 'Cannot sync while offline',
      ));
    }

    try {
      _startSync();

      // Get pending operations
      final pendingOperations = await _getPendingOperations();
      _totalOperations.value = pendingOperations.length;

      if (pendingOperations.isEmpty) {
        _completeSync('No operations to sync');
        return Right(SuccessObj(message: 'No operations to sync'));
      }

      dev.log(
          'SyncService: Starting sync of ${pendingOperations.length} operations');

      // Sort operations by priority and dependencies
      final sortedOperations = _sortOperationsByPriority(pendingOperations);

      // Process operations in batches
      final results = await _processSyncBatches(sortedOperations);

      // Update statistics
      final successful = results.where((r) => r.isRight()).length;
      final failed = results.where((r) => r.isLeft()).length;

      _completedOperations.value = successful;
      _failedOperations.value = failed;

      if (failed == 0) {
        _completeSync('All operations synced successfully');
        return Right(SuccessObj(message: 'All operations synced successfully'));
      } else {
        _completeSync('Sync completed with $failed failures');
        return Left(FailureObj(
          code: 'partial-sync-failure',
          message:
              'Sync completed with $failed failures out of ${pendingOperations.length} operations',
        ));
      }
    } catch (e) {
      _failSync('Sync failed: $e');
      dev.log('SyncService: Sync failed: $e');
      return Left(FailureObj(
        code: 'sync-failed',
        message: 'Sync failed: $e',
      ));
    }
  }

  /// Get pending operations from queue
  Future<List<SyncQueueEntry>> _getPendingOperations() async {
    try {
      final operations = <SyncQueueEntry>[];

      final values = _syncQueueBox?.values ?? <Map>[];
      for (final entry in values) {
        try {
          final operation =
              SyncQueueEntry.fromJson(Map<String, dynamic>.from(entry));

          // Only include operations that are ready for sync
          if (operation.state == OfflineTransactionState.pending ||
              (operation.state == OfflineTransactionState.failed &&
                  operation.canRetry &&
                  operation.shouldRetryNow())) {
            operations.add(operation);
          }
        } catch (e) {
          dev.log('SyncService: Failed to parse sync queue entry: $e');
        }
      }

      return operations;
    } catch (e) {
      dev.log('SyncService: Failed to get pending operations: $e');
      return [];
    }
  }

  /// Sort operations by priority and resolve dependencies
  List<SyncQueueEntry> _sortOperationsByPriority(
      List<SyncQueueEntry> operations) {
    // Create a map for quick lookup
    final operationMap = <String, SyncQueueEntry>{};
    for (final op in operations) {
      operationMap[op.id] = op;
    }

    // Topological sort to handle dependencies
    final sorted = <SyncQueueEntry>[];
    final visited = <String>{};
    final visiting = <String>{};

    void visit(SyncQueueEntry operation) {
      if (visiting.contains(operation.id)) {
        dev.log(
            'SyncService: Circular dependency detected for operation: ${operation.id}');
        return;
      }

      if (visited.contains(operation.id)) {
        return;
      }

      visiting.add(operation.id);

      // Visit dependencies first
      for (final depId in operation.dependencies) {
        final dependency = operationMap[depId];
        if (dependency != null) {
          visit(dependency);
        }
      }

      visiting.remove(operation.id);
      visited.add(operation.id);
      sorted.add(operation);
    }

    // Sort by priority first, then apply topological sort
    final prioritySorted = operations.toList()
      ..sort((a, b) => b.priority.value.compareTo(a.priority.value));

    for (final operation in prioritySorted) {
      visit(operation);
    }

    return sorted;
  }

  /// Process sync operations in batches
  Future<List<Either<FailureObj, SuccessObj>>> _processSyncBatches(
      List<SyncQueueEntry> operations) async {
    final results = <Either<FailureObj, SuccessObj>>[];

    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = operations.skip(i).take(batchSize).toList();

      // Process batch concurrently with limited concurrency
      final batchResults = await Future.wait(
        batch.map((operation) => _syncSingleOperation(operation)),
      );

      results.addAll(batchResults);

      // Update progress
      final progress = (i + batch.length) / operations.length;
      _syncProgress.value = progress;
      _offlineStateService.updateSyncProgress(
          progress, 'Syncing batch ${(i / batchSize).floor() + 1}');

      // Small delay between batches to avoid overwhelming the server
      if (i + batchSize < operations.length) {
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }

    return results;
  }

  /// Sync a single operation
  Future<Either<FailureObj, SuccessObj>> _syncSingleOperation(
      SyncQueueEntry operation) async {
    try {
      dev.log(
          'SyncService: Syncing operation: ${operation.id} (${operation.operationType.value})');

      // Mark as syncing
      final syncingOperation = operation.markAsSyncing();
      await _updateSyncQueueEntry(syncingOperation);

      // Perform the actual sync based on operation type
      final result = await _performSync(operation);

      return result.fold(
        (failure) async {
          // Mark as failed
          final failedOperation = operation.markAsFailed(failure.message);
          await _updateSyncQueueEntry(failedOperation);

          dev.log(
              'SyncService: Operation failed: ${operation.id} - ${failure.message}');
          return Left(failure);
        },
        (success) async {
          // Mark as synced
          final syncedOperation = operation.markAsSynced();
          await _updateSyncQueueEntry(syncedOperation);

          dev.log(
              'SyncService: Operation synced successfully: ${operation.id}');
          return Right(success);
        },
      );
    } catch (e) {
      dev.log(
          'SyncService: Unexpected error syncing operation ${operation.id}: $e');

      final failedOperation = operation.markAsFailed('Unexpected error: $e');
      await _updateSyncQueueEntry(failedOperation);

      return Left(FailureObj(
        code: 'sync-error',
        message: 'Unexpected error: $e',
      ));
    }
  }

  /// Perform the actual sync based on operation type
  Future<Either<FailureObj, SuccessObj>> _performSync(
      SyncQueueEntry operation) async {
    switch (operation.operationType) {
      case SyncOperationType.voucherCreate:
        return await _syncVoucherCreate(operation);
      case SyncOperationType.voucherUpdate:
        return await _syncVoucherUpdate(operation);
      case SyncOperationType.journalEntryCreate:
        return await _syncJournalEntryCreate(operation);
      case SyncOperationType.balanceUpdate:
        return await _syncBalanceUpdate(operation);
      default:
        return Left(FailureObj(
          code: 'unsupported-operation',
          message:
              'Unsupported operation type: ${operation.operationType.value}',
        ));
    }
  }

  /// Sync voucher creation
  Future<Either<FailureObj, SuccessObj>> _syncVoucherCreate(
      SyncQueueEntry operation) async {
    try {
      final voucherData = operation.data;
      final uid = operation.companyId ?? operation.userId ?? '';

      // Use existing voucher repository to create voucher
      final result = await _voucherRepository.createVoucher(
        uid: uid,
        voucher: voucherData,
      );

      return result;
    } catch (e) {
      return Left(FailureObj(
        code: 'voucher-sync-failed',
        message: 'Failed to sync voucher: $e',
      ));
    }
  }

  /// Sync voucher update
  Future<Either<FailureObj, SuccessObj>> _syncVoucherUpdate(
      SyncQueueEntry operation) async {
    try {
      final voucherData = operation.data;
      final uid = operation.companyId ?? operation.userId ?? '';

      // Use existing voucher repository to update voucher
      final result = await _voucherRepository.updateVoucher(
        uid: uid,
        voucher: voucherData,
      );

      return result;
    } catch (e) {
      return Left(FailureObj(
        code: 'voucher-update-sync-failed',
        message: 'Failed to sync voucher update: $e',
      ));
    }
  }

  /// Sync journal entry creation
  Future<Either<FailureObj, SuccessObj>> _syncJournalEntryCreate(
      SyncQueueEntry operation) async {
    try {
      // This would integrate with the journal entry service
      // For now, return success
      return Right(SuccessObj(message: 'Journal entry synced successfully'));
    } catch (e) {
      return Left(FailureObj(
        code: 'journal-sync-failed',
        message: 'Failed to sync journal entry: $e',
      ));
    }
  }

  /// Sync balance update
  Future<Either<FailureObj, SuccessObj>> _syncBalanceUpdate(
      SyncQueueEntry operation) async {
    try {
      // This would integrate with the balance update service
      // For now, return success
      return Right(SuccessObj(message: 'Balance update synced successfully'));
    } catch (e) {
      return Left(FailureObj(
        code: 'balance-sync-failed',
        message: 'Failed to sync balance update: $e',
      ));
    }
  }

  /// Update sync queue entry
  Future<void> _updateSyncQueueEntry(SyncQueueEntry operation) async {
    try {
      await _syncQueueBox?.put(operation.id, operation.toJson());
    } catch (e) {
      dev.log('SyncService: Failed to update sync queue entry: $e');
    }
  }

  /// Start sync operation
  void _startSync() {
    _isSyncing.value = true;
    _syncProgress.value = 0.0;
    _syncStatus.value = 'syncing';
    _completedOperations.value = 0;
    _failedOperations.value = 0;

    _offlineStateService.startSync();
  }

  /// Complete sync operation
  void _completeSync(String message) {
    _isSyncing.value = false;
    _syncProgress.value = 1.0;
    _syncStatus.value = message;

    _offlineStateService.completeSync();

    // Clean up completed operations
    _cleanupCompletedOperations();
  }

  /// Fail sync operation
  void _failSync(String error) {
    _isSyncing.value = false;
    _syncProgress.value = 0.0;
    _syncStatus.value = 'failed';

    _offlineStateService.failSync(error);
  }

  /// Clean up completed operations from sync queue
  Future<void> _cleanupCompletedOperations() async {
    try {
      final keysToDelete = <String>[];

      for (final entry
          in _syncQueueBox?.toMap().entries ?? <MapEntry<dynamic, Map>>[]) {
        try {
          final operation =
              SyncQueueEntry.fromJson(Map<String, dynamic>.from(entry.value));

          if (operation.state == OfflineTransactionState.synced) {
            keysToDelete.add(entry.key.toString());
          }
        } catch (e) {
          dev.log('SyncService: Failed to parse operation during cleanup: $e');
        }
      }

      for (final key in keysToDelete) {
        await _syncQueueBox?.delete(key);
      }

      dev.log(
          'SyncService: Cleaned up ${keysToDelete.length} completed operations');
    } catch (e) {
      dev.log('SyncService: Failed to cleanup completed operations: $e');
    }
  }

  /// Get sync statistics
  Map<String, dynamic> getSyncStats() {
    return {
      'isSyncing': isSyncing,
      'syncProgress': syncProgress,
      'syncStatus': syncStatus,
      'totalOperations': totalOperations,
      'completedOperations': completedOperations,
      'failedOperations': failedOperations,
      'pendingOperations':
          totalOperations - completedOperations - failedOperations,
    };
  }

  /// Force retry failed operations
  Future<void> retryFailedOperations() async {
    if (_isSyncing.value) {
      dev.log('SyncService: Cannot retry while sync is in progress');
      return;
    }

    try {
      final failedOperations = <SyncQueueEntry>[];

      final values = _syncQueueBox?.values ?? <Map>[];
      for (final entry in values) {
        try {
          final operation =
              SyncQueueEntry.fromJson(Map<String, dynamic>.from(entry));

          if (operation.state == OfflineTransactionState.failed &&
              operation.canRetry) {
            failedOperations.add(operation);
          }
        } catch (e) {
          dev.log('SyncService: Failed to parse operation during retry: $e');
        }
      }

      if (failedOperations.isNotEmpty) {
        dev.log(
            'SyncService: Retrying ${failedOperations.length} failed operations');
        await syncPendingOperations();
      } else {
        dev.log('SyncService: No failed operations to retry');
      }
    } catch (e) {
      dev.log('SyncService: Failed to retry operations: $e');
    }
  }
}
