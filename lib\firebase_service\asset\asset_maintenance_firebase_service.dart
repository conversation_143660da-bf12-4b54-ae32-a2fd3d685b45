import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/firebase_service/asset/asset_audit_firebase_service.dart';
import 'package:logestics/models/asset/asset_maintenance_model.dart';
import 'package:uuid/uuid.dart';

class AssetMaintenanceFirebaseService {
  late FirebaseFirestore _firestore;
  late FirebaseStorage _storage;
  late AssetAuditFirebaseService _auditService;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  AssetMaintenanceFirebaseService() {
    _firestore = FirebaseFirestore.instance;
    _storage = FirebaseStorage.instance;
    _auditService = AssetAuditFirebaseService();
  }

  /// Create a new maintenance record
  Future<void> createMaintenance(AssetMaintenanceModel maintenance,
      {List<File>? files,
      List<Uint8List>? fileBytes,
      List<String>? fileNames}) async {
    log('Creating maintenance record for asset: ${maintenance.assetId}');
    try {
      final maintenanceRef =
          _firestore.collection(AppCollection.assetMaintenanceCollection).doc();
      final maintenanceId = maintenanceRef.id;

      // Upload files if provided
      List<String> attachmentUrls = [];
      if (files != null && files.isNotEmpty) {
        attachmentUrls = await _uploadFiles(maintenanceId, files: files);
      } else if (fileBytes != null &&
          fileBytes.isNotEmpty &&
          fileNames != null) {
        attachmentUrls = await _uploadFiles(maintenanceId,
            fileBytes: fileBytes, fileNames: fileNames);
      }

      final maintenanceData = maintenance
          .copyWith(
            id: maintenanceId,
            attachmentUrls: attachmentUrls,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          )
          .toJson();
      maintenanceData['uid'] = _uid; // Add current user's UID

      await maintenanceRef.set(maintenanceData);
      log('Successfully created maintenance record: $maintenanceId');

      // Log maintenance addition in audit trail
      try {
        // Get asset name for audit logging
        String assetName = 'Unknown Asset';
        try {
          final assetDoc = await _firestore
              .collection(AppCollection.assetsCollection)
              .doc(maintenance.assetId)
              .get();
          if (assetDoc.exists) {
            assetName = assetDoc.data()?['name'] ?? 'Unknown Asset';
          }
        } catch (e) {
          log('Warning: Could not fetch asset name for audit: $e');
        }

        await _auditService.logMaintenanceAdded(
            maintenance.assetId, assetName, maintenanceId,
            notes: 'Maintenance record created: ${maintenance.description}');
      } catch (e) {
        log('Warning: Failed to log maintenance creation audit: $e');
        // Don't fail the main operation if audit logging fails
      }
    } catch (e) {
      log('Error creating maintenance record: $e');
      rethrow;
    }
  }

  /// Get all maintenance records for a specific asset
  Future<List<AssetMaintenanceModel>> getMaintenanceByAssetId(
      String assetId) async {
    log('Fetching maintenance records for asset: $assetId');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.assetMaintenanceCollection)
          .where('uid', isEqualTo: _uid)
          .where('assetId', isEqualTo: assetId)
          .orderBy('maintenanceDate', descending: true)
          .get();

      final maintenanceRecords = snapshot.docs
          .map((doc) => AssetMaintenanceModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${maintenanceRecords.length} maintenance records for asset: $assetId');
      return maintenanceRecords;
    } catch (e) {
      log('Error fetching maintenance records: $e');
      rethrow;
    }
  }

  /// Get all maintenance records for current user
  Future<List<AssetMaintenanceModel>> getAllMaintenance() async {
    log('Fetching all maintenance records for user: $_uid');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.assetMaintenanceCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('maintenanceDate', descending: true)
          .get();

      final maintenanceRecords = snapshot.docs
          .map((doc) => AssetMaintenanceModel.fromJson(doc.data()))
          .toList();

      log('Successfully fetched ${maintenanceRecords.length} maintenance records');
      return maintenanceRecords;
    } catch (e) {
      log('Error fetching all maintenance records: $e');
      rethrow;
    }
  }

  /// Get maintenance record by ID
  Future<AssetMaintenanceModel?> getMaintenanceById(
      String maintenanceId) async {
    log('Fetching maintenance record by ID: $maintenanceId');
    try {
      final doc = await _firestore
          .collection(AppCollection.assetMaintenanceCollection)
          .doc(maintenanceId)
          .get();

      if (!doc.exists) {
        log('Maintenance record not found: $maintenanceId');
        return null;
      }

      final data = doc.data()!;
      if (data['uid'] != _uid) {
        log('Maintenance record does not belong to current user: $maintenanceId');
        return null;
      }

      return AssetMaintenanceModel.fromJson(data);
    } catch (e) {
      log('Error fetching maintenance record by ID: $e');
      rethrow;
    }
  }

  /// Update an existing maintenance record
  Future<void> updateMaintenance(AssetMaintenanceModel maintenance,
      {List<File>? files,
      List<Uint8List>? fileBytes,
      List<String>? fileNames}) async {
    log('Updating maintenance record: ${maintenance.id}');
    try {
      // Upload new files if provided
      List<String> newAttachmentUrls = [];
      if (files != null && files.isNotEmpty) {
        newAttachmentUrls = await _uploadFiles(maintenance.id, files: files);
      } else if (fileBytes != null &&
          fileBytes.isNotEmpty &&
          fileNames != null) {
        newAttachmentUrls = await _uploadFiles(maintenance.id,
            fileBytes: fileBytes, fileNames: fileNames);
      }

      // Combine existing and new attachment URLs
      final allAttachmentUrls = [
        ...maintenance.attachmentUrls,
        ...newAttachmentUrls
      ];

      final maintenanceData = maintenance
          .copyWith(
            attachmentUrls: allAttachmentUrls,
            updatedAt: DateTime.now(),
          )
          .toJson();

      await _firestore
          .collection(AppCollection.assetMaintenanceCollection)
          .doc(maintenance.id)
          .update(maintenanceData);

      log('Successfully updated maintenance record: ${maintenance.id}');
    } catch (e) {
      log('Error updating maintenance record: $e');
      rethrow;
    }
  }

  /// Delete a maintenance record
  Future<void> deleteMaintenance(String maintenanceId) async {
    log('Deleting maintenance record: $maintenanceId');
    try {
      // Get maintenance record to check ownership and get attachment URLs
      final maintenanceDoc = await _firestore
          .collection(AppCollection.assetMaintenanceCollection)
          .doc(maintenanceId)
          .get();

      if (!maintenanceDoc.exists) {
        throw Exception('Maintenance record not found');
      }

      final maintenanceData = maintenanceDoc.data()!;
      if (maintenanceData['uid'] != _uid) {
        throw Exception('Maintenance record does not belong to current user');
      }

      // Delete attachment files from storage
      final attachmentUrls =
          List<String>.from(maintenanceData['attachmentUrls'] ?? []);
      for (final url in attachmentUrls) {
        try {
          await _storage.refFromURL(url).delete();
        } catch (e) {
          log('Error deleting attachment file: $e');
          // Continue with maintenance deletion even if file deletion fails
        }
      }

      // Delete the maintenance document
      await _firestore
          .collection(AppCollection.assetMaintenanceCollection)
          .doc(maintenanceId)
          .delete();

      log('Successfully deleted maintenance record: $maintenanceId');
    } catch (e) {
      log('Error deleting maintenance record: $e');
      rethrow;
    }
  }

  /// Calculate total maintenance cost for an asset
  Future<double> getTotalMaintenanceCost(String assetId) async {
    log('Calculating total maintenance cost for asset: $assetId');
    try {
      final maintenanceRecords = await getMaintenanceByAssetId(assetId);
      final totalCost = maintenanceRecords.fold<double>(
        0.0,
        (total, maintenance) => total + maintenance.cost,
      );

      log('Total maintenance cost for asset $assetId: $totalCost');
      return totalCost;
    } catch (e) {
      log('Error calculating total maintenance cost: $e');
      rethrow;
    }
  }

  /// Upload files to Firebase Storage
  Future<List<String>> _uploadFiles(String maintenanceId,
      {List<File>? files,
      List<Uint8List>? fileBytes,
      List<String>? fileNames}) async {
    List<String> downloadUrls = [];

    try {
      if (files != null) {
        for (int i = 0; i < files.length; i++) {
          final file = files[i];
          final fileName = '${const Uuid().v4()}_${file.path.split('/').last}';
          final ref = _storage
              .ref()
              .child('asset_maintenance/$maintenanceId/$fileName');

          final uploadTask = await ref.putFile(file);
          final downloadUrl = await uploadTask.ref.getDownloadURL();
          downloadUrls.add(downloadUrl);
        }
      } else if (fileBytes != null && fileNames != null) {
        for (int i = 0; i < fileBytes.length; i++) {
          final bytes = fileBytes[i];
          final fileName = '${const Uuid().v4()}_${fileNames[i]}';
          final ref = _storage
              .ref()
              .child('asset_maintenance/$maintenanceId/$fileName');

          final uploadTask = await ref.putData(bytes);
          final downloadUrl = await uploadTask.ref.getDownloadURL();
          downloadUrls.add(downloadUrl);
        }
      }

      log('Successfully uploaded ${downloadUrls.length} files for maintenance: $maintenanceId');
      return downloadUrls;
    } catch (e) {
      log('Error uploading files: $e');
      rethrow;
    }
  }

  /// Remove specific attachment from maintenance record
  Future<void> removeAttachment(
      String maintenanceId, String attachmentUrl) async {
    log('Removing attachment from maintenance record: $maintenanceId');
    try {
      // Get current maintenance data
      final maintenanceDoc = await _firestore
          .collection(AppCollection.assetMaintenanceCollection)
          .doc(maintenanceId)
          .get();

      if (!maintenanceDoc.exists) {
        throw Exception('Maintenance record not found');
      }

      final maintenanceData = maintenanceDoc.data()!;
      if (maintenanceData['uid'] != _uid) {
        throw Exception('Maintenance record does not belong to current user');
      }

      // Remove URL from attachment list
      final attachmentUrls =
          List<String>.from(maintenanceData['attachmentUrls'] ?? []);
      attachmentUrls.remove(attachmentUrl);

      // Update maintenance record with new attachment list
      await _firestore
          .collection(AppCollection.assetMaintenanceCollection)
          .doc(maintenanceId)
          .update({
        'attachmentUrls': attachmentUrls,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      // Delete file from storage
      try {
        await _storage.refFromURL(attachmentUrl).delete();
      } catch (e) {
        log('Error deleting attachment file from storage: $e');
        // Continue even if file deletion fails
      }

      log('Successfully removed attachment from maintenance record: $maintenanceId');
    } catch (e) {
      log('Error removing attachment: $e');
      rethrow;
    }
  }
}
