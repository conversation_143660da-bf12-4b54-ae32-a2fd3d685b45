import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

import '../../../../../models/finance/bill_model.dart';
import '../controllers/bill_detail_controller.dart';

class BillDetailView extends StatelessWidget {
  final BillModel bill;

  const BillDetailView({super.key, required this.bill});

  String formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd/MM/yyyy').format(date);
  }

  String formatCurrency(double amount) {
    return NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2)
        .format(amount);
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    // Initialize controller with bill data
    final controller = Get.put(BillDetailController());
    controller.initializeBill(bill);

    return Dialog(
      backgroundColor: notifier.getcardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 1000,
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bill Details',
                        style: AppTextStyles.titleStyle.copyWith(
                          color: notifier.text,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        bill.customBillName?.isNotEmpty == true
                            ? bill.customBillName!
                            : 'Bill #${bill.billNumber}',
                        style: AppTextStyles.subtitleStyle.copyWith(
                          color: notifier.text.withValues(alpha: 0.7),
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: Icon(
                      Icons.close,
                      color: notifier.text,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Bill Information Cards
              _buildBillInfoSection(notifier),

              const SizedBox(height: 24),

              // Linked Invoices Section
              Expanded(
                child: _buildLinkedInvoicesSection(controller, notifier),
              ),

              const SizedBox(height: 24),

              // Action Buttons
              _buildActionButtons(controller, notifier),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBillInfoSection(notifier) {
    return Row(
      children: [
        Expanded(
          child: _buildInfoCard(
            'Bill Date',
            formatDate(bill.billDate),
            Icons.calendar_today,
            notifier,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildInfoCard(
            'Total Amount',
            formatCurrency(bill.totalAmount),
            Icons.attach_money,
            notifier,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildInfoCard(
            'Linked Invoices',
            bill.numberOfLinkedInvoices.toString(),
            Icons.receipt_long,
            notifier,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildInfoCard(
            'Status',
            bill.billStatus,
            Icons.info_outline,
            notifier,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(String title, String value, IconData icon, notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notifier.text.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: notifier.text.withValues(alpha: 0.7),
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: notifier.text.withValues(alpha: 0.7),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: notifier.text,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLinkedInvoicesSection(
      BillDetailController controller, notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Linked Invoices',
          style: AppTextStyles.titleStyle.copyWith(
            color: notifier.text,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: Obx(() {
            if (controller.isLoadingInvoices.value) {
              return const Center(child: CircularProgressIndicator());
            }

            if (controller.linkedInvoices.isEmpty) {
              return Container(
                decoration: BoxDecoration(
                  border:
                      Border.all(color: notifier.text.withValues(alpha: 0.2)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long_outlined,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No linked invoices found',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            return _buildInvoicesTable(controller, notifier);
          }),
        ),
      ],
    );
  }

  Widget _buildInvoicesTable(BillDetailController controller, notifier) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: notifier.text.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Table Header
            Container(
              color: notifier.text.withValues(alpha: 0.05),
              child: Table(
                columnWidths: const {
                  0: FlexColumnWidth(0.6), // SN - minimal
                  1: FlexColumnWidth(1.3), // TAS Number
                  2: FlexColumnWidth(1.2), // Product
                  3: FlexColumnWidth(1.1), // Truck No
                  4: FlexColumnWidth(0.8), // Bags
                  5: FlexColumnWidth(0.9), // Weight
                  6: FlexColumnWidth(1.3), // Destination
                  7: FlexColumnWidth(0.6), // KM - minimal
                },
                children: [
                  TableRow(
                    children: [
                      DataTableCell(
                        text: 'SN.',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: notifier.text,
                        ),
                      ),
                      DataTableCell(
                        text: 'TAS Number',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: notifier.text,
                        ),
                      ),
                      DataTableCell(
                        text: 'Product',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: notifier.text,
                        ),
                      ),
                      DataTableCell(
                        text: 'Truck No',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: notifier.text,
                        ),
                      ),
                      DataTableCell(
                        text: 'Bags',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: notifier.text,
                        ),
                      ),
                      DataTableCell(
                        text: 'Weight (kg)',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: notifier.text,
                        ),
                      ),
                      DataTableCell(
                        text: 'Destination',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: notifier.text,
                        ),
                      ),
                      DataTableCell(
                        text: 'KM',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: notifier.text,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Table Body
            Table(
              columnWidths: const {
                0: FlexColumnWidth(0.6), // SN - minimal
                1: FlexColumnWidth(1.3), // TAS Number
                2: FlexColumnWidth(1.2), // Product
                3: FlexColumnWidth(1.1), // Truck No
                4: FlexColumnWidth(0.8), // Bags
                5: FlexColumnWidth(0.9), // Weight
                6: FlexColumnWidth(1.3), // Destination
                7: FlexColumnWidth(0.6), // KM - minimal
              },
              children: [
                ...controller.linkedInvoices.asMap().entries.map((entry) {
                  final index = entry.key;
                  final invoice = entry.value;
                  final totalWeight =
                      invoice.numberOfBags * invoice.weightPerBag;

                  return TableRow(
                    children: [
                      DataTableCell(text: (index + 1).toString()),
                      DataTableCell(text: invoice.tasNumber),
                      DataTableCell(text: invoice.productName),
                      DataTableCell(text: invoice.truckNumber),
                      DataTableCell(text: invoice.numberOfBags.toString()),
                      DataTableCell(text: totalWeight.toString()),
                      DataTableCell(text: invoice.stationName),
                      DataTableCell(
                          text: invoice.distanceInKilometers.toString()),
                    ],
                  );
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BillDetailController controller, notifier) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // PDF Export Button
        Obx(() => ElevatedButton.icon(
              onPressed: controller.isExportingPdf.value
                  ? null
                  : () => controller.exportToPdf(),
              icon: controller.isExportingPdf.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.picture_as_pdf),
              label: Text(controller.isExportingPdf.value
                  ? 'Generating PDF...'
                  : 'Export to PDF'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            )),
        const SizedBox(width: 12),
        // Excel Export Button
        Obx(() => ElevatedButton.icon(
              onPressed: controller.isExporting.value
                  ? null
                  : () => controller.exportToExcel(),
              icon: controller.isExporting.value
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.file_download),
              label: Text(controller.isExporting.value
                  ? 'Exporting...'
                  : 'Export to Excel'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            )),
        const SizedBox(width: 12),
        ElevatedButton(
          onPressed: () => Get.back(),
          style: ElevatedButton.styleFrom(
            backgroundColor: notifier.text.withValues(alpha: 0.1),
            foregroundColor: notifier.text,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('Close'),
        ),
      ],
    );
  }
}
