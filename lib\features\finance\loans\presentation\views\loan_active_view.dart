import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/features/finance/loans/presentation/controllers/loans_controller.dart';
import 'package:logestics/features/voucher/presentation/widgets/chart_of_accounts_dropdown.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class LoanActiveView extends StatefulWidget {
  const LoanActiveView({super.key});

  @override
  State<LoanActiveView> createState() => _LoanActiveViewState();
}

class _LoanActiveViewState extends State<LoanActiveView> {
  late LoansController loansController;

  @override
  void initState() {
    super.initState();
    loansController = Get.find<LoansController>();

    // Load only active loans data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      loansController.loadActiveLoansTab();
    });
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Active Loans'),
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: loansController.searchController,
              decoration: InputDecoration(
                hintText: 'Search loans by name, amount, account...',
                hintStyle:
                    TextStyle(color: notifier.text.withValues(alpha: 0.5)),
                prefixIcon: Icon(Icons.search,
                    color: notifier.text.withValues(alpha: 0.7)),
                suffixIcon:
                    Obx(() => loansController.searchQuery.value.isNotEmpty
                        ? IconButton(
                            icon: Icon(Icons.clear,
                                color: notifier.text.withValues(alpha: 0.7)),
                            onPressed: () {
                              loansController.searchController.clear();
                            },
                          )
                        : const SizedBox.shrink()),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF0f7bf4)),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              ),
              style: TextStyle(color: notifier.text),
            ),
          ),

          // Main content
          Expanded(
            child: Obx(() {
              if (loansController.isLoadingActive.value) {
                return const Center(child: CircularProgressIndicator());
              }

              final loansToShow = loansController.searchQuery.value.isEmpty
                  ? loansController.activeLoans
                  : loansController.filteredActiveLoans;

              if (loansToShow.isEmpty &&
                  !loansController.isLoadingActive.value) {
                return LayoutBuilder(
                  builder: (context, constraints) {
                    return SingleChildScrollView(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          minHeight: constraints.maxHeight,
                        ),
                        child: IntrinsicHeight(
                          child: Padding(
                            padding: const EdgeInsets.all(32),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.account_balance,
                                  size: 64,
                                  color: notifier.text.withValues(alpha: 0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No Active Loans',
                                  style: TextStyle(
                                    color: notifier.text,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'You have no active loans at the moment',
                                  style: TextStyle(
                                      color:
                                          notifier.text.withValues(alpha: 0.7)),
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: loansToShow.length,
                itemBuilder: (context, index) {
                  final loan = loansToShow[index];
                  return _buildActiveLoanCard(loan);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveLoanCard(LoanModel loan) {
    // Calculate days until due
    final daysUntilDue = loan.dueDate.difference(DateTime.now()).inDays;
    final isDue = daysUntilDue <= 0;

    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isDue ? Colors.red : Colors.transparent,
          width: isDue ? 2 : 0,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    loan.requestedByName ==
                            loansController.currentUser.value!.companyName
                        ? 'Borrowed From: ${loan.requestedToName}'
                        : 'Loaned To: ${loan.requestedByName}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Chip(
                  label: Text(isDue ? 'Due Now' : '$daysUntilDue days left'),
                  backgroundColor: _getDueDateColor(daysUntilDue),
                  labelStyle: const TextStyle(color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Amount: PKR ${loan.amount.toStringAsFixed(2)}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'From Account: ${loan.fromAccountName}',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 4),
            Text(
              'To Account: ${loan.toAccountName}',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            Text(
              'Due Date: ${_formatDate(loan.dueDate)}',
              style: TextStyle(
                fontSize: 14,
                color: isDue ? Colors.red : null,
                fontWeight: isDue ? FontWeight.bold : null,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Approved Date: ${_formatDate(loan.approvalDate ?? loan.requestDate)}',
              style: const TextStyle(fontSize: 14),
            ),
            if (loan.notes != null && loan.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Notes: ${loan.notes}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _showRepayDialog(loan),
                  icon: const Icon(Icons.payment),
                  label: const Text('Repay Loan'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showRepayDialog(LoanModel loan) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Repay Loan'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Are you sure you want to repay the loan of PKR ${loan.amount.toStringAsFixed(2)}?',
              ),
              const SizedBox(height: 16),
              const Text('Select an account to repay from:'),
              const SizedBox(height: 8),
              Obx(() {
                if (loansController.isLoadingChartAccounts.value) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (loansController.chartOfAccounts.isEmpty) {
                  return Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: const Text(
                        'No Chart of Accounts available. Please add an Asset account first.'),
                  );
                }

                return AssetAccountDropdown(
                  labelText: 'Repayment Account',
                  hintText: 'Select the account to repay from',
                  selectedAccount: loansController.selectedChartAccount.value,
                  onChanged: (ChartOfAccountsModel? account) {
                    if (account != null) {
                      loansController.setSelectedChartAccount(account);
                    }
                  },
                  isRequired: true,
                );
              }),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            Obx(() {
              return ElevatedButton(
                onPressed: loansController.isProcessing.value ||
                        loansController.selectedAccount.value == null
                    ? null
                    : () async {
                        final success = await loansController.repayLoan(
                            loan.id, loansController.selectedAccount.value!.id);
                        if (success) {
                          // ignore: use_build_context_synchronously
                          Navigator.pop(context);
                        }
                      },
                child: loansController.isProcessing.value
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Repay'),
              );
            }),
          ],
        );
      },
    );
  }

  Color _getDueDateColor(int daysUntilDue) {
    if (daysUntilDue < 0) {
      return Colors.red;
    } else if (daysUntilDue <= 7) {
      return Colors.orange;
    } else if (daysUntilDue <= 30) {
      return Colors.blue;
    } else {
      return Colors.green;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
