import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import '../firebase_service/accounting/journal_entry_firebase_service.dart';

/// Quick test to verify journal entry number generation fix
class TestJournalEntryNumberFix {
  static Future<void> runTest() async {
    log('🧪 ========== JOURNAL ENTRY NUMBER FIX TEST ==========');
    
    try {
      // Check authentication
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        log('❌ No authenticated user. Please login first.');
        return;
      }
      
      log('👤 Testing journal entry number generation for user: ${user.uid}');
      
      // Test journal entry number generation
      final journalService = JournalEntryFirebaseService();
      
      log('🔍 Testing getNextJournalEntryNumber method...');
      
      try {
        final nextNumber = await journalService.getNextJournalEntryNumber();
        log('✅ Successfully generated journal entry number: $nextNumber');
        
        // Verify format
        if (nextNumber.startsWith('JE') && nextNumber.length >= 8) {
          log('✅ Journal entry number format is correct');
        } else {
          log('⚠️ Journal entry number format might be incorrect: $nextNumber');
        }
        
        // Test multiple generations to ensure incrementing works
        log('🔍 Testing multiple number generations...');
        for (int i = 0; i < 3; i++) {
          final testNumber = await journalService.getNextJournalEntryNumber();
          log('  Generated number ${i + 2}: $testNumber');
        }
        
        log('🎉 Journal entry number generation test completed successfully!');
        
      } catch (e) {
        log('❌ Journal entry number generation failed: $e');
        log('📋 Stack trace: ${StackTrace.current}');
      }
      
    } catch (e) {
      log('❌ Journal entry number test failed: $e');
    }
    
    log('🏁 ========== JOURNAL ENTRY NUMBER FIX TEST COMPLETED ==========');
  }
}
