import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'lib/core/services/loan_balance_fix_service.dart';
import 'lib/core/services/balance_recalculation_service.dart';
import 'lib/features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';
import 'lib/firebase_service/accounting/journal_entry_firebase_service.dart';

/// Comprehensive utility to fix all loan balance calculation issues
/// Addresses manual loans, voucher-based loans, running balances, and synchronization
void main() {
  runApp(const ComprehensiveLoanBalanceFixApp());
}

class ComprehensiveLoanBalanceFixApp extends StatelessWidget {
  const ComprehensiveLoanBalanceFixApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Comprehensive Loan Balance Fix',
      theme: ThemeData(primarySwatch: Colors.deepOrange),
      home: const ComprehensiveLoanBalanceFixScreen(),
    );
  }
}

class ComprehensiveLoanBalanceFixScreen extends StatefulWidget {
  const ComprehensiveLoanBalanceFixScreen({super.key});

  @override
  State<ComprehensiveLoanBalanceFixScreen> createState() => _ComprehensiveLoanBalanceFixScreenState();
}

class _ComprehensiveLoanBalanceFixScreenState extends State<ComprehensiveLoanBalanceFixScreen> {
  final TextEditingController _companyUidController = TextEditingController();
  bool _isRunning = false;
  String _currentOperation = '';
  final List<String> _logs = [];

  // Services
  late LoanBalanceFixService _loanBalanceFixService;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  void _initializeServices() {
    // In a real app, these would be injected via GetX or other DI
    final journalService = JournalEntryFirebaseService();
    final accountsRepository = Get.find<ChartOfAccountsRepository>();
    final balanceRecalculationService = BalanceRecalculationService();
    
    _loanBalanceFixService = LoanBalanceFixService(
      journalService: journalService,
      accountsRepository: accountsRepository,
      balanceRecalculationService: balanceRecalculationService,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Comprehensive Loan Balance Fix'),
        backgroundColor: Colors.deepOrange[700],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Comprehensive Loan Balance Fix Utility',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.deepOrange[800],
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This utility fixes ALL loan balance calculation issues:\n'
                      '• Manual loan request acceptance balance problems\n'
                      '• Voucher-based loan request balance issues\n'
                      '• Running balance calculation errors\n'
                      '• Current balance synchronization problems\n'
                      '• Account type configuration issues\n'
                      '• Journal entry balance impact calculations\n\n'
                      'The fix ensures Asset accounts show positive balances when debited (loan approved).',
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _companyUidController,
                      decoration: const InputDecoration(
                        labelText: 'Company UID',
                        hintText: 'Enter the company UID to fix',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _isRunning ? null : _fixAccountTypes,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[700],
                          ),
                          child: const Text('1. Fix Account Types'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _fixRunningBalances,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange[700],
                          ),
                          child: const Text('2. Fix Running Balances'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _synchronizeBalances,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green[700],
                          ),
                          child: const Text('3. Sync Balances'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _isRunning ? null : _fixManualLoans,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purple[700],
                          ),
                          child: const Text('4. Fix Manual Loans'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _fixVoucherLoans,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.teal[700],
                          ),
                          child: const Text('5. Fix Voucher Loans'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isRunning ? null : _recalculateAll,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.indigo[700],
                          ),
                          child: const Text('6. Recalculate All'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isRunning ? null : _runComprehensiveFix,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[700],
                        minimumSize: const Size(double.infinity, 56),
                      ),
                      child: _isRunning 
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 3,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Text(
                                  _currentOperation.isEmpty ? 'Processing...' : _currentOperation,
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ],
                            )
                          : const Text(
                              'RUN COMPREHENSIVE LOAN BALANCE FIX',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Fix Progress Log:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _logs.isEmpty
                    ? const Center(
                        child: Text(
                          'No operations run yet. Click a button above to start fixing loan balance issues.',
                          style: TextStyle(color: Colors.grey),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(8),
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          final log = _logs[index];
                          Color textColor = Colors.black;
                          if (log.contains('✅')) textColor = Colors.green;
                          if (log.contains('❌')) textColor = Colors.red;
                          if (log.contains('⚠️')) textColor = Colors.orange;
                          if (log.contains('🔧')) textColor = Colors.blue;
                          if (log.contains('📊')) textColor = Colors.purple;
                          if (log.contains('💰')) textColor = Colors.teal;
                          
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 1),
                            child: Text(
                              log,
                              style: TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 11,
                                color: textColor,
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)} $message');
    });
    dev.log(message);
  }

  String _getCompanyUid() {
    final uid = _companyUidController.text.trim();
    if (uid.isEmpty) {
      _addLog('❌ Please enter a company UID');
      return '';
    }
    return uid;
  }

  Future<void> _fixAccountTypes() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
      _currentOperation = 'Fixing account types...';
    });

    try {
      _addLog('🔧 Starting account type configuration fix for company: $uid');
      
      final result = LoanBalanceFixResult(uid: uid, fixedAt: DateTime.now());
      await _loanBalanceFixService._fixLoanAccountTypeConfiguration(uid, result);
      
      _addLog('✅ Account type fix completed');
      _addLog('📊 Results: ${result.accountsFixed} accounts fixed');
      
      if (result.errors.isNotEmpty) {
        for (final error in result.errors) {
          _addLog('❌ Error: $error');
        }
      }
      
    } catch (e) {
      _addLog('❌ Error during account type fix: $e');
    } finally {
      setState(() {
        _isRunning = false;
        _currentOperation = '';
      });
    }
  }

  Future<void> _fixRunningBalances() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
      _currentOperation = 'Fixing running balances...';
    });

    try {
      _addLog('💰 Starting running balance calculation fix for company: $uid');
      
      final result = LoanBalanceFixResult(uid: uid, fixedAt: DateTime.now());
      await _loanBalanceFixService._fixRunningBalanceCalculations(uid, result);
      
      _addLog('✅ Running balance fix completed');
      _addLog('📊 Results: ${result.journalEntriesFixed} journal entries fixed');
      
    } catch (e) {
      _addLog('❌ Error during running balance fix: $e');
    } finally {
      setState(() {
        _isRunning = false;
        _currentOperation = '';
      });
    }
  }

  Future<void> _synchronizeBalances() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
      _currentOperation = 'Synchronizing balances...';
    });

    try {
      _addLog('🔄 Starting balance synchronization for company: $uid');
      
      final result = LoanBalanceFixResult(uid: uid, fixedAt: DateTime.now());
      await _loanBalanceFixService._fixCurrentBalanceSynchronization(uid, result);
      
      _addLog('✅ Balance synchronization completed');
      _addLog('📊 Results: ${result.balancesSynchronized} balances synchronized');
      
    } catch (e) {
      _addLog('❌ Error during balance synchronization: $e');
    } finally {
      setState(() {
        _isRunning = false;
        _currentOperation = '';
      });
    }
  }

  Future<void> _fixManualLoans() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
      _currentOperation = 'Fixing manual loans...';
    });

    try {
      _addLog('✋ Starting manual loan workflow fix for company: $uid');
      
      final result = LoanBalanceFixResult(uid: uid, fixedAt: DateTime.now());
      await _loanBalanceFixService._fixManualLoanWorkflow(uid, result);
      
      _addLog('✅ Manual loan workflow fix completed');
      _addLog('📊 Results: ${result.manualLoanEntriesFixed} manual loan entries fixed');
      
    } catch (e) {
      _addLog('❌ Error during manual loan fix: $e');
    } finally {
      setState(() {
        _isRunning = false;
        _currentOperation = '';
      });
    }
  }

  Future<void> _fixVoucherLoans() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
      _currentOperation = 'Fixing voucher loans...';
    });

    try {
      _addLog('📄 Starting voucher-based loan workflow fix for company: $uid');
      
      final result = LoanBalanceFixResult(uid: uid, fixedAt: DateTime.now());
      await _loanBalanceFixService._fixVoucherLoanWorkflow(uid, result);
      
      _addLog('✅ Voucher-based loan workflow fix completed');
      _addLog('📊 Results: ${result.voucherLoanEntriesFixed} voucher loan entries fixed');
      
    } catch (e) {
      _addLog('❌ Error during voucher loan fix: $e');
    } finally {
      setState(() {
        _isRunning = false;
        _currentOperation = '';
      });
    }
  }

  Future<void> _recalculateAll() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    setState(() {
      _isRunning = true;
      _currentOperation = 'Recalculating all balances...';
    });

    try {
      _addLog('🔢 Starting comprehensive balance recalculation for company: $uid');
      
      final result = LoanBalanceFixResult(uid: uid, fixedAt: DateTime.now());
      await _loanBalanceFixService._recalculateAllLoanAccountBalances(uid, result);
      
      _addLog('✅ Comprehensive balance recalculation completed');
      _addLog('📊 Results: ${result.accountsRecalculated} accounts recalculated');
      
    } catch (e) {
      _addLog('❌ Error during balance recalculation: $e');
    } finally {
      setState(() {
        _isRunning = false;
        _currentOperation = '';
      });
    }
  }

  Future<void> _runComprehensiveFix() async {
    final uid = _getCompanyUid();
    if (uid.isEmpty) return;

    _addLog('🚀 Starting comprehensive loan balance fix process for company: $uid');
    _addLog('📋 This will fix ALL loan balance calculation issues systematically...');
    
    setState(() {
      _currentOperation = 'Running comprehensive fix...';
    });

    try {
      final result = await _loanBalanceFixService.fixAllLoanBalanceIssues(uid);
      
      if (result.success) {
        _addLog('');
        _addLog('🎉 COMPREHENSIVE LOAN BALANCE FIX COMPLETED SUCCESSFULLY!');
        _addLog('');
        _addLog('📊 FINAL RESULTS SUMMARY:');
        _addLog('   ✅ Account types fixed: ${result.accountsFixed}');
        _addLog('   ✅ Journal entries fixed: ${result.journalEntriesFixed}');
        _addLog('   ✅ Balances synchronized: ${result.balancesSynchronized}');
        _addLog('   ✅ Manual loan entries fixed: ${result.manualLoanEntriesFixed}');
        _addLog('   ✅ Voucher loan entries fixed: ${result.voucherLoanEntriesFixed}');
        _addLog('   ✅ Accounts recalculated: ${result.accountsRecalculated}');
        _addLog('');
        _addLog('💡 EXPECTED RESULTS:');
        _addLog('   • Loan receivable accounts now show POSITIVE balances when loans are approved');
        _addLog('   • Running balances update correctly for all loan transactions');
        _addLog('   • Current balances are synchronized with calculated balances');
        _addLog('   • Manual and voucher-based loan workflows work consistently');
        _addLog('   • All loan accounts follow proper accounting principles');
        _addLog('');
        _addLog('🔍 VERIFICATION STEPS:');
        _addLog('   1. Check loan receivable account balances - should be positive after loan approval');
        _addLog('   2. Verify running balances in transaction history are correct');
        _addLog('   3. Confirm manual loan acceptance shows positive balances');
        _addLog('   4. Test voucher-based loan requests show correct balances');
        _addLog('   5. Ensure all balance calculations follow Asset account principles');
        
      } else {
        _addLog('❌ COMPREHENSIVE FIX FAILED: ${result.errorMessage}');
        
        if (result.errors.isNotEmpty) {
          _addLog('');
          _addLog('📋 DETAILED ERRORS:');
          for (final error in result.errors) {
            _addLog('   • $error');
          }
        }
      }
      
    } catch (e) {
      _addLog('❌ CRITICAL ERROR during comprehensive fix: $e');
    }
  }

  @override
  void dispose() {
    _companyUidController.dispose();
    super.dispose();
  }
}
