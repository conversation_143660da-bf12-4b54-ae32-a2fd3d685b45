import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

/// Test to verify external company bank selection uses Chart of Accounts
/// This test focuses on data model validation for external company integration
void main() {
  group('External Company Bank Selection Tests', () {
    // Mock Chart of Accounts for external company
    late List<ChartOfAccountsModel> externalBankAccounts;

    setUpAll(() {
      // Create mock Chart of Accounts for external company
      externalBankAccounts = [
        ChartOfAccountsModel(
          id: 'ext_bank_001',
          accountName: 'External Company Main Bank',
          accountNumber: '1100',
          category: AccountCategory.assets,
          accountType: AccountType.bank,
          description: 'Main bank account for external company',
          isActive: true,
          balance: 250000.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          uid: 'external_company_uid',
        ),
        ChartOfAccountsModel(
          id: 'ext_cash_001',
          accountName: 'External Company Cash',
          accountNumber: '1000',
          category: AccountCategory.assets,
          accountType: AccountType.cash,
          description: 'Cash account for external company',
          isActive: true,
          balance: 50000.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          uid: 'external_company_uid',
        ),
        ChartOfAccountsModel(
          id: 'ext_current_001',
          accountName: 'External Company Current Assets',
          accountNumber: '1200',
          category: AccountCategory.assets,
          accountType: AccountType.currentAssets,
          description: 'Current assets for external company',
          isActive: true,
          balance: 75000.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          uid: 'external_company_uid',
        ),
        // Non-asset account that should be filtered out
        ChartOfAccountsModel(
          id: 'ext_expense_001',
          accountName: 'External Company Expenses',
          accountNumber: '5000',
          category: AccountCategory.expenses,
          accountType: AccountType.operatingExpenses,
          description: 'Operating expenses for external company',
          isActive: true,
          balance: 0.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          uid: 'external_company_uid',
        ),
      ];
    });

    test('should filter external company accounts to only Asset types', () {
      // Act: Filter accounts to only Asset accounts suitable for payments
      final filteredAccounts = externalBankAccounts.where((account) {
        return account.category == AccountCategory.assets &&
               account.isActive &&
               (account.accountType == AccountType.cash ||
                account.accountType == AccountType.bank ||
                account.accountType == AccountType.currentAssets);
      }).toList();

      // Assert: Should only include Asset accounts
      expect(filteredAccounts, hasLength(3));
      
      // Verify each account is an Asset account
      for (final account in filteredAccounts) {
        expect(account.category, equals(AccountCategory.assets));
        expect(account.isActive, isTrue);
        expect([
          AccountType.cash,
          AccountType.bank,
          AccountType.currentAssets,
        ], contains(account.accountType));
      }

      // Verify expense account is filtered out
      final expenseAccounts = filteredAccounts.where(
        (account) => account.accountType == AccountType.operatingExpenses);
      expect(expenseAccounts, isEmpty);
    });

    test('should validate external company bank account properties', () {
      // Arrange: Get the bank account
      final bankAccount = externalBankAccounts.firstWhere(
        (account) => account.accountType == AccountType.bank);

      // Assert: Bank account should have proper properties
      expect(bankAccount.id, equals('ext_bank_001'));
      expect(bankAccount.accountName, equals('External Company Main Bank'));
      expect(bankAccount.accountNumber, equals('1100'));
      expect(bankAccount.category, equals(AccountCategory.assets));
      expect(bankAccount.accountType, equals(AccountType.bank));
      expect(bankAccount.isActive, isTrue);
      expect(bankAccount.balance, equals(250000.0));
      expect(bankAccount.uid, equals('external_company_uid'));
    });

    test('should validate external company cash account properties', () {
      // Arrange: Get the cash account
      final cashAccount = externalBankAccounts.firstWhere(
        (account) => account.accountType == AccountType.cash);

      // Assert: Cash account should have proper properties
      expect(cashAccount.id, equals('ext_cash_001'));
      expect(cashAccount.accountName, equals('External Company Cash'));
      expect(cashAccount.accountNumber, equals('1000'));
      expect(cashAccount.category, equals(AccountCategory.assets));
      expect(cashAccount.accountType, equals(AccountType.cash));
      expect(cashAccount.isActive, isTrue);
      expect(cashAccount.balance, equals(50000.0));
      expect(cashAccount.uid, equals('external_company_uid'));
    });

    test('should validate external company current assets account', () {
      // Arrange: Get the current assets account
      final currentAssetsAccount = externalBankAccounts.firstWhere(
        (account) => account.accountType == AccountType.currentAssets);

      // Assert: Current assets account should have proper properties
      expect(currentAssetsAccount.id, equals('ext_current_001'));
      expect(currentAssetsAccount.accountName, equals('External Company Current Assets'));
      expect(currentAssetsAccount.accountNumber, equals('1200'));
      expect(currentAssetsAccount.category, equals(AccountCategory.assets));
      expect(currentAssetsAccount.accountType, equals(AccountType.currentAssets));
      expect(currentAssetsAccount.isActive, isTrue);
      expect(currentAssetsAccount.balance, equals(75000.0));
      expect(currentAssetsAccount.uid, equals('external_company_uid'));
    });

    test('should handle account serialization for external companies', () {
      // Arrange: Get a bank account
      final bankAccount = externalBankAccounts.firstWhere(
        (account) => account.accountType == AccountType.bank);

      // Act: Convert to map and back
      final accountMap = bankAccount.toJson();
      final reconstructedAccount = ChartOfAccountsModel.fromJson(accountMap);

      // Assert: Data should be preserved through serialization
      expect(reconstructedAccount.id, equals(bankAccount.id));
      expect(reconstructedAccount.accountName, equals(bankAccount.accountName));
      expect(reconstructedAccount.accountNumber, equals(bankAccount.accountNumber));
      expect(reconstructedAccount.category, equals(bankAccount.category));
      expect(reconstructedAccount.accountType, equals(bankAccount.accountType));
      expect(reconstructedAccount.isActive, equals(bankAccount.isActive));
      expect(reconstructedAccount.balance, equals(bankAccount.balance));
      expect(reconstructedAccount.uid, equals(bankAccount.uid));
    });

    test('should validate account types are suitable for payment sources', () {
      // Arrange: Get asset accounts suitable for payments
      final paymentSourceAccounts = externalBankAccounts.where((account) {
        return account.category == AccountCategory.assets &&
               account.isActive &&
               (account.accountType == AccountType.cash ||
                account.accountType == AccountType.bank ||
                account.accountType == AccountType.currentAssets);
      }).toList();

      // Assert: All accounts should be suitable for payment sources
      expect(paymentSourceAccounts, hasLength(3));
      
      for (final account in paymentSourceAccounts) {
        // Asset accounts are suitable for payment sources (money going out)
        expect(account.category, equals(AccountCategory.assets));
        expect(account.isActive, isTrue);
        expect(account.balance, greaterThanOrEqualTo(0));
        
        // Verify account types are payment-suitable
        expect([
          AccountType.cash,
          AccountType.bank,
          AccountType.currentAssets,
        ], contains(account.accountType));
      }
    });

    test('should sort accounts by account number for consistent display', () {
      // Arrange: Get asset accounts
      final assetAccounts = externalBankAccounts.where((account) {
        return account.category == AccountCategory.assets &&
               account.isActive;
      }).toList();

      // Act: Sort by account number
      assetAccounts.sort((a, b) => a.accountNumber.compareTo(b.accountNumber));

      // Assert: Should be sorted correctly
      expect(assetAccounts, hasLength(3));
      expect(assetAccounts[0].accountNumber, equals('1000')); // Cash
      expect(assetAccounts[1].accountNumber, equals('1100')); // Bank
      expect(assetAccounts[2].accountNumber, equals('1200')); // Current Assets
    });

    test('should validate external company UID consistency', () {
      // Arrange: Get all asset accounts
      final assetAccounts = externalBankAccounts.where((account) {
        return account.category == AccountCategory.assets;
      }).toList();

      // Assert: All accounts should belong to the same external company
      for (final account in assetAccounts) {
        expect(account.uid, equals('external_company_uid'));
      }

      // Verify we have accounts from the external company
      expect(assetAccounts, isNotEmpty);
      expect(assetAccounts.every((account) => account.uid == 'external_company_uid'), isTrue);
    });
  });
}
