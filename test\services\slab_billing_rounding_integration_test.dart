import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/models/slab/formula_variables.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';

void main() {
  group('Slab Billing Rounding Integration Tests', () {
    late InvoiceModel testInvoice;
    late SlabModel testSlab;

    setUp(() {
      testInvoice = InvoiceModel(
        invoiceNumber: 1001,
        invoiceStatus: 'Active',
        tasNumber: 'TAS001',
        productName: 'Test Product',
        numberOfBags: 100,
        weightPerBag: 50.0, // 50 kg per bag = 5 tons total
        customerName: 'Test Customer',
        truckNumber: 'TRK001',
        conveyNoteNumber: 'CN001',
        biltyNumber: 'BLT001',
        consignorName: 'Test Consignor',
        deliveryMode: 'Road',
        districtId: 'DIST001',
        districtName: 'Test District',
        stationId: 'STN001',
        stationName: 'Test Station',
        fromPlaceId: 'PLC001',
        fromPlaceName: 'Test Place',
        distanceInKilometers: 150.0,
        consignorPickUpAddress: 'Test Address',
      );

      testSlab = SlabModel(
        slabId: 'test_slab',
        slabName: 'Test Slab with Rounding',
        startDate: DateTime.now().subtract(const Duration(days: 1)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now(),
        rates: [
          SlabRateModel(
            regionId: 'REG001',
            regionName: 'Test Region',
            districtId: 'DIST001',
            districtName: 'Test District',
            hmtRate:
                2.333, // This will create decimal results that need rounding
            nonFuelRate: 1.5,
          ),
        ],
      );
    });

    group('FlexibleFormulaCalculationService Rounding', () {
      test('should apply rounding to standard formula results', () {
        final formula =
            CalculationFormulaModel.fromJson(FormulaTemplates.standardFormula);

        // Standard formula: totalWeightTons × distanceInKilometers
        // Expected: 5.0 tons × 150.0 km = 750.0 (exact, no rounding needed)
        final result = FlexibleFormulaCalculationService.calculateWithFormula(
          invoice: testInvoice,
          formula: formula,
          customColumnValues: {},
        );

        expect(result, isNotNull);
        expect(result, equals(750.0)); // Should be exact
      });

      test('should apply rounding to markup formula results', () {
        final formula =
            CalculationFormulaModel.fromJson(FormulaTemplates.markupFormula);

        // Markup formula: (totalWeightTons × distanceInKilometers) × 1.1
        // Expected: (5.0 × 150.0) × 1.1 = 750.0 × 1.1 = 825.0 (exact)
        final result = FlexibleFormulaCalculationService.calculateWithFormula(
          invoice: testInvoice,
          formula: formula,
          customColumnValues: {},
        );

        expect(result, isNotNull);
        expect(result, equals(825.0)); // Should be exact
      });

      test('should apply rounding to custom formula with decimal results', () {
        // Create a custom formula that will produce decimal results
        final customFormula = CalculationFormulaModel(
          formulaId: 'decimal_test',
          formulaName: 'Decimal Test Formula',
          steps: [
            FormulaStepModel(
              stepId: 'step1',
              stepName: 'Calculate with Decimal Rate',
              formula: 'totalWeightTons × distanceInKilometers × rateValue',
              resultVariable: 'finalAmount',
              description: 'Test calculation with decimal rate',
            ),
          ],
          finalResultVariable: 'finalAmount',
        );

        // Using rate 2.333 will give: 5.0 × 150.0 × 2.333 = 1749.75
        // Should round to 1750.0 (0.75 > 0.5, so round up)
        final result = FlexibleFormulaCalculationService.calculateWithFormula(
          invoice: testInvoice,
          formula: customFormula,
          customColumnValues: {'rateValue': 2.333},
        );

        expect(result, isNotNull);
        expect(result, equals(1750.0)); // 1749.75 rounded up
      });

      test('should apply rounding rule correctly for 0.5 decimals', () {
        // Create a formula that produces exactly 0.5 decimal
        final customFormula = CalculationFormulaModel(
          formulaId: 'half_test',
          formulaName: 'Half Decimal Test',
          steps: [
            FormulaStepModel(
              stepId: 'step1',
              stepName: 'Calculate Half Decimal',
              formula: 'totalWeightTons × distanceInKilometers × rateValue',
              resultVariable: 'finalAmount',
              description: 'Test calculation that produces 0.5 decimal',
            ),
          ],
          finalResultVariable: 'finalAmount',
        );

        // Using rate 2.334 will give: 5.0 × 150.0 × 2.334 = 1750.5
        // Should round DOWN to 1750.0 (0.5 rounds down per requirements)
        final result = FlexibleFormulaCalculationService.calculateWithFormula(
          invoice: testInvoice,
          formula: customFormula,
          customColumnValues: {'rateValue': 2.334},
        );

        expect(result, isNotNull);
        expect(result, equals(1750.0)); // 1750.5 rounded down
      });
    });

    group('Rounding Examples from Requirements', () {
      test('should match all specified rounding examples', () {
        // Test all the examples from the requirements
        expect(
            MonetaryRounding.roundHalfUp(123.49), equals(123.0)); // round down
        expect(
            MonetaryRounding.roundHalfUp(123.50), equals(123.0)); // round down
        expect(MonetaryRounding.roundHalfUp(123.75), equals(124.0)); // round up
        expect(
            MonetaryRounding.roundHalfUp(123.00), equals(123.0)); // no change
      });

      test('should handle complex billing scenarios with rounding', () {
        // Simulate a complex billing calculation
        final invoiceAmounts = [
          1234.49, // → 1234.0
          567.50, // → 567.0
          890.75, // → 891.0
          123.00, // → 123.0
        ];

        final roundedAmounts =
            invoiceAmounts.map(MonetaryRounding.roundHalfUp).toList();

        expect(roundedAmounts, equals([1234.0, 567.0, 891.0, 123.0]));

        // Total of rounded amounts
        final totalRounded = roundedAmounts.reduce((a, b) => a + b);
        expect(totalRounded, equals(2815.0));

        // This demonstrates that individual rounding then summing
        // can differ from summing then rounding
        final totalBeforeRounding =
            invoiceAmounts.reduce((a, b) => a + b); // 2815.74
        final totalAfterRounding =
            MonetaryRounding.roundHalfUp(totalBeforeRounding); // 2816.0

        expect(totalAfterRounding, equals(2816.0));
        expect(
            totalRounded,
            isNot(equals(
                totalAfterRounding))); // Different approaches give different results
      });
    });

    group('Integration with Slab Calculations', () {
      test('should demonstrate end-to-end rounding in slab billing', () {
        // This test demonstrates how rounding is applied throughout the system

        // 1. Standard calculation without custom formula
        final totalWeightKg =
            testInvoice.numberOfBags * testInvoice.weightPerBag; // 5000 kg
        final totalWeightTons = totalWeightKg / 1000; // 5.0 tons
        final rate = testSlab.rates.first.hmtRate; // 2.333

        // Raw calculation: 5.0 × 150.0 × 2.333 = 1749.75 (approximately due to floating point)
        final rawAmount =
            totalWeightTons * testInvoice.distanceInKilometers * rate;
        expect(rawAmount,
            closeTo(1749.75, 0.01)); // Allow small floating point variance

        // After rounding: 1749.75 → 1750.0 (0.75 > 0.5, round up)
        final roundedAmount = MonetaryRounding.roundHalfUp(rawAmount);
        expect(roundedAmount, equals(1750.0));

        print('✅ Raw calculation: $rawAmount');
        print('✅ Rounded amount: $roundedAmount');
        print('✅ Rounding applied correctly: ${rawAmount != roundedAmount}');
      });

      test('should handle batch calculations with proper rounding', () {
        // Simulate multiple invoices with different amounts
        final invoices = [
          {
            'bags': 100,
            'weight': 50.0,
            'distance': 150.0
          }, // 5 tons × 150 km = 750 base
          {
            'bags': 75,
            'weight': 60.0,
            'distance': 200.0
          }, // 4.5 tons × 200 km = 900 base
          {
            'bags': 120,
            'weight': 45.0,
            'distance': 100.0
          }, // 5.4 tons × 100 km = 540 base
        ];

        final rate = 2.333; // Decimal rate to force rounding
        final roundedAmounts = <double>[];

        for (final invoice in invoices) {
          final tons = (invoice['bags']! * invoice['weight']!) / 1000;
          final rawAmount = tons * invoice['distance']! * rate;
          final rounded = MonetaryRounding.roundHalfUp(rawAmount);
          roundedAmounts.add(rounded);

          print(
              'Invoice: ${tons}t × ${invoice['distance']}km × $rate = $rawAmount → $rounded');
        }

        // Expected calculations:
        // Invoice 1: 5.0 × 150 × 2.333 = 1749.75 → 1750.0
        // Invoice 2: 4.5 × 200 × 2.333 = 2099.7 → 2100.0
        // Invoice 3: 5.4 × 100 × 2.333 = 1259.82 → 1260.0
        expect(roundedAmounts[0], equals(1750.0));
        expect(roundedAmounts[1], equals(2100.0));
        expect(roundedAmounts[2], equals(1260.0));

        final batchTotal = roundedAmounts.reduce((a, b) => a + b);
        expect(batchTotal, equals(5110.0));

        print('✅ Batch total after individual rounding: $batchTotal');
      });
    });
  });
}
