import 'dart:convert';
import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';

class BackupRestoreFirebaseService {
  final FirebaseFirestore _firestore;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  BackupRestoreFirebaseService({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// List of all collections that should be backed up for a company
  List<String> get _collectionsToBackup => [
        // Core business data
        AppCollection.invoicesCollection,
        AppCollection.vouchersCollection,
        AppCollection.billsCollection,

        // Location and regional data
        AppCollection.regionsCollection,
        AppCollection.districtsCollection,
        AppCollection.stationsCollection,
        AppCollection.zonesCollection,
        AppCollection.slabsCollection,

        // Finance and accounting data
        AppCollection.depositCategoriesCollection,
        AppCollection.accountsCollection,
        AppCollection.payersCollection,
        AppCollection.payeesCollection,
        AppCollection.brokersCollection,
        AppCollection.depositsCollection,
        AppCollection.expensesCollection,
        AppCollection.transactionsCollection,
        AppCollection.accountTypesCollection,
        AppCollection.paymentMethodsCollection,
        AppCollection.fuelCardsCollection,
        AppCollection.fuelRatesCollection,
        AppCollection.fuelCardUsageCollection,
        AppCollection.checkUsageCollection,
        AppCollection.loansCollection,
        AppCollection.expenseCategoriesCollection,
        AppCollection.brokerPaymentsCollection,
        AppCollection.brokerTransactionsCollection,

        // Chart of Accounts and Journal Entries (Critical for accounting)
        AppCollection.chartOfAccountsCollection,
        AppCollection.journalEntriesCollection,
        AppCollection.journalEntryLinesCollection,
        AppCollection.accountLedgerCollection,
        AppCollection.fiscalYearsCollection,
        AppCollection.fiscalPeriodsCollection,
        AppCollection.financialReportsCollection,
        AppCollection.agedReceivablesReportsCollection,
        AppCollection.agedPayablesReportsCollection,
        AppCollection.yearEndClosingsCollection,

        // Asset Management
        AppCollection.assetsCollection,
        AppCollection.assetMaintenanceCollection,
        AppCollection.assetAuditTrailCollection,
      ];

  /// Create a complete backup of all company data
  Future<Either<FailureObj, Map<String, dynamic>>> createBackup({
    Function(String)? onProgress,
  }) async {
    try {
      log('Starting backup creation for user: $_uid');

      final Map<String, dynamic> backupData = {
        'metadata': {
          'version': '2.0', // Updated version for comprehensive backup
          'backupFormat': 'comprehensive',
          'createdAt': DateTime.now().toIso8601String(),
          'companyUid': _uid,
          'totalCollections': _collectionsToBackup.length,
          'appVersion': '1.0.0', // Can be updated to match app version
          'backupType': 'full',
          'compatibility': {
            'minAppVersion': '1.0.0',
            'maxAppVersion': '2.0.0',
            'supportsCrossPlatform': true,
            'requiresValidation': true,
          },
          'dataIntegrity': {
            'includesRelationships': true,
            'includesCounters': true,
            'includesMetadata': true,
            'checksumEnabled':
                false, // Can be enabled for additional validation
          },
        },
        'collections': <String, dynamic>{},
        'counters': <String, dynamic>{}, // Will store counter values
        'relationships':
            <String, dynamic>{}, // Will store relationship mappings
      };

      int processedCollections = 0;

      for (String collectionName in _collectionsToBackup) {
        try {
          onProgress?.call('Backing up $collectionName...');

          final collectionData = await _backupCollection(collectionName);
          backupData['collections'][collectionName] = collectionData;

          processedCollections++;
          final progress =
              (processedCollections / _collectionsToBackup.length * 100)
                  .round();
          onProgress?.call('Backed up $collectionName ($progress%)');

          log('Successfully backed up collection: $collectionName with ${collectionData.length} documents');
        } catch (e) {
          log('Error backing up collection $collectionName: $e');
          // Continue with other collections even if one fails
          backupData['collections'][collectionName] = [];
        }
      }

      // Backup counter values for proper restoration
      onProgress?.call('Backing up counter values...');
      await _backupCounters(backupData);

      // Add summary to metadata
      final totalDocuments = (backupData['collections'] as Map<String, dynamic>)
          .values
          .map((collection) => (collection as List).length)
          .fold(0, (total, docCount) => total + docCount);

      backupData['metadata']['totalDocuments'] = totalDocuments;
      backupData['metadata']['completedAt'] = DateTime.now().toIso8601String();

      // Add data integrity summary
      backupData['metadata']['collectionSummary'] =
          _generateCollectionSummary(backupData['collections']);

      log('Backup creation completed. Total documents: $totalDocuments');
      return Right(backupData);
    } catch (e) {
      log('Error creating backup: $e');
      return Left(FailureObj(
        code: 'backup-creation-failed',
        message: 'Failed to create backup: $e',
      ));
    }
  }

  /// Backup a specific collection for the current user
  Future<List<Map<String, dynamic>>> _backupCollection(
      String collectionName) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(collectionName)
          .where('uid', isEqualTo: _uid)
          .get();

      final List<Map<String, dynamic>> documents = [];

      for (QueryDocumentSnapshot doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;

        // Convert Firestore data types to JSON-serializable format
        final convertedData = _convertFirestoreData(data);
        convertedData['_documentId'] = doc.id; // Preserve document ID

        documents.add(convertedData);
      }

      return documents;
    } catch (e) {
      log('Error backing up collection $collectionName: $e');
      rethrow;
    }
  }

  /// Convert Firestore-specific data types to JSON-serializable format
  Map<String, dynamic> _convertFirestoreData(Map<String, dynamic> data) {
    final Map<String, dynamic> converted = {};

    for (String key in data.keys) {
      final value = data[key];

      if (value is Timestamp) {
        converted[key] = {
          '_type': 'timestamp',
          '_value': value.toDate().toIso8601String(),
        };
      } else if (value is GeoPoint) {
        converted[key] = {
          '_type': 'geopoint',
          '_latitude': value.latitude,
          '_longitude': value.longitude,
        };
      } else if (value is DocumentReference) {
        converted[key] = {
          '_type': 'reference',
          '_path': value.path,
        };
      } else if (value is List) {
        converted[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return _convertFirestoreData(item);
          }
          return item;
        }).toList();
      } else if (value is Map<String, dynamic>) {
        converted[key] = _convertFirestoreData(value);
      } else {
        converted[key] = value;
      }
    }

    return converted;
  }

  /// Restore data from a backup file
  Future<Either<FailureObj, String>> restoreFromBackup({
    required Map<String, dynamic> backupData,
    Function(String)? onProgress,
    bool overwriteExisting = true,
  }) async {
    try {
      log('Starting restore operation for user: $_uid');

      // Validate backup data structure
      final validationResult = _validateBackupData(backupData);
      if (validationResult.isLeft()) {
        return validationResult.fold(
          (failure) => Left(failure),
          (_) => Left(
              FailureObj(code: 'unknown', message: 'Unknown validation error')),
        );
      }

      final metadata = backupData['metadata'] as Map<String, dynamic>;
      final collections = backupData['collections'] as Map<String, dynamic>;

      log('Restoring backup created at: ${metadata['createdAt']}');
      log('Total collections to restore: ${collections.length}');

      int processedCollections = 0;
      int totalDocumentsRestored = 0;

      // Clear existing data if overwrite is enabled
      if (overwriteExisting) {
        onProgress?.call('Clearing existing data...');
        await _clearExistingData();
      }

      // Restore each collection
      for (String collectionName in collections.keys) {
        try {
          onProgress?.call('Restoring $collectionName...');

          final collectionData = collections[collectionName] as List<dynamic>;
          final restoredCount =
              await _restoreCollection(collectionName, collectionData);

          totalDocumentsRestored += restoredCount;
          processedCollections++;

          final progress =
              (processedCollections / collections.length * 100).round();
          onProgress?.call(
              'Restored $collectionName: $restoredCount documents ($progress%)');

          log('Successfully restored collection: $collectionName with $restoredCount documents');
        } catch (e) {
          log('Error restoring collection $collectionName: $e');
          // Continue with other collections even if one fails
        }
      }

      // Restore counter values
      onProgress?.call('Restoring counter values...');
      await _restoreCounters(backupData);

      final message = 'Restore completed successfully. '
          'Restored $totalDocumentsRestored documents across $processedCollections collections.';

      log(message);
      return Right(message);
    } catch (e) {
      log('Error during restore operation: $e');
      return Left(FailureObj(
        code: 'restore-operation-failed',
        message: 'Failed to restore backup: $e',
      ));
    }
  }

  /// Validate backup data structure
  Either<FailureObj, bool> _validateBackupData(
      Map<String, dynamic> backupData) {
    try {
      log('Starting comprehensive backup validation...');

      // Check required top-level keys
      if (!backupData.containsKey('metadata') ||
          !backupData.containsKey('collections')) {
        return Left(FailureObj(
          code: 'invalid-backup-structure',
          message:
              'Invalid backup file: Missing required metadata or collections',
        ));
      }

      // Check for new format keys (optional for backward compatibility)
      final hasCounters = backupData.containsKey('counters');
      final hasRelationships = backupData.containsKey('relationships');
      log('Backup format: counters=$hasCounters, relationships=$hasRelationships');

      final metadata = backupData['metadata'];
      if (metadata is! Map<String, dynamic>) {
        return Left(FailureObj(
          code: 'invalid-metadata',
          message: 'Invalid backup file: Metadata must be an object',
        ));
      }

      // Check metadata fields
      if (!metadata.containsKey('version') ||
          !metadata.containsKey('createdAt')) {
        return Left(FailureObj(
          code: 'invalid-metadata-fields',
          message:
              'Invalid backup file: Missing version or createdAt in metadata',
        ));
      }

      final collections = backupData['collections'];
      if (collections is! Map<String, dynamic>) {
        return Left(FailureObj(
          code: 'invalid-collections',
          message: 'Invalid backup file: Collections must be an object',
        ));
      }

      // Validate version compatibility
      final version = metadata['version'] as String?;
      if (version == null || !_isVersionCompatible(version)) {
        return Left(FailureObj(
          code: 'incompatible-version',
          message:
              'Backup file version $version is not compatible with current system',
        ));
      }

      // Validate creation date
      final createdAt = metadata['createdAt'] as String?;
      if (createdAt == null) {
        return Left(FailureObj(
          code: 'invalid-created-date',
          message: 'Invalid backup file: Missing or invalid creation date',
        ));
      }

      try {
        DateTime.parse(createdAt);
      } catch (e) {
        return Left(FailureObj(
          code: 'invalid-date-format',
          message: 'Invalid backup file: Creation date format is invalid',
        ));
      }

      // Validate each collection is a list and contains valid data
      for (String collectionName in collections.keys) {
        final collectionData = collections[collectionName];
        if (collectionData is! List) {
          return Left(FailureObj(
            code: 'invalid-collection-data',
            message:
                'Invalid backup file: Collection $collectionName must be a list',
          ));
        }

        // Validate collection documents
        for (int i = 0; i < collectionData.length; i++) {
          final doc = collectionData[i];
          if (doc is! Map<String, dynamic>) {
            return Left(FailureObj(
              code: 'invalid-document-structure',
              message:
                  'Invalid backup file: Document $i in collection $collectionName must be an object',
            ));
          }

          // Check for required document ID
          if (!doc.containsKey('_documentId')) {
            return Left(FailureObj(
              code: 'missing-document-id',
              message:
                  'Invalid backup file: Document $i in collection $collectionName is missing document ID',
            ));
          }
        }
      }

      // Enhanced validation for new backup format
      final validationResult = _validateEnhancedBackupFormat(backupData);
      if (validationResult.isLeft()) {
        return validationResult;
      }

      // Validate data integrity and relationships
      final integrityResult = _validateDataIntegrity(backupData);
      if (integrityResult.isLeft()) {
        return integrityResult;
      }

      // Validate file size (approximate check)
      final estimatedSize = _estimateBackupSize(backupData);
      if (estimatedSize > 100 * 1024 * 1024) {
        // 100MB limit
        return Left(FailureObj(
          code: 'file-too-large',
          message:
              'Backup file is too large (>100MB). Please contact support for large data imports.',
        ));
      }

      return const Right(true);
    } catch (e) {
      return Left(FailureObj(
        code: 'backup-validation-error',
        message: 'Error validating backup file: $e',
      ));
    }
  }

  /// Check if backup version is compatible with current system
  bool _isVersionCompatible(String version) {
    const supportedVersions = ['1.0'];
    return supportedVersions.contains(version);
  }

  /// Estimate backup file size in bytes
  int _estimateBackupSize(Map<String, dynamic> backupData) {
    try {
      final jsonString = jsonEncode(backupData);
      return jsonString.length;
    } catch (e) {
      return 0;
    }
  }

  /// Clear existing data for the current user
  Future<void> _clearExistingData() async {
    try {
      log('Clearing existing data for user: $_uid');

      for (String collectionName in _collectionsToBackup) {
        try {
          final QuerySnapshot querySnapshot = await _firestore
              .collection(collectionName)
              .where('uid', isEqualTo: _uid)
              .get();

          // Delete documents in batches
          final batch = _firestore.batch();
          int batchCount = 0;

          for (QueryDocumentSnapshot doc in querySnapshot.docs) {
            batch.delete(doc.reference);
            batchCount++;

            // Firestore batch limit is 500 operations
            if (batchCount >= 500) {
              await batch.commit();
              batchCount = 0;
            }
          }

          if (batchCount > 0) {
            await batch.commit();
          }

          log('Cleared ${querySnapshot.docs.length} documents from $collectionName');
        } catch (e) {
          log('Error clearing collection $collectionName: $e');
          // Continue with other collections
        }
      }
    } catch (e) {
      log('Error clearing existing data: $e');
      rethrow;
    }
  }

  /// Restore a specific collection
  Future<int> _restoreCollection(
      String collectionName, List<dynamic> documents) async {
    try {
      if (documents.isEmpty) {
        return 0;
      }

      int restoredCount = 0;
      final batch = _firestore.batch();
      int batchCount = 0;

      for (dynamic docData in documents) {
        if (docData is! Map<String, dynamic>) {
          log('Skipping invalid document data in $collectionName');
          continue;
        }

        // Extract document ID and remove it from data
        final documentId = docData.remove('_documentId') as String?;

        // Convert back from JSON format to Firestore format
        final convertedData = _convertFromJsonData(docData);

        // Ensure the document belongs to current user
        convertedData['uid'] = _uid;

        // Create document reference
        final docRef = documentId != null
            ? _firestore.collection(collectionName).doc(documentId)
            : _firestore.collection(collectionName).doc();

        batch.set(docRef, convertedData);
        batchCount++;
        restoredCount++;

        // Firestore batch limit is 500 operations
        if (batchCount >= 500) {
          await batch.commit();
          batchCount = 0;
        }
      }

      // Commit remaining documents
      if (batchCount > 0) {
        await batch.commit();
      }

      return restoredCount;
    } catch (e) {
      log('Error restoring collection $collectionName: $e');
      rethrow;
    }
  }

  /// Convert JSON data back to Firestore format
  Map<String, dynamic> _convertFromJsonData(Map<String, dynamic> data) {
    final Map<String, dynamic> converted = {};

    for (String key in data.keys) {
      final value = data[key];

      if (value is Map<String, dynamic> && value.containsKey('_type')) {
        final type = value['_type'] as String;

        switch (type) {
          case 'timestamp':
            converted[key] =
                Timestamp.fromDate(DateTime.parse(value['_value'] as String));
            break;
          case 'geopoint':
            converted[key] = GeoPoint(
              value['_latitude'] as double,
              value['_longitude'] as double,
            );
            break;
          case 'reference':
            converted[key] = _firestore.doc(value['_path'] as String);
            break;
          default:
            converted[key] = value;
        }
      } else if (value is List) {
        converted[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return _convertFromJsonData(item);
          }
          return item;
        }).toList();
      } else if (value is Map<String, dynamic>) {
        converted[key] = _convertFromJsonData(value);
      } else {
        converted[key] = value;
      }
    }

    return converted;
  }

  /// Generate backup filename
  String generateBackupFilename(String companyName) {
    final now = DateTime.now();
    final dateStr =
        '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
    final timeStr =
        '${now.hour.toString().padLeft(2, '0')}-${now.minute.toString().padLeft(2, '0')}';

    // Clean company name for filename
    final cleanCompanyName = companyName
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .trim();

    return '${cleanCompanyName}_Backup_${dateStr}_$timeStr.json';
  }

  /// Convert backup data to JSON string
  String backupToJson(Map<String, dynamic> backupData) {
    return jsonEncode(backupData);
  }

  /// Parse JSON string to backup data
  Either<FailureObj, Map<String, dynamic>> parseBackupJson(String jsonString) {
    try {
      final data = jsonDecode(jsonString) as Map<String, dynamic>;
      return Right(data);
    } catch (e) {
      return Left(FailureObj(
        code: 'json-parse-error',
        message: 'Failed to parse backup file: $e',
      ));
    }
  }

  /// Backup counter values for proper restoration
  Future<void> _backupCounters(Map<String, dynamic> backupData) async {
    try {
      final counters = <String, dynamic>{};

      // List of counter document IDs to backup
      final counterIds = [
        AppCollection.invoiceCounter,
        AppCollection.voucherCounter,
        AppCollection.billCounter,
        AppCollection.regionCounter,
        AppCollection.assetCounter,
        AppCollection.journalEntryCounter,
      ];

      for (String counterId in counterIds) {
        try {
          final counterDoc = await _firestore
              .collection('counters')
              .doc('${_uid}_$counterId')
              .get();

          if (counterDoc.exists) {
            counters[counterId] = counterDoc.data();
            log('Backed up counter: $counterId');
          }
        } catch (e) {
          log('Error backing up counter $counterId: $e');
          // Continue with other counters
        }
      }

      backupData['counters'] = counters;
      log('Successfully backed up ${counters.length} counters');
    } catch (e) {
      log('Error backing up counters: $e');
      // Don't fail the entire backup for counter issues
      backupData['counters'] = <String, dynamic>{};
    }
  }

  /// Generate collection summary for data integrity
  Map<String, dynamic> _generateCollectionSummary(
      Map<String, dynamic> collections) {
    final summary = <String, dynamic>{};

    for (String collectionName in collections.keys) {
      final collectionData = collections[collectionName] as List;
      summary[collectionName] = {
        'documentCount': collectionData.length,
        'hasData': collectionData.isNotEmpty,
        'sampleFields': collectionData.isNotEmpty
            ? (collectionData.first as Map<String, dynamic>).keys.toList()
            : [],
      };
    }

    return summary;
  }

  /// Restore counter values during restoration
  Future<void> _restoreCounters(Map<String, dynamic> backupData) async {
    try {
      final counters = backupData['counters'] as Map<String, dynamic>?;
      if (counters == null || counters.isEmpty) {
        log('No counters to restore');
        return;
      }

      for (String counterId in counters.keys) {
        try {
          final counterData = counters[counterId] as Map<String, dynamic>;

          await _firestore
              .collection('counters')
              .doc('${_uid}_$counterId')
              .set(counterData);

          log('Restored counter: $counterId');
        } catch (e) {
          log('Error restoring counter $counterId: $e');
          // Continue with other counters
        }
      }

      log('Successfully restored ${counters.length} counters');
    } catch (e) {
      log('Error restoring counters: $e');
      // Don't fail the entire restore for counter issues
    }
  }

  /// Validate enhanced backup format (version 2.0+)
  Either<FailureObj, bool> _validateEnhancedBackupFormat(
      Map<String, dynamic> backupData) {
    try {
      final metadata = backupData['metadata'] as Map<String, dynamic>;

      // Check for enhanced metadata fields
      if (metadata.containsKey('compatibility')) {
        final compatibility =
            metadata['compatibility'] as Map<String, dynamic>?;
        if (compatibility != null) {
          // Validate cross-platform compatibility
          final supportsCrossPlatform =
              compatibility['supportsCrossPlatform'] as bool? ?? false;
          if (!supportsCrossPlatform) {
            log('Warning: Backup may not support cross-platform import');
          }

          // Check app version compatibility
          final minAppVersion = compatibility['minAppVersion'] as String?;
          final maxAppVersion = compatibility['maxAppVersion'] as String?;
          if (minAppVersion != null && maxAppVersion != null) {
            log('Backup compatible with app versions: $minAppVersion - $maxAppVersion');
          }
        }
      }

      // Validate counters format if present
      if (backupData.containsKey('counters')) {
        final counters = backupData['counters'];
        if (counters is! Map<String, dynamic>) {
          return Left(FailureObj(
            code: 'invalid-counters-format',
            message: 'Invalid backup file: Counters must be an object',
          ));
        }
      }

      // Validate relationships format if present
      if (backupData.containsKey('relationships')) {
        final relationships = backupData['relationships'];
        if (relationships is! Map<String, dynamic>) {
          return Left(FailureObj(
            code: 'invalid-relationships-format',
            message: 'Invalid backup file: Relationships must be an object',
          ));
        }
      }

      return const Right(true);
    } catch (e) {
      return Left(FailureObj(
        code: 'enhanced-validation-error',
        message: 'Error validating enhanced backup format: $e',
      ));
    }
  }

  /// Validate data integrity and critical collections
  Either<FailureObj, bool> _validateDataIntegrity(
      Map<String, dynamic> backupData) {
    try {
      final collections = backupData['collections'] as Map<String, dynamic>;

      // Check for critical collections that should always be present
      final criticalCollections = [
        AppCollection.chartOfAccountsCollection,
        AppCollection.journalEntriesCollection,
      ];

      for (String criticalCollection in criticalCollections) {
        if (!collections.containsKey(criticalCollection)) {
          log('Warning: Critical collection $criticalCollection is missing from backup');
        } else {
          final collectionData = collections[criticalCollection] as List;
          if (collectionData.isEmpty) {
            log('Warning: Critical collection $criticalCollection is empty');
          }
        }
      }

      // Validate Chart of Accounts structure if present
      if (collections.containsKey(AppCollection.chartOfAccountsCollection)) {
        final chartOfAccounts =
            collections[AppCollection.chartOfAccountsCollection] as List;
        for (var account in chartOfAccounts) {
          if (account is Map<String, dynamic>) {
            // Check for required Chart of Accounts fields
            final requiredFields = [
              'accountNumber',
              'accountName',
              'category',
              'accountType'
            ];
            for (String field in requiredFields) {
              if (!account.containsKey(field)) {
                return Left(FailureObj(
                  code: 'invalid-chart-of-accounts',
                  message:
                      'Chart of Accounts entry missing required field: $field',
                ));
              }
            }
          }
        }
      }

      // Validate Journal Entries structure if present
      if (collections.containsKey(AppCollection.journalEntriesCollection)) {
        final journalEntries =
            collections[AppCollection.journalEntriesCollection] as List;
        for (var entry in journalEntries) {
          if (entry is Map<String, dynamic>) {
            // Check for required Journal Entry fields
            final requiredFields = [
              'entryNumber',
              'entryDate',
              'description',
              'totalDebits',
              'totalCredits'
            ];
            for (String field in requiredFields) {
              if (!entry.containsKey(field)) {
                return Left(FailureObj(
                  code: 'invalid-journal-entries',
                  message: 'Journal Entry missing required field: $field',
                ));
              }
            }
          }
        }
      }

      return const Right(true);
    } catch (e) {
      return Left(FailureObj(
        code: 'data-integrity-error',
        message: 'Error validating data integrity: $e',
      ));
    }
  }
}
