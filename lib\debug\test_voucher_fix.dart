import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import '../core/services/voucher_accounting_hook_service.dart';
import '../core/services/voucher_account_setup_service.dart';
import '../models/voucher_model.dart';

/// Simple test to verify voucher integration fixes
class TestVoucherFix {
  static Future<void> runTest() async {
    log('🧪 ========== TESTING VOUCHER INTEGRATION FIXES ==========');
    
    try {
      // Check authentication
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        log('❌ No authenticated user. Please login first.');
        return;
      }
      
      final uid = user.uid;
      log('👤 Testing with user: $uid');
      
      // Step 1: Ensure required accounts exist
      log('🔍 Step 1: Ensuring required accounts exist...');
      final accountsExist = await VoucherAccountSetupService.ensureRequiredAccountsExist();
      if (!accountsExist) {
        log('❌ Failed to ensure required accounts exist');
        return;
      }
      log('✅ Required accounts verified/created');
      
      // Step 2: Create test voucher data
      log('🔍 Step 2: Creating test voucher data...');
      final testVoucherData = _createTestVoucherData(uid);
      log('📋 Test voucher created: ${testVoucherData['voucherNumber']}');
      
      // Step 3: Test VoucherModel conversion
      log('🔍 Step 3: Testing VoucherModel conversion...');
      try {
        final voucher = VoucherModel.fromJson(testVoucherData);
        log('✅ VoucherModel conversion successful');
        log('  - Voucher Number: ${voucher.voucherNumber}');
        log('  - Total Freight: ${voucher.totalFreight}');
        log('  - Broker Fees: ${voucher.brokerFees}');
        log('  - Munshiana Fees: ${voucher.munshianaFees}');
        log('  - Broker Account ID: ${voucher.brokerAccountId ?? 'null (will use default mapping)'}');
        log('  - Munshiana Account ID: ${voucher.munshianaAccountId ?? 'null (will use default mapping)'}');
      } catch (e) {
        log('❌ VoucherModel conversion failed: $e');
        return;
      }
      
      // Step 4: Test hook service call
      log('🔍 Step 4: Testing hook service call...');
      final hookService = VoucherAccountingHookService();
      await hookService.onVoucherCreated(testVoucherData, uid);
      log('✅ Hook service call completed');
      
      log('🎉 ========== VOUCHER INTEGRATION TEST COMPLETED ==========');
      log('✅ All tests passed! Check the logs above for any issues.');
      
    } catch (e) {
      log('❌ Test failed with error: $e');
      log('📋 Stack trace: ${StackTrace.current}');
    }
  }
  
  static Map<String, dynamic> _createTestVoucherData(String uid) {
    final now = DateTime.now();
    final voucherNumber = 'TEST-FIX-${now.millisecondsSinceEpoch}';
    
    return {
      'voucherNumber': voucherNumber,
      'voucherStatus': 'Completed',
      'driverName': 'Test Driver',
      'invoiceTasNumberList': ['TEST-001'],
      'invoiceBiltyNumberList': ['BILTY-001'],
      'weightInTons': 10,
      'departureDate': now.toIso8601String().split('T')[0],
      'productName': 'Test Product',
      'totalNumberOfBags': 100,
      'brokerType': 'Internal',
      'brokerName': 'Test Broker',
      'selectedBroker': 'test-broker-id',
      'brokerFees': 5000.0,
      'munshianaFees': 2000.0,
      'brokerAccount': 'test-broker-account',
      'munshianaAccount': 'test-munshiana-account',
      'driverPhoneNumber': '**********',
      'truckNumber': 'TEST-123',
      'conveyNoteNumber': 'CN-001',
      'totalFreight': 50000.0,
      'companyFreight': 43000.0,
      'settledFreight': 43000.0,
      'paymentTransactions': [],
      // Chart of Accounts fields (null to test fallback mechanism)
      'brokerAccountId': null,
      'munshianaAccountId': null,
      'salesTaxAccountId': null,
      'freightTaxAccountId': null,
      'profitAccountId': null,
      'truckFreightAccountId': null,
      'companyFreightAccountId': null,
      // Tax and profit fields
      'calculatedProfit': 0.0,
      'calculatedTax': 0.0,
      'calculatedFreightTax': 0.0,
      // Required fields
      'brokerList': [],
      'selectedTaxAuthorities': [],
      'uid': uid,
      'createdAt': now.millisecondsSinceEpoch,
    };
  }
}
