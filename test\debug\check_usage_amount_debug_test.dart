import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/payment_transaction_model.dart';
import 'package:logestics/models/voucher_model.dart';

void main() {
  group('Check Usage Amount Debug Tests', () {
    test('should verify check usage amount logic with sample data', () {
      print('🔍 DEBUG: Check Usage Amount Verification');
      print('=' * 50);

      // Create a sample voucher with total freight
      final voucher = VoucherModel(
        voucherNumber: 'V-TEST-001',
        voucherStatus: 'Active',
        departureDate: '2024-01-15',
        driverName: 'Test Driver',
        invoiceTasNumberList: ['TAS-001'],
        invoiceBiltyNumberList: ['BILTY-001'],
        weightInTons: 25,
        productName: 'Test Product',
        totalNumberOfBags: 500,
        brokerType: 'Outsource',
        brokerName: 'Test Broker',
        brokerFees: 5000.0,
        munshianaFees: 3000.0,
        brokerAccount: 'Test Broker Account',
        munshianaAccount: 'Test Munshiana Account',
        driverPhoneNumber: '**********',
        truckNumber: 'TRK-001',
        conveyNoteNumber: 'CN-001',
        totalFreight: 100000.0, // Total voucher freight
        companyFreight: 100000.0,
        settledFreight: 0.0,
      );

      // Create sample payment transactions with different amounts
      final checkPayment1 = PaymentTransactionModel(
        id: 'payment-001',
        voucherId: voucher.voucherNumber,
        method: PaymentMethod.check,
        status: PaymentStatus.partial,
        amount: 30000.0, // Individual check amount
        pendingAmount: 70000.0,
        transactionDate: DateTime.now(),
        checkNumber: 'CHK-001',
        bankName: 'Test Bank',
        notes: 'Partial payment by check',
      );

      final checkPayment2 = PaymentTransactionModel(
        id: 'payment-002',
        voucherId: voucher.voucherNumber,
        method: PaymentMethod.check,
        status: PaymentStatus.partial,
        amount: 25000.0, // Different individual check amount
        pendingAmount: 45000.0,
        transactionDate: DateTime.now(),
        checkNumber: 'CHK-002',
        bankName: 'Test Bank',
        notes: 'Second partial payment by check',
      );

      final accountTransferPayment = PaymentTransactionModel(
        id: 'payment-003',
        voucherId: voucher.voucherNumber,
        method: PaymentMethod.accountTransfer,
        status: PaymentStatus.paid,
        amount: 45000.0, // Remaining amount via transfer
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: 'account-001',
        accountName: 'Test Account',
        notes: 'Final payment via account transfer',
      );

      print('📊 VOUCHER DATA:');
      print('   Voucher Number: ${voucher.voucherNumber}');
      print('   Total Freight: ${voucher.totalFreight}');
      print('   Settled Freight: ${voucher.settledFreight}');

      print('\n💳 PAYMENT TRANSACTIONS:');
      print('1. Check Payment 1:');
      print('   - Amount: ${checkPayment1.amount}');
      print('   - Check Number: ${checkPayment1.checkNumber}');
      print('   - Method: ${checkPayment1.method.name}');

      print('\n2. Check Payment 2:');
      print('   - Amount: ${checkPayment2.amount}');
      print('   - Check Number: ${checkPayment2.checkNumber}');
      print('   - Method: ${checkPayment2.method.name}');

      print('\n3. Account Transfer Payment:');
      print('   - Amount: ${accountTransferPayment.amount}');
      print('   - Method: ${accountTransferPayment.method.name}');

      print('\n✅ EXPECTED CHECK USAGE RECORDS:');
      print(
          'Check Usage 1: Amount = ${checkPayment1.amount} (NOT ${voucher.totalFreight})');
      print(
          'Check Usage 2: Amount = ${checkPayment2.amount} (NOT ${voucher.totalFreight})');
      print('No check usage for account transfer (not a check payment)');

      print('\n🎯 VERIFICATION:');
      print('- Each check usage record should use paymentTransaction.amount');
      print('- Check usage records should NOT use voucher.totalFreight');
      print('- Only check payments should create check usage records');

      // Verify the amounts are different
      expect(checkPayment1.amount, isNot(equals(voucher.totalFreight)));
      expect(checkPayment2.amount, isNot(equals(voucher.totalFreight)));
      expect(checkPayment1.amount, isNot(equals(checkPayment2.amount)));

      print('\n✅ Test data verification passed');
    });

    test('should demonstrate the debugging approach for check usage amounts',
        () {
      print('\n🐛 DEBUGGING CHECK USAGE AMOUNTS');
      print('=' * 50);

      print('STEPS TO DEBUG:');
      print('1. Look for these debug logs in the console:');
      print('   - "🔍 DEBUG: Check payment amount: [amount]"');
      print('   - "🔍 DEBUG: Total voucher freight: [total]"');
      print('   - "🔍 DEBUG: Check number: [number]"');
      print('   - "✅ Creating check usage record with amount: [amount]"');

      print('\n2. Verify the amounts:');
      print(
          '   - Check payment amount should be the individual payment amount');
      print('   - Total voucher freight should be the full voucher amount');
      print('   - Check usage record should use the individual payment amount');

      print('\n3. Check the code locations:');
      print(
          '   - voucher_accounting_hook_service.dart: _createCheckUsageRecord()');
      print('   - add_voucher_controller.dart: _createCheckUsageRecord()');
      print(
          '   - Both should use paymentTransaction.amount (line 882 and 3557)');

      print('\n4. Possible issues to investigate:');
      print('   - PaymentTransactionModel.amount field populated incorrectly');
      print('   - UI displaying wrong data from check usage records');
      print('   - Multiple check usage creation methods being called');
      print('   - Data corruption during save/load process');

      print('\n✅ Use these debugging steps to identify the root cause');
    });

    test('should verify the check usage record creation logic', () {
      print('\n🔧 CHECK USAGE RECORD CREATION LOGIC');
      print('=' * 50);

      print('CURRENT IMPLEMENTATION:');
      print('```dart');
      print('final checkUsageRecord = CheckUsageModel(');
      print('  amount: paymentTransaction.amount, // ✅ CORRECT');
      print('  // ... other fields');
      print(');');
      print('```');

      print('\nINCORRECT IMPLEMENTATION WOULD BE:');
      print('```dart');
      print('final checkUsageRecord = CheckUsageModel(');
      print('  amount: voucher.totalFreight, // ❌ WRONG');
      print('  // ... other fields');
      print(');');
      print('```');

      print('\nVERIFICATION:');
      print('- The code correctly uses paymentTransaction.amount');
      print('- The issue might be elsewhere in the data flow');
      print('- Check the UI that displays check usage records');
      print('- Verify the PaymentTransactionModel creation process');

      print('\n🎯 The check usage creation logic appears correct');
      print('🔍 The issue might be in data population or display');
    });

    test('should simulate the payment transaction creation workflow', () {
      print('\n🔄 PAYMENT TRANSACTION CREATION WORKFLOW');
      print('=' * 50);

      // Simulate voucher data
      final totalFreight = 100000.0;
      final settledFreight = 0.0;
      final pendingAmount = totalFreight - settledFreight;

      print('VOUCHER DATA:');
      print('   Total Freight: $totalFreight');
      print('   Settled Freight: $settledFreight');
      print('   Pending Amount: $pendingAmount');

      print('\nPAYMENT DIALOG WORKFLOW:');
      print('1. Dialog opens with default amount: $pendingAmount');
      print('2. User should change amount to specific check amount');
      print('3. User enters check amount: 30000.0');
      print('4. PaymentTransactionModel created with amount: 30000.0');

      // Simulate the dialog logic
      final userEnteredAmount = 30000.0; // User manually enters this
      final remainingAfterPayment =
          totalFreight - settledFreight - userEnteredAmount;

      print('\nAFTER USER INPUT:');
      print('   User Entered Amount: $userEnteredAmount');
      print('   Remaining After Payment: $remainingAfterPayment');

      print('\nPOSSIBLE ISSUES:');
      print('1. User not changing the default amount');
      print('2. UI not updating the amount field properly');
      print('3. Amount field being reset to default after user input');
      print('4. Multiple payment transactions with same default amount');

      print('\n🔍 INVESTIGATION NEEDED:');
      print('- Check if users are manually entering check amounts');
      print('- Verify the amount field in the payment dialog is working');
      print('- Look for any code that resets amount to total/pending amount');
      print('- Check if multiple payments are using the same default amount');

      // Verify the logic
      expect(userEnteredAmount, isNot(equals(pendingAmount)));
      expect(userEnteredAmount, equals(30000.0));

      print('\n✅ Workflow simulation completed');
    });

    test('should identify potential root causes', () {
      print('\n🎯 POTENTIAL ROOT CAUSES');
      print('=' * 40);

      print('MOST LIKELY CAUSES:');
      print('1. 🔴 User Behavior Issue:');
      print('   - Users not manually entering check amounts');
      print('   - Using default pending amount for all payments');
      print('   - Not realizing they need to change the amount');

      print('\n2. 🟡 UI/UX Issue:');
      print('   - Amount field not clearly editable');
      print('   - Default amount not being cleared when user starts typing');
      print('   - Amount field being reset after user input');

      print('\n3. 🟢 Data Flow Issue:');
      print('   - Amount being overwritten somewhere in the save process');
      print('   - Multiple payment transactions sharing the same amount');
      print('   - Amount calculation logic error');

      print('\nDEBUGGING STEPS:');
      print('1. Add more detailed logging to payment dialog');
      print('2. Log the amount at every step of the creation process');
      print('3. Check if amount field is being modified after user input');
      print('4. Verify that each payment transaction has unique amounts');

      print('\n🔍 RECOMMENDATION:');
      print('Add logging to PaymentTransactionDialog to track:');
      print('- Initial default amount');
      print('- User-entered amount');
      print('- Final amount used in PaymentTransactionModel');

      print('\n✅ Root cause analysis completed');
    });
  });
}
