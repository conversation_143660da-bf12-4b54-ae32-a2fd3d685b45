import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/widgets/loading_indicator.dart';
import 'package:logestics/features/finance/loans/presentation/controllers/loan_requests_controller.dart';
import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:intl/intl.dart';
import 'package:excel/excel.dart' as excel;
import 'package:web/web.dart' as web;
import 'dart:js_interop';
import 'dart:typed_data';

class LoanRequestsExportController extends GetxController {
  final LoanRequestsController requestsController;

  LoanRequestsExportController(this.requestsController);

  final exportType = 'all'.obs;
  final startDate = Rxn<DateTime>();
  final endDate = Rxn<DateTime>();
  final isExporting = false.obs;

  @override
  void onInit() {
    super.onInit();
    final now = DateTime.now();
    startDate.value = DateTime(now.year, now.month, 1);
    endDate.value = now;
  }

  void setExportType(String type) => exportType.value = type;
  void setStartDate(DateTime? date) => startDate.value = date;
  void setEndDate(DateTime? date) => endDate.value = date;

  List<LoanModel> getPreviewData() {
    List<LoanModel> data = [];

    if (exportType.value == 'incoming' || exportType.value == 'all') {
      data.addAll(requestsController.allIncomingRequests);
    }
    if (exportType.value == 'outgoing' || exportType.value == 'all') {
      data.addAll(requestsController.allOutgoingRequests);
    }

    if (startDate.value != null && endDate.value != null) {
      data = data.where((loan) {
        return loan.requestDate
                .isAfter(startDate.value!.subtract(const Duration(days: 1))) &&
            loan.requestDate
                .isBefore(endDate.value!.add(const Duration(days: 1)));
      }).toList();
    }

    data.sort((a, b) => a.requestDate.compareTo(b.requestDate));
    return data;
  }

  String formatDate(DateTime date) => DateFormat('MMM dd, yyyy').format(date);

  Future<void> exportToExcel() async {
    isExporting.value = true;
    try {
      final data = getPreviewData();
      if (data.isEmpty) {
        Get.snackbar('Warning', 'No data to export');
        return;
      }

      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile['Loan Requests'];

      // Headers
      final headers = [
        'Type',
        'From',
        'To',
        'Amount',
        'Status',
        'Request Date',
        'Due Date',
        'Notes'
      ];
      for (int i = 0; i < headers.length; i++) {
        sheet
            .cell(excel.CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
            .value = excel.TextCellValue(headers[i]);
      }

      // Data
      for (int i = 0; i < data.length; i++) {
        final loan = data[i];
        final isIncoming =
            requestsController.allIncomingRequests.contains(loan);

        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 0, rowIndex: i + 1))
            .value = excel.TextCellValue(isIncoming ? 'Incoming' : 'Outgoing');
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 1, rowIndex: i + 1))
            .value = excel.TextCellValue(loan.requestedByName);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 2, rowIndex: i + 1))
            .value = excel.TextCellValue(loan.requestedToName);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 3, rowIndex: i + 1))
            .value = excel.DoubleCellValue(loan.amount);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 4, rowIndex: i + 1))
            .value = excel.TextCellValue(loan.status);
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 5, rowIndex: i + 1))
            .value = excel.TextCellValue(formatDate(loan.requestDate));
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 6, rowIndex: i + 1))
            .value = excel.TextCellValue(formatDate(loan.dueDate));
        sheet
            .cell(excel.CellIndex.indexByColumnRow(
                columnIndex: 7, rowIndex: i + 1))
            .value = excel.TextCellValue(loan.notes ?? '');
      }

      final bytes = excelFile.encode()!;
      final uint8bytes = Uint8List.fromList(bytes);
      final blob = web.Blob(
          [uint8bytes.toJS].toJS,
          web.BlobPropertyBag(
              type:
                  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'));
      final url = web.URL.createObjectURL(blob);
      final anchor = web.HTMLAnchorElement()
        ..href = url
        ..download =
            'loan_requests_${DateFormat('yyyyMMdd').format(DateTime.now())}.xlsx';
      anchor.click();
      web.URL.revokeObjectURL(url);

      Get.back();
      Get.snackbar('Success', 'Loan requests exported successfully');
    } catch (e) {
      Get.snackbar('Error', 'Failed to export: $e');
    } finally {
      isExporting.value = false;
    }
  }
}

class LoanRequestsExportDialog extends StatelessWidget {
  final LoanRequestsController controller;

  const LoanRequestsExportDialog({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);
    final exportController = Get.put(LoanRequestsExportController(controller));

    return Dialog(
      backgroundColor: notifier.getBgColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.file_download,
                  color: const Color(0xFF0f7bf4),
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Export Loan Requests',
                  style: TextStyle(
                    color: notifier.text,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: Icon(Icons.close, color: notifier.text),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Export Type Selection
            Text(
              'Export Type',
              style: TextStyle(
                color: notifier.text,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 12),
            Obx(() => Column(
                  children: [
                    RadioListTile<String>(
                      title: Text(
                        'Incoming Requests Only',
                        style: TextStyle(color: notifier.text),
                      ),
                      subtitle: Text(
                        'Export requests received from others',
                        style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7)),
                      ),
                      value: 'incoming',
                      groupValue: exportController.exportType.value,
                      onChanged: (value) =>
                          exportController.setExportType(value!),
                      activeColor: const Color(0xFF0f7bf4),
                    ),
                    RadioListTile<String>(
                      title: Text(
                        'Outgoing Requests Only',
                        style: TextStyle(color: notifier.text),
                      ),
                      subtitle: Text(
                        'Export requests sent to others',
                        style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7)),
                      ),
                      value: 'outgoing',
                      groupValue: exportController.exportType.value,
                      onChanged: (value) =>
                          exportController.setExportType(value!),
                      activeColor: const Color(0xFF0f7bf4),
                    ),
                    RadioListTile<String>(
                      title: Text(
                        'All Requests',
                        style: TextStyle(color: notifier.text),
                      ),
                      subtitle: Text(
                        'Export both incoming and outgoing requests',
                        style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7)),
                      ),
                      value: 'all',
                      groupValue: exportController.exportType.value,
                      onChanged: (value) =>
                          exportController.setExportType(value!),
                      activeColor: const Color(0xFF0f7bf4),
                    ),
                  ],
                )),

            const SizedBox(height: 24),

            // Date Range Selection
            Text(
              'Date Range',
              style: TextStyle(
                color: notifier.text,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildDateField(
                    'Start Date',
                    exportController.startDate.value,
                    (date) => exportController.setStartDate(date),
                    notifier,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateField(
                    'End Date',
                    exportController.endDate.value,
                    (date) => exportController.setEndDate(date),
                    notifier,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Preview Section
            Obx(() {
              final previewData = exportController.getPreviewData();
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: notifier.text.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: notifier.text.withValues(alpha: 0.1)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Export Preview',
                      style: TextStyle(
                        color: notifier.text,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Total records to export: ${previewData.length}',
                      style: TextStyle(color: notifier.text),
                    ),
                    if (previewData.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Date range: ${exportController.formatDate(previewData.first.requestDate)} - ${exportController.formatDate(previewData.last.requestDate)}',
                        style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7)),
                      ),
                    ],
                  ],
                ),
              );
            }),

            const SizedBox(height: 24),

            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton(
                  onPressed: () => Get.back(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: notifier.text,
                    side:
                        BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                  ),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 16),
                Obx(() => ElevatedButton.icon(
                      onPressed: exportController.isExporting.value
                          ? null
                          : () => exportController.exportToExcel(),
                      icon: exportController.isExporting.value
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: LoadingIndicator(
                                type: LoadingIndicatorType.circular,
                                size: LoadingIndicatorSize.small,
                              ),
                            )
                          : const Icon(Icons.download),
                      label: Text(
                        exportController.isExporting.value
                            ? 'Exporting...'
                            : 'Export to Excel',
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0f7bf4),
                        foregroundColor: Colors.white,
                      ),
                    )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField(
    String label,
    DateTime? selectedDate,
    Function(DateTime?) onDateSelected,
    ColorNotifier notifier,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: notifier.text,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: Get.context!,
              initialDate: selectedDate ?? DateTime.now(),
              firstDate: DateTime(2020),
              lastDate: DateTime.now(),
            );
            if (date != null) {
              onDateSelected(date);
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: notifier.text.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: notifier.text.withValues(alpha: 0.7),
                ),
                const SizedBox(width: 8),
                Text(
                  selectedDate != null
                      ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'
                      : 'Select date',
                  style: TextStyle(
                    color: selectedDate != null
                        ? notifier.text
                        : notifier.text.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
