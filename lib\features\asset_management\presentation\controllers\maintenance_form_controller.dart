import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logestics/repositories/asset/asset_repository.dart';
import 'package:logestics/models/asset/asset_maintenance_model.dart';

class MaintenanceFormController extends GetxController {
  final AssetRepository _assetRepository = Get.find<AssetRepository>();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Form controllers
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController costController = TextEditingController();
  final TextEditingController performedByController = TextEditingController();
  final TextEditingController notesController = TextEditingController();

  // Observable variables
  final RxString selectedMaintenanceType = ''.obs;
  final Rx<DateTime?> maintenanceDate = Rx<DateTime?>(null);
  final RxBool isLoading = false.obs;
  final RxList<PlatformFile> selectedFiles = <PlatformFile>[].obs;

  // Form data
  String assetId = '';
  String assetName = '';
  AssetMaintenanceModel? existingMaintenance;

  @override
  void onInit() {
    super.onInit();
    // Set default maintenance date to today
    maintenanceDate.value = DateTime.now();
  }

  @override
  void onClose() {
    descriptionController.dispose();
    costController.dispose();
    performedByController.dispose();
    notesController.dispose();
    super.onClose();
  }

  void initializeForm({
    required String assetId,
    required String assetName,
    AssetMaintenanceModel? maintenance,
  }) {
    this.assetId = assetId;
    this.assetName = assetName;
    existingMaintenance = maintenance;

    if (maintenance != null) {
      // Populate form with existing data
      descriptionController.text = maintenance.description;
      costController.text = maintenance.cost.toString();
      performedByController.text = maintenance.performedBy;
      notesController.text = maintenance.notes;
      selectedMaintenanceType.value = maintenance.maintenanceType;
      maintenanceDate.value = maintenance.maintenanceDate;
    }
  }

  Future<void> pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
      );

      if (result != null) {
        selectedFiles.addAll(result.files);
      }
    } catch (e) {
      log('Error picking files: $e');
      Get.snackbar(
        'Error',
        'Failed to pick files. Please try again.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> pickImages() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.camera);

      if (image != null) {
        final bytes = await image.readAsBytes();
        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          bytes: bytes,
        );
        selectedFiles.add(platformFile);
      }
    } catch (e) {
      log('Error picking image: $e');
      Get.snackbar(
        'Error',
        'Failed to take photo. Please try again.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void removeFile(PlatformFile file) {
    selectedFiles.remove(file);
  }

  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  String? validatePositiveNumber(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }

    final number = double.tryParse(value);
    if (number == null) {
      return 'Please enter a valid number';
    }

    if (number <= 0) {
      return '$fieldName must be greater than 0';
    }

    return null;
  }

  Future<bool> saveMaintenance() async {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    if (selectedMaintenanceType.value.isEmpty) {
      Get.snackbar(
        'Validation Error',
        'Please select a maintenance type',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    if (maintenanceDate.value == null) {
      Get.snackbar(
        'Validation Error',
        'Please select a maintenance date',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    isLoading.value = true;

    try {
      final now = DateTime.now();
      final maintenance = AssetMaintenanceModel(
        id: existingMaintenance?.id ?? '',
        assetId: assetId,
        assetName: assetName,
        maintenanceDate: maintenanceDate.value!,
        cost: double.parse(costController.text),
        description: descriptionController.text.trim(),
        maintenanceType: selectedMaintenanceType.value,
        performedBy: performedByController.text.trim(),
        notes: notesController.text.trim(),
        attachmentUrls: existingMaintenance?.attachmentUrls ?? [],
        createdAt: existingMaintenance?.createdAt ?? now,
        updatedAt: now,
      );

      // Prepare files for upload
      List<File>? files;
      List<Uint8List>? fileBytes;
      List<String>? fileNames;

      if (selectedFiles.isNotEmpty) {
        if (kIsWeb) {
          // Web platform - use bytes
          fileBytes = [];
          fileNames = [];
          for (final file in selectedFiles) {
            if (file.bytes != null) {
              fileBytes.add(file.bytes!);
              fileNames.add(file.name);
            }
          }
        } else {
          // Mobile platform - use file paths
          files = [];
          for (final file in selectedFiles) {
            if (file.path != null) {
              files.add(File(file.path!));
            }
          }
        }
      }

      final result = existingMaintenance != null
          ? await _assetRepository.updateMaintenance(
              maintenance,
              files: files,
              fileBytes: fileBytes,
              fileNames: fileNames,
            )
          : await _assetRepository.createMaintenance(
              maintenance,
              files: files,
              fileBytes: fileBytes,
              fileNames: fileNames,
            );

      return result.fold(
        (failure) {
          log('Error saving maintenance: ${failure.message}');
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          return false;
        },
        (success) {
          log('Maintenance saved successfully');
          return true;
        },
      );
    } catch (e) {
      log('Unexpected error saving maintenance: $e');
      Get.snackbar(
        'Error',
        'An unexpected error occurred. Please try again.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  void clearForm() {
    descriptionController.clear();
    costController.clear();
    performedByController.clear();
    notesController.clear();
    selectedMaintenanceType.value = '';
    maintenanceDate.value = DateTime.now();
    selectedFiles.clear();
  }
}
