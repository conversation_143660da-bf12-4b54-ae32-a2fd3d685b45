import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../controllers/financial_dashboard_controller.dart';

/// Widget displaying key financial summary metrics
class FinancialSummaryWidget extends StatelessWidget {
  const FinancialSummaryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FinancialDashboardController>();
    final currencyFormatter =
        NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0);

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Financial Summary',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Icon(
                  Icons.account_balance,
                  color: Theme.of(context).primaryColor,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              final summary = controller.financialSummary.value;
              if (summary == null) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              return Column(
                children: [
                  // Revenue and Expenses Row
                  Row(
                    children: [
                      Expanded(
                        child: _buildMetricCard(
                          'Total Revenue',
                          currencyFormatter.format(summary.totalRevenue),
                          Icons.trending_up,
                          Colors.green,
                          context,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildMetricCard(
                          'Total Expenses',
                          currencyFormatter.format(summary.totalExpenses),
                          Icons.trending_down,
                          Colors.red,
                          context,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Net Income (Full Width)
                  _buildMetricCard(
                    'Net Income',
                    currencyFormatter.format(summary.netIncome),
                    summary.netIncome >= 0
                        ? Icons.attach_money
                        : Icons.money_off,
                    summary.netIncome >= 0 ? Colors.green : Colors.red,
                    context,
                    isFullWidth: true,
                  ),
                  const SizedBox(height: 12),

                  // Assets and Liabilities Row
                  Row(
                    children: [
                      Expanded(
                        child: _buildMetricCard(
                          'Total Assets',
                          currencyFormatter.format(summary.totalAssets),
                          Icons.account_balance_wallet,
                          Colors.blue,
                          context,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildMetricCard(
                          'Total Liabilities',
                          currencyFormatter.format(summary.totalLiabilities),
                          Icons.credit_card,
                          Colors.orange,
                          context,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Cash and Ratios Row
                  Row(
                    children: [
                      Expanded(
                        child: _buildMetricCard(
                          'Cash Balance',
                          currencyFormatter.format(summary.cashBalance),
                          Icons.account_balance,
                          Colors.teal,
                          context,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildMetricCard(
                          'Profit Margin',
                          '${summary.profitMargin.toStringAsFixed(1)}%',
                          Icons.percent,
                          summary.profitMargin >= 0 ? Colors.green : Colors.red,
                          context,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Last Updated
                  Text(
                    'As of ${DateFormat('MMM dd, yyyy').format(summary.asOfDate)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
    BuildContext context, {
    bool isFullWidth = false,
  }) {
    return Container(
      width: isFullWidth ? double.infinity : null,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: color,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
          ),
        ],
      ),
    );
  }
}
