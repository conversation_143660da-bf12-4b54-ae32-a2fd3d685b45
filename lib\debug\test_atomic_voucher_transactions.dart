import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/features/voucher/repositories/voucher_repository.dart';
import 'package:logestics/firebase_service/voucher/voucher_crud_firebase_service.dart';
import 'package:logestics/firebase_service/accounting/journal_entry_firebase_service.dart';

/// Test class for verifying atomic transaction implementation in voucher operations
class AtomicVoucherTransactionTest {
  late final VoucherRepositoryImp _voucherRepository;

  AtomicVoucherTransactionTest() {
    _voucherRepository = VoucherRepositoryImp(VoucherCrudFirebaseService());
  }

  /// Test atomic voucher creation with valid data
  Future<void> testAtomicVoucherCreation(String uid) async {
    log('🧪 Starting atomic voucher creation test...');

    try {
      final testVoucherData = _createTestVoucherData(uid);

      log('📝 Test data prepared: ${testVoucherData['voucherNumber']}');

      final result = await _voucherRepository.createVoucher(
        uid: uid,
        voucher: testVoucherData,
      );

      result.fold(
        (failure) {
          log('❌ Atomic voucher creation test FAILED: ${failure.message}');
          log('Error code: ${failure.code}');
        },
        (success) {
          log('✅ Atomic voucher creation test PASSED: ${success.message}');
        },
      );
    } catch (e) {
      log('❌ Atomic voucher creation test EXCEPTION: $e');
    }
  }

  /// Test atomic voucher creation with invalid data (should fail and rollback)
  Future<void> testAtomicVoucherCreationFailure(String uid) async {
    log('🧪 Starting atomic voucher creation failure test...');

    try {
      final testVoucherData = _createInvalidTestVoucherData(uid);

      log('📝 Invalid test data prepared: ${testVoucherData['voucherNumber']}');

      final result = await _voucherRepository.createVoucher(
        uid: uid,
        voucher: testVoucherData,
      );

      result.fold(
        (failure) {
          log('✅ Atomic voucher creation failure test PASSED: Expected failure occurred');
          log('Failure message: ${failure.message}');
          log('Error code: ${failure.code}');
        },
        (success) {
          log('❌ Atomic voucher creation failure test FAILED: Expected failure but got success');
        },
      );
    } catch (e) {
      log('✅ Atomic voucher creation failure test PASSED: Expected exception occurred: $e');
    }
  }

  /// Test atomic voucher update
  Future<void> testAtomicVoucherUpdate(String uid) async {
    log('🧪 Starting atomic voucher update test...');

    try {
      // First create a voucher
      final testVoucherData = _createTestVoucherData(uid);

      final createResult = await _voucherRepository.createVoucher(
        uid: uid,
        voucher: testVoucherData,
      );

      await createResult.fold(
        (failure) async {
          log('❌ Failed to create voucher for update test: ${failure.message}');
        },
        (success) async {
          log('✅ Voucher created for update test');

          // Now update the voucher
          final updatedVoucherData = Map<String, dynamic>.from(testVoucherData);
          updatedVoucherData['brokerFees'] = 150.0; // Change broker fees
          updatedVoucherData['munshianaFees'] = 75.0; // Change munshiana fees

          final updateResult = await _voucherRepository.updateVoucher(
            uid: uid,
            voucher: updatedVoucherData,
          );

          updateResult.fold(
            (failure) {
              log('❌ Atomic voucher update test FAILED: ${failure.message}');
            },
            (success) {
              log('✅ Atomic voucher update test PASSED: ${success.message}');
            },
          );
        },
      );
    } catch (e) {
      log('❌ Atomic voucher update test EXCEPTION: $e');
    }
  }

  /// Test duplicate voucher number handling
  Future<void> testDuplicateVoucherNumber(String uid) async {
    log('🧪 Starting duplicate voucher number test...');

    try {
      final testVoucherData = _createTestVoucherData(uid);

      // Create first voucher
      final firstResult = await _voucherRepository.createVoucher(
        uid: uid,
        voucher: testVoucherData,
      );

      await firstResult.fold(
        (failure) async {
          log('❌ Failed to create first voucher: ${failure.message}');
        },
        (success) async {
          log('✅ First voucher created successfully');

          // Try to create second voucher with same number
          final secondResult = await _voucherRepository.createVoucher(
            uid: uid,
            voucher: testVoucherData, // Same data, same voucher number
          );

          secondResult.fold(
            (failure) {
              if (failure.message.contains('already exists')) {
                log('✅ Duplicate voucher number test PASSED: Correctly rejected duplicate');
              } else {
                log('❌ Duplicate voucher number test FAILED: Wrong error message');
              }
            },
            (success) {
              log('❌ Duplicate voucher number test FAILED: Should have rejected duplicate');
            },
          );
        },
      );
    } catch (e) {
      log('❌ Duplicate voucher number test EXCEPTION: $e');
    }
  }

  /// Test Firebase read-before-write compliance
  Future<void> testFirebaseReadBeforeWrite(String uid) async {
    log('🧪 Starting Firebase read-before-write compliance test...');

    try {
      final testVoucherData = _createTestVoucherData(uid);

      log('📝 Testing read-before-write pattern with voucher: ${testVoucherData['voucherNumber']}');

      final result = await _voucherRepository.createVoucher(
        uid: uid,
        voucher: testVoucherData,
      );

      result.fold(
        (failure) {
          if (failure.message
              .contains('reads to be executed before all writes')) {
            log('❌ Firebase read-before-write test FAILED: Transaction still violates read-before-write rule');
          } else {
            log('✅ Firebase read-before-write test PASSED: No read-before-write violation (other error: ${failure.message})');
          }
        },
        (success) {
          log('✅ Firebase read-before-write test PASSED: Transaction completed successfully');
        },
      );
    } catch (e) {
      if (e.toString().contains('reads to be executed before all writes')) {
        log('❌ Firebase read-before-write test FAILED: Exception indicates read-before-write violation');
      } else {
        log('✅ Firebase read-before-write test PASSED: No read-before-write violation (other exception: $e)');
      }
    }
  }

  /// Test journal entry line items generation
  Future<void> testJournalEntryLineItems(String uid) async {
    log('🧪 Starting journal entry line items test...');

    try {
      final testVoucherData = _createDetailedTestVoucherData(uid);

      log('📝 Testing journal entry line items with voucher: ${testVoucherData['voucherNumber']}');
      log('📊 Test voucher details: Broker=${testVoucherData['brokerFees']}, Munshiana=${testVoucherData['munshianaFees']}, Freight=${testVoucherData['companyFreight']}');

      final result = await _voucherRepository.createVoucher(
        uid: uid,
        voucher: testVoucherData,
      );

      result.fold(
        (failure) {
          log('❌ Journal entry line items test FAILED: ${failure.message}');
        },
        (success) {
          log('✅ Journal entry line items test PASSED: Voucher created successfully');
          log('📋 Check the logs above for detailed journal entry line items');

          // Additional verification: Try to retrieve the created journal entries
          _verifyJournalEntriesCreated(testVoucherData['voucherNumber'], uid);
        },
      );
    } catch (e) {
      log('❌ Journal entry line items test EXCEPTION: $e');
    }
  }

  /// Verify that journal entries were actually created with line items
  Future<void> _verifyJournalEntriesCreated(
      String voucherNumber, String uid) async {
    try {
      log('🔍 Verifying journal entries were created for voucher: $voucherNumber');

      // Wait a moment for the transaction to complete
      await Future.delayed(const Duration(seconds: 2));

      // Query Firebase directly to check if journal entries exist
      final firestore = FirebaseFirestore.instance;
      final journalEntriesQuery = await firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .where('sourceTransactionId', isEqualTo: voucherNumber)
          .where('sourceTransactionType', isEqualTo: 'voucher')
          .get();

      // Also test using the journal entry service to verify the fix
      final journalEntryService = JournalEntryFirebaseService();
      final serviceEntries =
          await journalEntryService.getJournalEntriesBySource(
        voucherNumber,
        'voucher',
      );

      if (journalEntriesQuery.docs.isEmpty) {
        log('❌ VERIFICATION FAILED: No journal entries found for voucher $voucherNumber');
        return;
      }

      log('✅ VERIFICATION SUCCESS: Found ${journalEntriesQuery.docs.length} journal entries for voucher $voucherNumber');
      log('📊 SERVICE VERIFICATION: Found ${serviceEntries.length} journal entries via service');

      // Compare direct Firebase query vs service results
      if (serviceEntries.length != journalEntriesQuery.docs.length) {
        log('⚠️ WARNING: Service returned different number of entries than direct query');
      } else {
        log('✅ SERVICE CONSISTENCY: Service and direct query returned same number of entries');
      }

      for (int i = 0; i < journalEntriesQuery.docs.length; i++) {
        final doc = journalEntriesQuery.docs[i];
        final data = doc.data();
        final lines = data['lines'] as List<dynamic>? ?? [];

        log('📊 Journal Entry ${i + 1}:');
        log('  - ID: ${doc.id}');
        log('  - Entry Number: ${data['entryNumber'] ?? 'Not set'}');
        log('  - Description: ${data['description'] ?? 'No description'}');
        log('  - Total Debits: ${data['totalDebits'] ?? 0}');
        log('  - Total Credits: ${data['totalCredits'] ?? 0}');
        log('  - Line Items: ${lines.length}');

        if (lines.isEmpty) {
          log('  ❌ WARNING: This journal entry has NO line items!');
        } else {
          for (int j = 0; j < lines.length; j++) {
            final line = lines[j];
            log('    📝 Line ${j + 1}: ${line['accountName']} (${line['accountNumber']}) - Debit: ${line['debitAmount']}, Credit: ${line['creditAmount']} - "${line['description']}"');
          }
        }
      }

      // Verify service entries also have line items
      log('\n🔍 VERIFYING SERVICE ENTRIES:');
      for (int i = 0; i < serviceEntries.length; i++) {
        final entry = serviceEntries[i];
        log('📊 Service Entry ${i + 1}:');
        log('  - ID: ${entry.id}');
        log('  - Entry Number: ${entry.entryNumber}');
        log('  - Description: ${entry.description}');
        log('  - Total Debits: ${entry.totalDebits}');
        log('  - Total Credits: ${entry.totalCredits}');
        log('  - Line Items: ${entry.lines.length}');

        if (entry.lines.isEmpty) {
          log('  ❌ WARNING: Service entry has NO line items!');
        } else {
          log('  ✅ SUCCESS: Service entry has ${entry.lines.length} line items');
          for (int j = 0; j < entry.lines.length; j++) {
            final line = entry.lines[j];
            log('    📝 Line ${j + 1}: ${line.accountName} (${line.accountNumber}) - Debit: ${line.debitAmount}, Credit: ${line.creditAmount} - "${line.description}"');
          }
        }
      }
    } catch (e) {
      log('❌ VERIFICATION ERROR: Failed to verify journal entries: $e');
    }
  }

  /// Test comprehensive journal entry creation (voucher + payments)
  Future<void> testPaymentJournalEntryCreation(String uid) async {
    log('🧪 Starting comprehensive journal entry creation test...');

    try {
      // First create a voucher with payments
      final testVoucherData = _createVoucherWithPayments(uid);

      log('📝 Testing comprehensive journal entries with voucher: ${testVoucherData['voucherNumber']}');
      log('💳 Test voucher has ${(testVoucherData['paymentTransactions'] as List).length} payment transactions');
      log('💰 Test voucher has broker fees: ${testVoucherData['brokerFees']}');
      log('💰 Test voucher has company freight: ${testVoucherData['companyFreight']}');

      final result = await _voucherRepository.createVoucher(
        uid: uid,
        voucher: testVoucherData,
      );

      result.fold(
        (failure) {
          log('❌ Comprehensive journal entry test FAILED: ${failure.message}');
        },
        (success) {
          log('✅ Comprehensive journal entry test PASSED: Voucher created successfully');

          // Verify both voucher and payment journal entries were created
          _verifyComprehensiveJournalEntries(
              testVoucherData['voucherNumber'], uid);
        },
      );
    } catch (e) {
      log('❌ Comprehensive journal entry test EXCEPTION: $e');
    }
  }

  /// Verify that payment journal entries were created with line items
  Future<void> _verifyPaymentJournalEntries(
      String voucherNumber, String uid) async {
    try {
      log('🔍 Verifying payment journal entries for voucher: $voucherNumber');

      // Wait for journal entries to be created
      await Future.delayed(const Duration(seconds: 3));

      // Query for payment journal entries
      final firestore = FirebaseFirestore.instance;
      final paymentJournalEntriesQuery = await firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .where('sourceTransactionType', isEqualTo: 'payment')
          .get();

      // Filter for entries related to our voucher
      final voucherPaymentEntries =
          paymentJournalEntriesQuery.docs.where((doc) {
        final data = doc.data();
        final description = data['description'] as String? ?? '';
        return description.contains(voucherNumber);
      }).toList();

      if (voucherPaymentEntries.isEmpty) {
        log('❌ PAYMENT VERIFICATION FAILED: No payment journal entries found for voucher $voucherNumber');
        return;
      }

      log('✅ PAYMENT VERIFICATION SUCCESS: Found ${voucherPaymentEntries.length} payment journal entries for voucher $voucherNumber');

      // Verify each payment journal entry has line items
      for (int i = 0; i < voucherPaymentEntries.length; i++) {
        final doc = voucherPaymentEntries[i];
        final data = doc.data();
        final lines = data['lines'] as List<dynamic>? ?? [];

        log('📊 Payment Journal Entry ${i + 1}:');
        log('  - ID: ${doc.id}');
        log('  - Entry Number: ${data['entryNumber'] ?? 'Not set'}');
        log('  - Description: ${data['description'] ?? 'No description'}');
        log('  - Total Debits: ${data['totalDebits'] ?? 0}');
        log('  - Total Credits: ${data['totalCredits'] ?? 0}');
        log('  - Line Items: ${lines.length}');

        if (lines.isEmpty) {
          log('  ❌ WARNING: Payment journal entry has NO line items!');
        } else {
          log('  ✅ SUCCESS: Payment journal entry has ${lines.length} line items');
          for (int j = 0; j < lines.length; j++) {
            final line = lines[j];
            log('    📝 Line ${j + 1}: ${line['accountName']} (${line['accountNumber']}) - Debit: ${line['debitAmount']}, Credit: ${line['creditAmount']} - "${line['description']}"');
          }
        }
      }
    } catch (e) {
      log('❌ PAYMENT VERIFICATION ERROR: Failed to verify payment journal entries: $e');
    }
  }

  /// Verify that both voucher and payment journal entries were created with line items
  Future<void> _verifyComprehensiveJournalEntries(
      String voucherNumber, String uid) async {
    try {
      log('🔍 Verifying comprehensive journal entries for voucher: $voucherNumber');

      // Wait for journal entries to be created
      await Future.delayed(const Duration(seconds: 5));

      final firestore = FirebaseFirestore.instance;

      // Query for ALL journal entries related to this voucher
      final allJournalEntriesQuery = await firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: uid)
          .get();

      // Filter for entries related to our voucher
      final voucherRelatedEntries = allJournalEntriesQuery.docs.where((doc) {
        final data = doc.data();
        final sourceTransactionId =
            data['sourceTransactionId'] as String? ?? '';
        final description = data['description'] as String? ?? '';
        return sourceTransactionId == voucherNumber ||
            description.contains(voucherNumber);
      }).toList();

      if (voucherRelatedEntries.isEmpty) {
        log('❌ COMPREHENSIVE VERIFICATION FAILED: No journal entries found for voucher $voucherNumber');
        return;
      }

      log('✅ COMPREHENSIVE VERIFICATION SUCCESS: Found ${voucherRelatedEntries.length} journal entries for voucher $voucherNumber');

      // Categorize entries by type
      final voucherEntries = <Map<String, dynamic>>[];
      final paymentEntries = <Map<String, dynamic>>[];

      for (final doc in voucherRelatedEntries) {
        final data = doc.data();
        final sourceTransactionType =
            data['sourceTransactionType'] as String? ?? '';

        if (sourceTransactionType == 'voucher') {
          voucherEntries.add(data);
        } else if (sourceTransactionType == 'payment') {
          paymentEntries.add(data);
        }
      }

      log('📊 ENTRY BREAKDOWN:');
      log('  - Voucher entries: ${voucherEntries.length}');
      log('  - Payment entries: ${paymentEntries.length}');

      // Summary
      final totalLineItems = voucherRelatedEntries.fold<int>(0, (total, doc) {
        final data = doc.data();
        final lines = data['lines'] as List<dynamic>? ?? [];
        return total + lines.length;
      });

      log('\n📈 COMPREHENSIVE VERIFICATION SUMMARY:');
      log('  - Total journal entries: ${voucherRelatedEntries.length}');
      log('  - Total line items: $totalLineItems');
      log('  - Voucher entries: ${voucherEntries.length}');
      log('  - Payment entries: ${paymentEntries.length}');

      if (voucherEntries.isNotEmpty &&
          paymentEntries.isNotEmpty &&
          totalLineItems > 0) {
        log('🎉 COMPREHENSIVE VERIFICATION: ALL TESTS PASSED!');
      } else {
        log('⚠️ COMPREHENSIVE VERIFICATION: Some entries may be missing');
      }
    } catch (e) {
      log('❌ COMPREHENSIVE VERIFICATION ERROR: Failed to verify journal entries: $e');
    }
  }

  /// Run all atomic transaction tests
  Future<void> runAllTests(String uid) async {
    log('🚀 Starting comprehensive atomic transaction tests...');

    await testFirebaseReadBeforeWrite(uid);
    await Future.delayed(const Duration(seconds: 2));

    await testJournalEntryLineItems(uid);
    await Future.delayed(const Duration(seconds: 2));

    await testPaymentJournalEntryCreation(uid);
    await Future.delayed(const Duration(seconds: 2));

    await testAtomicVoucherCreation(uid);
    await Future.delayed(const Duration(seconds: 2));

    await testAtomicVoucherCreationFailure(uid);
    await Future.delayed(const Duration(seconds: 2));

    await testAtomicVoucherUpdate(uid);
    await Future.delayed(const Duration(seconds: 2));

    await testDuplicateVoucherNumber(uid);

    log('🏁 All atomic transaction tests completed');
  }

  /// Create valid test voucher data
  Map<String, dynamic> _createTestVoucherData(String uid) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final voucherNumber = 'TEST-ATOMIC-$timestamp';

    return {
      'voucherNumber': voucherNumber,
      'uid': uid,
      'departureDate': DateTime.now().toIso8601String(),
      'truckNumber': 'TEST-TRUCK-001',
      'driverName': 'Test Driver',
      'brokerType': 'own',
      'brokerFees': 100.0,
      'munshianaFees': 50.0,
      'companyFreight': 1000.0,
      'salesTax': 170.0,
      'netProfit': 680.0,
      'paymentTransactions': [],
      'createdAt': timestamp,
    };
  }

  /// Create invalid test voucher data (missing required fields)
  Map<String, dynamic> _createInvalidTestVoucherData(String uid) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final voucherNumber = 'TEST-INVALID-$timestamp';

    return {
      'voucherNumber': voucherNumber,
      'uid': uid,
      // Missing required fields to trigger validation errors
      'brokerType': 'own',
      'brokerFees': 100.0,
      // Missing departureDate, truckNumber, etc.
    };
  }

  /// Create detailed test voucher data with multiple line items
  Map<String, dynamic> _createDetailedTestVoucherData(String uid) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final voucherNumber = 'TEST-DETAILED-$timestamp';

    return {
      'voucherNumber': voucherNumber,
      'uid': uid,
      'departureDate': DateTime.now().toIso8601String(),
      'truckNumber': 'TEST-TRUCK-DETAILED',
      'driverName': 'Test Driver Detailed',
      'brokerType': 'own',
      'brokerFees': 250.0, // Higher amount to ensure journal entry creation
      'munshianaFees': 125.0, // Higher amount to ensure journal entry creation
      'companyFreight':
          2500.0, // Higher amount to ensure journal entry creation
      'salesTax': 425.0, // 17% of freight
      'netProfit': 1700.0, // Calculated profit
      'paymentTransactions': [],
      'createdAt': timestamp,
      // Add Chart of Accounts fields to ensure proper journal entry generation
      'brokerChartAccountId': 'test-broker-account',
      'munshianaChartAccountId': 'test-munshiana-account',
      'companyFreightChartAccountId': 'test-freight-account',
      'salesTaxChartAccountId': 'test-tax-account',
      'netProfitChartAccountId': 'test-profit-account',
    };
  }

  /// Create test voucher data with payment transactions
  Map<String, dynamic> _createVoucherWithPayments(String uid) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final voucherNumber = 'TEST-PAYMENTS-$timestamp';

    return {
      'voucherNumber': voucherNumber,
      'uid': uid,
      'departureDate': DateTime.now().toIso8601String(),
      'truckNumber': 'TEST-TRUCK-PAYMENTS',
      'driverName': 'Test Driver Payments',
      'brokerType': 'own',
      'brokerFees': 300.0,
      'munshianaFees': 150.0,
      'companyFreight': 3000.0,
      'salesTax': 510.0, // 17% of freight
      'netProfit': 2040.0, // Calculated profit
      'paymentTransactions': [
        {
          'id': 'payment-1-$timestamp',
          'voucherId': voucherNumber,
          'method': 'cash',
          'status': 'paid',
          'amount': 1500.0,
          'pendingAmount': 1500.0,
          'transactionDate': DateTime.now().toIso8601String(),
          'notes': 'Test cash payment',
          'accountId': 'test-cash-account-id',
          'accountName': 'Test Cash Account',
        },
        {
          'id': 'payment-2-$timestamp',
          'voucherId': voucherNumber,
          'method': 'check',
          'status': 'partial',
          'amount': 1000.0,
          'pendingAmount': 500.0,
          'transactionDate': DateTime.now().toIso8601String(),
          'notes': 'Test check payment',
          'checkNumber': 'CHK-TEST-$timestamp',
          'bankName': 'Test Bank',
          'accountId': 'test-bank-account-id',
          'accountName': 'Test Bank Account',
        },
      ],
      'createdAt': timestamp,
    };
  }
}
