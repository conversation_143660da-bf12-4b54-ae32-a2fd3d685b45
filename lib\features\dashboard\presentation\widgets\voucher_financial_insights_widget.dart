import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import '../controllers/dashboard_data_controller.dart';

class VoucherFinancialInsightsWidget extends StatelessWidget {
  const VoucherFinancialInsightsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return GetBuilder<DashboardDataController>(
      builder: (controller) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with date filter
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Financial Insights',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                    ),
                  ),
                  Row(
                    children: [
                      Icon(
                        Icons.account_balance_wallet,
                        color: notifier.text.withOpacity(0.6),
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      _buildDateFilterDropdown(controller),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Content
              Obx(() {
                if (controller.isLoadingVouchers.value) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (controller.voucherError.value.isNotEmpty) {
                  return Center(
                    child: Text(
                      'Error loading data',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 14,
                      ),
                    ),
                  );
                }

                return Column(
                  children: [
                    // Total Profit
                    _buildFinancialCard(
                      'Total Profit',
                      'PKR ${_formatCurrency(controller.totalProfit.value)}',
                      Icons.trending_up,
                      notifier.ligreenColor,
                      Colors.green,
                    ),
                    const SizedBox(height: 16),

                    // Tax breakdown
                    _buildTaxBreakdown(controller),
                    const SizedBox(height: 16),

                    // Total Tax Collected
                    _buildFinancialCard(
                      'Total Tax Collected',
                      'PKR ${_formatCurrency(controller.totalTaxCollected.value)}',
                      Icons.receipt,
                      notifier.liyellowColor,
                      Colors.orange,
                    ),
                  ],
                );
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDateFilterDropdown(DashboardDataController controller) {
    return Obx(() => Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: notifier.text.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: controller.selectedDateFilter.value,
              isDense: true,
              style: TextStyle(
                fontSize: 12,
                color: notifier.text,
              ),
              dropdownColor: notifier.getBgColor,
              items: [
                DropdownMenuItem(value: 'all', child: Text('All Time')),
                DropdownMenuItem(value: 'today', child: Text('Today')),
                DropdownMenuItem(value: 'week', child: Text('This Week')),
                DropdownMenuItem(value: 'month', child: Text('This Month')),
                DropdownMenuItem(
                    value: 'last30days', child: Text('Last 30 Days')),
                DropdownMenuItem(value: 'custom', child: Text('Custom Range')),
              ],
              onChanged: (value) {
                if (value != null) {
                  if (value == 'custom') {
                    _showCustomDatePicker(controller);
                  } else {
                    controller.setDateFilter(value);
                  }
                }
              },
            ),
          ),
        ));
  }

  Widget _buildFinancialCard(
    String title,
    String amount,
    IconData icon,
    Color backgroundColor,
    Color textColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: textColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: textColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: textColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: textColor.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  amount,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: textColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaxBreakdown(DashboardDataController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.liredColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.percent,
                  color: Colors.red,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Tax Breakdown',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Tax calculations table with borders
          _buildTaxCalculationsTable(controller),
        ],
      ),
    );
  }

  /// Build tax calculations table with bordered layout
  Widget _buildTaxCalculationsTable(DashboardDataController controller) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red.withValues(alpha: 0.3), width: 1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Sales Tax row
          _buildTaxTableRow(
            'Sales Tax (6.9%)',
            'PKR ${_formatCurrency(controller.tax46Percent.value)}',
            Colors.red,
            isFirst: true,
          ),
          // Freight Tax row
          _buildTaxTableRow(
            'Freight Tax (15%)',
            'PKR ${_formatCurrency(controller.tax15Percent.value)}',
            Colors.red,
            isLast: true,
          ),
        ],
      ),
    );
  }

  /// Build individual tax table row with borders
  Widget _buildTaxTableRow(
    String label,
    String value,
    Color valueColor, {
    bool isFirst = false,
    bool isLast = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: isLast
              ? BorderSide.none
              : BorderSide(color: Colors.red.withValues(alpha: 0.2), width: 1),
        ),
        color: Colors.white,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.red.withValues(alpha: 0.8),
              ),
            ),
          ),
          Container(
            width: 1,
            height: 20,
            color: Colors.red.withValues(alpha: 0.2),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: Text(
              value,
              textAlign: TextAlign.right,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: valueColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatCurrency(double amount) {
    if (amount >= 1000000) {
      return 'PKR ${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return 'PKR ${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return 'PKR ${amount.toStringAsFixed(0)}';
    }
  }

  void _showCustomDatePicker(DashboardDataController controller) async {
    DateTime? startDate = controller.customStartDate.value;
    DateTime? endDate = controller.customEndDate.value;

    // Set default dates if not already set
    startDate ??= DateTime.now().subtract(const Duration(days: 30));
    endDate ??= DateTime.now();

    await Get.dialog(
      AlertDialog(
        backgroundColor: notifier.getBgColor,
        title: Text(
          'Select Date Range',
          style: TextStyle(color: notifier.text),
        ),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Start Date
                ListTile(
                  title: Text(
                    'Start Date',
                    style: TextStyle(color: notifier.text),
                  ),
                  subtitle: Text(
                    startDate != null
                        ? '${startDate!.day}/${startDate!.month}/${startDate!.year}'
                        : 'Select start date',
                    style:
                        TextStyle(color: notifier.text.withValues(alpha: 0.7)),
                  ),
                  trailing: Icon(Icons.calendar_today, color: notifier.text),
                  onTap: () async {
                    final picked = await showDatePicker(
                      context: context,
                      initialDate: startDate ?? DateTime.now(),
                      firstDate: DateTime(2020),
                      lastDate: DateTime.now(),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: ColorScheme.light(
                              primary: AppColors.primary,
                              onPrimary: Colors.white,
                              surface: notifier.getBgColor,
                              onSurface: notifier.text,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null) {
                      setState(() {
                        startDate = picked;
                      });
                    }
                  },
                ),
                const SizedBox(height: 8),
                // End Date
                ListTile(
                  title: Text(
                    'End Date',
                    style: TextStyle(color: notifier.text),
                  ),
                  subtitle: Text(
                    endDate != null
                        ? '${endDate!.day}/${endDate!.month}/${endDate!.year}'
                        : 'Select end date',
                    style:
                        TextStyle(color: notifier.text.withValues(alpha: 0.7)),
                  ),
                  trailing: Icon(Icons.calendar_today, color: notifier.text),
                  onTap: () async {
                    final picked = await showDatePicker(
                      context: context,
                      initialDate: endDate ?? DateTime.now(),
                      firstDate: startDate ?? DateTime(2020),
                      lastDate: DateTime.now(),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: ColorScheme.light(
                              primary: AppColors.primary,
                              onPrimary: Colors.white,
                              surface: notifier.getBgColor,
                              onSurface: notifier.text,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null) {
                      setState(() {
                        endDate = picked;
                      });
                    }
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (startDate != null && endDate != null) {
                if (startDate!.isAfter(endDate!)) {
                  Get.snackbar(
                    'Invalid Date Range',
                    'Start date cannot be after end date',
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                  return;
                }
                controller.setCustomDateRange(startDate!, endDate!);
                Get.back();
              } else {
                Get.snackbar(
                  'Incomplete Selection',
                  'Please select both start and end dates',
                  backgroundColor: Colors.orange,
                  colorText: Colors.white,
                );
              }
            },
            child: Text('Apply'),
          ),
        ],
      ),
    );
  }
}
