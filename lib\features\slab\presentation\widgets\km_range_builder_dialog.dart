import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/models/slab/km_range_rate_model.dart';
import 'package:logestics/features/slab/presentation/widgets/km_range_builder_widget.dart';
import 'package:logestics/features/home/<USER>/theme.dart';

/// Dialog wrapper for the KM Range Builder
/// Provides a full-screen dialog interface for creating KM range-based formulas
class KmRangeBuilderDialog extends StatefulWidget {
  final List<KmRangeRateModel> initialRanges;
  final Function(String formula, List<KmRangeRateModel> ranges)?
      onFormulaCreated;

  const KmRangeBuilderDialog({
    super.key,
    this.initialRanges = const [],
    this.onFormulaCreated,
  });

  @override
  State<KmRangeBuilderDialog> createState() => _KmRangeBuilderDialogState();
}

class _KmRangeBuilderDialogState extends State<KmRangeBuilderDialog> {
  late ColorNotifier notifier;
  List<KmRangeRateModel> currentRanges = [];
  String generatedFormula = '';
  bool hasValidRanges = false;

  @override
  void initState() {
    super.initState();
    currentRanges = List.from(widget.initialRanges);
  }

  void _onRangesChanged(List<KmRangeRateModel> ranges) {
    setState(() {
      currentRanges = ranges;
      hasValidRanges = ranges.isNotEmpty &&
          KmRangeRateService.validateRanges(ranges).isEmpty;
    });
  }

  void _onFormulaGenerated(String formula) {
    setState(() {
      generatedFormula = formula;
    });
  }

  void _applyFormula() {
    if (hasValidRanges && generatedFormula.isNotEmpty) {
      widget.onFormulaCreated?.call(generatedFormula, currentRanges);
      Get.back();
    }
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return Dialog.fullscreen(
      child: Scaffold(
        backgroundColor: notifier.getBgColor,
        appBar: AppBar(
          backgroundColor: notifier.getBgColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.close, color: notifier.text),
            onPressed: () => Get.back(),
          ),
          title: Text(
            'KM Range Builder',
            style: AppTextStyles.invoiceHeaderStyle.copyWith(
              color: notifier.text,
            ),
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ElevatedButton.icon(
                onPressed: hasValidRanges ? _applyFormula : null,
                icon: const Icon(Icons.check, size: 16),
                label: const Text('Apply Formula'),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      hasValidRanges ? const Color(0xff0f79f3) : Colors.grey,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInstructions(),
              const SizedBox(height: 24),
              KmRangeBuilderWidget(
                initialRanges: currentRanges,
                onRangesChanged: _onRangesChanged,
                onFormulaGenerated: _onFormulaGenerated,
                readOnly: false,
              ),
              const SizedBox(height: 24),
              _buildUsageExample(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xff0f79f3).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border:
            Border.all(color: const Color(0xff0f79f3).withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: const Color(0xff0f79f3),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'How to Use KM Range Builder',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xff0f79f3),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '1. Define your distance ranges by setting "From KM" and "To KM" values\n'
            '2. Set the rate for each range\n'
            '3. Add descriptions to help identify each range\n'
            '4. Use "Load Default Ranges" to start with standard rates\n'
            '5. The system will automatically generate the complex formula for you\n'
            '6. Click "Apply Formula" when you\'re satisfied with your ranges',
            style: TextStyle(
              color: notifier.text,
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageExample() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getHoverColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: notifier.getfillborder),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Example Usage',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: notifier.text,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Example: Create a rate structure where:\n'
            '• 1-40 KM: ₹1196.38 (fixed rate)\n'
            '• 41-80 KM: ₹1196.38 (fixed rate)\n'
            '• 81-120 KM: ₹1196.38 (fixed rate)\n'
            '• 121+ KM: ₹7.68 per KM\n\n'
            'The system will automatically create the complex IF-ELSE formula needed for this calculation.',
            style: TextStyle(
              color: notifier.text,
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
