import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';
// DEBUG IMPORT - COMMENTED OUT FOR PRODUCTION
// import 'package:logestics/debug/firebase_audit_trail_test_view.dart';

import 'package:logestics/core/router/route_guard.dart';
import 'package:logestics/features/authentication/presentation/screens/forget_screen/forget_password_screen.dart';
import 'package:logestics/features/authentication/presentation/screens/root_screen.dart';
import 'package:logestics/features/dashboard/presentation/screens/dashboard_screen/dashboard_screen.dart';
import 'package:logestics/features/finance/expenses/presentation/views/expense_categories_view.dart';
import 'package:logestics/features/finance/expenses/presentation/views/expenses_view.dart';
import 'package:logestics/features/finance/fuel_cards/presentation/views/fuel_card_management_view.dart';
import 'package:logestics/features/finance/loans/presentation/views/loan_active_view.dart';
import 'package:logestics/features/finance/loans/presentation/views/loan_history_view.dart';
import 'package:logestics/features/finance/loans/presentation/views/loan_requests_view.dart';
import 'package:logestics/features/finance/loans/presentation/views/loans_view.dart';
import 'package:logestics/features/finance/brokers/presentation/views/broker_list_view.dart';
import 'package:logestics/features/finance/check_usage/presentation/views/active_checks_view.dart';
import 'package:logestics/features/locations/presentation/zones/presentation/add_zone_view.dart';
import 'package:logestics/features/voucher/presentation/views/add_voucher_view.dart';
import 'package:logestics/features/backup_restore/presentation/views/backup_restore_view.dart';
import 'package:logestics/features/accounting/fiscal_periods/presentation/screens/fiscal_period_screen.dart';
import 'package:logestics/features/accounting/trial_balance/presentation/screens/trial_balance_screen.dart';
import 'package:logestics/features/accounting/profit_loss/presentation/screens/profit_loss_screen.dart';
import 'package:logestics/features/accounting/balance_sheet/presentation/screens/balance_sheet_screen.dart';
import 'package:logestics/features/accounting/cash_flow_statement/presentation/screens/cash_flow_statement_screen.dart';
import 'package:logestics/features/accounting/journal_entries/presentation/screens/journal_entries_screen.dart';
import 'package:logestics/features/accounting/general_ledger/presentation/screens/general_ledger_screen.dart';
import 'package:logestics/features/accounting/dashboard/presentation/screens/financial_dashboard_screen.dart';
import 'package:logestics/features/accounting/aged_reports/presentation/screens/aged_reports_screen.dart';
// DEBUG IMPORT - COMMENTED OUT FOR PRODUCTION
// import 'package:logestics/features/accounting/mock_data/presentation/views/financial_mock_data_view.dart';
import 'package:logestics/features/slab/presentation/views/slab_screen_view.dart';
import 'package:logestics/features/asset_management/presentation/views/asset_list_view.dart';
import 'package:logestics/features/asset_management/presentation/views/asset_detail_view.dart';
import 'package:logestics/features/asset_management/presentation/views/audit_trail_view.dart';

import '../../features/authentication/presentation/screens/login_screen/login_screen.dart';
import '../../features/authentication/presentation/screens/signup_screen/signup_screen.dart';
import '../../features/dashboard/utils/middle_wares/dashboard_auth_middle_were.dart';
import '../../features/home/<USER>/home_page.dart';
import '../../features/invoices/presentation/views/invoice_form_view.dart';
import '../../features/billing/presentation/views/billing_screen_view.dart';
import '../../features/finance/bills/presentation/views/bills_screen_view.dart';

class AppRoutes {
  static const root = '/';
  static const String login = '/login';
  static const String forgotPassword = '/forgot-password';
  static const String signup = '/signup';
  static const String dashboard = '/dashboard';
  static const String home = '/home';
  static const String addInvoice = '/add-invoice';
  static const String billing = '/billing';
  static const String bills = '/bills';
  static const String addVoucher = '/add-voucher';
  static const String addZone = '/add-zone';
  static const String finance = '/finance';
  static const String fuelCardManagement = '/fuel-card-management';
  static const String expenses = '/expenses';
  static const String expenseCategories = '/expense-categories';
  static const String loans = '/finance/loans';
  static const String loanRequests = '/finance/loans/requests';
  static const String backupRestore = '/backup-restore';
  static const String slabs = '/slabs';
  static const String activeLoans = '/finance/loans/active';
  static const String loanHistory = '/finance/loans/history';
  static const String brokers = '/finance/brokers';
  static const String activeChecks = '/finance/checks/active';
  static const String assetManagement = '/asset-management';
  static const String assetDetail = '/asset-detail';
  static const String auditTrail = '/audit-trail';
  static const String chartOfAccounts = '/accounting/chart-of-accounts';
  static const String accountDetails = '/accounting/account-details';
  static const String fiscalPeriods = '/accounting/fiscal-periods';
  static const String trialBalance = '/accounting/trial-balance';
  static const String profitLoss = '/accounting/profit-loss';
  static const String balanceSheet = '/accounting/balance-sheet';
  static const String cashFlowStatement = '/accounting/cash-flow-statement';
  static const String journalEntries = '/accounting/journal-entries';
  static const String generalLedger = '/accounting/general-ledger';
  static const String financialDashboard = '/accounting/dashboard';
  static const String agedReports = '/accounting/aged-reports';
  static const String financialMockData = '/accounting/mock-data';
  static const String firebaseTest = '/firebase-test';
  static const String notFound = '/not-found';

  static final getPages = [
    GetPage(name: root, page: () => const RootScreen()),
    GetPage(
        name: login, page: () => const LoginScreen(), binding: AppBindings()),
    GetPage(
      name: forgotPassword,
      page: () => const ForgetPasswordScreen(),
      binding: AppBindings(),
    ),
    GetPage(
      name: signup,
      page: () => const SignupScreen(),
      binding: AppBindings(),
    ),
    GetPage(
        name: dashboard,
        page: () => DashboardScreen(),
        middlewares: [AuthMiddleware()],
        binding: AppBindings()),
    GetPage(
        name: home,
        page: () => const MyHomePage(),
        middlewares: [AuthMiddleware()],
        binding: AppBindings()),
    GetPage(
      name: addInvoice,
      middlewares: [HomeRouteCheckMiddleware()],
      page: () => InvoiceFormView(),
      binding: AppBindings(),
    ),
    GetPage(
      name: billing,
      middlewares: [HomeRouteCheckMiddleware()],
      page: () => const BillingScreenView(),
      binding: AppBindings(),
    ),
    GetPage(
      name: bills,
      middlewares: [HomeRouteCheckMiddleware()],
      page: () => const BillsScreenView(),
      binding: AppBindings(),
    ),
    GetPage(
      name: addVoucher,
      middlewares: [HomeRouteCheckMiddleware()],
      page: () => AddVoucherView(),
      binding: AppBindings(),
    ),
    GetPage(
      name: addZone,
      page: () => AddZoneView(),
      binding: AppBindings(),
    ),
    GetPage(
      name: fuelCardManagement,
      page: () => const FuelCardManagementView(),
      binding: AppBindings(),
    ),
    GetPage(
      name: expenses,
      page: () => const ExpensesView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: expenseCategories,
      page: () => const ExpenseCategoriesView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: loans,
      page: () => const LoansView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: loanRequests,
      page: () => const LoanRequestsView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: backupRestore,
      page: () => const BackupRestoreView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: slabs,
      page: () => const SlabScreenView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: activeLoans,
      page: () => const LoanActiveView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: loanHistory,
      page: () => const LoanHistoryView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: brokers,
      page: () => const BrokerListView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: activeChecks,
      page: () => const ActiveChecksView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: assetManagement,
      page: () => const AssetListView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: assetDetail,
      page: () => AssetDetailView(assetId: Get.arguments ?? ''),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: auditTrail,
      page: () => const AuditTrailView(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    // Chart of Accounts route removed - now only accessible via drawer system
    // GetPage(
    //   name: chartOfAccounts,
    //   page: () => const ChartOfAccountsView(),
    //   binding: AppBindings(),
    //   middlewares: [AuthMiddleware()],
    // ),
    GetPage(
      name: fiscalPeriods,
      page: () => const FiscalPeriodScreen(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: trialBalance,
      page: () => const TrialBalanceScreen(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: profitLoss,
      page: () => const ProfitLossScreen(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: balanceSheet,
      page: () => const BalanceSheetScreen(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: cashFlowStatement,
      page: () => const CashFlowStatementScreen(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: journalEntries,
      page: () => const JournalEntriesScreen(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: generalLedger,
      page: () => const GeneralLedgerScreen(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: financialDashboard,
      page: () => const FinancialDashboardScreen(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: agedReports,
      page: () => const AgedReportsScreen(),
      binding: AppBindings(),
      middlewares: [AuthMiddleware()],
    ),
    // DEBUG ROUTES - COMMENTED OUT FOR PRODUCTION
    // GetPage(
    //   name: financialMockData,
    //   page: () => const FinancialMockDataView(),
    //   binding: AppBindings(),
    //   middlewares: [AuthMiddleware()],
    // ),
    // GetPage(
    //   name: firebaseTest,
    //   page: () => const FirebaseAuditTrailTestView(),
    //   binding: AppBindings(),
    //   middlewares: [AuthMiddleware()],
    // ),
    // Catch-all route for handling unknown routes
    GetPage(
      name: notFound,
      page: () => const UnknownRoutePage(),
    ),
  ];
}

class HomeRouteCheckMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    try {
      // Check if user is authenticated first
      final authMiddleware = AuthMiddleware();
      final authResult = authMiddleware.redirect(route);
      if (authResult != null) {
        return authResult;
      }

      // Get the list of all registered routes in the app
      final registeredRoutes = Get.routeTree.routes.map((r) => r.name).toList();

      // Check if '/home' exists in the registered routes
      if (!registeredRoutes.contains('/home')) {
        // Redirect to '/home' if it is not a registered route
        return const RouteSettings(name: '/home');
      }

      return null; // Allow access if '/home' is a registered route
    } catch (e) {
      // If any error occurs, redirect to home
      return const RouteSettings(name: '/home');
    }
  }
}
