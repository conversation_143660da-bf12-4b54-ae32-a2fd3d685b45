import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/payment_transaction_model.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/models/voucher_model.dart';
import 'package:logestics/core/services/automatic_journal_entry_service.dart';
import 'package:logestics/firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import 'package:logestics/firebase_service/voucher/voucher_crud_firebase_service.dart';

/// Test to verify that payment journal entries include the 6.9% sales tax entry
/// This test ensures the sales tax is properly calculated and included in payment processing
void main() {
  group('Sales Tax Payment Journal Entry Tests', () {
    late ChartOfAccountsModel bankAccount;
    late ChartOfAccountsModel truckFareAccount;
    late ChartOfAccountsModel salesTaxAccount;
    late VoucherModel testVoucher;
    late PaymentTransactionModel testPayment;

    setUpAll(() {
      // Create mock bank account (Asset)
      bankAccount = ChartOfAccountsModel(
        id: 'bank_001',
        accountName: 'Main Bank Account',
        accountNumber: '1100',
        category: AccountCategory.assets,
        accountType: AccountType.bank,
        description: 'Primary bank account',
        isActive: true,
        balance: 500000.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );

      // Create mock truck fare account (Liability)
      truckFareAccount = ChartOfAccountsModel(
        id: 'truck_fare_001',
        accountName: 'Truck Fare Payable',
        accountNumber: '2100',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        description: 'Truck fare expenses payable',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );

      // Create mock sales tax account (Liability)
      salesTaxAccount = ChartOfAccountsModel(
        id: 'sales_tax_001',
        accountName: 'Sales Tax Payable',
        accountNumber: '2200',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        description: '6.9% sales tax payable',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );

      // Create test voucher with sales tax
      testVoucher = VoucherModel(
        voucherNumber: 'V-TEST-001',
        voucherStatus: 'Active',
        departureDate: DateTime.now().toIso8601String(),
        driverName: 'Test Driver',
        invoiceTasNumberList: ['TAS001'],
        invoiceBiltyNumberList: ['BILTY001'],
        weightInTons: 10,
        productName: 'Test Product',
        totalNumberOfBags: 100,
        brokerType: 'External',
        brokerName: 'Test Broker',
        brokerFees: 1000.0,
        munshianaFees: 500.0,
        brokerAccount: 'Test Broker Account',
        munshianaAccount: 'Test Munshiana Account',
        driverPhoneNumber: '**********',
        truckNumber: 'ABC-123',
        conveyNoteNumber: 'CN001',
        totalFreight: 100000.0, // Total freight amount
        calculatedFreightTax: 6900.0, // 6.9% of total freight
        salesTaxAccountId: salesTaxAccount.id, // Sales tax account configured
      );

      // Create test payment transaction
      testPayment = PaymentTransactionModel(
        id: 'payment_001',
        voucherId: testVoucher.voucherNumber,
        method: PaymentMethod.check,
        status: PaymentStatus.paid,
        amount: 50000.0, // Half of total freight
        pendingAmount: 50000.0,
        transactionDate: DateTime.now(),
        accountId: bankAccount.id,
        accountName: bankAccount.accountName,
      );
    });

    test('should calculate proportional sales tax correctly', () {
      // Arrange
      const totalFreight = 100000.0;
      const paymentAmount = 50000.0;
      const totalSalesTax = 6900.0;

      // Act
      final proportionalSalesTax =
          (paymentAmount / totalFreight) * totalSalesTax;

      // Assert
      expect(proportionalSalesTax, equals(3450.0),
          reason: 'Proportional sales tax should be 50% of total sales tax');
    });

    test('should validate sales tax calculation for different payment amounts',
        () {
      // Test different payment scenarios
      final testCases = [
        {'payment': 25000.0, 'expected': 1725.0}, // 25% payment
        {'payment': 50000.0, 'expected': 3450.0}, // 50% payment
        {'payment': 75000.0, 'expected': 5175.0}, // 75% payment
        {'payment': 100000.0, 'expected': 6900.0}, // 100% payment
      ];

      for (final testCase in testCases) {
        final paymentAmount = testCase['payment'] as double;
        final expectedTax = testCase['expected'] as double;

        final proportionalSalesTax =
            (paymentAmount / testVoucher.totalFreight) *
                testVoucher.calculatedFreightTax;

        expect(proportionalSalesTax, equals(expectedTax),
            reason:
                'Sales tax for payment ${paymentAmount} should be ${expectedTax}');
      }
    });

    test('should validate voucher sales tax configuration', () {
      // Test voucher with sales tax configured
      expect(testVoucher.calculatedFreightTax, greaterThan(0),
          reason: 'Test voucher should have sales tax configured');

      expect(testVoucher.salesTaxAccountId, isNotNull,
          reason: 'Test voucher should have sales tax account configured');

      expect(testVoucher.salesTaxAccountId, equals(salesTaxAccount.id),
          reason: 'Sales tax account ID should match test account');
    });

    test('should validate payment transaction structure', () {
      // Test payment transaction has required fields
      expect(testPayment.voucherId, equals(testVoucher.voucherNumber),
          reason: 'Payment should reference the correct voucher');

      expect(testPayment.amount, greaterThan(0),
          reason: 'Payment amount should be positive');

      expect(testPayment.accountId, isNotNull,
          reason: 'Payment should have source account configured');
    });

    test('should demonstrate sales tax journal entry logic', () {
      // This test demonstrates the logic that would be used in the automatic journal entry service

      // Arrange: Define the sales tax entry creation function (simulating the service logic)
      Map<String, dynamic> createSalesTaxEntry(
          VoucherModel voucher,
          PaymentTransactionModel payment,
          ChartOfAccountsModel salesTaxAccount) {
        // Calculate proportional sales tax
        final proportionalSalesTax = voucher.totalFreight > 0
            ? (payment.amount / voucher.totalFreight) *
                voucher.calculatedFreightTax
            : 0.0;

        // For liability accounts, debit decreases the balance (paying off the liability)
        return {
          'accountId': salesTaxAccount.id,
          'accountName': salesTaxAccount.accountName,
          'debitAmount': proportionalSalesTax,
          'creditAmount': 0.0,
          'description':
              'Sales Tax Payment (6.9%) - Voucher #${payment.voucherId} - ${payment.method.name}',
          'amount': proportionalSalesTax,
        };
      }

      // Act: Create sales tax entry
      final salesTaxEntry =
          createSalesTaxEntry(testVoucher, testPayment, salesTaxAccount);

      // Assert: Verify sales tax entry structure
      expect(salesTaxEntry['accountId'], equals(salesTaxAccount.id));
      expect(salesTaxEntry['accountName'], equals(salesTaxAccount.accountName));
      expect(salesTaxEntry['debitAmount'], equals(3450.0)); // 50% of 6900
      expect(salesTaxEntry['creditAmount'], equals(0.0));
      expect(
          salesTaxEntry['description'], contains('Sales Tax Payment (6.9%)'));
      expect(salesTaxEntry['description'], contains(testVoucher.voucherNumber));
      expect(salesTaxEntry['description'], contains(testPayment.method.name));
    });

    test(
        'should handle voucher without sales tax account ID (fallback to settings)',
        () {
      // This test simulates the scenario where voucher.salesTaxAccountId is null
      // and the system should fall back to tax payable account from settings

      // Arrange: Create voucher without sales tax account ID
      final voucherWithoutSalesTaxAccount = VoucherModel(
        voucherNumber: 'V-TEST-002',
        voucherStatus: 'Active',
        departureDate: DateTime.now().toIso8601String(),
        driverName: 'Test Driver',
        invoiceTasNumberList: ['TAS002'],
        invoiceBiltyNumberList: ['BILTY002'],
        weightInTons: 5,
        productName: 'Test Product',
        totalNumberOfBags: 50,
        brokerType: 'External',
        brokerName: 'Test Broker',
        brokerFees: 500.0,
        munshianaFees: 250.0,
        brokerAccount: 'Test Broker Account',
        munshianaAccount: 'Test Munshiana Account',
        driverPhoneNumber: '**********',
        truckNumber: 'XYZ-456',
        conveyNoteNumber: 'CN002',
        totalFreight: 50000.0, // Total freight amount
        calculatedFreightTax: 3450.0, // 6.9% of total freight
        salesTaxAccountId: null, // No sales tax account configured in voucher
      );

      // Define the account selection logic (simulating the service logic)
      ChartOfAccountsModel? selectSalesTaxAccount(VoucherModel voucher) {
        // First try voucher's sales tax account
        if (voucher.salesTaxAccountId != null &&
            voucher.salesTaxAccountId!.isNotEmpty) {
          return salesTaxAccount; // Would fetch from service in real implementation
        }

        // Fall back to tax payable account from settings
        return salesTaxAccount; // Simulating settings fallback
      }

      // Act: Select sales tax account
      final selectedAccount =
          selectSalesTaxAccount(voucherWithoutSalesTaxAccount);

      // Assert: Should fall back to settings account
      expect(selectedAccount, isNotNull,
          reason: 'Should fall back to tax payable account from settings');
      expect(selectedAccount?.id, equals(salesTaxAccount.id),
          reason: 'Should use the tax payable account from settings');
    });

    test('should validate 6.9% tax rate in descriptions and calculations', () {
      // Verify that all tax-related descriptions use 6.9% rate
      const expectedTaxRate = 6.9;
      const totalFreight = 100000.0;
      const expectedSalesTax = totalFreight * (expectedTaxRate / 100);

      // Test calculation (use closeTo for floating point precision)
      expect(expectedSalesTax, closeTo(6900.0, 0.01),
          reason: '6.9% of 100,000 should be 6,900');

      // Test description format
      const description =
          'Sales Tax Payment (6.9%) - Voucher #V-TEST-001 - check';
      expect(description, contains('6.9%'),
          reason: 'Description should contain 6.9% tax rate');
      expect(description, isNot(contains('4.6%')),
          reason: 'Description should not contain old 4.6% tax rate');
    });
  });
}
