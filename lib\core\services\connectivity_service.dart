import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'offline_state_service.dart';

/// Enumeration for connectivity status
enum ConnectivityStatus { online, offline, unknown }

/// Service to monitor network connectivity and manage offline/online state
class ConnectivityService extends GetxController {
  static ConnectivityService get instance => Get.find<ConnectivityService>();

  final Connectivity _connectivity = Connectivity();
  final Rx<ConnectivityStatus> _status = ConnectivityStatus.unknown.obs;

  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  Timer? _connectionTestTimer;

  // Public getters
  ConnectivityStatus get status => _status.value;
  bool get isOnline => _status.value == ConnectivityStatus.online;
  bool get isOffline => _status.value == ConnectivityStatus.offline;
  Stream<ConnectivityStatus> get statusStream => _status.stream;

  // Configuration
  static const Duration _connectionTestInterval = Duration(seconds: 30);
  static const Duration _connectionTestTimeout = Duration(seconds: 10);

  @override
  void onInit() {
    super.onInit();
    _initConnectivity();
    _startConnectivityMonitoring();
    _startPeriodicConnectionTest();
  }

  @override
  void onClose() {
    _connectivitySubscription?.cancel();
    _connectionTestTimer?.cancel();
    super.onClose();
  }

  /// Initialize connectivity status
  Future<void> _initConnectivity() async {
    try {
      final results = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(results);
      log('ConnectivityService: Initial connectivity status: ${_status.value}');
    } catch (e) {
      log('ConnectivityService: Failed to get initial connectivity: $e');
      // Set a default status based on platform
      if (kIsWeb) {
        // For web, assume online unless proven otherwise
        _status.value = ConnectivityStatus.online;
      } else {
        // For mobile/desktop, assume offline if we can't check
        _status.value = ConnectivityStatus.offline;
      }
    }
  }

  /// Start monitoring connectivity changes
  void _startConnectivityMonitoring() {
    try {
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _updateConnectionStatus,
        onError: (error) {
          log('ConnectivityService: Error in connectivity stream: $error');
          // Set offline status on stream error
          _status.value = ConnectivityStatus.offline;
        },
      );
    } catch (e) {
      log('ConnectivityService: Failed to start connectivity monitoring: $e');
      // Continue without monitoring if it fails
    }
  }

  /// Start periodic connection testing for more accurate status
  void _startPeriodicConnectionTest() {
    try {
      _connectionTestTimer = Timer.periodic(_connectionTestInterval, (_) {
        _testInternetConnection().catchError((error) {
          log('ConnectivityService: Periodic connection test error: $error');
          return false; // Return false on error
        });
      });
    } catch (e) {
      log('ConnectivityService: Failed to start periodic connection test: $e');
      // Continue without periodic testing if it fails
    }
  }

  /// Update connection status based on connectivity results
  Future<void> _updateConnectionStatus(List<ConnectivityResult> results) async {
    final previousStatus = _status.value;

    // Check if any connection type is available
    final hasConnection = results.any((result) =>
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.ethernet ||
        result == ConnectivityResult.vpn);

    if (!hasConnection) {
      _status.value = ConnectivityStatus.offline;
    } else {
      // Test actual internet connectivity
      final isReachable = await _testInternetConnection();
      _status.value =
          isReachable ? ConnectivityStatus.online : ConnectivityStatus.offline;
    }

    // Log status changes
    if (previousStatus != _status.value) {
      log('ConnectivityService: Status changed from $previousStatus to ${_status.value}');
      _handleStatusChange(previousStatus, _status.value);
    }
  }

  /// Test actual internet connectivity by attempting to reach a reliable host
  Future<bool> _testInternetConnection() async {
    try {
      // Use platform-specific connectivity testing
      if (kIsWeb) {
        // For web platform, assume connectivity if we have a connection type
        return true;
      } else {
        // For mobile/desktop platforms, use InternetAddress.lookup
        final result = await InternetAddress.lookup('google.com')
            .timeout(_connectionTestTimeout);
        return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      }
    } catch (e) {
      log('ConnectivityService: Internet connection test failed: $e');
      // If test fails, assume we're offline
      return false;
    }
  }

  /// Handle connectivity status changes
  void _handleStatusChange(
      ConnectivityStatus previous, ConnectivityStatus current) {
    if (previous == ConnectivityStatus.offline &&
        current == ConnectivityStatus.online) {
      _onConnectivityRestored();
    } else if (previous == ConnectivityStatus.online &&
        current == ConnectivityStatus.offline) {
      _onConnectivityLost();
    }
  }

  /// Called when connectivity is restored
  void _onConnectivityRestored() {
    log('ConnectivityService: Connectivity restored - triggering sync');

    // Notify offline state service if available
    try {
      if (Get.isRegistered<OfflineStateService>()) {
        Get.find<OfflineStateService>().onConnectivityRestored();
      }
    } catch (e) {
      log('ConnectivityService: Failed to notify offline state service: $e');
    }
  }

  /// Called when connectivity is lost
  void _onConnectivityLost() {
    log('ConnectivityService: Connectivity lost - entering offline mode');

    // Notify offline state service if available
    try {
      if (Get.isRegistered<OfflineStateService>()) {
        Get.find<OfflineStateService>().onConnectivityLost();
      }
    } catch (e) {
      log('ConnectivityService: Failed to notify offline state service: $e');
    }
  }

  /// Force refresh connectivity status
  Future<void> refreshStatus() async {
    try {
      final results = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(results);
    } catch (e) {
      log('ConnectivityService: Failed to refresh status: $e');
    }
  }

  /// Wait for online connectivity with timeout
  Future<bool> waitForOnline(
      {Duration timeout = const Duration(minutes: 5)}) async {
    if (isOnline) return true;

    try {
      await statusStream
          .where((status) => status == ConnectivityStatus.online)
          .first
          .timeout(timeout);
      return true;
    } catch (e) {
      log('ConnectivityService: Timeout waiting for online connectivity: $e');
      return false;
    }
  }

  /// Check if specific operation should proceed based on connectivity
  bool shouldProceedOffline(String operationType) {
    // Define which operations can proceed offline
    const offlineCapableOperations = {
      'voucher_create',
      'journal_entry_create',
      'balance_update',
      'expense_create',
      'deposit_create',
    };

    return offlineCapableOperations.contains(operationType);
  }
}
