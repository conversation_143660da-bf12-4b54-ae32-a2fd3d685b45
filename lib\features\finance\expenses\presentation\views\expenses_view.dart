import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/widgets/header_secondary.dart';

import 'package:logestics/models/finance/expense_category_model.dart';
import 'package:logestics/features/finance/expenses/presentation/controllers/expense_controller.dart';
import 'package:logestics/core/utils/widgets/expense_filters.dart';

import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/features/finance/expenses/presentation/views/expenses_pdf_generation_dialog.dart';
import 'package:logestics/features/voucher/presentation/widgets/chart_of_accounts_dropdown.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

class ExpensesView extends StatefulWidget {
  const ExpensesView({super.key});

  @override
  State<ExpensesView> createState() => _ExpensesViewState();
}

class _ExpensesViewState extends State<ExpensesView> {
  late final ExpenseController expenseController;

  @override
  void initState() {
    super.initState();
    // Use the controller from GetX bindings instead of creating a new one
    expenseController = Get.find<ExpenseController>();
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Expenses',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          HeaderSecondary(
                            option1: AppStrings.dashboard,
                            option2: AppStrings.system,
                            option3: 'Expenses',
                            notifier: notifier,
                          ),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Expenses',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          HeaderSecondary(
                            option1: AppStrings.dashboard,
                            option2: AppStrings.system,
                            option3: 'Expenses',
                            notifier: notifier,
                          ),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: _buildExpensesList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildExpensesList() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        InkWell(
                          onTap: () => expenseController.openDrawer(),
                          child: Text(
                            'Add New Expense',
                            style: AppTextStyles.addNewInvoiceStyle,
                          ),
                        ),
                        const SizedBox(width: 20),
                        ElevatedButton.icon(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (context) =>
                                  const ExpensesPDFGenerationDialog(),
                            );
                          },
                          icon: const Icon(Icons.picture_as_pdf, size: 18),
                          label: const Text('Export PDF'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      width: Get.width < 850 ? Get.width / 2 : Get.width / 3.5,
                      child: TextField(
                        controller: expenseController.searchController,
                        decoration: InputDecoration(
                          hintText: 'Search expenses...',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.filter_list,
                        color: notifier.text,
                      ),
                      onPressed: () => expenseController.toggleFilter(),
                    ),
                  ],
                ),
              ),
              // Filter section
              Obx(() => expenseController.showFilter.value
                  ? ExpenseFilters(
                      controller: expenseController,
                    )
                  : const SizedBox.shrink()),
              const SizedBox(height: 20),
              Expanded(
                child: SizedBox(
                  width: Get.width,
                  child: Obx(() {
                    if (expenseController.isLoading.value) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    return _buildExpensesTable();
                  }),
                ),
              ),
              // Pagination
              Obx(() => PaginationWidget(
                    currentPage: expenseController.currentPage.value,
                    totalPages: expenseController.totalPages,
                    itemsPerPage: expenseController.itemsPerPage.value,
                    onPageChanged: (page) =>
                        expenseController.setCurrentPage(page),
                    onItemsPerPageChanged: (count) =>
                        expenseController.setItemsPerPage(count),
                  )),
            ],
          ),
        ),
        // Add/Edit Expense Drawer
        Obx(() {
          if (!expenseController.isDrawerOpen.value) {
            return const SizedBox.shrink();
          }

          return Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: Get.width < 650 ? Get.width : 400,
            child: Container(
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Form(
                key: expenseController.formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Add New Expense',
                            style: AppTextStyles.titleStyle.copyWith(
                              color: notifier.text,
                            ),
                          ),
                          IconButton(
                            onPressed: expenseController.closeDrawer,
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: expenseController.titleController,
                        decoration: InputDecoration(
                          labelText: 'Title/Description',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        validator: expenseController.validateTitle,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: expenseController.amountController,
                        decoration: InputDecoration(
                          labelText: 'Amount (PKR)',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        keyboardType: TextInputType.number,
                        validator: expenseController.validateAmount,
                      ),
                      const SizedBox(height: 16),
                      _buildSourceAccountDropdown(),
                      const SizedBox(height: 16),
                      _buildDestinationAccountDropdown(),
                      const SizedBox(height: 16),
                      _buildCategoryDropdown(),
                      const SizedBox(height: 16),
                      _buildPayeeDropdown(),
                      const SizedBox(height: 16),
                      // Date Picker
                      InkWell(
                        onTap: () => expenseController.selectDate(context),
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: 'Date',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            filled: true,
                            fillColor: notifier.textFileColor,
                          ),
                          child: Obx(() => Text(
                                expenseController.formatDate(
                                    expenseController.selectedDate.value),
                                style: TextStyle(
                                  color: notifier.text,
                                ),
                              )),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: expenseController.referenceController,
                        decoration: InputDecoration(
                          labelText: 'Reference Number',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: expenseController.notesController,
                        decoration: InputDecoration(
                          labelText: 'Notes',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: notifier.textFileColor,
                        ),
                        maxLines: 3,
                      ),
                      const SizedBox(height: 24),
                      Obx(() => SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: expenseController.isLoading.value
                                  ? null
                                  : expenseController.createExpense,
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: expenseController.isLoading.value
                                  ? const CircularProgressIndicator()
                                  : const Text('Save Expense'),
                            ),
                          )),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildExpensesTable() {
    return Obx(() {
      if (expenseController.paginatedExpenses.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.receipt_long,
                  size: 48,
                  color: notifier.text.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No expenses found',
                  style: TextStyle(
                    fontSize: 18,
                    color: notifier.text.withValues(alpha: 0.7),
                  ),
                ),
                if (expenseController.searchQuery.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      expenseController.searchController.text = '';
                    },
                    child: const Text('Clear search'),
                  ),
              ],
            ),
          ),
        );
      }

      return ListView(
        shrinkWrap: true,
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 15),
            decoration: BoxDecoration(
              color: notifier.getcardColor,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Table(
              border: TableBorder(
                horizontalInside: BorderSide(
                  color: notifier.getfillborder,
                ),
              ),
              children: [
                TableRow(
                  decoration: BoxDecoration(
                    color: notifier.getHoverColor,
                  ),
                  children: [
                    DataTableCell(
                      text: 'S.No',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: notifier.text,
                      ),
                    ),
                    DataTableCell(
                      text: 'Title',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: notifier.text,
                      ),
                    ),
                    DataTableCell(
                      text: 'Amount',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: notifier.text,
                      ),
                    ),
                    DataTableCell(
                      text: 'Account',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: notifier.text,
                      ),
                    ),
                    DataTableCell(
                      text: 'Category',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: notifier.text,
                      ),
                    ),
                    DataTableCell(
                      text: 'Date',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: notifier.text,
                      ),
                    ),
                    DataTableCell(
                      text: 'Actions',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: notifier.text,
                      ),
                    ),
                  ],
                ),
                ...expenseController.paginatedExpenses
                    .asMap()
                    .entries
                    .map((entry) {
                  final i = entry.key;
                  final expense = entry.value;
                  return TableRow(
                    children: [
                      DataTableCell(
                        text: ((expenseController.currentPage.value - 1) *
                                    expenseController.itemsPerPage.value +
                                i +
                                1)
                            .toString(),
                        style: TextStyle(color: notifier.text),
                      ),
                      DataTableCell(
                        text: expense.title,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: notifier.text,
                        ),
                      ),
                      DataTableCell(
                        text: expenseController.formatCurrency(expense.amount),
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.red[700],
                        ),
                      ),
                      DataTableCell(
                        text: expense.accountName,
                        style: TextStyle(color: notifier.text),
                      ),
                      DataTableCell(
                        text: expense.categoryName,
                        style: TextStyle(color: Colors.blue[700]),
                      ),
                      DataTableCell(
                        text: expenseController.formatDate(expense.createdAt),
                        style: TextStyle(color: notifier.text),
                      ),
                      DataTableActionsCell(
                        menuItems: [
                          DataTablePopupMenuItem(
                            text: 'Delete',
                            icon: Icons.delete_outline,
                            textColor: AppColors.darkOnSurfaceVariant,
                            isDanger: false,
                            onTap: () {},
                          ),
                        ],
                      ),
                    ],
                  );
                }),
              ],
            ),
          ),
        ],
      );
    });
  }

  Widget _buildSourceAccountDropdown() {
    return Obx(() {
      return ChartOfAccountsDropdown(
        labelText: 'Source Account (Money Decreases From)',
        hintText: 'Select source account (e.g., Cash, Bank)',
        selectedAccount: expenseController.selectedSourceAccount.value,
        onChanged: (account) {
          expenseController.setSelectedSourceAccount(account);
        },
        allowedCategories: const [AccountCategory.assets],
        allowedTypes: const [
          AccountType.cash,
          AccountType.bank,
          AccountType.currentAssets,
        ],
        isRequired: true,
        validator: (account) {
          if (account == null) {
            return 'Please select a source account';
          }
          return null;
        },
        showAccountNumber: true,
        showBalance: true,
        activeOnly: true,
      );
    });
  }

  Widget _buildDestinationAccountDropdown() {
    return Obx(() {
      return ChartOfAccountsDropdown(
        labelText: 'Expense Account (Money Increases To)',
        hintText:
            'Select expense account (e.g., Fuel, Office Supplies, Travel)',
        selectedAccount: expenseController.selectedDestinationAccount.value,
        onChanged: (account) {
          // Debug logging to verify account selection
          log('EXPENSE FORM: Selected destination account: ${account?.accountName} (${account?.category.displayName})');
          expenseController.setSelectedDestinationAccount(account);
        },
        allowedCategories: const [AccountCategory.expenses],
        // Allow all expense account types to ensure maximum compatibility
        allowedTypes: null,
        isRequired: true,
        validator: (account) {
          if (account == null) {
            return 'Please select an expense account';
          }
          return null;
        },
        showAccountNumber: true,
        showBalance: false,
        activeOnly: true,
      );
    });
  }

  Widget _buildCategoryDropdown() {
    return Obx(() {
      final categories = expenseController.categories;

      if (categories.isEmpty) {
        return InputDecorator(
          decoration: InputDecoration(
            labelText: 'Select Category',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            filled: true,
            fillColor: notifier.textFileColor,
            suffixIcon: IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => expenseController.fetchCategories(),
            ),
          ),
          child:
              const Text('No categories available, tap refresh to try again'),
        );
      }

      return DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: 'Select Category',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
          filled: true,
          fillColor: notifier.textFileColor,
        ),
        value: expenseController.selectedCategory.value?.id,
        items: categories.map((category) {
          return DropdownMenuItem<String>(
            value: category.id,
            child: Text(category.name),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null) {
            final selectedCategory = categories.firstWhere(
              (category) => category.id == value,
              orElse: () => ExpenseCategoryModel(
                id: '',
                name: '',
                createdAt: DateTime.now(),
              ),
            );
            expenseController.selectedCategory.value = selectedCategory;
          }
        },
        validator: (value) => expenseController.validateCategory(
          value != null
              ? categories.firstWhere(
                  (category) => category.id == value,
                  orElse: () => ExpenseCategoryModel(
                    id: '',
                    name: '',
                    createdAt: DateTime.now(),
                  ),
                )
              : null,
        ),
      );
    });
  }

  Widget _buildPayeeDropdown() {
    return Obx(() {
      return DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: 'Select Payee',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
          filled: true,
          fillColor: notifier.textFileColor,
        ),
        value: expenseController.selectedPayee.value?.id,
        items: expenseController.payees.map((payee) {
          return DropdownMenuItem<String>(
            value: payee.id,
            child: Text(payee.name),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null) {
            final selectedPayee = expenseController.payees.firstWhere(
              (payee) => payee.id == value,
            );
            expenseController.selectedPayee.value = selectedPayee;
          }
        },
        validator: (value) => expenseController.validatePayee(
          value != null
              ? expenseController.payees
                  .firstWhere((payee) => payee.id == value)
              : null,
        ),
      );
    });
  }
}
