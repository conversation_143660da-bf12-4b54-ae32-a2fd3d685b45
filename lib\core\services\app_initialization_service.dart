import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'startup_error_handler.dart';
import 'connectivity_service.dart';
import 'offline_state_service.dart';
import 'sync_service.dart';
import 'offline_voucher_service.dart';
import 'offline_journal_service.dart';
import '../../features/voucher/repositories/voucher_repository.dart';
import '../../features/voucher/repositories/offline_voucher_repository.dart';

/// Service to handle app initialization with proper error handling
class AppInitializationService {
  static bool _isInitialized = false;
  static bool _hasErrors = false;

  /// Check if app is properly initialized
  static bool get isInitialized => _isInitialized;

  /// Check if there were initialization errors
  static bool get hasErrors => _hasErrors;

  /// Initialize the app with offline capabilities
  static Future<void> initializeApp() async {
    try {
      log('AppInitializationService: Starting app initialization...');

      // Initialize Hive for offline storage
      await _initializeHive();

      // Initialize offline services with error handling
      await _initializeOfflineServices();

      // Initialize voucher repository replacement
      await _initializeVoucherRepository();

      _isInitialized = true;
      log('AppInitializationService: App initialization completed successfully');
    } catch (e) {
      _hasErrors = true;
      log('AppInitializationService: Critical error during initialization: $e');
      StartupErrorHandler.handleDependencyError('AppInitialization', e);

      // Try to recover from errors
      await StartupErrorHandler.attemptRecovery();
    }
  }

  /// Initialize Hive for offline storage
  static Future<void> _initializeHive() async {
    try {
      if (!kIsWeb) {
        await Hive.initFlutter();
        log('AppInitializationService: Hive initialized successfully');
      } else {
        log('AppInitializationService: Skipping Hive initialization on web platform');
      }
    } catch (e) {
      log('AppInitializationService: Hive initialization failed: $e');
      // Continue without Hive on error
    }
  }

  /// Initialize offline services with comprehensive error handling
  static Future<void> _initializeOfflineServices() async {
    // Initialize connectivity service
    await _initializeConnectivityService();

    // Initialize offline state service
    await _initializeOfflineStateService();

    // Initialize sync service (depends on previous services)
    await _initializeSyncService();

    // Initialize offline data services
    await _initializeOfflineDataServices();
  }

  /// Initialize connectivity service with fallback
  static Future<void> _initializeConnectivityService() async {
    try {
      if (!Get.isRegistered<ConnectivityService>()) {
        final connectivityService = ConnectivityService();
        Get.put<ConnectivityService>(connectivityService, permanent: true);

        // Give it a moment to initialize
        await Future.delayed(const Duration(milliseconds: 100));

        log('AppInitializationService: ConnectivityService initialized successfully');
      }
    } catch (e) {
      log('AppInitializationService: ConnectivityService initialization failed: $e');
      StartupErrorHandler.handleConnectivityError(e);

      // Register fallback connectivity service
      try {
        final fallbackService = FallbackConnectivityService();
        Get.put(fallbackService, permanent: true);
        log('AppInitializationService: Fallback ConnectivityService registered');
      } catch (fallbackError) {
        log('AppInitializationService: Fallback ConnectivityService registration failed: $fallbackError');
      }
    }
  }

  /// Initialize offline state service with fallback
  static Future<void> _initializeOfflineStateService() async {
    try {
      if (!Get.isRegistered<OfflineStateService>()) {
        final offlineStateService = OfflineStateService();
        Get.put<OfflineStateService>(offlineStateService, permanent: true);

        // Give it a moment to initialize
        await Future.delayed(const Duration(milliseconds: 100));

        log('AppInitializationService: OfflineStateService initialized successfully');
      }
    } catch (e) {
      log('AppInitializationService: OfflineStateService initialization failed: $e');
      StartupErrorHandler.handleOfflineServiceError('OfflineStateService', e);

      // Register fallback offline state service
      try {
        final fallbackService = FallbackOfflineStateService();
        Get.put(fallbackService, permanent: true, tag: 'OfflineStateService');
        log('AppInitializationService: Fallback OfflineStateService registered');
      } catch (fallbackError) {
        log('AppInitializationService: Fallback OfflineStateService registration failed: $fallbackError');
      }
    }
  }

  /// Initialize sync service
  static Future<void> _initializeSyncService() async {
    try {
      if (!Get.isRegistered<SyncService>()) {
        final syncService = SyncService();
        Get.put<SyncService>(syncService, permanent: true);
        log('AppInitializationService: SyncService initialized successfully');
      }
    } catch (e) {
      log('AppInitializationService: SyncService initialization failed: $e');
      StartupErrorHandler.handleOfflineServiceError('SyncService', e);
      // Continue without sync service - not critical for basic operation
    }
  }

  /// Initialize offline data services
  static Future<void> _initializeOfflineDataServices() async {
    // Initialize offline voucher service
    try {
      if (!Get.isRegistered<OfflineVoucherService>()) {
        final offlineVoucherService = OfflineVoucherService();
        Get.put<OfflineVoucherService>(offlineVoucherService, permanent: true);
        log('AppInitializationService: OfflineVoucherService initialized successfully');
      }
    } catch (e) {
      log('AppInitializationService: OfflineVoucherService initialization failed: $e');
      StartupErrorHandler.handleOfflineServiceError('OfflineVoucherService', e);
    }

    // Initialize offline journal service
    try {
      if (!Get.isRegistered<OfflineJournalService>()) {
        final offlineJournalService = OfflineJournalService();
        Get.put<OfflineJournalService>(offlineJournalService, permanent: true);
        log('AppInitializationService: OfflineJournalService initialized successfully');
      }
    } catch (e) {
      log('AppInitializationService: OfflineJournalService initialization failed: $e');
      StartupErrorHandler.handleOfflineServiceError('OfflineJournalService', e);
    }
  }

  /// Initialize voucher repository with offline capabilities
  static Future<void> _initializeVoucherRepository() async {
    try {
      // Check if VoucherRepository exists
      if (Get.isRegistered<VoucherRepository>()) {
        // Check if offline services are available
        if (Get.isRegistered<ConnectivityService>() &&
            Get.isRegistered<OfflineVoucherService>()) {
          // Get the existing online repository
          final onlineRepository = Get.find<VoucherRepository>();
          final connectivityService = Get.find<ConnectivityService>();
          final offlineVoucherService = Get.find<OfflineVoucherService>();

          // Create offline-capable repository
          final offlineRepository = OfflineVoucherRepository(
            onlineRepository: onlineRepository,
            connectivityService: connectivityService,
            offlineVoucherService: offlineVoucherService,
          );

          // Replace the existing repository
          Get.delete<VoucherRepository>();
          Get.put<VoucherRepository>(offlineRepository, permanent: true);

          log('AppInitializationService: VoucherRepository replaced with offline-capable version');
        } else {
          log('AppInitializationService: Offline services not available - keeping original VoucherRepository');
        }
      } else {
        log('AppInitializationService: VoucherRepository not found - will be registered by AppBindings');
      }
    } catch (e) {
      log('AppInitializationService: VoucherRepository initialization failed: $e');
      StartupErrorHandler.handleDependencyError('VoucherRepository', e);
      // Keep the original repository if replacement fails
    }
  }

  /// Get initialization summary for debugging
  static Map<String, dynamic> getInitializationSummary() {
    return {
      'isInitialized': _isInitialized,
      'hasErrors': _hasErrors,
      'services': {
        'ConnectivityService': Get.isRegistered<ConnectivityService>(),
        'OfflineStateService': Get.isRegistered<OfflineStateService>(),
        'SyncService': Get.isRegistered<SyncService>(),
        'OfflineVoucherService': Get.isRegistered<OfflineVoucherService>(),
        'OfflineJournalService': Get.isRegistered<OfflineJournalService>(),
        'VoucherRepository': Get.isRegistered<VoucherRepository>(),
      },
      'startupErrors': StartupErrorHandler.getInitializationSummary(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Reset initialization state (for testing)
  static void reset() {
    _isInitialized = false;
    _hasErrors = false;
    StartupErrorHandler.clearErrors();
  }
}
