import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../controllers/financial_dashboard_controller.dart';

/// Widget displaying cash flow chart
class CashFlowChartWidget extends StatelessWidget {
  const CashFlowChartWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FinancialDashboardController>();
    final currencyFormatter =
        NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0);
    final dateFormatter = DateFormat('MMM dd');

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Cash Flow Trend',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.refresh, size: 20),
                      onPressed: controller.refreshDashboard,
                      tooltip: 'Refresh Chart',
                    ),
                    IconButton(
                      icon: const Icon(Icons.fullscreen, size: 20),
                      onPressed: () =>
                          _showFullScreenChart(context, controller),
                      tooltip: 'View Full Screen',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              final cashFlowData = controller.cashFlowData;
              if (cashFlowData.isEmpty) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: Column(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading cash flow data...'),
                      ],
                    ),
                  ),
                );
              }

              return Column(
                children: [
                  // Chart legend
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildLegendItem('Cash In', Colors.green, context),
                      _buildLegendItem('Cash Out', Colors.red, context),
                      _buildLegendItem('Net Flow', Colors.blue, context),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Simple chart representation
                  Container(
                    height: 200,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: _buildSimpleChart(cashFlowData, currencyFormatter,
                          dateFormatter, context),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Summary statistics
                  Row(
                    children: [
                      Expanded(
                        child: _buildSummaryCard(
                          'Total Cash In',
                          currencyFormatter.format(
                            cashFlowData.fold<double>(
                                0.0, (sum, data) => sum + data.cashIn),
                          ),
                          Icons.arrow_downward,
                          Colors.green,
                          context,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildSummaryCard(
                          'Total Cash Out',
                          currencyFormatter.format(
                            cashFlowData.fold<double>(
                                0.0, (sum, data) => sum + data.cashOut),
                          ),
                          Icons.arrow_upward,
                          Colors.red,
                          context,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildSummaryCard(
                          'Net Cash Flow',
                          currencyFormatter.format(
                            cashFlowData.isNotEmpty
                                ? cashFlowData.last.cumulativeBalance
                                : 0.0,
                          ),
                          Icons.account_balance,
                          Colors.blue,
                          context,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color, BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildSimpleChart(
    List<dynamic> cashFlowData,
    NumberFormat currencyFormatter,
    DateFormat dateFormatter,
    BuildContext context,
  ) {
    if (cashFlowData.isEmpty) {
      return const Center(
        child: Text('No cash flow data available'),
      );
    }

    // Find max values for scaling
    final maxCashIn = cashFlowData.fold<double>(
        0.0, (max, data) => data.cashIn > max ? data.cashIn : max);
    final maxCashOut = cashFlowData.fold<double>(
        0.0, (max, data) => data.cashOut > max ? data.cashOut : max);
    final maxValue = maxCashIn > maxCashOut ? maxCashIn : maxCashOut;

    if (maxValue == 0) {
      return const Center(
        child: Text('No cash flow activity in selected period'),
      );
    }

    return Column(
      children: [
        // Chart bars
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: cashFlowData.asMap().entries.map((entry) {
              final data = entry.value;
              final cashInHeight = (data.cashIn / maxValue) * 120;
              final cashOutHeight = (data.cashOut / maxValue) * 120;

              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Cash In bar
                      Container(
                        height: cashInHeight,
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(2)),
                        ),
                      ),
                      const SizedBox(height: 2),
                      // Cash Out bar
                      Container(
                        height: cashOutHeight,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(2)),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),

        const SizedBox(height: 8),

        // Date labels (show every few dates to avoid crowding)
        Row(
          children: cashFlowData.asMap().entries.map((entry) {
            final index = entry.key;
            final data = entry.value;
            final showLabel = index % (cashFlowData.length > 10 ? 3 : 1) == 0;

            return Expanded(
              child: showLabel
                  ? Text(
                      dateFormatter.format(data.date),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontSize: 10,
                          ),
                      textAlign: TextAlign.center,
                    )
                  : const SizedBox(),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
    BuildContext context,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontSize: 11,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showFullScreenChart(
      BuildContext context, FinancialDashboardController controller) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Cash Flow Chart',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const Expanded(
                child: Center(
                  child: Text(
                      'Full screen chart would be implemented here with a proper charting library'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
