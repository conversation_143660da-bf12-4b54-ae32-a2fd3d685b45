import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/router/app_routes.dart';
import 'package:logestics/core/router/route_guard.dart';
import 'package:logestics/firebase_service/firebase_auth_service.dart';
import 'package:web/web.dart';

class RootScreen extends StatefulWidget {
  const RootScreen({super.key});

  @override
  State<RootScreen> createState() => _RootScreenState();
}

class _RootScreenState extends State<RootScreen> {
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();
    _checkInitialRoute();
  }

  void _checkInitialRoute() {
    // Longer delay to ensure GetMaterialApp and all dependencies are fully initialized
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (_hasNavigated || !mounted) return; // Prevent multiple navigation

      try {
        // Check if GetX is properly initialized
        if (Get.context == null) {
          log('GetX context not available yet, retrying...');
          _checkInitialRoute(); // Retry
          return;
        }

        // Ensure auth service is available
        if (!Get.isRegistered<FirebaseAuthService>()) {
          log('FirebaseAuthService not registered, navigating to login');
          _hasNavigated = true;
          Get.offAllNamed(AppRoutes.login);
          return;
        }

        // Check if we have a stored path from a direct URL access
        final initialPath = window.sessionStorage.getItem('initialPath');

        if (initialPath != null && initialPath.isNotEmpty) {
          // Clear the stored path to prevent loops
          window.sessionStorage.removeItem('initialPath');

          // Check if the path is valid and user is authenticated
          final authService = Get.find<FirebaseAuthService>();

          if (authService.isAuthenticated) {
            // Try to navigate to the stored path
            final route = initialPath.toString();
            if (AppRoutes.getPages.any((page) => page.name == route)) {
              _hasNavigated = true;
              Get.offAllNamed(route);
              return;
            } else {
              // Route doesn't exist, go to home
              _hasNavigated = true;
              Get.offAllNamed(AppRoutes.home);
              return;
            }
          } else {
            // User not authenticated, go to login
            _hasNavigated = true;
            Get.offAllNamed(AppRoutes.login);
            return;
          }
        }

        // No stored path, perform normal routing
        final initialRoute = RouteGuard.getInitialRoute();
        _hasNavigated = true;

        // Use safe navigation with error handling
        try {
          Get.offAllNamed(initialRoute);
        } catch (navError) {
          log('Navigation error: $navError, falling back to login');
          Get.offAllNamed(AppRoutes.login);
        }
      } catch (e) {
        log('Error during route checking: $e');
        // Something went wrong, just go to login
        if (!_hasNavigated) {
          _hasNavigated = true;
          try {
            Get.offAllNamed(AppRoutes.login);
          } catch (navError) {
            log('Critical navigation error: $navError');
            // If even login navigation fails, we have a serious problem
            // The app will stay on the loading screen
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
