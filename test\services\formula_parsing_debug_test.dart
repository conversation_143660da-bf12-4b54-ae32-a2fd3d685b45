import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';

void main() {
  group('Formula Parsing Debug Tests', () {
    late InvoiceModel testInvoice;

    setUp(() {
      testInvoice = InvoiceModel(
        invoiceNumber: 1122,
        invoiceStatus: 'Active',
        tasNumber: 'TAS1122',
        productName: 'Test Product',
        numberOfBags: 400,
        weightPerBag: 50.0,
        customerName: 'Test Customer',
        truckNumber: 'TRK001',
        conveyNoteNumber: 'CN001',
        biltyNumber: 'BLT001',
        consignorName: 'Test Consignor',
        deliveryMode: 'Road',
        districtId: 'DIST001',
        districtName: 'Test District',
        stationId: 'STN001',
        stationName: 'Test Station',
        fromPlaceId: 'PLC001',
        fromPlaceName: 'Test Place',
        distanceInKilometers: 175.0, // This should fall in the 161-200 range
        consignorPickUpAddress: 'Test Address',
      );
    });

    test('should parse complete KM range formula correctly', () {
      // Create a complete KM range formula similar to the one causing issues
      final completeFormula =
          'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 1196.38 ELSE IF distanceInKilometers >= 121 AND distanceInKilometers <= 160 THEN 7.68 ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 7.68';

      print('Testing complete formula: $completeFormula');
      print('Formula length: ${completeFormula.length}');

      // Test the formula parsing directly
      final result =
          FlexibleFormulaCalculationService.evaluateExpression(completeFormula);
      print('Direct evaluation result: $result');

      expect(result, isNotNull);
      // For distance 175, should use the 161-200 range with rate 7.68
      expect(result, equals(7.68));
    });

    test('should handle truncated formula and identify the issue', () {
      // Test the truncated formula from the debug log
      final truncatedFormula =
          'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 11';

      print('Testing truncated formula: $truncatedFormula');
      print('Formula length: ${truncatedFormula.length}');

      // This should fail because the formula is incomplete
      final result = FlexibleFormulaCalculationService.evaluateExpression(
          truncatedFormula);
      print('Truncated evaluation result: $result');

      // The truncated formula should fail to parse
      expect(result, isNull);
    });

    test('should test variable replacement in KM range formula', () {
      // Test the variable replacement process
      final originalFormula =
          'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 1196.38 ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 7.68';

      // Simulate variable replacement
      final variables = {
        'distanceInKilometers': 175.0,
      };

      String processedFormula = originalFormula;
      for (final entry in variables.entries) {
        processedFormula =
            processedFormula.replaceAll(entry.key, entry.value.toString());
      }

      print('Original formula: $originalFormula');
      print('Processed formula: $processedFormula');
      print('Processed formula length: ${processedFormula.length}');

      // Test the processed formula
      final result = FlexibleFormulaCalculationService.evaluateExpression(
          processedFormula);
      print('Variable replacement result: $result');

      expect(result, isNotNull);
      expect(result, equals(7.68));
    });

    test('should test formula step execution with complete formula', () {
      // Create a formula step with the complete KM range formula
      final step = FormulaStepModel(
        stepId: 'step1',
        stepName: 'Determine Effective Rate Based on Distance',
        formula:
            'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 1196.38 ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 7.68',
        resultVariable: 'effectiveRate',
        description: 'Test KM range formula',
      );

      final formula = CalculationFormulaModel(
        formulaId: 'test_km_range',
        formulaName: 'Test KM Range Formula',
        steps: [
          step,
          FormulaStepModel(
            stepId: 'step2',
            stepName: 'Calculate Final Amount',
            formula: 'totalWeightTons × effectiveRate',
            resultVariable: 'finalAmount',
            description: 'Apply effective rate to weight',
          ),
        ],
        finalResultVariable: 'finalAmount',
      );

      // Test the complete formula execution
      final result =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice,
        formula: formula,
        customColumnValues: {},
      );

      print('Formula execution result: ${result.isSuccess}');
      print('Final amount: ${result.finalAmount}');
      print('Error message: ${result.errorMessage}');
      print('Step results: ${result.stepResults}');

      expect(result.isSuccess, isTrue);
      expect(result.finalAmount, isNotNull);

      // For 175 KM distance, should use rate 7.68
      // Expected: 20 tons × 7.68 = 153.6
      expect(result.finalAmount, equals(153.6));
    });

    test('should debug step execution with simple formula', () {
      // Test with a simpler formula first
      final simpleStep = FormulaStepModel(
        stepId: 'step1',
        stepName: 'Simple Test',
        formula: 'IF distanceInKilometers > 120 THEN 7.68 ELSE 1196.38',
        resultVariable: 'rate',
        description: 'Simple KM test',
      );

      final simpleFormula = CalculationFormulaModel(
        formulaId: 'simple_test',
        formulaName: 'Simple Test Formula',
        steps: [simpleStep],
        finalResultVariable: 'rate',
      );

      final result =
          FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice,
        formula: simpleFormula,
        customColumnValues: {},
      );

      print('Simple formula execution result: ${result.isSuccess}');
      print('Simple final amount: ${result.finalAmount}');
      print('Simple error message: ${result.errorMessage}');

      expect(result.isSuccess, isTrue);
      expect(result.finalAmount, equals(7.68)); // 175 > 120, so should be 7.68
    });

    test('should identify position 9 parsing error', () {
      // Test the exact scenario that causes "Expected number at position 9"
      final problematicFormula =
          'IF 175.0 >= 1 AND 175.0 <= 40 THEN 1196.38 ELSE IF 175.0 >= 41 AND 175.0 <= 80 THEN 1196.38 ELSE IF 175.0 >= 81 AND 175.0 <= 120 THEN 11';

      print('Testing problematic formula: $problematicFormula');
      print(
          'Character at position 9: "${problematicFormula.length > 9 ? problematicFormula[9] : 'N/A'}"');

      // Check what's around position 9
      if (problematicFormula.length > 9) {
        final start = (9 - 5).clamp(0, problematicFormula.length);
        final end = (9 + 5).clamp(0, problematicFormula.length);
        print(
            'Context around position 9: "${problematicFormula.substring(start, end)}"');
      }

      final result = FlexibleFormulaCalculationService.evaluateExpression(
          problematicFormula);
      print('Problematic formula result: $result');

      // This should fail due to truncation
      expect(result, isNull);
    });
  });
}
