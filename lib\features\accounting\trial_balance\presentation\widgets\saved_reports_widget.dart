import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../models/accounting/financial_report_models.dart';
import '../controllers/trial_balance_controller.dart';

/// Widget for displaying saved Trial Balance reports
class SavedReportsWidget extends StatelessWidget {
  const SavedReportsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<TrialBalanceController>();

    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(32.0),
            child: CircularProgressIndicator(),
          ),
        );
      }

      if (controller.savedReports.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(32.0),
            child: Column(
              children: [
                Icon(
                  Icons.folder_open,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No saved reports',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Generate and save trial balance reports to see them here',
                  style: TextStyle(
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }

      return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: controller.savedReports.length,
        separatorBuilder: (context, index) => const SizedBox(height: 8),
        itemBuilder: (context, index) {
          final report = controller.savedReports[index];
          return _buildReportCard(context, report, controller);
        },
      );
    });
  }

  /// Build report card
  Widget _buildReportCard(
    BuildContext context,
    TrialBalanceReport report,
    TrialBalanceController controller,
  ) {
    return Card(
      elevation: 1,
      child: InkWell(
        onTap: () => controller.loadTrialBalanceReport(report.reportId),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  // Report icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color:
                          Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.assessment,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Report details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          report.companyName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_formatDate(report.startDate)} to ${_formatDate(report.endDate)}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Actions
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'load':
                          controller.loadTrialBalanceReport(report.reportId);
                          break;
                        case 'delete':
                          _showDeleteConfirmation(context, report, controller);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'load',
                        child: Row(
                          children: [
                            Icon(Icons.open_in_new, size: 18),
                            SizedBox(width: 8),
                            Text('Load Report'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 18, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Report summary
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    // Accounts count
                    Expanded(
                      child: _buildSummaryItem(
                        'Accounts',
                        '${report.accounts.length}',
                        Icons.account_balance_wallet,
                      ),
                    ),

                    // Total debits
                    Expanded(
                      child: _buildSummaryItem(
                        'Total Debits',
                        _formatCurrency(report.totalDebits),
                        Icons.trending_up,
                      ),
                    ),

                    // Total credits
                    Expanded(
                      child: _buildSummaryItem(
                        'Total Credits',
                        _formatCurrency(report.totalCredits),
                        Icons.trending_down,
                      ),
                    ),

                    // Balance status
                    Expanded(
                      child: _buildSummaryItem(
                        'Status',
                        _isBalanced(report) ? 'Balanced' : 'Unbalanced',
                        _isBalanced(report) ? Icons.check_circle : Icons.error,
                        color: _isBalanced(report) ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 8),

              // Generated date
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 14,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Generated: ${_formatDateTime(report.generatedAt)}',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build summary item
  Widget _buildSummaryItem(
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: 16,
          color: color ?? Colors.grey.shade600,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: color ?? Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(
    BuildContext context,
    TrialBalanceReport report,
    TrialBalanceController controller,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Report'),
        content: Text(
          'Are you sure you want to delete the trial balance report for "${report.companyName}"?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.deleteTrialBalanceReport(report.reportId);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Check if trial balance is balanced
  bool _isBalanced(TrialBalanceReport report) {
    return (report.totalDebits - report.totalCredits).abs() < 0.01;
  }

  /// Format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Format date and time
  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Format currency amount
  String _formatCurrency(double amount) {
    if (amount == 0) return '0.00';

    // Format with commas and 2 decimal places
    final formatted = amount.toStringAsFixed(2);
    final parts = formatted.split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];

    // Add commas to integer part
    final regex = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    final integerWithCommas = integerPart.replaceAllMapped(
      regex,
      (Match match) => '${match[1]},',
    );

    return '$integerWithCommas.$decimalPart';
  }
}
