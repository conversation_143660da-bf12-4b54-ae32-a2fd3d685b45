import 'dart:developer';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/features/slab/domain/usecases/get_active_slabs_for_district_use_case.dart';
import 'package:logestics/models/invoice_model.dart';

import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';

/// Service for calculating billing amounts using slab rates
class SlabRateCalculationService {
  final GetActiveSlabsForDistrictUseCase getActiveSlabsForDistrictUseCase;

  SlabRateCalculationService({
    required this.getActiveSlabsForDistrictUseCase,
  });

  /// Calculate billing amount for a single invoice using slab rates with detailed breakdown
  /// Returns detailed calculation result including breakdown information
  Future<InvoiceCalculationResult> calculateInvoiceAmountDetailed({
    required InvoiceModel invoice,
    String rateType = 'hmtRate', // Default to HMT rate
    SlabModel? selectedSlab, // Optional specific slab to use for calculation
  }) async {
    try {
      SlabModel? slab;

      // If a specific slab is provided, use it directly
      if (selectedSlab != null) {
        slab = selectedSlab;
        log('Using selected slab: ${slab.slabName} for invoice ${invoice.tasNumber}');
      } else {
        // Use belongsToDate, fallback to orderDate, then createdAt for rate calculation
        final invoiceDate =
            invoice.belongsToDate ?? invoice.orderDate ?? invoice.createdAt;

        // Get active slabs for the invoice's district and date
        final result = await getActiveSlabsForDistrictUseCase.call(
          districtId: invoice.districtId,
          date: invoiceDate,
        );

        final slabResult = result.fold(
          (failure) {
            log('Error getting slab rates for invoice ${invoice.tasNumber}: ${failure.message}');
            return null;
          },
          (slabs) {
            if (slabs.isEmpty) {
              log('No active slab rates found for district ${invoice.districtName} on $invoiceDate');
              return null;
            }

            // Use the most recent slab (slabs are ordered by creation date)
            return slabs.first;
          },
        );

        if (slabResult == null) {
          return InvoiceCalculationResult(
            calculationMethod: 'No Slab Found',
            calculationBreakdown:
                'No active slab rates found for district ${invoice.districtName}',
          );
        }
        slab = slabResult;
      }

      final rate = slab.getRateForDistrict(invoice.districtId);

      if (rate == null) {
        log('No rate found for district ${invoice.districtName} in slab ${slab.slabName}');
        return InvoiceCalculationResult(
          calculationMethod: 'No Rate Found',
          calculationBreakdown:
              'No rate found for district ${invoice.districtName} in slab ${slab.slabName}',
        );
      }

      // Check if slab has a custom calculation formula
      if (slab.calculationFormula != null) {
        log('Using custom formula for slab: ${slab.slabName}');

        // Get custom column values from the rate
        final customColumnValues = <String, double>{};
        for (final entry in rate.customColumns.entries) {
          if (entry.value is num) {
            customColumnValues[entry.key] = (entry.value as num).toDouble();
          }
        }

        final formulaResult =
            FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
          invoice: invoice,
          formula: slab.calculationFormula!,
          customColumnValues: customColumnValues,
        );

        if (formulaResult.isSuccess && formulaResult.finalAmount != null) {
          final roundedResult =
              MonetaryRounding.roundHalfUp(formulaResult.finalAmount!);
          log('Custom formula calculation successful: ${formulaResult.finalAmount} → rounded to: $roundedResult');

          return InvoiceCalculationResult(
            amount: roundedResult,
            calculationMethod:
                'Custom Formula: ${slab.calculationFormula!.formulaName}',
            calculationBreakdown: formulaResult.calculationBreakdown,
            usedCustomFormula: true,
          );
        } else {
          log('Custom formula calculation failed: ${formulaResult.errorMessage}');
          // Fall through to standard calculation
        }
      }

      // Standard calculation logic continues...
      // (I'll add the rest in the next chunk to stay under 150 lines)
      return await _performStandardCalculation(invoice, slab, rate, rateType);
    } catch (e) {
      log('Error calculating invoice amount: $e');
      return InvoiceCalculationResult(
        calculationMethod: 'Calculation Error',
        calculationBreakdown: 'Error calculating invoice amount: $e',
      );
    }
  }

  /// Calculate billing amount for a single invoice using slab rates
  /// Returns the calculated amount or null if no applicable slab rate is found
  Future<double?> calculateInvoiceAmount({
    required InvoiceModel invoice,
    String rateType = 'hmtRate', // Default to HMT rate
    SlabModel? selectedSlab, // Optional specific slab to use for calculation
  }) async {
    final result = await calculateInvoiceAmountDetailed(
      invoice: invoice,
      rateType: rateType,
      selectedSlab: selectedSlab,
    );
    return result.amount;
  }

  /// Perform standard calculation with detailed breakdown
  Future<InvoiceCalculationResult> _performStandardCalculation(
    InvoiceModel invoice,
    SlabModel slab,
    SlabRateModel rate,
    String rateType,
  ) async {
    try {
      // Standard calculation logic
      double rateValue;
      String calculationMethodUsed;

      // Priority: Custom columns > Legacy fields (for clean slate implementation)
      if (rate.customColumns.containsKey(rateType)) {
        final customValue = rate.customColumns[rateType];
        if (customValue is num && customValue.toDouble() > 0) {
          rateValue = customValue.toDouble();
          calculationMethodUsed = 'Custom Column: $rateType';
        } else {
          log('Custom column $rateType exists but has invalid value: $customValue');
          return InvoiceCalculationResult(
            calculationMethod: 'Invalid Rate',
            calculationBreakdown:
                'Custom column $rateType has invalid value: $customValue',
          );
        }
      } else {
        // Fallback to legacy fields
        switch (rateType) {
          case 'hmtRate':
            rateValue = rate.hmtRate;
            calculationMethodUsed = 'HMT Rate';
            break;
          case 'nonFuelRate':
            rateValue = rate.nonFuelRate;
            calculationMethodUsed = 'Non-Fuel Rate';
            break;
          default:
            log('Unknown rate type: $rateType');
            return InvoiceCalculationResult(
              calculationMethod: 'Unknown Rate Type',
              calculationBreakdown: 'Unknown rate type: $rateType',
            );
        }
      }

      if (rateValue <= 0) {
        log('Rate value is zero or negative: $rateValue');
        return InvoiceCalculationResult(
          calculationMethod: 'Invalid Rate Value',
          calculationBreakdown: 'Rate value is zero or negative: $rateValue',
        );
      }

      // Standard calculation: (numberOfBags × weightPerBag × distanceInKilometers × rate) / 1000
      final totalWeightKg = invoice.numberOfBags * invoice.weightPerBag;
      final totalWeightTons = totalWeightKg / 1000;
      final calculatedAmount =
          totalWeightTons * invoice.distanceInKilometers * rateValue;

      // Apply monetary rounding
      final roundedAmount = MonetaryRounding.roundHalfUp(calculatedAmount);

      final breakdown = 'Standard Calculation:\n'
          '${invoice.numberOfBags} bags × ${invoice.weightPerBag} kg = $totalWeightKg kg (${totalWeightTons.toStringAsFixed(3)} tons)\n'
          '${totalWeightTons.toStringAsFixed(3)} tons × ${invoice.distanceInKilometers} km × ${rateValue.toStringAsFixed(2)} rate = ${calculatedAmount.toStringAsFixed(2)}\n'
          'Rounded to: ${roundedAmount.toStringAsFixed(2)}';

      log('Standard calculation for invoice ${invoice.tasNumber}: $calculatedAmount → rounded to: $roundedAmount');

      return InvoiceCalculationResult(
        amount: roundedAmount,
        calculationMethod: calculationMethodUsed,
        calculationBreakdown: breakdown,
        usedCustomFormula: false,
      );
    } catch (e) {
      log('Error in standard calculation: $e');
      return InvoiceCalculationResult(
        calculationMethod: 'Calculation Error',
        calculationBreakdown: 'Error in standard calculation: $e',
      );
    }
  }

  /// Calculate total billing amount for multiple invoices using slab rates
  /// Returns a map with total amount and breakdown by invoice
  Future<SlabCalculationResult> calculateBatchInvoiceAmounts({
    required List<InvoiceModel> invoices,
    String rateType = 'hmtRate',
    double fallbackRate = 0.1, // Fallback rate if no slab rate is found
    SlabModel?
        selectedSlab, // Optional specific slab to use for all calculations
  }) async {
    double totalAmount = 0.0;
    final Map<String, double> invoiceAmounts = {};
    final Map<String, String> calculationDetails = {};
    final Map<String, String> calculationBreakdowns =
        {}; // New: detailed breakdowns
    int slabRateUsedCount = 0;
    int fallbackRateUsedCount = 0;

    for (final invoice in invoices) {
      final detailedResult = await calculateInvoiceAmountDetailed(
        invoice: invoice,
        rateType: rateType,
        selectedSlab: selectedSlab,
      );

      double invoiceAmount;
      String calculationMethod;
      String calculationBreakdown;

      if (detailedResult.amount != null) {
        // Use detailed calculation result
        invoiceAmount = detailedResult.amount!;
        calculationMethod = detailedResult.calculationMethod;
        calculationBreakdown =
            detailedResult.calculationBreakdown ?? 'No breakdown available';
        slabRateUsedCount++;
      } else {
        // Fallback to the original calculation method
        final fallbackAmount = invoice.numberOfBags *
            invoice.weightPerBag *
            invoice.distanceInKilometers *
            fallbackRate;

        // Apply monetary rounding to fallback calculation
        invoiceAmount = MonetaryRounding.roundHalfUp(fallbackAmount);
        calculationMethod = 'Fallback Rate';
        calculationBreakdown = 'Fallback Calculation:\n'
            '${invoice.numberOfBags} bags × ${invoice.weightPerBag} kg × ${invoice.distanceInKilometers} km × $fallbackRate rate = ${fallbackAmount.toStringAsFixed(2)}\n'
            'Rounded to: ${invoiceAmount.toStringAsFixed(2)}';
        fallbackRateUsedCount++;

        log('Using fallback rate for invoice ${invoice.tasNumber}: ${fallbackAmount.toStringAsFixed(2)} → rounded to: $invoiceAmount');
      }

      totalAmount += invoiceAmount;
      invoiceAmounts[invoice.tasNumber] = invoiceAmount;
      calculationDetails[invoice.tasNumber] = calculationMethod;
      calculationBreakdowns[invoice.tasNumber] = calculationBreakdown;
    }

    // Apply monetary rounding to the total amount
    final roundedTotalAmount = MonetaryRounding.roundHalfUp(totalAmount);

    log('Batch calculation complete: $slabRateUsedCount invoices used slab rates, $fallbackRateUsedCount used fallback rate');
    log('Total amount: ${totalAmount.toStringAsFixed(2)} → rounded to: $roundedTotalAmount');

    return SlabCalculationResult(
      totalAmount: roundedTotalAmount,
      invoiceAmounts: invoiceAmounts,
      calculationDetails: calculationDetails,
      calculationBreakdowns: calculationBreakdowns,
      slabRateUsedCount: slabRateUsedCount,
      fallbackRateUsedCount: fallbackRateUsedCount,
    );
  }

  /// Get available rate types for a specific district and date
  Future<List<String>> getAvailableRateTypes({
    required String districtId,
    required DateTime date,
  }) async {
    try {
      final result = await getActiveSlabsForDistrictUseCase.call(
        districtId: districtId,
        date: date,
      );

      return result.fold(
        (failure) => ['hmtRate', 'nonFuelRate'], // Default rate types
        (slabs) {
          if (slabs.isEmpty) return ['hmtRate', 'nonFuelRate'];

          final slab = slabs.first;
          final rate = slab.getRateForDistrict(districtId);

          if (rate == null) return ['hmtRate', 'nonFuelRate'];

          // Return standard rates plus custom columns
          final rateTypes = ['hmtRate', 'nonFuelRate'];
          rateTypes.addAll(rate.customColumns.keys);

          return rateTypes;
        },
      );
    } catch (e) {
      log('Error getting available rate types: $e');
      return ['hmtRate', 'nonFuelRate'];
    }
  }
}

/// Result class for individual invoice calculations
class InvoiceCalculationResult {
  final double? amount;
  final String calculationMethod;
  final String? calculationBreakdown;
  final bool usedCustomFormula;

  InvoiceCalculationResult({
    this.amount,
    required this.calculationMethod,
    this.calculationBreakdown,
    this.usedCustomFormula = false,
  });
}

/// Result class for batch slab calculations
class SlabCalculationResult {
  final double totalAmount;
  final Map<String, double> invoiceAmounts;
  final Map<String, String> calculationDetails;
  final Map<String, String> calculationBreakdowns; // New: detailed breakdowns
  final int slabRateUsedCount;
  final int fallbackRateUsedCount;

  SlabCalculationResult({
    required this.totalAmount,
    required this.invoiceAmounts,
    required this.calculationDetails,
    required this.calculationBreakdowns,
    required this.slabRateUsedCount,
    required this.fallbackRateUsedCount,
  });

  /// Get calculation summary
  String get summary {
    final total = slabRateUsedCount + fallbackRateUsedCount;
    return 'Total: ${totalAmount.toStringAsFixed(2)} | '
        'Slab rates: $slabRateUsedCount/$total | '
        'Fallback rates: $fallbackRateUsedCount/$total';
  }

  /// Check if all invoices used slab rates
  bool get allUsedSlabRates => fallbackRateUsedCount == 0;

  /// Check if any invoices used slab rates
  bool get anyUsedSlabRates => slabRateUsedCount > 0;
}
