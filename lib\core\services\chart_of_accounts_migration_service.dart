import 'dart:developer' as dev;
import '../../models/finance/expense_model.dart';
import '../../models/finance/deposit_model.dart';
import '../../models/finance/account_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../firebase_service/finance/expense_firebase_service.dart';
import '../../firebase_service/finance/deposit_firebase_service.dart';
import '../../firebase_service/finance/account_firebase_service.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';

/// Service for migrating legacy expense and deposit records to Chart of Accounts
class ChartOfAccountsMigrationService {
  final ExpenseFirebaseService _expenseService;
  final DepositFirebaseService _depositService;
  final AccountFirebaseService _accountService;
  final ChartOfAccountsFirebaseService _chartOfAccountsService;

  ChartOfAccountsMigrationService({
    ExpenseFirebaseService? expenseService,
    DepositFirebaseService? depositService,
    AccountFirebaseService? accountService,
    ChartOfAccountsFirebaseService? chartOfAccountsService,
  })  : _expenseService = expenseService ?? ExpenseFirebaseService(),
        _depositService = depositService ?? DepositFirebaseService(),
        _accountService = accountService ?? AccountFirebaseService(),
        _chartOfAccountsService =
            chartOfAccountsService ?? ChartOfAccountsFirebaseService();

  /// Migrate legacy expenses to Chart of Accounts
  Future<MigrationResult> migrateExpenses({
    String? uid,
    bool dryRun = false,
  }) async {
    try {
      dev.log(
          'Starting expense migration to Chart of Accounts (dryRun: $dryRun)');

      final expenses = await _expenseService.getExpenses();
      final legacyExpenses =
          expenses.where((e) => !e.usesChartOfAccounts).toList();

      dev.log('Found ${legacyExpenses.length} legacy expenses to migrate');

      if (legacyExpenses.isEmpty) {
        return MigrationResult(
          totalRecords: 0,
          migratedRecords: 0,
          failedRecords: 0,
          errors: [],
          dryRun: dryRun,
        );
      }

      final accounts = await _accountService.getAccounts();
      final chartOfAccounts = await _chartOfAccountsService.getAccounts();

      int migratedCount = 0;
      int failedCount = 0;
      final errors = <String>[];

      for (final expense in legacyExpenses) {
        try {
          final migrationMapping = await _mapExpenseToChartOfAccounts(
            expense,
            accounts,
            chartOfAccounts,
          );

          if (migrationMapping == null) {
            errors.add(
                'Could not map expense ${expense.id} to Chart of Accounts');
            failedCount++;
            continue;
          }

          if (!dryRun) {
            final migratedExpense = expense.copyWith(
              sourceAccountId: migrationMapping.sourceAccountId,
              sourceAccountName: migrationMapping.sourceAccountName,
              destinationAccountId: migrationMapping.destinationAccountId,
              destinationAccountName: migrationMapping.destinationAccountName,
            );

            await _expenseService.updateExpense(migratedExpense);
          }

          migratedCount++;
          dev.log(
              '${dryRun ? 'Would migrate' : 'Migrated'} expense: ${expense.title}');
        } catch (e) {
          errors.add('Failed to migrate expense ${expense.id}: $e');
          failedCount++;
        }
      }

      return MigrationResult(
        totalRecords: legacyExpenses.length,
        migratedRecords: migratedCount,
        failedRecords: failedCount,
        errors: errors,
        dryRun: dryRun,
      );
    } catch (e) {
      dev.log('Error during expense migration: $e');
      return MigrationResult(
        totalRecords: 0,
        migratedRecords: 0,
        failedRecords: 0,
        errors: ['Migration failed: $e'],
        dryRun: dryRun,
      );
    }
  }

  /// Migrate legacy deposits to Chart of Accounts
  Future<MigrationResult> migrateDeposits({
    String? uid,
    bool dryRun = false,
  }) async {
    try {
      dev.log(
          'Starting deposit migration to Chart of Accounts (dryRun: $dryRun)');

      final deposits = await _depositService.getDeposits();
      final legacyDeposits =
          deposits.where((d) => !d.usesChartOfAccounts).toList();

      dev.log('Found ${legacyDeposits.length} legacy deposits to migrate');

      if (legacyDeposits.isEmpty) {
        return MigrationResult(
          totalRecords: 0,
          migratedRecords: 0,
          failedRecords: 0,
          errors: [],
          dryRun: dryRun,
        );
      }

      final accounts = await _accountService.getAccounts();
      final chartOfAccounts = await _chartOfAccountsService.getAccounts();

      int migratedCount = 0;
      int failedCount = 0;
      final errors = <String>[];

      for (final deposit in legacyDeposits) {
        try {
          final migrationMapping = await _mapDepositToChartOfAccounts(
            deposit,
            accounts,
            chartOfAccounts,
          );

          if (migrationMapping == null) {
            errors.add(
                'Could not map deposit ${deposit.id} to Chart of Accounts');
            failedCount++;
            continue;
          }

          if (!dryRun) {
            final migratedDeposit = deposit.copyWith(
              sourceAccountId: migrationMapping.sourceAccountId,
              sourceAccountName: migrationMapping.sourceAccountName,
              destinationAccountId: migrationMapping.destinationAccountId,
              destinationAccountName: migrationMapping.destinationAccountName,
            );

            await _depositService.updateDeposit(migratedDeposit);
          }

          migratedCount++;
          dev.log(
              '${dryRun ? 'Would migrate' : 'Migrated'} deposit: ${deposit.id}');
        } catch (e) {
          errors.add('Failed to migrate deposit ${deposit.id}: $e');
          failedCount++;
        }
      }

      return MigrationResult(
        totalRecords: legacyDeposits.length,
        migratedRecords: migratedCount,
        failedRecords: failedCount,
        errors: errors,
        dryRun: dryRun,
      );
    } catch (e) {
      dev.log('Error during deposit migration: $e');
      return MigrationResult(
        totalRecords: 0,
        migratedRecords: 0,
        failedRecords: 0,
        errors: ['Migration failed: $e'],
        dryRun: dryRun,
      );
    }
  }

  /// Map legacy expense to Chart of Accounts
  Future<AccountMapping?> _mapExpenseToChartOfAccounts(
    ExpenseModel expense,
    List<AccountModel> legacyAccounts,
    List<ChartOfAccountsModel> chartOfAccounts,
  ) async {
    try {
      // Find the legacy account
      final legacyAccount = legacyAccounts.firstWhere(
        (a) => a.id == expense.accountId,
        orElse: () => throw Exception('Legacy account not found'),
      );

      // For expenses: source should be Asset (Cash/Bank), destination should be Expense
      final sourceAccount =
          _findBestAssetAccount(chartOfAccounts, legacyAccount);
      final destinationAccount =
          _findBestExpenseAccount(chartOfAccounts, expense.categoryName);

      if (sourceAccount == null || destinationAccount == null) {
        return null;
      }

      return AccountMapping(
        sourceAccountId: sourceAccount.id,
        sourceAccountName: sourceAccount.accountName,
        destinationAccountId: destinationAccount.id,
        destinationAccountName: destinationAccount.accountName,
      );
    } catch (e) {
      dev.log('Error mapping expense to Chart of Accounts: $e');
      return null;
    }
  }

  /// Map legacy deposit to Chart of Accounts
  Future<AccountMapping?> _mapDepositToChartOfAccounts(
    DepositModel deposit,
    List<AccountModel> legacyAccounts,
    List<ChartOfAccountsModel> chartOfAccounts,
  ) async {
    try {
      // Find the legacy account
      final legacyAccount = legacyAccounts.firstWhere(
        (a) => a.id == deposit.accountId,
        orElse: () => throw Exception('Legacy account not found'),
      );

      // For deposits: source should be Revenue/Liability, destination should be Asset (Cash/Bank)
      final sourceAccount =
          _findBestRevenueAccount(chartOfAccounts, deposit.categoryName);
      final destinationAccount =
          _findBestAssetAccount(chartOfAccounts, legacyAccount);

      if (sourceAccount == null || destinationAccount == null) {
        return null;
      }

      return AccountMapping(
        sourceAccountId: sourceAccount.id,
        sourceAccountName: sourceAccount.accountName,
        destinationAccountId: destinationAccount.id,
        destinationAccountName: destinationAccount.accountName,
      );
    } catch (e) {
      dev.log('Error mapping deposit to Chart of Accounts: $e');
      return null;
    }
  }

  /// Find best matching Asset account
  ChartOfAccountsModel? _findBestAssetAccount(
    List<ChartOfAccountsModel> chartOfAccounts,
    AccountModel legacyAccount,
  ) {
    final assetAccounts = chartOfAccounts
        .where((a) => a.category == AccountCategory.assets && a.isActive)
        .toList();

    // Try to find by name similarity first
    final nameMatch = assetAccounts
        .where((a) =>
            a.accountName
                .toLowerCase()
                .contains(legacyAccount.name.toLowerCase()) ||
            legacyAccount.name
                .toLowerCase()
                .contains(a.accountName.toLowerCase()))
        .toList();

    if (nameMatch.isNotEmpty) {
      return nameMatch.first;
    }

    // Fallback to first Cash or Bank account
    final cashAccount = assetAccounts
        .where((a) =>
            a.accountType == AccountType.cash ||
            a.accountType == AccountType.bank)
        .toList();

    return cashAccount.isNotEmpty
        ? cashAccount.first
        : (assetAccounts.isNotEmpty ? assetAccounts.first : null);
  }

  /// Find best matching Expense account
  ChartOfAccountsModel? _findBestExpenseAccount(
    List<ChartOfAccountsModel> chartOfAccounts,
    String categoryName,
  ) {
    final expenseAccounts = chartOfAccounts
        .where((a) => a.category == AccountCategory.expenses && a.isActive)
        .toList();

    // Try to find by category name similarity
    final nameMatch = expenseAccounts
        .where((a) =>
            a.accountName.toLowerCase().contains(categoryName.toLowerCase()) ||
            categoryName.toLowerCase().contains(a.accountName.toLowerCase()))
        .toList();

    if (nameMatch.isNotEmpty) {
      return nameMatch.first;
    }

    // Fallback to first expense account
    return expenseAccounts.isNotEmpty ? expenseAccounts.first : null;
  }

  /// Find best matching Revenue account
  ChartOfAccountsModel? _findBestRevenueAccount(
    List<ChartOfAccountsModel> chartOfAccounts,
    String categoryName,
  ) {
    final revenueAccounts = chartOfAccounts
        .where((a) => a.category == AccountCategory.revenue && a.isActive)
        .toList();

    // Try to find by category name similarity
    final nameMatch = revenueAccounts
        .where((a) =>
            a.accountName.toLowerCase().contains(categoryName.toLowerCase()) ||
            categoryName.toLowerCase().contains(a.accountName.toLowerCase()))
        .toList();

    if (nameMatch.isNotEmpty) {
      return nameMatch.first;
    }

    // Fallback to first revenue account
    return revenueAccounts.isNotEmpty ? revenueAccounts.first : null;
  }
}

/// Result of migration operation
class MigrationResult {
  final int totalRecords;
  final int migratedRecords;
  final int failedRecords;
  final List<String> errors;
  final bool dryRun;

  MigrationResult({
    required this.totalRecords,
    required this.migratedRecords,
    required this.failedRecords,
    required this.errors,
    required this.dryRun,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get allSuccessful => failedRecords == 0;
  double get successRate =>
      totalRecords > 0 ? migratedRecords / totalRecords : 0.0;

  @override
  String toString() {
    return 'MigrationResult(total: $totalRecords, migrated: $migratedRecords, '
        'failed: $failedRecords, dryRun: $dryRun)';
  }
}

/// Account mapping for migration
class AccountMapping {
  final String sourceAccountId;
  final String sourceAccountName;
  final String destinationAccountId;
  final String destinationAccountName;

  AccountMapping({
    required this.sourceAccountId,
    required this.sourceAccountName,
    required this.destinationAccountId,
    required this.destinationAccountName,
  });
}
