import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/core/services/transaction_account_mapping_service.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

void main() {
  group('Transaction Account Mapping Tests', () {
    group('VoucherAccountMapping Validation', () {
      test('should validate correct account types for voucher components', () {
        final mapping = _createTestVoucherAccountMapping();
        final validation = mapping.validateAccountTypes();

        expect(validation.isValid, true);
        expect(validation.errors, isEmpty);
        print(
            '✅ All account types are correctly configured for voucher transactions');
      });

      test('should identify account type categories correctly', () {
        final mapping = _createTestVoucherAccountMapping();

        // Verify liability accounts
        expect(mapping.brokerFeesAccount.accountType.category,
            AccountCategory.liabilities);
        expect(mapping.truckFarePayableAccount.accountType.category,
            AccountCategory.liabilities);
        expect(mapping.freightTaxPayableAccount.accountType.category,
            AccountCategory.liabilities);

        // Verify equity accounts
        expect(mapping.munshianaAccount.accountType.category,
            AccountCategory.equity);
        expect(mapping.netProfitAccount.accountType.category,
            AccountCategory.equity);

        // Verify asset accounts
        expect(
            mapping.cashAccount.accountType.category, AccountCategory.assets);
        expect(mapping.nlcFareReceivableAccount.accountType.category,
            AccountCategory.assets);
        expect(mapping.salesTaxReceivableAccount.accountType.category,
            AccountCategory.assets);

        // Verify revenue accounts
        expect(mapping.freightRevenueAccount.accountType.category,
            AccountCategory.revenue);

        print('✅ All account categories are correctly mapped');
      });

      test('should have correct debit/credit behavior for each account type',
          () {
        final mapping = _createTestVoucherAccountMapping();

        // Test liability accounts (should be credit accounts)
        expect(_isCreditAccount(mapping.brokerFeesAccount.accountType), true);
        expect(_isCreditAccount(mapping.truckFarePayableAccount.accountType),
            true);
        expect(_isCreditAccount(mapping.freightTaxPayableAccount.accountType),
            true);

        // Test equity accounts (should be credit accounts)
        expect(_isCreditAccount(mapping.munshianaAccount.accountType), true);
        expect(_isCreditAccount(mapping.netProfitAccount.accountType), true);

        // Test asset accounts (should be debit accounts)
        expect(_isDebitAccount(mapping.cashAccount.accountType), true);
        expect(_isDebitAccount(mapping.nlcFareReceivableAccount.accountType),
            true);
        expect(_isDebitAccount(mapping.salesTaxReceivableAccount.accountType),
            true);

        // Test revenue accounts (should be credit accounts)
        expect(
            _isCreditAccount(mapping.freightRevenueAccount.accountType), true);

        print('✅ All accounts have correct debit/credit behavior');
      });
    });

    group('Account Type Requirements', () {
      test('should meet voucher system requirements', () {
        final mapping = _createTestVoucherAccountMapping();

        // Verify specific account type requirements from the specification

        // 1. Liability Accounts (Credit increases balance)
        expect(
            mapping.brokerFeesAccount.accountType, AccountType.accountsPayable);
        expect(mapping.truckFarePayableAccount.accountType,
            AccountType.accountsPayable);
        expect(mapping.freightTaxPayableAccount.accountType,
            AccountType.currentLiabilities);

        // 2. Equity Accounts (Credit increases balance)
        expect(mapping.munshianaAccount.accountType,
            AccountType.equityServiceRevenue);
        expect(
            mapping.netProfitAccount.accountType, AccountType.retainedEarnings);

        // 3. Asset Accounts (Debit increases balance)
        expect(mapping.nlcFareReceivableAccount.accountType,
            AccountType.accountsReceivable);
        expect(mapping.salesTaxReceivableAccount.accountType,
            AccountType.accountsReceivable);
        expect(mapping.cashAccount.accountType, AccountType.cash);

        print('✅ All account types meet voucher system requirements');
      });
    });
  });
}

/// Create a test voucher account mapping with correct account types
VoucherAccountMapping _createTestVoucherAccountMapping() {
  return VoucherAccountMapping(
    // Liability accounts
    brokerFeesAccount: ChartOfAccountsModel(
      id: 'broker_fees_001',
      accountNumber: 'AP-001',
      accountName: 'Broker Fees Payable',
      category: AccountCategory.liabilities,
      accountType: AccountType.accountsPayable,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    truckFarePayableAccount: ChartOfAccountsModel(
      id: 'truck_fare_001',
      accountNumber: 'AP-002',
      accountName: 'Truck Fare Payable',
      category: AccountCategory.liabilities,
      accountType: AccountType.accountsPayable,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    brokerFeePayableAccount: ChartOfAccountsModel(
      id: 'broker_fee_payable_001',
      accountNumber: 'AP-003',
      accountName: 'Broker Fee Payable',
      category: AccountCategory.liabilities,
      accountType: AccountType.accountsPayable,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    freightTaxPayableAccount: ChartOfAccountsModel(
      id: 'freight_tax_001',
      accountNumber: 'CL-001',
      accountName: '6.9% Tax Payable',
      category: AccountCategory.liabilities,
      accountType: AccountType.currentLiabilities,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    salesTaxPayableAccount: ChartOfAccountsModel(
      id: 'sales_tax_payable_001',
      accountNumber: 'CL-002',
      accountName: 'Sales Tax Payable',
      category: AccountCategory.liabilities,
      accountType: AccountType.currentLiabilities,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),

    // Equity accounts
    munshianaAccount: ChartOfAccountsModel(
      id: 'munshiana_001',
      accountNumber: 'EQ-001',
      accountName: 'Munshiana Service Revenue',
      category: AccountCategory.equity,
      accountType: AccountType.equityServiceRevenue,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    netProfitAccount: ChartOfAccountsModel(
      id: 'net_profit_001',
      accountNumber: 'EQ-002',
      accountName: 'Retained Earnings',
      category: AccountCategory.equity,
      accountType: AccountType.retainedEarnings,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),

    // Revenue accounts
    freightRevenueAccount: ChartOfAccountsModel(
      id: 'freight_revenue_001',
      accountNumber: 'REV-001',
      accountName: 'Freight Revenue',
      category: AccountCategory.revenue,
      accountType: AccountType.salesRevenue,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),

    // Asset accounts
    cashAccount: ChartOfAccountsModel(
      id: 'cash_001',
      accountNumber: 'CASH-001',
      accountName: 'Cash',
      category: AccountCategory.assets,
      accountType: AccountType.cash,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    nlcFareReceivableAccount: ChartOfAccountsModel(
      id: 'nlc_receivable_001',
      accountNumber: 'AR-001',
      accountName: 'NLC Receivable',
      category: AccountCategory.assets,
      accountType: AccountType.accountsReceivable,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
    salesTaxReceivableAccount: ChartOfAccountsModel(
      id: 'sales_tax_receivable_001',
      accountNumber: 'AR-002',
      accountName: '15% Tax Receivable',
      category: AccountCategory.assets,
      accountType: AccountType.accountsReceivable,
      isActive: true,
      balance: 0.0,
      createdAt: DateTime.now(),
      uid: 'test_uid',
      childAccountIds: [],
    ),
  );
}

/// Helper function to check if account type is a debit account
bool _isDebitAccount(AccountType accountType) {
  switch (accountType.category) {
    case AccountCategory.assets:
    case AccountCategory.expenses:
      return true;
    case AccountCategory.liabilities:
    case AccountCategory.equity:
    case AccountCategory.revenue:
      return false;
  }
}

/// Helper function to check if account type is a credit account
bool _isCreditAccount(AccountType accountType) {
  return !_isDebitAccount(accountType);
}
