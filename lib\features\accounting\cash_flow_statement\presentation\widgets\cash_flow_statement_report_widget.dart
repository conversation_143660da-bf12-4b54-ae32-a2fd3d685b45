import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../../models/finance/cash_flow_statement_model.dart';
import '../controllers/cash_flow_statement_controller.dart';

/// Widget to display Cash Flow Statement report
class CashFlowStatementReportWidget extends StatelessWidget {
  final CashFlowStatementController controller;

  const CashFlowStatementReportWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final report = controller.currentReport;
      if (report == null) return const SizedBox.shrink();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Report header
          _buildReportHeader(context, report),
          const SizedBox(height: 24),

          // Operating Activities
          _buildActivitySection(
            context,
            'Operating Activities',
            report.operatingActivities,
            Icons.business_center,
            Colors.blue,
          ),
          const SizedBox(height: 16),

          // Investing Activities
          _buildActivitySection(
            context,
            'Investing Activities',
            report.investingActivities,
            Icons.trending_up,
            Colors.green,
          ),
          const SizedBox(height: 16),

          // Financing Activities
          _buildActivitySection(
            context,
            'Financing Activities',
            report.financingActivities,
            Icons.account_balance,
            Colors.orange,
          ),
          const SizedBox(height: 24),

          // Summary section
          _buildSummarySection(context, report),
          const SizedBox(height: 24),

          // Export buttons
          _buildExportButtons(context),
        ],
      );
    });
  }

  /// Build report header
  Widget _buildReportHeader(
      BuildContext context, CashFlowStatementReport report) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.1),
            Colors.transparent
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.assessment,
                  color: Theme.of(context).primaryColor, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      report.companyName,
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                    ),
                    Text(
                      'Cash Flow Statement',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildInfoChip(
                context,
                'Period',
                controller.reportPeriod,
                Icons.date_range,
              ),
              const SizedBox(width: 12),
              _buildInfoChip(
                context,
                'Method',
                controller.useDirectMethod ? 'Direct' : 'Indirect',
                Icons.analytics,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build info chip
  Widget _buildInfoChip(
      BuildContext context, String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 6),
          Text(
            '$label: $value',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }

  /// Build activity section
  Widget _buildActivitySection(
    BuildContext context,
    String title,
    CashFlowActivitySection section,
    IconData icon,
    Color color,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Section header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: section.netCashFlow >= 0
                        ? Colors.green[100]
                        : Colors.red[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _formatCurrency(section.netCashFlow),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: section.netCashFlow >= 0
                              ? Colors.green[700]
                              : Colors.red[700],
                        ),
                  ),
                ),
              ],
            ),
          ),

          // Line items or empty state
          if (section.lineItems.isNotEmpty) ...[
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: section.lineItems.length,
              separatorBuilder: (context, index) =>
                  Divider(height: 1, color: Colors.grey[200]),
              itemBuilder: (context, index) {
                final item = section.lineItems[index];
                return _buildLineItem(context, item);
              },
            ),
          ] else ...[
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Icon(Icons.info_outline, color: Colors.grey[400], size: 32),
                  const SizedBox(height: 8),
                  Text(
                    'No ${title.toLowerCase()} for this period',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build line item
  Widget _buildLineItem(BuildContext context, CashFlowLineItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Account info
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.accountName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                if (item.description.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    item.description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ],
            ),
          ),

          // Amount
          Expanded(
            flex: 1,
            child: Text(
              _formatCurrency(item.displayAmount),
              textAlign: TextAlign.right,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: item.isInflow ? Colors.green[700] : Colors.red[700],
                  ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build summary section
  Widget _buildSummarySection(
      BuildContext context, CashFlowStatementReport report) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cash Flow Summary',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          _buildSummaryRow(
              context, 'Beginning Cash Balance', report.beginningCashBalance),
          _buildSummaryRow(context, 'Net Cash from Operating Activities',
              report.operatingActivities.netCashFlow),
          _buildSummaryRow(context, 'Net Cash from Investing Activities',
              report.investingActivities.netCashFlow),
          _buildSummaryRow(context, 'Net Cash from Financing Activities',
              report.financingActivities.netCashFlow),
          const Divider(),
          _buildSummaryRow(
            context,
            'Net Change in Cash',
            report.netChangeInCash,
            isTotal: true,
          ),
          _buildSummaryRow(
            context,
            'Ending Cash Balance',
            report.endingCashBalance,
            isTotal: true,
          ),
        ],
      ),
    );
  }

  /// Build summary row
  Widget _buildSummaryRow(BuildContext context, String label, double amount,
      {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                ),
          ),
          Text(
            _formatCurrency(amount),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
                  color: amount >= 0 ? Colors.green[700] : Colors.red[700],
                ),
          ),
        ],
      ),
    );
  }

  /// Build export buttons
  Widget _buildExportButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: controller.exportToPDF,
            icon: const Icon(Icons.picture_as_pdf),
            label: const Text('Export PDF'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: controller.exportToExcel,
            icon: const Icon(Icons.table_chart),
            label: const Text('Export Excel'),
          ),
        ),
      ],
    );
  }

  /// Format currency
  String _formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2);
    return formatter.format(amount);
  }
}
