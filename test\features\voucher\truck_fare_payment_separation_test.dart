import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/payment_transaction_model.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

/// Test to verify truck fare payment separation based on payment method
/// This test ensures check payments use truck fare by cheque account
/// while account transfers use regular truck fare account
void main() {
  group('Truck Fare Payment Separation Tests', () {
    late ChartOfAccountsModel regularTruckFareAccount;
    late ChartOfAccountsModel truckFareByChequeAccount;

    setUpAll(() {
      // Create mock accounts
      regularTruckFareAccount = ChartOfAccountsModel(
        id: 'truck_fare_regular_001',
        accountName: 'Truck Fare Payable',
        accountNumber: '2100',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        description: 'Regular truck fare account for account transfers',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );

      truckFareByChequeAccount = ChartOfAccountsModel(
        id: 'truck_fare_cheque_001',
        accountName: 'Truck Fare by Cheque',
        accountNumber: '2101',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        description: 'Truck fare account for check payments',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );
    });

    test('should correctly identify check payment method for truck fare separation', () {
      // Arrange: Create different payment methods
      const checkPayment = PaymentMethod.check;
      const accountTransferPayment = PaymentMethod.accountTransfer;
      const cashPayment = PaymentMethod.cash;
      const fuelCardPayment = PaymentMethod.fuelCard;

      // Act & Assert: Verify check payment is correctly identified
      expect(checkPayment == PaymentMethod.check, isTrue, 
          reason: 'Check payment should be identified as check method');
      
      expect(accountTransferPayment == PaymentMethod.check, isFalse,
          reason: 'Account transfer should NOT be identified as check method');
      
      expect(cashPayment == PaymentMethod.check, isFalse,
          reason: 'Cash payment should NOT be identified as check method');
      
      expect(fuelCardPayment == PaymentMethod.check, isFalse,
          reason: 'Fuel card payment should NOT be identified as check method');
    });

    test('should demonstrate truck fare account selection logic', () {
      // This test demonstrates the logic that would be used in the automatic journal entry service
      
      // Arrange: Define the account selection function (simulating the service logic)
      ChartOfAccountsModel? selectTruckFareAccount(PaymentMethod paymentMethod) {
        if (paymentMethod == PaymentMethod.check) {
          // For check payments, use the truck fare by cheque account
          return truckFareByChequeAccount;
        } else {
          // For account transfers and other methods, use the regular truck fare account
          return regularTruckFareAccount;
        }
      }

      // Act & Assert: Test different payment methods
      final checkAccount = selectTruckFareAccount(PaymentMethod.check);
      expect(checkAccount?.id, equals(truckFareByChequeAccount.id),
          reason: 'Check payments should use truck fare by cheque account');

      final transferAccount = selectTruckFareAccount(PaymentMethod.accountTransfer);
      expect(transferAccount?.id, equals(regularTruckFareAccount.id),
          reason: 'Account transfers should use regular truck fare account');

      final cashAccount = selectTruckFareAccount(PaymentMethod.cash);
      expect(cashAccount?.id, equals(regularTruckFareAccount.id),
          reason: 'Cash payments should use regular truck fare account');

      final fuelCardAccount = selectTruckFareAccount(PaymentMethod.fuelCard);
      expect(fuelCardAccount?.id, equals(regularTruckFareAccount.id),
          reason: 'Fuel card payments should use regular truck fare account');
    });

    test('should validate payment method enum values for truck fare separation', () {
      // Arrange & Act: Check all payment method enum values
      final allMethods = PaymentMethod.values;

      print('📋 All PaymentMethod enum values for truck fare separation:');
      for (final method in allMethods) {
        final shouldUseChequeAccount = method == PaymentMethod.check;
        print('  - ${method.name}: use cheque account = $shouldUseChequeAccount');
      }

      // Assert: Verify expected behavior
      expect(allMethods.contains(PaymentMethod.accountTransfer), isTrue);
      expect(allMethods.contains(PaymentMethod.check), isTrue);
      expect(allMethods.contains(PaymentMethod.cash), isTrue);
      expect(allMethods.contains(PaymentMethod.fuelCard), isTrue);

      // Verify check method is correctly identified
      expect(PaymentMethod.check == PaymentMethod.check, isTrue);
      expect(PaymentMethod.accountTransfer == PaymentMethod.check, isFalse);
      expect(PaymentMethod.cash == PaymentMethod.check, isFalse);
      expect(PaymentMethod.fuelCard == PaymentMethod.check, isFalse);
    });
  });
}
