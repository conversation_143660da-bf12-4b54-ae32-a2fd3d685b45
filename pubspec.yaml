name: logestics
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.3.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  get_storage: ^2.1.1

  either_dart: ^1.0.0
  firebase_auth: ^5.5.2
  firebase_core: ^3.12.0
  cloud_firestore: ^5.6.4
  firebase_storage: ^12.4.5
  image_picker: ^1.0.4
  url_launcher: ^6.3.1
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.17
  get: ^4.7.2
  syncfusion_flutter_charts: ^29.2.5
  simple_progress_indicators: ^0.2.0
  styled_divider: ^1.0.5
  smooth_star_rating_nsafe: ^1.0.0+1
  syncfusion_flutter_maps: ^29.2.5
  fl_chart: ^1.0.0
  pie_chart: ^5.4.0
  number_paginator: ^1.1.0
  syncfusion_flutter_datepicker: ^29.2.5
  sleek_circular_slider: ^2.0.1
  smooth_page_indicator: ^1.2.1
  carousel_slider: ^5.0.0
  table_calendar: ^3.2.0
  # quill_html_editor: ^2.2.8
  kanban_board: ^1.0.0
  file_picker: ^10.1.2
  simple_tags: ^0.0.6
  badges: ^3.1.2
  flutter_typeahead: ^5.2.0
  textfield_tags: ^3.0.1
  super_drag_and_drop: ^0.8.24
  flutter_colorpicker: ^1.1.0
  intl: ^0.20.2
  provider: ^6.1.5
  flutter_spinkit: ^5.2.1
  percent_indicator: ^4.2.5
  dartz: ^0.10.1
  mask_text_input_formatter: ^2.9.0
  uuid: ^4.5.1
  layout_pro:
  universal_html:
  pdf: ^3.11.3
  printing: ^5.14.2
  path_provider: ^2.1.4
  excel: ^4.0.6


  path: any
  web: ^1.1.1
  open_file: ^3.3.2

  # Offline functionality dependencies
  drift: ^2.14.0
  sqlite3_flutter_libs: ^0.5.0
  connectivity_plus: ^6.0.5
  hive: ^2.2.3
  hive_flutter: ^1.1.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # Offline functionality dev dependencies
  drift_dev: ^2.14.0
  build_runner: ^2.4.0
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  assets:
    - assets/fonts/
    - assets/images/

  fonts:
    - family: nuberNextBold
      fonts:
        - asset: assets/fonts/NuberNext-Bold.otf
          weight: 700
    - family: nuberNextRegular
      fonts:
        - asset: assets/fonts/NuberNext-Regular.otf

    - family: Outfit
      fonts:
        - asset: assets/fonts/Outfit-Regular.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
