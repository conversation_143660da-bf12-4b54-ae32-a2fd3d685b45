# Voucher Chart of Accounts Migration Guide

## Overview

This document describes the migration process for converting existing voucher data from legacy account references to the new Chart of Accounts structure.

## Background

The voucher system previously used simple string-based account names (e.g., `brokerAccount`, `munshianaAccount`) to reference accounts. The new Chart of Accounts system provides:

- Hierarchical account structure with proper categorization
- Unique account IDs for reliable references
- Better integration with double-entry accounting
- Enhanced reporting and financial analysis capabilities

## Migration Process

### What Gets Migrated

The migration process converts the following legacy fields to Chart of Accounts references:

| Legacy Field | New Field | Description |
|--------------|-----------|-------------|
| `brokerAccount` | `brokerAccountId` | Broker fee expense account |
| `munshianaAccount` | `munshianaAccountId` | Munshiana fee expense account |

### Migration Steps

1. **Account Mapping Creation**
   - Load all Chart of Accounts for the company
   - Create mappings from legacy account names to Chart of Accounts IDs
   - Support both account names and account codes for mapping

2. **Voucher Analysis**
   - Scan all vouchers for legacy account fields
   - Identify vouchers that need migration
   - Skip vouchers already migrated

3. **Data Migration**
   - Map legacy account names to Chart of Accounts IDs
   - Add new Chart of Accounts fields to voucher data
   - Preserve legacy fields for backward compatibility
   - Add migration metadata

4. **Validation**
   - Verify all mappings are successful
   - Report unmapped accounts as warnings
   - Ensure data integrity

## Migration Services

### VoucherChartOfAccountsMigrationService

Core migration service that handles:
- Account mapping creation
- Voucher data migration
- Progress reporting
- Error handling

### MigrationCommandService

High-level service for:
- Migration execution
- Status checking
- Rollback operations
- Report generation

### VoucherMigrationController

UI controller for:
- Migration UI management
- Progress tracking
- User interaction

## Usage

### Programmatic Migration

```dart
final migrationService = VoucherChartOfAccountsMigrationService();

// Check if migration is needed
final migrationNeeded = await migrationService.checkMigrationNeeded(companyUid);

// Run dry run to preview changes
final dryRunResult = await migrationService.migrateVouchersToChartOfAccounts(
  uid: companyUid,
  dryRun: true,
);

// Execute actual migration
final migrationResult = await migrationService.migrateVouchersToChartOfAccounts(
  uid: companyUid,
  dryRun: false,
);
```

### UI Migration

```dart
// Navigate to migration screen
Get.to(() => const VoucherMigrationScreen());

// Or use controller directly
final controller = Get.put(VoucherMigrationController());
await controller.runDryRun();
await controller.runMigration();
```

### Command Service

```dart
final commandService = MigrationCommandService();

// Check status
final status = await commandService.checkMigrationStatus(companyUid);

// Execute migration
final result = await commandService.executeVoucherMigration(
  uid: companyUid,
  dryRun: false,
);

// Generate report
final report = await commandService.generateMigrationReport(uid: companyUid);
```

## Migration Data Structure

### Before Migration
```json
{
  "voucherNumber": "V001",
  "brokerAccount": "Broker Expenses",
  "munshianaAccount": "Munshiana Fees",
  // ... other fields
}
```

### After Migration
```json
{
  "voucherNumber": "V001",
  "brokerAccount": "Broker Expenses",           // Preserved for compatibility
  "munshianaAccount": "Munshiana Fees",        // Preserved for compatibility
  "brokerAccountId": "acc_123456789",          // New Chart of Accounts ID
  "munshianaAccountId": "acc_987654321",       // New Chart of Accounts ID
  "migratedToChartOfAccounts": true,           // Migration flag
  "migrationDate": *************,              // Migration timestamp
  // ... other fields
}
```

## Error Handling

### Common Issues

1. **Account Not Found**
   - Legacy account name doesn't match any Chart of Accounts
   - Reported as warning, migration continues
   - Manual mapping may be required

2. **Duplicate Account Names**
   - Multiple Chart of Accounts with same name
   - First match is used, warning is logged
   - Review Chart of Accounts for duplicates

3. **Invalid Account Data**
   - Corrupted or malformed voucher data
   - Reported as error, voucher is skipped
   - Manual review required

### Rollback

If migration issues occur, individual vouchers can be rolled back:

```dart
final rollbackResult = await migrationService.rollbackVoucherMigration(
  uid: companyUid,
  voucherNumber: "V001",
);
```

This removes all Chart of Accounts fields and migration metadata.

## Best Practices

### Before Migration

1. **Backup Data**
   - Create full company backup before migration
   - Use the backup/restore system

2. **Review Chart of Accounts**
   - Ensure all required accounts exist
   - Remove duplicate account names
   - Verify account categories are correct

3. **Run Dry Run**
   - Always run dry run first
   - Review migration report
   - Address any warnings

### During Migration

1. **Monitor Progress**
   - Watch for errors and warnings
   - Don't interrupt migration process
   - Keep migration logs

2. **Validate Results**
   - Check migration report
   - Verify account mappings
   - Test voucher functionality

### After Migration

1. **Test System**
   - Create new vouchers
   - Verify journal entry generation
   - Check account balance updates

2. **Monitor Performance**
   - Ensure no performance degradation
   - Check for any UI issues
   - Validate reporting accuracy

## Troubleshooting

### Migration Fails to Start
- Check Chart of Accounts service availability
- Verify company UID is correct
- Ensure user has proper permissions

### Partial Migration
- Review error logs for specific issues
- Fix data issues and re-run migration
- Use rollback if necessary

### Performance Issues
- Migration processes large datasets
- Run during off-peak hours
- Monitor system resources

## Integration

The migration system integrates with:

- **Chart of Accounts System**: For account mapping
- **Voucher System**: For data updates
- **Journal Entry System**: For accounting integration
- **Backup/Restore System**: For data safety
- **Validation Services**: For data integrity

## Future Considerations

- **Automated Migration**: Trigger migration on Chart of Accounts creation
- **Incremental Migration**: Migrate vouchers as they're accessed
- **Migration Scheduling**: Schedule migrations during maintenance windows
- **Advanced Mapping**: Support custom account mapping rules
