import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:logestics/features/voucher/repositories/voucher_repository.dart';
import 'package:logestics/firebase_service/voucher/voucher_crud_firebase_service.dart';
import 'package:logestics/core/services/voucher_accounting_hook_service.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/models/voucher_model.dart';

// Generate mocks
@GenerateMocks([
  VoucherCrudFirebaseService,
  VoucherAccountingHookService,
])
import 'voucher_atomic_transaction_test.mocks.dart';

void main() {
  group('Voucher Atomic Transaction Tests', () {
    late VoucherRepositoryImp repository;
    late MockVoucherCrudFirebaseService mockFirebaseService;
    late MockVoucherAccountingHookService mockHookService;

    setUp(() {
      mockFirebaseService = MockVoucherCrudFirebaseService();
      mockHookService = MockVoucherAccountingHookService();
      repository = VoucherRepositoryImp(mockFirebaseService);

      // Replace the hook service with our mock
      // Note: This would require making _hookService injectable or public
      // For now, we'll test the behavior through integration
    });

    group('Atomic Transaction Rollback', () {
      test('should rollback voucher creation when journal entry creation fails',
          () async {
        // Arrange
        const uid = 'test-uid';
        final voucherData = _createTestVoucherData();

        // Mock successful voucher creation
        when(mockFirebaseService.createVoucherToFirebase(
          uid: uid,
          voucher: voucherData,
        )).thenAnswer((_) async => {});

        // Mock journal entry creation failure
        when(mockHookService.onVoucherCreated(voucherData, uid)).thenThrow(
            Exception(
                'Journal entry creation failed: Double-entry validation failed'));

        // Mock successful voucher deletion (rollback)
        when(mockFirebaseService.deleteVoucherFromFirebase(
          uid: uid,
          voucherNumber: voucherData['voucherNumber'] as String,
        )).thenAnswer((_) async => {});

        // Act
        final result = await repository.createVoucher(
          uid: uid,
          voucher: voucherData,
        );

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) {
            expect(failure.code, 'unexpected-error');
            expect(failure.message, contains('Journal entry creation failed'));
          },
          (success) => fail('Expected failure but got success'),
        );

        // Verify voucher creation was attempted
        verify(mockFirebaseService.createVoucherToFirebase(
          uid: uid,
          voucher: voucherData,
        )).called(1);

        // Verify journal entry creation was attempted
        verify(mockHookService.onVoucherCreated(voucherData, uid)).called(1);

        // Verify voucher deletion (rollback) was called
        verify(mockFirebaseService.deleteVoucherFromFirebase(
          uid: uid,
          voucherNumber: voucherData['voucherNumber'] as String,
        )).called(1);
      });

      test('should handle rollback failure gracefully', () async {
        // Arrange
        const uid = 'test-uid';
        final voucherData = _createTestVoucherData();

        // Mock successful voucher creation
        when(mockFirebaseService.createVoucherToFirebase(
          uid: uid,
          voucher: voucherData,
        )).thenAnswer((_) async => {});

        // Mock journal entry creation failure
        when(mockHookService.onVoucherCreated(voucherData, uid))
            .thenThrow(Exception('Journal entry creation failed'));

        // Mock rollback failure
        when(mockFirebaseService.deleteVoucherFromFirebase(
          uid: uid,
          voucherNumber: voucherData['voucherNumber'] as String,
        )).thenThrow(Exception('Rollback failed'));

        // Act
        final result = await repository.createVoucher(
          uid: uid,
          voucher: voucherData,
        );

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) {
            expect(failure.code, 'unexpected-error');
            expect(
                failure.message,
                contains(
                    'Journal entry creation failed and voucher rollback also failed'));
          },
          (success) => fail('Expected failure but got success'),
        );
      });

      test(
          'should succeed when both voucher and journal entry creation succeed',
          () async {
        // Arrange
        const uid = 'test-uid';
        final voucherData = _createTestVoucherData();

        // Mock successful voucher creation
        when(mockFirebaseService.createVoucherToFirebase(
          uid: uid,
          voucher: voucherData,
        )).thenAnswer((_) async => {});

        // Mock successful journal entry creation
        when(mockHookService.onVoucherCreated(voucherData, uid))
            .thenAnswer((_) async => {});

        // Act
        final result = await repository.createVoucher(
          uid: uid,
          voucher: voucherData,
        );

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) =>
              fail('Expected success but got failure: ${failure.message}'),
          (success) {
            expect(success.message, 'Voucher created successfully.');
          },
        );

        // Verify voucher creation was called
        verify(mockFirebaseService.createVoucherToFirebase(
          uid: uid,
          voucher: voucherData,
        )).called(1);

        // Verify journal entry creation was called
        verify(mockHookService.onVoucherCreated(voucherData, uid)).called(1);

        // Verify no rollback was attempted
        verifyNever(mockFirebaseService.deleteVoucherFromFirebase(
          uid: any,
          voucherNumber: any,
        ));
      });
    });

    group('Validation Tests', () {
      test('should fail validation when required accounts are missing',
          () async {
        // Arrange
        const uid = 'test-uid';
        final voucherData = _createInvalidVoucherData();

        // Act
        final result = await repository.createVoucher(
          uid: uid,
          voucher: voucherData,
        );

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) {
            expect(failure.code, 'validation-error');
            expect(failure.message,
                contains('Chart of Accounts validation failed'));
          },
          (success) => fail('Expected validation failure but got success'),
        );

        // Verify no Firebase operations were attempted
        verifyNever(mockFirebaseService.createVoucherToFirebase(
          uid: any,
          voucher: any,
        ));
        verifyNever(mockHookService.onVoucherCreated(any, any));
      });
    });
  });
}

/// Create test voucher data with valid structure
Map<String, dynamic> _createTestVoucherData() {
  return {
    'voucherNumber': 'TEST-001',
    'voucherStatus': 'Active',
    'departureDate': '2024-01-15',
    'driverName': 'Test Driver',
    'invoiceTasNumberList': ['TAS-001'],
    'invoiceBiltyNumberList': ['BILTY-001'],
    'weightInTons': 25,
    'productName': 'Test Product',
    'totalNumberOfBags': 500,
    'brokerType': 'Outsource',
    'brokerName': 'Test Broker',
    'brokerFees': 5000.0,
    'munshianaFees': 3000.0,
    'brokerAccount': 'Test Broker Account',
    'munshianaAccount': 'Test Munshiana Account',
    'driverPhoneNumber': '**********',
    'truckNumber': 'TRK-001',
    'conveyNoteNumber': 'CN-001',
    'totalFreight': 50000.0,
    'companyFreight': 50000.0,
    'settledFreight': 45000.0,
    'paymentTransactions': [
      {
        'id': 'payment-1',
        'voucherId': 'TEST-001',
        'method': 'cash',
        'status': 'paid',
        'amount': 10000.0,
        'pendingAmount': 0.0,
        'transactionDate': DateTime.now().toIso8601String(),
        'accountId': 'account-1',
        'accountName': 'Test Account',
        'notes': 'Test payment',
      }
    ],
    'brokerAccountId': 'broker-account-id',
    'munshianaAccountId': 'munshiana-account-id',
    'createdAt': DateTime.now().millisecondsSinceEpoch,
  };
}

/// Create invalid voucher data for validation testing
Map<String, dynamic> _createInvalidVoucherData() {
  return {
    'voucherNumber': 'TEST-002',
    'voucherStatus': 'Active',
    'departureDate': '2024-01-15',
    'driverName': 'Test Driver',
    'invoiceTasNumberList': ['TAS-002'],
    'invoiceBiltyNumberList': ['BILTY-002'],
    'weightInTons': 25,
    'productName': 'Test Product',
    'totalNumberOfBags': 500,
    'brokerType': 'Outsource',
    'brokerName': 'Test Broker',
    'brokerFees': 5000.0,
    'munshianaFees': 3000.0,
    'brokerAccount': 'Test Broker Account',
    'munshianaAccount': 'Test Munshiana Account',
    'driverPhoneNumber': '**********',
    'truckNumber': 'TRK-002',
    'conveyNoteNumber': 'CN-002',
    'totalFreight': 0.0, // Invalid: total freight must be > 0
    'companyFreight': 50000.0,
    'settledFreight': 45000.0,
    'paymentTransactions': [
      {
        'id': 'payment-2',
        'voucherId': 'TEST-002',
        'method': 'cash',
        'status': 'paid',
        'amount': -1000.0, // Invalid: negative amount
        'pendingAmount': 0.0,
        'transactionDate': DateTime.now().toIso8601String(),
        'accountId': '', // Invalid: empty account ID
        'accountName': '', // Invalid: empty account name
        'notes': '',
      }
    ],
    'brokerAccountId': '', // Invalid: empty broker account ID
    'munshianaAccountId': '', // Invalid: empty munshiana account ID
    'createdAt': DateTime.now().millisecondsSinceEpoch,
  };
}
