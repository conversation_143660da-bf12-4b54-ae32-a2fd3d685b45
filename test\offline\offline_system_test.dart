import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:logestics/core/services/connectivity_service.dart';
import 'package:logestics/core/services/offline_state_service.dart';
import 'package:logestics/core/services/sync_service.dart';
import 'package:logestics/core/services/offline_voucher_service.dart';
import 'package:logestics/core/services/offline_journal_service.dart';
import 'package:logestics/features/voucher/repositories/offline_voucher_repository.dart';
import 'package:logestics/features/voucher/repositories/voucher_repository.dart';
import 'package:logestics/models/offline/offline_transaction_state.dart';

// Generate mocks
@GenerateMocks([
  ConnectivityService,
  OfflineStateService,
  SyncService,
  OfflineVoucherService,
  OfflineJournalService,
  VoucherRepository,
])
import 'offline_system_test.mocks.dart';

void main() {
  group('Offline System Tests', () {
    late MockConnectivityService mockConnectivityService;
    late MockOfflineStateService mockOfflineStateService;
    late MockSyncService mockSyncService;
    late MockOfflineVoucherService mockOfflineVoucherService;
    late MockOfflineJournalService mockOfflineJournalService;
    late MockVoucherRepository mockOnlineVoucherRepository;
    late OfflineVoucherRepository offlineVoucherRepository;

    setUp(() async {
      // Initialize Hive for testing
      await Hive.initFlutter();
      
      // Create mocks
      mockConnectivityService = MockConnectivityService();
      mockOfflineStateService = MockOfflineStateService();
      mockSyncService = MockSyncService();
      mockOfflineVoucherService = MockOfflineVoucherService();
      mockOfflineJournalService = MockOfflineJournalService();
      mockOnlineVoucherRepository = MockVoucherRepository();
      
      // Setup Get dependencies
      Get.reset();
      Get.put<ConnectivityService>(mockConnectivityService);
      Get.put<OfflineStateService>(mockOfflineStateService);
      Get.put<SyncService>(mockSyncService);
      Get.put<OfflineVoucherService>(mockOfflineVoucherService);
      Get.put<OfflineJournalService>(mockOfflineJournalService);
      
      // Create offline voucher repository
      offlineVoucherRepository = OfflineVoucherRepository(
        onlineRepository: mockOnlineVoucherRepository,
        connectivityService: mockConnectivityService,
        offlineVoucherService: mockOfflineVoucherService,
      );
    });

    tearDown(() async {
      Get.reset();
      await Hive.close();
    });

    group('Connectivity Management', () {
      test('should detect online status correctly', () {
        // Arrange
        when(mockConnectivityService.isOnline).thenReturn(true);
        when(mockConnectivityService.status).thenReturn(ConnectivityStatus.online);
        
        // Act & Assert
        expect(mockConnectivityService.isOnline, true);
        expect(mockConnectivityService.status, ConnectivityStatus.online);
      });

      test('should detect offline status correctly', () {
        // Arrange
        when(mockConnectivityService.isOnline).thenReturn(false);
        when(mockConnectivityService.status).thenReturn(ConnectivityStatus.offline);
        
        // Act & Assert
        expect(mockConnectivityService.isOnline, false);
        expect(mockConnectivityService.status, ConnectivityStatus.offline);
      });

      test('should trigger sync when connectivity is restored', () async {
        // Arrange
        when(mockConnectivityService.isOnline).thenReturn(true);
        when(mockOfflineStateService.pendingOperations).thenReturn(5);
        
        // Act
        mockConnectivityService.onConnectivityRestored();
        
        // Assert
        verify(mockOfflineStateService.onConnectivityRestored()).called(1);
      });
    });

    group('Offline Voucher Creation', () {
      test('should create voucher offline when no connectivity', () async {
        // Arrange
        final voucherData = _createTestVoucherData();
        when(mockConnectivityService.isOnline).thenReturn(false);
        when(mockOfflineVoucherService.createVoucherOffline(
          uid: anyNamed('uid'),
          voucherData: anyNamed('voucherData'),
        )).thenAnswer((_) async => Right(SuccessObj(message: 'Voucher created offline')));
        
        // Act
        final result = await offlineVoucherRepository.createVoucher(
          uid: 'test-uid',
          voucher: voucherData,
        );
        
        // Assert
        expect(result.isRight(), true);
        verify(mockOfflineVoucherService.createVoucherOffline(
          uid: 'test-uid',
          voucherData: voucherData,
        )).called(1);
        verifyNever(mockOnlineVoucherRepository.createVoucher(
          uid: anyNamed('uid'),
          voucher: anyNamed('voucher'),
        ));
      });

      test('should fallback to offline when online creation fails', () async {
        // Arrange
        final voucherData = _createTestVoucherData();
        when(mockConnectivityService.isOnline).thenReturn(true);
        when(mockOnlineVoucherRepository.createVoucher(
          uid: anyNamed('uid'),
          voucher: anyNamed('voucher'),
        )).thenAnswer((_) async => Left(FailureObj(code: 'network-error', message: 'Network failed')));
        when(mockOfflineVoucherService.createVoucherOffline(
          uid: anyNamed('uid'),
          voucherData: anyNamed('voucherData'),
        )).thenAnswer((_) async => Right(SuccessObj(message: 'Voucher created offline')));
        
        // Act
        final result = await offlineVoucherRepository.createVoucher(
          uid: 'test-uid',
          voucher: voucherData,
        );
        
        // Assert
        expect(result.isRight(), true);
        verify(mockOnlineVoucherRepository.createVoucher(
          uid: 'test-uid',
          voucher: voucherData,
        )).called(1);
        verify(mockOfflineVoucherService.createVoucherOffline(
          uid: 'test-uid',
          voucherData: voucherData,
        )).called(1);
      });

      test('should create voucher online when connectivity is available', () async {
        // Arrange
        final voucherData = _createTestVoucherData();
        when(mockConnectivityService.isOnline).thenReturn(true);
        when(mockOnlineVoucherRepository.createVoucher(
          uid: anyNamed('uid'),
          voucher: anyNamed('voucher'),
        )).thenAnswer((_) async => Right(SuccessObj(message: 'Voucher created online')));
        
        // Act
        final result = await offlineVoucherRepository.createVoucher(
          uid: 'test-uid',
          voucher: voucherData,
        );
        
        // Assert
        expect(result.isRight(), true);
        verify(mockOnlineVoucherRepository.createVoucher(
          uid: 'test-uid',
          voucher: voucherData,
        )).called(1);
        verifyNever(mockOfflineVoucherService.createVoucherOffline(
          uid: anyNamed('uid'),
          voucherData: anyNamed('voucherData'),
        ));
      });
    });

    group('Sync Operations', () {
      test('should sync pending operations when online', () async {
        // Arrange
        when(mockConnectivityService.isOnline).thenReturn(true);
        when(mockSyncService.isSyncing).thenReturn(false);
        when(mockSyncService.syncPendingOperations()).thenAnswer(
          (_) async => Right(SuccessObj(message: 'Sync completed')),
        );
        
        // Act
        final result = await mockSyncService.syncPendingOperations();
        
        // Assert
        expect(result.isRight(), true);
        verify(mockSyncService.syncPendingOperations()).called(1);
      });

      test('should not sync when already syncing', () async {
        // Arrange
        when(mockSyncService.isSyncing).thenReturn(true);
        when(mockSyncService.syncPendingOperations()).thenAnswer(
          (_) async => Left(FailureObj(code: 'sync-in-progress', message: 'Sync already in progress')),
        );
        
        // Act
        final result = await mockSyncService.syncPendingOperations();
        
        // Assert
        expect(result.isLeft(), true);
      });

      test('should not sync when offline', () async {
        // Arrange
        when(mockConnectivityService.isOffline).thenReturn(true);
        when(mockSyncService.syncPendingOperations()).thenAnswer(
          (_) async => Left(FailureObj(code: 'offline', message: 'Cannot sync while offline')),
        );
        
        // Act
        final result = await mockSyncService.syncPendingOperations();
        
        // Assert
        expect(result.isLeft(), true);
      });
    });

    group('Data Integrity', () {
      test('should maintain data consistency during offline operations', () async {
        // Arrange
        final voucherData = _createTestVoucherData();
        when(mockConnectivityService.isOnline).thenReturn(false);
        when(mockOfflineVoucherService.createVoucherOffline(
          uid: anyNamed('uid'),
          voucherData: anyNamed('voucherData'),
        )).thenAnswer((_) async => Right(SuccessObj(message: 'Voucher created offline')));
        
        // Act
        final result = await offlineVoucherRepository.createVoucher(
          uid: 'test-uid',
          voucher: voucherData,
        );
        
        // Assert
        expect(result.isRight(), true);
        // Verify that offline service was called with correct data
        verify(mockOfflineVoucherService.createVoucherOffline(
          uid: 'test-uid',
          voucherData: voucherData,
        )).called(1);
      });

      test('should handle atomic transaction failures correctly', () async {
        // Arrange
        final voucherData = _createTestVoucherData();
        when(mockConnectivityService.isOnline).thenReturn(false);
        when(mockOfflineVoucherService.createVoucherOffline(
          uid: anyNamed('uid'),
          voucherData: anyNamed('voucherData'),
        )).thenAnswer((_) async => Left(FailureObj(
          code: 'atomic-transaction-failed',
          message: 'Atomic transaction failed',
        )));
        
        // Act
        final result = await offlineVoucherRepository.createVoucher(
          uid: 'test-uid',
          voucher: voucherData,
        );
        
        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) => expect(failure.code, 'atomic-transaction-failed'),
          (success) => fail('Expected failure but got success'),
        );
      });
    });

    group('Error Recovery', () {
      test('should recover from interrupted operations', () async {
        // This test would verify that interrupted operations are properly
        // detected and recovered when the app restarts
        // Implementation would depend on the specific recovery mechanism
        expect(true, true); // Placeholder
      });

      test('should handle storage corruption gracefully', () async {
        // This test would verify that corrupted local storage is handled
        // without crashing the application
        expect(true, true); // Placeholder
      });
    });

    group('Performance Tests', () {
      test('should handle large numbers of offline operations efficiently', () async {
        // Test with 1000+ offline operations
        when(mockOfflineStateService.pendingOperations).thenReturn(1000);
        
        // Verify that the system can handle large queues
        expect(mockOfflineStateService.pendingOperations, 1000);
      });

      test('should sync operations in reasonable time', () async {
        // Test sync performance with time constraints
        final stopwatch = Stopwatch()..start();
        
        when(mockSyncService.syncPendingOperations()).thenAnswer(
          (_) async {
            await Future.delayed(Duration(milliseconds: 100)); // Simulate sync time
            return Right(SuccessObj(message: 'Sync completed'));
          },
        );
        
        final result = await mockSyncService.syncPendingOperations();
        stopwatch.stop();
        
        expect(result.isRight(), true);
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Should complete within 1 second
      });
    });
  });
}

/// Helper function to create test voucher data
Map<String, dynamic> _createTestVoucherData() {
  return {
    'voucherNumber': 'TEST-001',
    'voucherStatus': 'pending',
    'departureDate': '01/01/2024',
    'driverName': 'Test Driver',
    'invoiceTasNumberList': [],
    'invoiceBiltyNumberList': [],
    'weightInTons': 10,
    'productName': 'Test Product',
    'totalNumberOfBags': 100,
    'brokerType': 'outsource',
    'brokerName': 'Test Broker',
    'brokerFees': 1000.0,
    'munshianaFees': 500.0,
    'brokerAccount': 'Test Broker Account',
    'munshianaAccount': 'Test Munshiana Account',
    'driverPhoneNumber': '***********',
    'truckNumber': 'ABC123',
    'conveyNoteNumber': 'CN123',
    'totalFreight': 50000.0,
    'companyFreight': 45000.0,
    'settledFreight': 0.0,
    'paymentTransactions': [],
  };
}
