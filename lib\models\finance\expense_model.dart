class ExpenseModel {
  final String id;
  final String title;

  // Legacy account fields (maintained for backward compatibility)
  final String accountId;
  final String accountName;

  // Chart of Accounts fields for two-account system
  final String? sourceAccountId; // Account where money decreases from
  final String? sourceAccountName;
  final String? destinationAccountId; // Account where money increases to
  final String? destinationAccountName;

  final double amount;

  final String categoryId;
  final String categoryName;
  final String payeeId;
  final String payeeName;
  final String referenceNumber;
  final String notes;
  final DateTime createdAt;
  final String uid; // User ID who created this expense

  ExpenseModel({
    required this.id,
    required this.title,
    required this.accountId,
    required this.accountName,
    this.sourceAccountId,
    this.sourceAccountName,
    this.destinationAccountId,
    this.destinationAccountName,
    required this.amount,
    required this.categoryId,
    required this.categoryName,
    required this.payeeId,
    required this.payeeName,
    required this.referenceNumber,
    this.notes = '',
    required this.createdAt,
    this.uid = '', // Default empty string for backward compatibility
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      // Legacy account fields
      'accountId': accountId,
      'accountName': accountName,
      // Chart of Accounts fields
      'sourceAccountId': sourceAccountId,
      'sourceAccountName': sourceAccountName,
      'destinationAccountId': destinationAccountId,
      'destinationAccountName': destinationAccountName,
      'amount': amount,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'payeeId': payeeId,
      'payeeName': payeeName,
      'referenceNumber': referenceNumber,
      'notes': notes,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'uid': uid,
    };
  }

  factory ExpenseModel.fromJson(Map<String, dynamic> json) {
    return ExpenseModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      // Legacy account fields
      accountId: json['accountId'] ?? '',
      accountName: json['accountName'] ?? '',
      // Chart of Accounts fields
      sourceAccountId: json['sourceAccountId'],
      sourceAccountName: json['sourceAccountName'],
      destinationAccountId: json['destinationAccountId'],
      destinationAccountName: json['destinationAccountName'],
      amount: (json['amount'] ?? 0).toDouble(),
      categoryId: json['categoryId'] ?? '',
      categoryName: json['categoryName'] ?? '',
      payeeId: json['payeeId'] ?? '',
      payeeName: json['payeeName'] ?? '',
      referenceNumber: json['referenceNumber'] ?? '',
      notes: json['notes'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      uid: json['uid'] ?? '',
    );
  }

  // Create a copy of this model with updated fields
  ExpenseModel copyWith({
    String? id,
    String? title,
    String? accountId,
    String? accountName,
    String? sourceAccountId,
    String? sourceAccountName,
    String? destinationAccountId,
    String? destinationAccountName,
    double? amount,
    String? categoryId,
    String? categoryName,
    String? payeeId,
    String? payeeName,
    String? referenceNumber,
    String? notes,
    DateTime? createdAt,
    String? uid,
  }) {
    return ExpenseModel(
      id: id ?? this.id,
      title: title ?? this.title,
      accountId: accountId ?? this.accountId,
      accountName: accountName ?? this.accountName,
      sourceAccountId: sourceAccountId ?? this.sourceAccountId,
      sourceAccountName: sourceAccountName ?? this.sourceAccountName,
      destinationAccountId: destinationAccountId ?? this.destinationAccountId,
      destinationAccountName:
          destinationAccountName ?? this.destinationAccountName,
      amount: amount ?? this.amount,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      payeeId: payeeId ?? this.payeeId,
      payeeName: payeeName ?? this.payeeName,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      uid: uid ?? this.uid,
    );
  }

  // Helper methods for Chart of Accounts integration

  /// Check if this expense uses Chart of Accounts (has source and destination accounts)
  bool get usesChartOfAccounts =>
      sourceAccountId != null &&
      sourceAccountId!.isNotEmpty &&
      destinationAccountId != null &&
      destinationAccountId!.isNotEmpty;

  /// Check if this expense uses legacy account system
  bool get usesLegacyAccount => accountId.isNotEmpty && !usesChartOfAccounts;

  /// Get the effective source account ID (Chart of Accounts or legacy)
  String get effectiveSourceAccountId =>
      usesChartOfAccounts ? sourceAccountId! : accountId;

  /// Get the effective source account name (Chart of Accounts or legacy)
  String get effectiveSourceAccountName =>
      usesChartOfAccounts ? sourceAccountName! : accountName;

  /// Get a display-friendly description of the account setup
  String get accountSetupDescription {
    if (usesChartOfAccounts) {
      return 'From: $sourceAccountName → To: $destinationAccountName';
    } else {
      return 'Legacy Account: $accountName';
    }
  }

  @override
  String toString() {
    return 'ExpenseModel(id: $id, title: $title, amount: $amount, '
        'usesChartOfAccounts: $usesChartOfAccounts, '
        'accountSetup: $accountSetupDescription)';
  }
}
