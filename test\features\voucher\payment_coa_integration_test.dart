import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/payment_transaction_model.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

/// Test to verify payment system integration with Chart of Accounts
/// This test focuses on data model validation and business logic
void main() {
  group('Payment Chart of Accounts Integration Tests', () {
    // Mock accounts for testing
    late ChartOfAccountsModel bankAccount;
    late ChartOfAccountsModel cashAccount;

    setUpAll(() {
      // Create mock accounts
      bankAccount = ChartOfAccountsModel(
        id: 'bank_account_001',
        accountName: 'Main Bank Account',
        accountNumber: '1100',
        category: AccountCategory.assets,
        accountType: AccountType.bank,
        description: 'Primary bank account for payments',
        isActive: true,
        balance: 100000.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );

      cashAccount = ChartOfAccountsModel(
        id: 'cash_account_001',
        accountName: 'Cash Account',
        accountNumber: '1000',
        category: AccountCategory.assets,
        accountType: AccountType.cash,
        description: 'Cash account for payments',
        isActive: true,
        balance: 50000.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );
    });

    test('should create payment transaction with Chart of Accounts reference', () {
      // Arrange & Act: Create a check payment transaction with Chart of Accounts
      final checkPayment = PaymentTransactionModel(
        id: 'payment_001',
        voucherId: 'V-TEST-001',
        method: PaymentMethod.check,
        status: PaymentStatus.paid,
        amount: 25000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: bankAccount.id,
        accountName: bankAccount.accountName,
        checkNumber: 'CHK-001',
        bankName: bankAccount.accountName,
        notes: 'Test check payment',
      );

      // Assert: Payment should have proper Chart of Accounts integration
      expect(checkPayment.accountId, equals(bankAccount.id));
      expect(checkPayment.accountName, equals(bankAccount.accountName));
      expect(checkPayment.method, equals(PaymentMethod.check));
      expect(checkPayment.amount, equals(25000.0));
      expect(checkPayment.voucherId, equals('V-TEST-001'));
      expect(checkPayment.checkNumber, equals('CHK-001'));
    });

    test('should create account transfer payment with Chart of Accounts', () {
      // Arrange & Act: Create an account transfer payment
      final transferPayment = PaymentTransactionModel(
        id: 'payment_002',
        voucherId: 'V-TEST-002',
        method: PaymentMethod.accountTransfer,
        status: PaymentStatus.paid,
        amount: 15000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: cashAccount.id,
        accountName: cashAccount.accountName,
        notes: 'Test account transfer payment',
      );

      // Assert: Payment should reference correct Chart of Accounts
      expect(transferPayment.accountId, equals(cashAccount.id));
      expect(transferPayment.accountName, equals(cashAccount.accountName));
      expect(transferPayment.method, equals(PaymentMethod.accountTransfer));
      expect(transferPayment.amount, equals(15000.0));
    });

    test('should validate payment transaction data integrity', () {
      // Arrange: Create payment with all required fields
      final payment = PaymentTransactionModel(
        id: 'payment_003',
        voucherId: 'V-TEST-003',
        method: PaymentMethod.check,
        status: PaymentStatus.partial,
        amount: 10000.0,
        pendingAmount: 5000.0,
        transactionDate: DateTime.now(),
        accountId: bankAccount.id,
        accountName: bankAccount.accountName,
        checkNumber: 'CHK-003',
        bankName: bankAccount.accountName,
        checkIssueDate: DateTime.now().subtract(const Duration(days: 1)),
        checkExpiryDate: DateTime.now().add(const Duration(days: 90)),
        notes: 'Partial payment with check',
      );

      // Assert: All fields should be properly set
      expect(payment.id, equals('payment_003'));
      expect(payment.voucherId, equals('V-TEST-003'));
      expect(payment.method, equals(PaymentMethod.check));
      expect(payment.status, equals(PaymentStatus.partial));
      expect(payment.amount, equals(10000.0));
      expect(payment.pendingAmount, equals(5000.0));
      expect(payment.accountId, equals(bankAccount.id));
      expect(payment.accountName, equals(bankAccount.accountName));
      expect(payment.checkNumber, equals('CHK-003'));
      expect(payment.bankName, equals(bankAccount.accountName));
      expect(payment.checkIssueDate, isNotNull);
      expect(payment.checkExpiryDate, isNotNull);
      expect(payment.notes, contains('Partial payment'));
    });

    test('should handle payment serialization with Chart of Accounts', () {
      // Arrange: Create payment with Chart of Accounts reference
      final payment = PaymentTransactionModel(
        id: 'payment_004',
        voucherId: 'V-TEST-004',
        method: PaymentMethod.accountTransfer,
        status: PaymentStatus.paid,
        amount: 20000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: bankAccount.id,
        accountName: bankAccount.accountName,
        notes: 'Serialization test payment',
      );

      // Act: Convert to map and back
      final paymentMap = payment.toMap();
      final reconstructedPayment = PaymentTransactionModel.fromMap(paymentMap);

      // Assert: Data should be preserved through serialization
      expect(reconstructedPayment.id, equals(payment.id));
      expect(reconstructedPayment.voucherId, equals(payment.voucherId));
      expect(reconstructedPayment.method, equals(payment.method));
      expect(reconstructedPayment.status, equals(payment.status));
      expect(reconstructedPayment.amount, equals(payment.amount));
      expect(reconstructedPayment.pendingAmount, equals(payment.pendingAmount));
      expect(reconstructedPayment.accountId, equals(payment.accountId));
      expect(reconstructedPayment.accountName, equals(payment.accountName));
      expect(reconstructedPayment.notes, equals(payment.notes));
    });

    test('should validate Chart of Accounts account types for payments', () {
      // Test that asset accounts are appropriate for payment sources
      
      // Bank account (Asset) - Valid for payments
      expect(bankAccount.category, equals(AccountCategory.assets));
      expect(bankAccount.accountType, equals(AccountType.bank));
      expect(bankAccount.isActive, isTrue);

      // Cash account (Asset) - Valid for payments
      expect(cashAccount.category, equals(AccountCategory.assets));
      expect(cashAccount.accountType, equals(AccountType.cash));
      expect(cashAccount.isActive, isTrue);

      // Both accounts should have positive balances for payments
      expect(bankAccount.balance, greaterThan(0));
      expect(cashAccount.balance, greaterThan(0));
    });

    test('should create payment with withChartOfAccounts helper method', () {
      // Arrange & Act: Use the helper method to create payment
      final payment = PaymentTransactionModel.withChartOfAccounts(
        id: 'payment_005',
        voucherId: 'V-TEST-005',
        method: PaymentMethod.check,
        status: PaymentStatus.paid,
        amount: 30000.0,
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        chartOfAccountsId: bankAccount.id,
        chartOfAccountsDisplayName: bankAccount.accountName,
        checkNumber: 'CHK-005',
        bankName: bankAccount.accountName,
        notes: 'Payment created with helper method',
      );

      // Assert: Payment should be created with Chart of Accounts integration
      expect(payment.accountId, equals(bankAccount.id));
      expect(payment.accountName, equals(bankAccount.accountName));
      expect(payment.method, equals(PaymentMethod.check));
      expect(payment.amount, equals(30000.0));
      expect(payment.checkNumber, equals('CHK-005'));
      expect(payment.notes, contains('helper method'));
    });

    test('should handle multiple payment methods with Chart of Accounts', () {
      // Arrange: Create different payment types
      final payments = [
        PaymentTransactionModel(
          id: 'payment_006',
          voucherId: 'V-TEST-006',
          method: PaymentMethod.check,
          status: PaymentStatus.paid,
          amount: 5000.0,
          pendingAmount: 0.0,
          transactionDate: DateTime.now(),
          accountId: bankAccount.id,
          accountName: bankAccount.accountName,
          checkNumber: 'CHK-006',
        ),
        PaymentTransactionModel(
          id: 'payment_007',
          voucherId: 'V-TEST-006',
          method: PaymentMethod.accountTransfer,
          status: PaymentStatus.paid,
          amount: 3000.0,
          pendingAmount: 0.0,
          transactionDate: DateTime.now(),
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
        ),
      ];

      // Assert: Each payment should have proper Chart of Accounts reference
      expect(payments, hasLength(2));
      
      final checkPayment = payments[0];
      expect(checkPayment.method, equals(PaymentMethod.check));
      expect(checkPayment.accountId, equals(bankAccount.id));
      expect(checkPayment.checkNumber, equals('CHK-006'));

      final transferPayment = payments[1];
      expect(transferPayment.method, equals(PaymentMethod.accountTransfer));
      expect(transferPayment.accountId, equals(cashAccount.id));
      expect(transferPayment.checkNumber, isNull);

      // Verify total amounts
      final totalAmount = payments.fold<double>(0, (sum, payment) => sum + payment.amount);
      expect(totalAmount, equals(8000.0)); // 5000 + 3000
    });
  });
}
