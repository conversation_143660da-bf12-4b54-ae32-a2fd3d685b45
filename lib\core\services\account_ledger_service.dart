import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../core/utils/app_constants/firebase/collection_names.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/account_ledger_model.dart';

/// Service for managing individual account ledger entries
/// This service creates detailed transaction history for each account
class AccountLedgerService {
  static AccountLedgerService? _instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  AccountLedgerService._internal();

  factory AccountLedgerService() {
    _instance ??= AccountLedgerService._internal();
    return _instance!;
  }

  String get _uid => _auth.currentUser?.uid ?? '';

  /// Create ledger entries for all accounts involved in a journal entry
  Future<bool> createLedgerEntriesFromJournalEntry(
    JournalEntryModel journalEntry,
  ) async {
    try {
      log('Creating ledger entries for journal entry: ${journalEntry.entryNumber}');

      final batch = _firestore.batch();
      bool hasEntries = false;

      for (final line in journalEntry.lines) {
        // Create ledger entry for debit amount
        if (line.debitAmount > 0) {
          final debitLedgerEntry = AccountLedgerModel(
            id: '',
            accountId: line.accountId,
            journalEntryId: journalEntry.id,
            journalEntryLineId: line.id,
            transactionDate: journalEntry.entryDate,
            description: line.description,
            referenceNumber: journalEntry.referenceNumber ?? '',
            referenceType: journalEntry.sourceTransactionType ?? 'journal',
            debitAmount: line.debitAmount,
            creditAmount: 0.0,
            runningBalance: 0.0, // Will be calculated after creation
            createdAt: DateTime.now(),
            uid: _uid,
          );

          final debitRef = _firestore
              .collection(AppCollection.accountLedgerCollection)
              .doc();

          final debitData = debitLedgerEntry.toFirestore();
          debitData['id'] = debitRef.id;

          batch.set(debitRef, debitData);
          hasEntries = true;
        }

        // Create ledger entry for credit amount
        if (line.creditAmount > 0) {
          final creditLedgerEntry = AccountLedgerModel(
            id: '',
            accountId: line.accountId,
            journalEntryId: journalEntry.id,
            journalEntryLineId: line.id,
            transactionDate: journalEntry.entryDate,
            description: line.description,
            referenceNumber: journalEntry.referenceNumber ?? '',
            referenceType: journalEntry.sourceTransactionType ?? 'journal',
            debitAmount: 0.0,
            creditAmount: line.creditAmount,
            runningBalance: 0.0, // Will be calculated after creation
            createdAt: DateTime.now(),
            uid: _uid,
          );

          final creditRef = _firestore
              .collection(AppCollection.accountLedgerCollection)
              .doc();

          final creditData = creditLedgerEntry.toFirestore();
          creditData['id'] = creditRef.id;

          batch.set(creditRef, creditData);
          hasEntries = true;
        }
      }

      if (hasEntries) {
        await batch.commit();
        log('Successfully created ledger entries for journal entry: ${journalEntry.entryNumber}');

        // Update running balances for all affected accounts
        await _updateRunningBalancesForAccounts(
          journalEntry.lines.map((line) => line.accountId).toSet().toList(),
        );

        return true;
      } else {
        log('No ledger entries to create for journal entry: ${journalEntry.entryNumber}');
        return true;
      }
    } catch (e) {
      log('Error creating ledger entries: $e');
      return false;
    }
  }

  /// Update running balances for specific accounts
  Future<void> _updateRunningBalancesForAccounts(
      List<String> accountIds) async {
    try {
      for (final accountId in accountIds) {
        await _updateRunningBalanceForAccount(accountId);
      }
    } catch (e) {
      log('Error updating running balances: $e');
    }
  }

  /// Update running balance for a specific account
  Future<void> _updateRunningBalanceForAccount(String accountId) async {
    try {
      log('Updating running balance for account: $accountId');

      // Get all ledger entries for this account ordered by date
      final ledgerQuery = await _firestore
          .collection(AppCollection.accountLedgerCollection)
          .where('uid', isEqualTo: _uid)
          .where('accountId', isEqualTo: accountId)
          .orderBy('transactionDate')
          .orderBy('createdAt')
          .get();

      if (ledgerQuery.docs.isEmpty) {
        log('No ledger entries found for account: $accountId');
        return;
      }

      // Get account information to determine balance calculation rules
      final accountDoc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('id', isEqualTo: accountId)
          .limit(1)
          .get();

      if (accountDoc.docs.isEmpty) {
        log('Account not found: $accountId');
        return;
      }

      final account =
          ChartOfAccountsModel.fromJson(accountDoc.docs.first.data());
      double runningBalance = 0.0;

      final batch = _firestore.batch();

      for (final doc in ledgerQuery.docs) {
        final ledgerEntry = AccountLedgerModel.fromFirestore(doc.data());

        // Calculate running balance based on account type
        if (_isDebitAccount(account.accountType)) {
          // Assets and Expenses: Debit increases, Credit decreases
          runningBalance += ledgerEntry.debitAmount - ledgerEntry.creditAmount;
        } else {
          // Liabilities, Equity, Revenue: Credit increases, Debit decreases
          runningBalance += ledgerEntry.creditAmount - ledgerEntry.debitAmount;
        }

        // Update the ledger entry with the new running balance
        batch.update(doc.reference, {'runningBalance': runningBalance});
      }

      await batch.commit();
      log('Successfully updated running balances for account: $accountId');
    } catch (e) {
      log('Error updating running balance for account $accountId: $e');
    }
  }

  /// Check if an account type follows debit rules
  bool _isDebitAccount(AccountType accountType) {
    switch (accountType) {
      case AccountType.cash:
      case AccountType.bank:
      case AccountType.accountsReceivable:
      case AccountType.inventory:
      case AccountType.currentAssets:
      case AccountType.fixedAssets:
      case AccountType.otherAssets:
      case AccountType.operatingExpenses:
      case AccountType.administrativeExpenses:
      case AccountType.interestExpense:
      case AccountType.taxExpense:
      case AccountType.otherExpenses:
        return true;
      case AccountType.accountsPayable:
      case AccountType.loansPayable:
      case AccountType.currentLiabilities:
      case AccountType.longTermLiabilities:
      case AccountType.ownersEquity:
      case AccountType.retainedEarnings:
      case AccountType.equityServiceRevenue:
      case AccountType.equity:
      case AccountType.salesRevenue:
      case AccountType.serviceRevenue:
      case AccountType.revenue:
      case AccountType.otherRevenue:
        return false;
    }
  }

  /// Get ledger entries for a specific account with pagination
  Future<List<AccountLedgerModel>> getAccountLedgerEntries({
    required String accountId,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      log('Getting ledger entries for account: $accountId');

      Query query = _firestore
          .collection(AppCollection.accountLedgerCollection)
          .where('uid', isEqualTo: _uid)
          .where('accountId', isEqualTo: accountId);

      if (startDate != null) {
        query = query.where('transactionDate',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('transactionDate',
            isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      query = query
          .orderBy('transactionDate', descending: true)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => AccountLedgerModel.fromFirestore(
              doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      log('Error getting ledger entries: $e');
      return [];
    }
  }

  /// Get current account balance from ledger entries
  Future<double> getAccountBalanceFromLedger(String accountId) async {
    try {
      // Get the most recent ledger entry for this account
      final latestQuery = await _firestore
          .collection(AppCollection.accountLedgerCollection)
          .where('uid', isEqualTo: _uid)
          .where('accountId', isEqualTo: accountId)
          .orderBy('transactionDate', descending: true)
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (latestQuery.docs.isEmpty) {
        return 0.0;
      }

      final latestEntry =
          AccountLedgerModel.fromFirestore(latestQuery.docs.first.data());
      return latestEntry.runningBalance;
    } catch (e) {
      log('Error getting account balance from ledger: $e');
      return 0.0;
    }
  }

  /// Delete ledger entries for a specific journal entry (for reversals)
  Future<bool> deleteLedgerEntriesForJournalEntry(String journalEntryId) async {
    try {
      log('Deleting ledger entries for journal entry: $journalEntryId');

      final ledgerQuery = await _firestore
          .collection(AppCollection.accountLedgerCollection)
          .where('uid', isEqualTo: _uid)
          .where('journalEntryId', isEqualTo: journalEntryId)
          .get();

      if (ledgerQuery.docs.isEmpty) {
        log('No ledger entries found for journal entry: $journalEntryId');
        return true;
      }

      final batch = _firestore.batch();
      final affectedAccounts = <String>{};

      for (final doc in ledgerQuery.docs) {
        final ledgerEntry = AccountLedgerModel.fromFirestore(doc.data());
        affectedAccounts.add(ledgerEntry.accountId);
        batch.delete(doc.reference);
      }

      await batch.commit();
      log('Successfully deleted ledger entries for journal entry: $journalEntryId');

      // Update running balances for affected accounts
      await _updateRunningBalancesForAccounts(affectedAccounts.toList());

      return true;
    } catch (e) {
      log('Error deleting ledger entries: $e');
      return false;
    }
  }
}
