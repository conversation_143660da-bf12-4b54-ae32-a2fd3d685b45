import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/features/home/<USER>/drawer_controllers.dart';
import 'package:logestics/features/home/<USER>/dashbord/e_commerece/e_commerce.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/app_constants/assets/app_assets.dart';

import 'voucher_list_widget.dart';

class VoucherScreenView extends StatefulWidget {
  const VoucherScreenView({super.key});

  @override
  State<VoucherScreenView> createState() => _VoucherScreenViewState();
}

class _VoucherScreenViewState extends State<VoucherScreenView> {
  MainDrawerController mainDrawerController = Get.put(MainDrawerController());

  Widget _buildNavigationDot() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      height: 5,
      width: 5,
      decoration: const BoxDecoration(
        color: Colors.grey,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildNavigationRow() {
    return Row(
      children: [
        InkWell(
          onTap: () => mainDrawerController.updateSelectedScreen(
              const ECommercePageView(), "main/dashboard"),
          child: Row(
            children: [
              Image.asset(
                AppAssets.homeIcon,
                height: 15,
                color: const Color(0xFF0f7bf4),
              ),
              const Text(
                AppStrings.dashboard,
                style: AppTextStyles.navigationTextStyle,
              ),
            ],
          ),
        ),
        _buildNavigationDot(),
        const Text(
          AppStrings.system,
          style: AppTextStyles.navigationTextStyle,
        ),
        _buildNavigationDot(),
        Text(
          AppStrings.vouchers,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyles.activeNavigationTextStyle.copyWith(
            color: notifier.text,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppStrings.voucherTitle,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.voucherTitleStyle.copyWith(
                              color: notifier.text,
                            ),
                          ),
                          const Spacer(),
                          _buildNavigationRow(),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppStrings.voucherTitle,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.voucherTitleStyle.copyWith(
                              color: notifier.text,
                            ),
                          ),
                          const Spacer(),
                          _buildNavigationRow(),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: voucherListWidget(context,
                    titleShow: false,
                    showTestWidget:
                        false), // DEBUG: Changed to false for production
              ),
            ],
          ),
        );
      },
    );
  }
}
