import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/features/finance/brokers/presentation/controllers/broker_detail_controller.dart';
import 'package:logestics/main.dart';
import 'package:logestics/models/finance/broker_model.dart';
import 'package:provider/provider.dart';

class BrokerPaymentDialog extends StatelessWidget {
  final BrokerModel broker;
  final BrokerDetailController controller;

  const BrokerPaymentDialog({
    super.key,
    required this.broker,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return Dialog(
      backgroundColor: notifier.getcardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 600,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: controller.formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 24),
              _buildPaymentForm(),
              const SizedBox(height: 24),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.payment,
          color: Colors.green,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Record Payment',
                style: AppTextStyles.titleStyle.copyWith(
                  color: notifier.text,
                  fontSize: 20,
                ),
              ),
              Text(
                'To: ${broker.name}',
                style: AppTextStyles.subtitleStyle.copyWith(
                  color: notifier.text.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Get.back(),
          icon: Icon(Icons.close, color: notifier.text),
        ),
      ],
    );
  }

  Widget _buildPaymentForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Amount field
        TextFormField(
          controller: controller.amountController,
          decoration: InputDecoration(
            labelText: 'Amount *',
            labelStyle: TextStyle(color: notifier.text),
            prefixText: '\$ ',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: notifier.getfillborder),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue),
            ),
          ),
          style: TextStyle(color: notifier.text),
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter an amount';
            }
            if (double.tryParse(value) == null) {
              return 'Please enter a valid amount';
            }
            if (double.parse(value) <= 0) {
              return 'Amount must be greater than 0';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // Payment method dropdown
        Obx(() => DropdownButtonFormField<BrokerPaymentMethod>(
              value: controller.selectedPaymentMethod.value,
              decoration: InputDecoration(
                labelText: 'Payment Method *',
                labelStyle: TextStyle(color: notifier.text),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: notifier.getfillborder),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.blue),
                ),
              ),
              style: TextStyle(color: notifier.text),
              dropdownColor: notifier.getcardColor,
              items: BrokerPaymentMethod.values.map((method) {
                return DropdownMenuItem(
                  value: method,
                  child: Text(
                    method.name.toUpperCase(),
                    style: TextStyle(color: notifier.text),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  controller.selectedPaymentMethod.value = value;
                }
              },
            )),

        const SizedBox(height: 16),

        // Payment account text field (tracking only)
        TextFormField(
          controller: controller.paymentAccountController,
          decoration: InputDecoration(
            labelText: 'Payment Account *',
            labelStyle: TextStyle(color: notifier.text),
            hintText: 'e.g., Cash, Bank Account, etc.',
            hintStyle: TextStyle(color: notifier.text.withOpacity(0.5)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: notifier.getfillborder),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue),
            ),
          ),
          style: TextStyle(color: notifier.text),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a payment account name';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // Payment date picker
        Obx(() => InkWell(
              onTap: () => _selectPaymentDate(),
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'Payment Date *',
                  labelStyle: TextStyle(color: notifier.text),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: notifier.getfillborder),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.blue),
                  ),
                  suffixIcon: Icon(Icons.calendar_today, color: notifier.text),
                ),
                child: Text(
                  controller.formatDate(controller.selectedPaymentDate.value),
                  style: TextStyle(color: notifier.text),
                ),
              ),
            )),

        const SizedBox(height: 16),

        // Check-specific fields
        Obx(() {
          if (controller.selectedPaymentMethod.value ==
              BrokerPaymentMethod.check) {
            return Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: controller.checkNumberController,
                        decoration: InputDecoration(
                          labelText: 'Check Number',
                          labelStyle: TextStyle(color: notifier.text),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: notifier.getfillborder),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.blue),
                          ),
                        ),
                        style: TextStyle(color: notifier.text),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: controller.bankNameController,
                        decoration: InputDecoration(
                          labelText: 'Bank Name',
                          labelStyle: TextStyle(color: notifier.text),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: notifier.getfillborder),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.blue),
                          ),
                        ),
                        style: TextStyle(color: notifier.text),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            );
          }
          return const SizedBox.shrink();
        }),

        // Reference number field
        TextFormField(
          controller: controller.referenceNumberController,
          decoration: InputDecoration(
            labelText: 'Reference Number',
            labelStyle: TextStyle(color: notifier.text),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: notifier.getfillborder),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue),
            ),
          ),
          style: TextStyle(color: notifier.text),
        ),

        const SizedBox(height: 16),

        // Notes field
        TextFormField(
          controller: controller.notesController,
          decoration: InputDecoration(
            labelText: 'Notes',
            labelStyle: TextStyle(color: notifier.text),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: notifier.getfillborder),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue),
            ),
          ),
          style: TextStyle(color: notifier.text),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Get.back(),
          child: Text(
            'Cancel',
            style: TextStyle(color: notifier.text.withOpacity(0.7)),
          ),
        ),
        const SizedBox(width: 16),
        Obx(() => ElevatedButton(
              onPressed: controller.isRecordingPayment.value
                  ? null
                  : () => controller.recordPayment(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: controller.isRecordingPayment.value
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('Record Payment'),
            )),
      ],
    );
  }

  Future<void> _selectPaymentDate() async {
    final DateTime? picked = await showDatePicker(
      context: Get.context!,
      initialDate: controller.selectedPaymentDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              surface: notifier.getcardColor,
              onSurface: notifier.text,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      controller.selectedPaymentDate.value = picked;
    }
  }
}
