import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/finance/broker_model.dart';

void main() {
  group('Broker Financial System Tests', () {
    test('BrokerPaymentModel creation and serialization', () {
      final payment = BrokerPaymentModel(
        id: 'test-payment-1',
        brokerId: 'broker-1',
        brokerName: 'Test Broker',
        amount: 1000.0,
        method: BrokerPaymentMethod.cash,
        status: BrokerPaymentStatus.paid,
        paymentDate: DateTime.now(),
        uid: 'test-user',
        createdBy: 'Test User',
      );

      expect(payment.id, 'test-payment-1');
      expect(payment.brokerId, 'broker-1');
      expect(payment.brokerName, 'Test Broker');
      expect(payment.amount, 1000.0);
      expect(payment.method, BrokerPaymentMethod.cash);
      expect(payment.status, BrokerPaymentStatus.paid);

      // Test serialization
      final json = payment.toJson();
      expect(json['id'], 'test-payment-1');
      expect(json['brokerId'], 'broker-1');
      expect(json['amount'], 1000.0);
      expect(json['method'], 'cash');
      expect(json['status'], 'paid');

      // Test deserialization
      final deserializedPayment = BrokerPaymentModel.fromJson(json);
      expect(deserializedPayment.id, payment.id);
      expect(deserializedPayment.brokerId, payment.brokerId);
      expect(deserializedPayment.amount, payment.amount);
      expect(deserializedPayment.method, payment.method);
      expect(deserializedPayment.status, payment.status);
    });

    test('BrokerTransactionModel creation and serialization', () {
      final transaction = BrokerTransactionModel(
        id: 'test-transaction-1',
        brokerId: 'broker-1',
        brokerName: 'Test Broker',
        type: BrokerTransactionType.fee,
        amount: 500.0,
        transactionDate: DateTime.now(),
        description: 'Broker fee for voucher V-001',
        voucherId: 'voucher-1',
        voucherNumber: 'V-001',
        uid: 'test-user',
      );

      expect(transaction.id, 'test-transaction-1');
      expect(transaction.brokerId, 'broker-1');
      expect(transaction.type, BrokerTransactionType.fee);
      expect(transaction.amount, 500.0);
      expect(transaction.voucherId, 'voucher-1');
      expect(transaction.voucherNumber, 'V-001');

      // Test serialization
      final json = transaction.toJson();
      expect(json['id'], 'test-transaction-1');
      expect(json['type'], 'fee');
      expect(json['amount'], 500.0);
      expect(json['voucherId'], 'voucher-1');

      // Test deserialization
      final deserializedTransaction = BrokerTransactionModel.fromJson(json);
      expect(deserializedTransaction.id, transaction.id);
      expect(deserializedTransaction.type, transaction.type);
      expect(deserializedTransaction.amount, transaction.amount);
      expect(deserializedTransaction.voucherId, transaction.voucherId);
    });

    test('BrokerPaymentMethod enum values', () {
      expect(BrokerPaymentMethod.values.length, 3);
      expect(BrokerPaymentMethod.values, contains(BrokerPaymentMethod.cash));
      expect(BrokerPaymentMethod.values, contains(BrokerPaymentMethod.check));
      expect(BrokerPaymentMethod.values, contains(BrokerPaymentMethod.accountTransfer));
    });

    test('BrokerPaymentStatus enum values', () {
      expect(BrokerPaymentStatus.values.length, 3);
      expect(BrokerPaymentStatus.values, contains(BrokerPaymentStatus.paid));
      expect(BrokerPaymentStatus.values, contains(BrokerPaymentStatus.partial));
      expect(BrokerPaymentStatus.values, contains(BrokerPaymentStatus.pending));
    });

    test('BrokerTransactionType enum values', () {
      expect(BrokerTransactionType.values.length, 2);
      expect(BrokerTransactionType.values, contains(BrokerTransactionType.fee));
      expect(BrokerTransactionType.values, contains(BrokerTransactionType.payment));
    });

    test('BrokerPaymentModel copyWith functionality', () {
      final originalPayment = BrokerPaymentModel(
        id: 'test-payment-1',
        brokerId: 'broker-1',
        brokerName: 'Test Broker',
        amount: 1000.0,
        method: BrokerPaymentMethod.cash,
        status: BrokerPaymentStatus.paid,
        paymentDate: DateTime.now(),
        uid: 'test-user',
        createdBy: 'Test User',
      );

      final updatedPayment = originalPayment.copyWith(
        amount: 1500.0,
        status: BrokerPaymentStatus.partial,
      );

      expect(updatedPayment.id, originalPayment.id);
      expect(updatedPayment.brokerId, originalPayment.brokerId);
      expect(updatedPayment.amount, 1500.0); // Updated
      expect(updatedPayment.status, BrokerPaymentStatus.partial); // Updated
      expect(updatedPayment.method, originalPayment.method); // Unchanged
    });

    test('BrokerTransactionModel copyWith functionality', () {
      final originalTransaction = BrokerTransactionModel(
        id: 'test-transaction-1',
        brokerId: 'broker-1',
        brokerName: 'Test Broker',
        type: BrokerTransactionType.fee,
        amount: 500.0,
        transactionDate: DateTime.now(),
        description: 'Original description',
        uid: 'test-user',
      );

      final updatedTransaction = originalTransaction.copyWith(
        amount: 750.0,
        description: 'Updated description',
      );

      expect(updatedTransaction.id, originalTransaction.id);
      expect(updatedTransaction.brokerId, originalTransaction.brokerId);
      expect(updatedTransaction.amount, 750.0); // Updated
      expect(updatedTransaction.description, 'Updated description'); // Updated
      expect(updatedTransaction.type, originalTransaction.type); // Unchanged
    });

    test('BrokerPaymentModel equality and hashCode', () {
      final payment1 = BrokerPaymentModel(
        id: 'test-payment-1',
        brokerId: 'broker-1',
        brokerName: 'Test Broker',
        amount: 1000.0,
        method: BrokerPaymentMethod.cash,
        status: BrokerPaymentStatus.paid,
        paymentDate: DateTime.now(),
        uid: 'test-user',
        createdBy: 'Test User',
      );

      final payment2 = BrokerPaymentModel(
        id: 'test-payment-1', // Same ID
        brokerId: 'broker-2', // Different broker
        brokerName: 'Different Broker',
        amount: 2000.0,
        method: BrokerPaymentMethod.check,
        status: BrokerPaymentStatus.pending,
        paymentDate: DateTime.now(),
        uid: 'test-user',
        createdBy: 'Test User',
      );

      final payment3 = BrokerPaymentModel(
        id: 'test-payment-2', // Different ID
        brokerId: 'broker-1',
        brokerName: 'Test Broker',
        amount: 1000.0,
        method: BrokerPaymentMethod.cash,
        status: BrokerPaymentStatus.paid,
        paymentDate: DateTime.now(),
        uid: 'test-user',
        createdBy: 'Test User',
      );

      // Same ID should be equal
      expect(payment1, equals(payment2));
      expect(payment1.hashCode, equals(payment2.hashCode));

      // Different ID should not be equal
      expect(payment1, isNot(equals(payment3)));
      expect(payment1.hashCode, isNot(equals(payment3.hashCode)));
    });

    test('BrokerTransactionModel equality and hashCode', () {
      final transaction1 = BrokerTransactionModel(
        id: 'test-transaction-1',
        brokerId: 'broker-1',
        brokerName: 'Test Broker',
        type: BrokerTransactionType.fee,
        amount: 500.0,
        transactionDate: DateTime.now(),
        description: 'Test transaction',
        uid: 'test-user',
      );

      final transaction2 = BrokerTransactionModel(
        id: 'test-transaction-1', // Same ID
        brokerId: 'broker-2', // Different broker
        brokerName: 'Different Broker',
        type: BrokerTransactionType.payment,
        amount: 1000.0,
        transactionDate: DateTime.now(),
        description: 'Different transaction',
        uid: 'test-user',
      );

      final transaction3 = BrokerTransactionModel(
        id: 'test-transaction-2', // Different ID
        brokerId: 'broker-1',
        brokerName: 'Test Broker',
        type: BrokerTransactionType.fee,
        amount: 500.0,
        transactionDate: DateTime.now(),
        description: 'Test transaction',
        uid: 'test-user',
      );

      // Same ID should be equal
      expect(transaction1, equals(transaction2));
      expect(transaction1.hashCode, equals(transaction2.hashCode));

      // Different ID should not be equal
      expect(transaction1, isNot(equals(transaction3)));
      expect(transaction1.hashCode, isNot(equals(transaction3.hashCode)));
    });
  });
}
