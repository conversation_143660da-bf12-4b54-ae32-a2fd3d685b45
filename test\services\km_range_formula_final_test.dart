import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';

void main() {
  group('KM Range Formula Final Fix Tests', () {
    late InvoiceModel testInvoice175km;

    setUp(() {
      // Invoice with 175 KM distance - the exact scenario from the original problem
      testInvoice175km = InvoiceModel(
        invoiceNumber: 1122,
        invoiceStatus: 'Active',
        tasNumber: 'TAS1122',
        productName: 'Test Product',
        numberOfBags: 400,
        weightPerBag: 50.0,
        customerName: 'Test Customer',
        truckNumber: 'TRK001',
        conveyNoteNumber: 'CN001',
        biltyNumber: 'BLT001',
        consignorName: 'Test Consignor',
        deliveryMode: 'Road',
        districtId: 'DIST001',
        districtName: 'Test District',
        stationId: 'STN001',
        stationName: 'Test Station',
        fromPlaceId: 'PLC001',
        fromPlaceName: 'Test Place',
        distanceInKilometers: 175.0, // This should fall in the 161-200 range
        consignorPickUpAddress: 'Test Address',
      );
    });

    test('should fix the original problematic scenario', () {
      // This is the exact formula that was failing in the original problem
      final problematicFormula = 'IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 1196.38 ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 7.68';

      final formula = CalculationFormulaModel(
        formulaId: 'original_problematic_formula',
        formulaName: 'Original Problematic Formula',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Determine Effective Rate Based on Distance',
            formula: problematicFormula,
            resultVariable: 'effectiveRate',
            description: 'KM-based rate calculation that was failing',
          ),
          FormulaStepModel(
            stepId: 'step2',
            stepName: 'Calculate Final Amount',
            formula: 'totalWeightTons × effectiveRate',
            resultVariable: 'finalAmount',
            description: 'Apply effective rate to weight',
          ),
        ],
        finalResultVariable: 'finalAmount',
      );

      // Test the complete formula execution
      final result = FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice175km,
        formula: formula,
        customColumnValues: {},
      );

      // Verify the fix works
      expect(result.isSuccess, isTrue, reason: 'Formula execution should succeed');
      expect(result.finalAmount, isNotNull, reason: 'Final amount should not be null');
      expect(result.errorMessage, isNull, reason: 'Should not have error message');
      
      // Verify the step results
      expect(result.stepResults['effectiveRate'], equals(7.68), 
             reason: 'Step 1 should return effective rate of 7.68 for 175 KM');
      
      // For 175 KM distance with 20 tons:
      // Step 1: effectiveRate = 7.68 (175 falls in 161-200 range)
      // Step 2: finalAmount = 20 × 7.68 = 153.6, rounded to 154.0
      expect(result.finalAmount, equals(154.0), 
             reason: 'Should calculate 20 tons × 7.68 = 153.6, rounded to 154.0');
    });

    test('should handle the expected behavior from original problem description', () {
      // Original problem expected behavior:
      // For 175 KM distance:
      // 1. The formula should execute the KM range logic: 175 KM falls in the 161-200 range
      // 2. The rate for this range should be 7.68
      // 3. The calculation should be: 175 × 7.68 = 1344 (effective rate)
      // 4. Final amount should be: 20 tons × 1344 = 26,880

      final formula = CalculationFormulaModel(
        formulaId: 'expected_behavior_test',
        formulaName: 'Expected Behavior Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Determine Rate Per KM',
            formula: 'IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 175.0',
            resultVariable: 'ratePerKm',
            description: 'Get rate per KM for the distance range',
          ),
          FormulaStepModel(
            stepId: 'step2',
            stepName: 'Calculate Effective Rate',
            formula: 'distanceInKilometers × ratePerKm',
            resultVariable: 'effectiveRate',
            description: 'Calculate effective rate: 175 × 7.68 = 1344',
          ),
          FormulaStepModel(
            stepId: 'step3',
            stepName: 'Calculate Final Amount',
            formula: 'totalWeightTons × effectiveRate',
            resultVariable: 'finalAmount',
            description: 'Apply effective rate to weight: 20 × 1344 = 26,880',
          ),
        ],
        finalResultVariable: 'finalAmount',
      );

      final result = FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice175km,
        formula: formula,
        customColumnValues: {},
      );

      expect(result.isSuccess, isTrue);
      expect(result.stepResults['ratePerKm'], equals(7.68));
      expect(result.stepResults['effectiveRate'], equals(1344.0)); // 175 × 7.68
      expect(result.finalAmount, equals(26880.0)); // 20 × 1344
    });

    test('should not fall back to standard calculation anymore', () {
      // This test verifies that the system no longer falls back to nonFuelRate
      // when the formula execution succeeds

      final kmRangeFormula = 'IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 ELSE 175.0';

      // Test direct formula evaluation
      final directResult = FlexibleFormulaCalculationService.evaluateExpression(
        kmRangeFormula.replaceAll('distanceInKilometers', '175.0')
      );

      expect(directResult, isNotNull, reason: 'Direct evaluation should work');
      expect(directResult, equals(7.68), reason: 'Should return 7.68 for 175 KM');

      // Test with formula step execution
      final formula = CalculationFormulaModel(
        formulaId: 'no_fallback_test',
        formulaName: 'No Fallback Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'KM Range Test',
            formula: kmRangeFormula,
            resultVariable: 'rate',
            description: 'Should not fall back to standard calculation',
          ),
        ],
        finalResultVariable: 'rate',
      );

      final stepResult = FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice175km,
        formula: formula,
        customColumnValues: {},
      );

      expect(stepResult.isSuccess, isTrue, reason: 'Step execution should succeed');
      expect(stepResult.finalAmount, equals(7.68), reason: 'Should use formula result, not fallback');
    });

    test('should handle complex nested IF-ELSE chains correctly', () {
      // Test with an even more complex formula to ensure robustness
      final complexFormula = '''
        IF distanceInKilometers >= 1 AND distanceInKilometers <= 40 THEN 1196.38 
        ELSE IF distanceInKilometers >= 41 AND distanceInKilometers <= 80 THEN 1196.38 
        ELSE IF distanceInKilometers >= 81 AND distanceInKilometers <= 120 THEN 1196.38 
        ELSE IF distanceInKilometers >= 121 AND distanceInKilometers <= 160 THEN 7.68 
        ELSE IF distanceInKilometers >= 161 AND distanceInKilometers <= 200 THEN 7.68 
        ELSE IF distanceInKilometers >= 201 AND distanceInKilometers <= 300 THEN 6.5 
        ELSE 5.0
      '''.replaceAll('\n', '').replaceAll(RegExp(r'\s+'), ' ').trim();

      final formula = CalculationFormulaModel(
        formulaId: 'complex_nested_test',
        formulaName: 'Complex Nested Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Complex KM Range Logic',
            formula: complexFormula,
            resultVariable: 'complexRate',
            description: 'Test complex nested IF-ELSE chains',
          ),
        ],
        finalResultVariable: 'complexRate',
      );

      final result = FlexibleFormulaCalculationService.calculateWithFormulaDetailed(
        invoice: testInvoice175km,
        formula: formula,
        customColumnValues: {},
      );

      expect(result.isSuccess, isTrue, reason: 'Complex formula should execute successfully');
      expect(result.finalAmount, equals(7.68), reason: 'Should return correct rate for 175 KM');
    });
  });
}
