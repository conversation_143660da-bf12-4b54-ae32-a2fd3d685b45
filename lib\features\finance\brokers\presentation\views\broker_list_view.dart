import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/data_table_cell.dart';
import 'package:logestics/features/finance/brokers/presentation/controllers/broker_controller.dart';
import 'package:logestics/features/finance/brokers/presentation/views/broker_form_dialog.dart';
import 'package:logestics/features/finance/brokers/presentation/views/broker_detail_view.dart';
import 'package:logestics/features/finance/brokers/presentation/views/broker_financial_dashboard_view.dart';
import 'package:logestics/main.dart';

import 'package:provider/provider.dart';

class BrokerListView extends StatelessWidget {
  const BrokerListView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<BrokerController>();
    var width = Get.width;
    notifier = Provider.of(context, listen: true);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: width < 650 ? 55 : 40,
                width: width,
                child: width < 650
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Brokers',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Brokers',
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.titleStyle
                                .copyWith(color: notifier.text),
                          ),
                          const Spacer(),
                          ElevatedButton.icon(
                            onPressed: () => _showFinancialDashboard(),
                            icon: const Icon(Icons.dashboard, size: 16),
                            label: const Text('Financial Dashboard'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                      ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                height: 570,
                child: _buildBrokersList(controller),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBrokersList(BrokerController controller) {
    return Container(
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(vertical: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: Get.width < 650
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: () {
                          controller.clearForm();
                          _showBrokerFormDialog(Get.context!, isEdit: false);
                        },
                        child: Text(
                          'Add New Broker',
                          style: AppTextStyles.addNewInvoiceStyle,
                        ),
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () {
                          controller.clearForm();
                          _showBrokerFormDialog(Get.context!, isEdit: false);
                        },
                        child: Text(
                          'Add New Broker',
                          style: AppTextStyles.addNewInvoiceStyle,
                        ),
                      ),
                    ],
                  ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SizedBox(
              width: Get.width,
              child: Obx(() {
                if (controller.isLoading.value) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (controller.brokers.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.business_center_outlined,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'No brokers found',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Click "Add New Broker" to get started',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView(
                  shrinkWrap: true,
                  children: [
                    Table(
                      columnWidths: const {
                        0: FlexColumnWidth(0.5), // S.No
                        1: FlexColumnWidth(1.5), // Name
                        2: FlexColumnWidth(1.5), // Phone/Email
                        3: FlexColumnWidth(1.0), // Actions
                      },
                      border: TableBorder(
                        horizontalInside: BorderSide(
                          color: notifier.getfillborder,
                        ),
                      ),
                      children: [
                        TableRow(
                          decoration: BoxDecoration(
                            color: notifier.getHoverColor,
                          ),
                          children: [
                            DataTableCell(
                              text: 'S.No',
                              style: AppTextStyles.invoiceDataStyle.copyWith(
                                color: notifier.text,
                              ),
                            ),
                            DataTableCell(
                              text: 'Name',
                              style: AppTextStyles.invoiceDataStyle.copyWith(
                                color: notifier.text,
                              ),
                            ),
                            DataTableCell(
                              text: 'Contact',
                              style: AppTextStyles.invoiceDataStyle.copyWith(
                                color: notifier.text,
                              ),
                            ),
                            DataTableCell(
                              text: 'Actions',
                              style: AppTextStyles.invoiceDataStyle.copyWith(
                                color: notifier.text,
                              ),
                            ),
                          ],
                        ),
                        ...controller.brokers.asMap().entries.map((entry) {
                          final i = entry.key;
                          final broker = entry.value;
                          return TableRow(
                            children: [
                              DataTableCell(
                                text: (i + 1).toString(),
                                style: AppTextStyles.invoiceDataStyle.copyWith(
                                  color: notifier.text,
                                ),
                              ),
                              DataTableCell(
                                text: broker.name,
                                style: AppTextStyles.invoiceDataStyle.copyWith(
                                  color: notifier.text,
                                  decoration: TextDecoration.underline,
                                ),
                                onTap: () =>
                                    _showBrokerDetails(Get.context!, broker),
                              ),
                              DataTableCell(
                                text: broker.phoneNumber?.isNotEmpty == true
                                    ? broker.phoneNumber!
                                    : broker.email?.isNotEmpty == true
                                        ? broker.email!
                                        : 'No contact',
                                style: AppTextStyles.invoiceDataStyle.copyWith(
                                  color: notifier.text,
                                ),
                              ),
                              DataTableActionsCell(
                                menuItems: [
                                  DataTablePopupMenuItem(
                                    text: 'Edit',
                                    icon: Icons.edit_outlined,
                                    onTap: () {
                                      controller.editBroker(broker);
                                      _showBrokerFormDialog(Get.context!,
                                          isEdit: true);
                                    },
                                  ),
                                  DataTablePopupMenuItem(
                                    text: 'Delete',
                                    icon: Icons.delete_outline,
                                    isDanger: true,
                                    onTap: () =>
                                        controller.deleteBroker(broker.id),
                                  ),
                                ],
                              ),
                            ],
                          );
                        }),
                      ],
                    ),
                  ],
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  void _showBrokerFormDialog(BuildContext context, {required bool isEdit}) {
    showDialog(
      context: context,
      builder: (context) => BrokerFormDialog(isEdit: isEdit),
    );
  }

  void _showBrokerDetails(BuildContext context, broker) {
    // Navigate to the broker detail view in a dialog
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width * 0.9,
          height: Get.height * 0.9,
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(16),
          ),
          child: BrokerDetailView(broker: broker),
        ),
      ),
    );
  }

  void _showFinancialDashboard() {
    showDialog(
      context: Get.context!,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width * 0.95,
          height: Get.height * 0.95,
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(16),
          ),
          child: const BrokerFinancialDashboardView(),
        ),
      ),
    );
  }
}
