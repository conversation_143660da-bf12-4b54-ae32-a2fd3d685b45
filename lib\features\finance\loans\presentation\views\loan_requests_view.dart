import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/widgets/loading_indicator.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/loans/presentation/controllers/loan_requests_controller.dart';
import 'package:logestics/features/finance/loans/presentation/widgets/loan_requests_filters.dart';
import 'package:logestics/features/finance/loans/presentation/widgets/loan_requests_export_dialog.dart';
import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/features/voucher/presentation/widgets/chart_of_accounts_dropdown.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

class LoanRequestsView extends StatelessWidget {
  final bool titleShow;

  const LoanRequestsView({
    super.key,
    this.titleShow = true,
  });

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    // Try to find the controller with error handling
    late LoanRequestsController controller;
    try {
      controller = Get.find<LoanRequestsController>();
    } catch (e) {
      return Scaffold(
        backgroundColor: notifier.mainBgColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Controller not found',
                style: TextStyle(
                  color: notifier.text,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Please ensure the LoanRequestsController is properly initialized',
                style: TextStyle(color: notifier.text.withValues(alpha: 0.7)),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: notifier.mainBgColor,
      body: Column(
        children: [
          // Navigation breadcrumb (fixed at top)
          if (titleShow) _buildNavigationRow(notifier),

          // Statistics section
          Container(
            margin: const EdgeInsets.all(16),
            child: LoanRequestsStats(controller: controller),
          ),

          // Filters section
          LoanRequestsFilters(controller: controller),

          // Main content area
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: notifier.getBgColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                children: [
                  // Header with tabs, search and refresh
                  _buildHeader(notifier, controller),

                  // Main content area (table, loading, error, empty states)
                  Expanded(
                    child: Obx(() {
                      if (controller.isInitialLoading.value) {
                        return const Center(
                          child: LoadingIndicator(
                            type: LoadingIndicatorType.circular,
                            size: LoadingIndicatorSize.medium,
                            message: 'Loading loan requests...',
                          ),
                        );
                      }

                      if (controller.hasError.value) {
                        return _buildErrorState(notifier, controller);
                      }

                      if (controller.currentTabData.isEmpty) {
                        return _buildEmptyState(notifier, controller);
                      }

                      return _buildTableWithPagination(notifier, controller);
                    }),
                  ),

                  // Export button (always at bottom)
                  if (titleShow) _buildExportButton(notifier, controller),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationRow(ColorNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.request_quote,
            color: const Color(0xFF0f7bf4),
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            'Finance',
            style: TextStyle(
              color: notifier.text.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(width: 8),
          Icon(
            Icons.chevron_right,
            color: notifier.text.withValues(alpha: 0.5),
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'Loan Requests',
            style: TextStyle(
              color: notifier.text,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(
      ColorNotifier notifier, LoanRequestsController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: notifier.text.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Tab bar and actions row
          Row(
            children: [
              // Tab buttons
              Expanded(
                child: Row(
                  children: [
                    _buildTabButton(
                      'Incoming Requests',
                      0,
                      controller,
                      notifier,
                      badge: controller.unreadIncomingCount.value > 0
                          ? controller.unreadIncomingCount.value.toString()
                          : null,
                    ),
                    const SizedBox(width: 16),
                    _buildTabButton(
                      'Your Requests',
                      1,
                      controller,
                      notifier,
                    ),
                  ],
                ),
              ),

              // Refresh button
              IconButton(
                onPressed: () => controller.refreshData(),
                icon: Obx(() => controller.isRefreshing.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: LoadingIndicator(
                          type: LoadingIndicatorType.circular,
                          size: LoadingIndicatorSize.small,
                        ),
                      )
                    : Icon(
                        Icons.refresh,
                        color: notifier.text.withValues(alpha: 0.7),
                      )),
                tooltip: 'Refresh',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton(
    String title,
    int index,
    LoanRequestsController controller,
    ColorNotifier notifier, {
    String? badge,
  }) {
    return Obx(() {
      final isSelected = controller.currentTabIndex.value == index;
      return GestureDetector(
        onTap: () => controller.switchTab(index),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFF0f7bf4) : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected
                  ? const Color(0xFF0f7bf4)
                  : notifier.text.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: isSelected ? Colors.white : notifier.text,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
              if (badge != null) ...[
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    badge,
                    style: TextStyle(
                      color:
                          isSelected ? const Color(0xFF0f7bf4) : Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    });
  }

  Widget _buildErrorState(
      ColorNotifier notifier, LoanRequestsController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Data',
            style: TextStyle(
              color: notifier.text,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            controller.errorMessage.value,
            style: TextStyle(color: notifier.text.withValues(alpha: 0.7)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => controller.retryLoading(),
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0f7bf4),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(
      ColorNotifier notifier, LoanRequestsController controller) {
    return Obx(() {
      final isIncomingTab = controller.currentTabIndex.value == 0;
      return LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              child: IntrinsicHeight(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        isIncomingTab ? Icons.call_received : Icons.call_made,
                        size: 64,
                        color: notifier.text.withValues(alpha: 0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        isIncomingTab
                            ? 'No Incoming Requests'
                            : 'No Outgoing Requests',
                        style: TextStyle(
                          color: notifier.text,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        isIncomingTab
                            ? 'You have no pending loan requests from others'
                            : 'You haven\'t sent any loan requests yet',
                        style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7)),
                        textAlign: TextAlign.center,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (!isIncomingTab) ...[
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: () {
                            // Navigate to create loan request
                            Get.toNamed('/finance/loans');
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('Create Request'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0f7bf4),
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildTableWithPagination(
      ColorNotifier notifier, LoanRequestsController controller) {
    return Obx(() {
      final data = controller.currentTabData;
      final isIncomingTab = controller.currentTabIndex.value == 0;

      return Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: notifier.text.withValues(alpha: 0.05),
              border: Border(
                bottom: BorderSide(
                  color: notifier.text.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    isIncomingTab ? 'From' : 'To',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Amount',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Status',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Request Date',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Due Date',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Actions',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: notifier.text,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Scrollable data rows
          Expanded(
            child: ListView.builder(
              itemCount: data.length,
              itemBuilder: (context, index) {
                final loan = data[index];
                return _buildLoanRequestRow(
                  loan,
                  controller,
                  notifier,
                  isIncomingTab,
                  index,
                );
              },
            ),
          ),
        ],
      );
    });
  }

  Widget _buildLoanRequestRow(
    LoanModel loan,
    LoanRequestsController controller,
    ColorNotifier notifier,
    bool isIncomingTab,
    int index,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: index.isEven
            ? notifier.getBgColor
            : notifier.text.withValues(alpha: 0.02),
        border: Border(
          bottom: BorderSide(
            color: notifier.text.withValues(alpha: 0.05),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // From/To column
          Expanded(
            flex: 2,
            child: Text(
              isIncomingTab
                  ? controller.getResolvedBorrowerName(loan)
                  : controller.getResolvedLenderName(loan),
              style: TextStyle(
                color: notifier.text,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Amount column
          Expanded(
            flex: 2,
            child: Text(
              controller.formatCurrency(loan.amount),
              style: TextStyle(
                color: notifier.text,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Status column
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: controller.getStatusColor(loan.status),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                controller.getStatusDisplayText(loan.status),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          // Request Date column
          Expanded(
            flex: 2,
            child: Text(
              controller.formatDate(loan.requestDate),
              style: TextStyle(
                color: notifier.text,
                fontSize: 13,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Due Date column
          Expanded(
            flex: 2,
            child: Text(
              controller.formatDate(loan.dueDate),
              style: TextStyle(
                color: notifier.text,
                fontSize: 13,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Actions column
          Expanded(
            flex: 2,
            child:
                _buildActionButtons(loan, controller, notifier, isIncomingTab),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(
    LoanModel loan,
    LoanRequestsController controller,
    ColorNotifier notifier,
    bool isIncomingTab,
  ) {
    if (loan.status != 'pending') {
      return Text(
        loan.status.capitalizeFirst ?? 'N/A',
        style: TextStyle(
          color: controller.getStatusColor(loan.status),
          fontWeight: FontWeight.w600,
        ),
      );
    }

    if (isIncomingTab) {
      // Incoming requests - show approve/reject buttons
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: () => _showApproveDialog(loan.id, controller, notifier),
            icon: const Icon(Icons.check_circle, color: Colors.green),
            tooltip: 'Approve',
          ),
          IconButton(
            onPressed: () => _showRejectDialog(loan.id, controller, notifier),
            icon: const Icon(Icons.cancel, color: Colors.red),
            tooltip: 'Reject',
          ),
        ],
      );
    } else {
      // Outgoing requests - show view/cancel buttons
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: () => _showLoanDetails(loan, notifier, controller),
            icon: Icon(Icons.visibility,
                color: notifier.text.withValues(alpha: 0.7)),
            tooltip: 'View Details',
          ),
          if (loan.status == 'pending')
            IconButton(
              onPressed: () => _showCancelDialog(loan.id, controller, notifier),
              icon: const Icon(Icons.delete, color: Colors.orange),
              tooltip: 'Cancel Request',
            ),
        ],
      );
    }
  }

  Widget _buildExportButton(
      ColorNotifier notifier, LoanRequestsController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: notifier.text.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton.icon(
            onPressed: () {
              showDialog(
                context: Get.context!,
                builder: (context) =>
                    LoanRequestsExportDialog(controller: controller),
              );
            },
            icon: const Icon(Icons.download),
            label: const Text('Export Requests'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0f7bf4),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showApproveDialog(String loanId, LoanRequestsController controller,
      ColorNotifier notifier) {
    // Find the loan to get the amount for validation
    final loan =
        controller.allIncomingRequests.firstWhereOrNull((l) => l.id == loanId);
    if (loan == null) {
      SnackbarUtils.showError('Error', 'Loan request not found');
      return;
    }

    showDialog(
      context: Get.context!,
      builder: (context) {
        return Dialog(
          backgroundColor: notifier.getBgColor,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Container(
            constraints: const BoxConstraints(
              maxWidth: 500,
              maxHeight: 600,
            ),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Text(
                  'Approve Loan Request',
                  style: TextStyle(
                    color: notifier.text,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
                const SizedBox(height: 20),

                // Scrollable content
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Loan details
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: notifier.text.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Loan Details',
                                style: TextStyle(
                                  color: notifier.text,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Amount:',
                                    style: TextStyle(
                                        color: notifier.text
                                            .withValues(alpha: 0.7)),
                                  ),
                                  Text(
                                    controller.formatCurrency(loan.amount),
                                    style: TextStyle(
                                      color: notifier.text,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'From:',
                                    style: TextStyle(
                                        color: notifier.text
                                            .withValues(alpha: 0.7)),
                                  ),
                                  Text(
                                    controller.getResolvedBorrowerName(loan),
                                    style: TextStyle(color: notifier.text),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Account selection
                        Text(
                          'Select Account for Disbursement',
                          style: TextStyle(
                            color: notifier.text,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),

                        Obx(() {
                          if (controller.isLoadingChartAccounts.value) {
                            return Container(
                              padding: const EdgeInsets.all(16),
                              child: const Center(
                                  child: CircularProgressIndicator()),
                            );
                          }

                          if (controller.chartOfAccounts.isEmpty) {
                            return Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.orange),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.warning,
                                      color: Colors.orange),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'No Chart of Accounts available. Please add accounts first.',
                                      style: TextStyle(color: notifier.text),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }

                          return AssetAccountDropdown(
                            labelText: 'Select Account for Loan Approval',
                            hintText: 'Choose an asset account',
                            selectedAccount:
                                controller.selectedChartAccount.value,
                            onChanged: (ChartOfAccountsModel? account) {
                              if (account != null) {
                                controller.setSelectedChartAccount(account);
                              }
                            },
                            isRequired: true,
                          );
                        }),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        'Cancel',
                        style: TextStyle(
                            color: notifier.text.withValues(alpha: 0.7)),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Obx(() {
                      final selectedChartAccount =
                          controller.selectedChartAccount.value;
                      final canApprove = selectedChartAccount != null &&
                          !controller.isProcessing.value;

                      return ElevatedButton(
                        onPressed: canApprove
                            ? () async {
                                final success = await controller
                                    .approveLoanRequestWithChartOfAccounts(
                                        loanId);
                                if (success && context.mounted) {
                                  Navigator.pop(context);
                                }
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                        child: controller.isProcessing.value
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Text('Approve'),
                      );
                    }),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showRejectDialog(String loanId, LoanRequestsController controller,
      ColorNotifier notifier) {
    final reasonController = TextEditingController();

    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          backgroundColor: notifier.getBgColor,
          title: Text(
            'Reject Loan Request',
            style: TextStyle(color: notifier.text),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Please provide a reason for rejecting this loan request:',
                style: TextStyle(color: notifier.text),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: reasonController,
                decoration: InputDecoration(
                  labelText: 'Reason',
                  labelStyle:
                      TextStyle(color: notifier.text.withValues(alpha: 0.7)),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide:
                        BorderSide(color: notifier.text.withValues(alpha: 0.3)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: const BorderSide(color: Color(0xFF0f7bf4)),
                  ),
                ),
                style: TextStyle(color: notifier.text),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: TextStyle(color: notifier.text.withValues(alpha: 0.7)),
              ),
            ),
            Obx(() => ElevatedButton(
                  onPressed: controller.isProcessing.value
                      ? null
                      : () async {
                          final reason = reasonController.text.trim();
                          if (reason.isNotEmpty) {
                            final success = await controller.rejectLoanRequest(
                                loanId, reason);
                            if (success && context.mounted) {
                              Navigator.pop(context);
                            }
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: controller.isProcessing.value
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Reject'),
                )),
          ],
        );
      },
    );
  }

  void _showCancelDialog(String loanId, LoanRequestsController controller,
      ColorNotifier notifier) {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          backgroundColor: notifier.getBgColor,
          title: Text(
            'Cancel Loan Request',
            style: TextStyle(color: notifier.text),
          ),
          content: Text(
            'Are you sure you want to cancel this loan request?',
            style: TextStyle(color: notifier.text),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'No',
                style: TextStyle(color: notifier.text.withValues(alpha: 0.7)),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                // Implement cancel logic here
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Yes, Cancel'),
            ),
          ],
        );
      },
    );
  }

  void _showLoanDetails(LoanModel loan, ColorNotifier notifier,
      LoanRequestsController controller) {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          backgroundColor: notifier.getBgColor,
          title: Text(
            'Loan Request Details',
            style: TextStyle(color: notifier.text),
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDetailRow('Amount:',
                    'PKR ${loan.amount.toStringAsFixed(2)}', notifier),
                _buildDetailRow(
                    'To:', controller.getResolvedLenderName(loan), notifier),
                _buildDetailRow(
                    'Status:', loan.status.capitalizeFirst ?? 'N/A', notifier),
                _buildDetailRow(
                    'Request Date:', _formatDate(loan.requestDate), notifier),
                _buildDetailRow(
                    'Due Date:', _formatDate(loan.dueDate), notifier),
                if (loan.notes?.isNotEmpty ?? false)
                  _buildDetailRow('Notes:', loan.notes!, notifier),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Close',
                style: TextStyle(color: notifier.text),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value, ColorNotifier notifier) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                color: notifier.text,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: notifier.text),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
