import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/payment_transaction_model.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

/// Test to verify payment journal entries do NOT include sales tax logic
/// This test ensures sales tax is completely removed from payment processing
void main() {
  group('Payment Journal Entry - No Sales Tax Tests', () {
    late PaymentTransactionModel testPayment;
    late ChartOfAccountsModel bankAccount;
    late ChartOfAccountsModel truckFareAccount;

    setUpAll(() {
      // Create test accounts
      bankAccount = ChartOfAccountsModel(
        id: 'bank_001',
        accountName: 'Main Bank Account',
        accountNumber: '1001',
        category: AccountCategory.assets,
        accountType: AccountType.bank,
        description: 'Main bank account',
        isActive: true,
        balance: 50000.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );

      truckFareAccount = ChartOfAccountsModel(
        id: 'truck_fare_001',
        accountName: 'Truck Fare Payable',
        accountNumber: '2100',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        description: 'Truck fare liability',
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: 'test_uid',
      );

      // Create test payment
      testPayment = PaymentTransactionModel(
        id: 'payment_no_tax_001',
        voucherId: 'V-NO-TAX-001',
        method: PaymentMethod.accountTransfer,
        status: PaymentStatus.paid,
        amount: 25000.0, // Full payment amount
        pendingAmount: 0.0,
        transactionDate: DateTime.now(),
        accountId: bankAccount.id,
        accountName: bankAccount.accountName,
      );
    });

    test('should verify payment amount is NOT split for sales tax', () {
      // This test verifies that the full payment amount goes to freight
      // with NO portion allocated to sales tax

      final paymentAmount = testPayment.amount; // 25,000

      // In the corrected system, there should be NO sales tax calculation
      // The full payment amount should go to truck freight
      final freightAmount = paymentAmount; // Full amount, no split
      final salesTaxAmount = 0.0; // No sales tax in payments

      print('\n💰 Payment Amount Allocation:');
      print('  - Total Payment: \$${paymentAmount.toStringAsFixed(2)}');
      print('  - Freight Amount: \$${freightAmount.toStringAsFixed(2)}');
      print('  - Sales Tax Amount: \$${salesTaxAmount.toStringAsFixed(2)}');
      print(
          '  - Split Logic: REMOVED (sales tax handled in voucher creation only)');

      // Verify no splitting occurs
      expect(freightAmount, equals(paymentAmount),
          reason: 'Full payment amount should go to freight');

      expect(salesTaxAmount, equals(0.0),
          reason: 'No sales tax should be calculated in payment processing');

      expect(freightAmount + salesTaxAmount, equals(paymentAmount),
          reason: 'Payment allocation should equal total payment');

      print('\n✅ Payment amount integrity verified!');
    });

    test('should demonstrate simple payment journal entry structure', () {
      // This test demonstrates the corrected payment journal entry structure
      // WITHOUT any sales tax logic

      final journalEntries = <Map<String, dynamic>>[];

      // 1. Source account (credit - money going out)
      journalEntries.add({
        'type': 'Source Account',
        'accountName': bankAccount.accountName,
        'accountType': 'Asset',
        'debitAmount': 0.0,
        'creditAmount': testPayment.amount, // Full payment amount
        'description':
            'Payment - Voucher #${testPayment.voucherId} - ${testPayment.method.name}',
      });

      // 2. Truck freight account (debit - paying off freight liability)
      journalEntries.add({
        'type': 'Truck Freight',
        'accountName': truckFareAccount.accountName,
        'accountType': 'Liability',
        'debitAmount': testPayment.amount, // Full payment amount - NO SPLIT
        'creditAmount': 0.0,
        'description':
            'Truck Freight Payment - Voucher #${testPayment.voucherId} - ${testPayment.method.name}',
      });

      // 3. NO SALES TAX ENTRY - This should not exist in payment journal entries

      // Calculate totals
      final totalDebits = journalEntries.fold<double>(
          0, (sum, entry) => sum + (entry['debitAmount'] as double));
      final totalCredits = journalEntries.fold<double>(
          0, (sum, entry) => sum + (entry['creditAmount'] as double));

      print('\n📊 Simple Payment Journal Entries:');
      for (int i = 0; i < journalEntries.length; i++) {
        final entry = journalEntries[i];
        print('  ${i + 1}. ${entry['type']} (${entry['accountType']}):');
        print('     - Account: ${entry['accountName']}');
        print(
            '     - Debit: \$${(entry['debitAmount'] as double).toStringAsFixed(2)}');
        print(
            '     - Credit: \$${(entry['creditAmount'] as double).toStringAsFixed(2)}');
        print('     - Description: ${entry['description']}');
      }

      print('\n💰 Journal Entry Balance:');
      print('  - Total Debits: \$${totalDebits.toStringAsFixed(2)}');
      print('  - Total Credits: \$${totalCredits.toStringAsFixed(2)}');
      print(
          '  - Difference: \$${(totalDebits - totalCredits).abs().toStringAsFixed(2)}');

      // Assertions
      expect(journalEntries.length, equals(2),
          reason:
              'Should create exactly 2 journal entries: source and truck freight');

      // Verify no sales tax entry exists
      final entryTypes =
          journalEntries.map((e) => e['type'] as String).toList();
      expect(entryTypes, isNot(contains('Sales Tax')),
          reason: 'Should NOT contain any sales tax entries');
      expect(entryTypes, isNot(contains('Sales Tax (6.9%)')),
          reason: 'Should NOT contain 6.9% sales tax entries');

      // Verify perfect balance
      expect(totalDebits, equals(totalCredits),
          reason: 'Journal entries should be perfectly balanced');

      // Verify full payment amounts
      expect(totalDebits, equals(testPayment.amount),
          reason: 'Total debits should equal full payment amount');
      expect(totalCredits, equals(testPayment.amount),
          reason: 'Total credits should equal full payment amount');

      print('\n✅ Simple payment journal entry structure verified!');
      print('   - Only 2 entries: source account and truck freight');
      print('   - NO sales tax entries included');
      print('   - Perfect balance achieved');
      print('   - Full payment amount used (no splitting)');
    });

    test('should verify all payment types exclude sales tax', () {
      // Test different payment methods to ensure none include sales tax

      final paymentMethods = [
        PaymentMethod.cash,
        PaymentMethod.check,
        PaymentMethod.accountTransfer,
        PaymentMethod.fuelCard,
      ];

      for (final method in paymentMethods) {
        final payment = PaymentTransactionModel(
          id: 'payment_${method.name}_001',
          voucherId: 'V-${method.name.toUpperCase()}-001',
          method: method,
          status: PaymentStatus.paid,
          amount: 10000.0,
          pendingAmount: 0.0,
          transactionDate: DateTime.now(),
          accountId: bankAccount.id,
          accountName: bankAccount.accountName,
        );

        // Simulate journal entry creation (without sales tax)
        final entries = <String>[];

        // Source account entry
        entries.add('Source Account (Credit): \$${payment.amount}');

        // Truck freight entry
        entries.add('Truck Freight (Debit): \$${payment.amount}');

        // NO sales tax entry should be added

        print('\n💳 ${method.name} Payment Journal Entries:');
        for (final entry in entries) {
          print('  - $entry');
        }

        // Verify only 2 entries
        expect(entries.length, equals(2),
            reason: '${method.name} payment should have exactly 2 entries');

        // Verify no sales tax entries
        final entriesText = entries.join(' ');
        expect(entriesText, isNot(contains('Sales Tax')),
            reason:
                '${method.name} payment should not contain sales tax entries');
      }

      print('\n✅ All payment methods exclude sales tax!');
    });

    test('should verify payment processing is independent of voucher sales tax',
        () {
      // This test verifies that payment processing doesn't care about voucher sales tax

      // Simulate different voucher scenarios
      final voucherScenarios = [
        {
          'name': 'Voucher with High Sales Tax',
          'totalFreight': 100000.0,
          'salesTax': 6900.0, // 6.9%
        },
        {
          'name': 'Voucher with Low Sales Tax',
          'totalFreight': 10000.0,
          'salesTax': 690.0, // 6.9%
        },
        {
          'name': 'Voucher with Zero Sales Tax',
          'totalFreight': 50000.0,
          'salesTax': 0.0, // No tax
        },
      ];

      for (final scenario in voucherScenarios) {
        final scenarioName = scenario['name'] as String;
        final paymentAmount = 5000.0; // Same payment amount for all scenarios

        // In the corrected system, payment journal entries should be identical
        // regardless of voucher sales tax amounts
        final expectedEntries = {
          'sourceCredit': paymentAmount,
          'truckFreightDebit': paymentAmount,
          'salesTaxDebit': 0.0, // Always zero in payments
        };

        print('\n📋 $scenarioName:');
        print('  - Voucher Sales Tax: \$${scenario['salesTax']}');
        print('  - Payment Amount: \$${paymentAmount.toStringAsFixed(2)}');
        print(
            '  - Source Credit: \$${expectedEntries['sourceCredit']!.toStringAsFixed(2)}');
        print(
            '  - Truck Freight Debit: \$${expectedEntries['truckFreightDebit']!.toStringAsFixed(2)}');
        print(
            '  - Sales Tax Debit: \$${expectedEntries['salesTaxDebit']!.toStringAsFixed(2)}');

        // Verify payment processing is independent of voucher sales tax
        expect(expectedEntries['sourceCredit'], equals(paymentAmount),
            reason: '$scenarioName: Source credit should equal payment amount');

        expect(expectedEntries['truckFreightDebit'], equals(paymentAmount),
            reason:
                '$scenarioName: Truck freight debit should equal payment amount');

        expect(expectedEntries['salesTaxDebit'], equals(0.0),
            reason:
                '$scenarioName: Sales tax debit should always be zero in payments');
      }

      print('\n✅ Payment processing is independent of voucher sales tax!');
      print('   - Same payment amount produces identical journal entries');
      print('   - Voucher sales tax has no impact on payment processing');
      print('   - Sales tax is handled separately in voucher creation');
    });
  });
}
