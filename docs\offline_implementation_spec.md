# Offline Implementation Technical Specification

## Overview

This document provides detailed technical specifications for implementing offline resilience in the logistics application, focusing on voucher creation and financial transactions.

## 1. Core Infrastructure Components

### 1.1 Local Database Setup

#### Database Choice: Drift (SQLite)
```yaml
dependencies:
  drift: ^2.14.0
  sqlite3_flutter_libs: ^0.5.0
  path_provider: ^2.1.0
  path: ^1.8.0

dev_dependencies:
  drift_dev: ^2.14.0
  build_runner: ^2.4.0
```

#### Database Schema Design
```dart
// lib/database/offline_database.dart
@DriftDatabase(tables: [
  OfflineVouchers,
  SyncQueue,
  LocalChartAccounts,
  OfflineJournalEntries,
  ConflictResolution,
  SyncMetadata
])
class OfflineDatabase extends _$OfflineDatabase {
  OfflineDatabase() : super(_openConnection());
  
  @override
  int get schemaVersion => 1;
}
```

#### Table Definitions
```dart
// lib/database/tables/offline_vouchers.dart
class OfflineVouchers extends Table {
  TextColumn get id => text()();
  TextColumn get voucherNumber => text().unique()();
  TextColumn get data => text()(); // JSON blob
  TextColumn get state => text()();
  DateTimeColumn get createdAt => dateTime()();
  IntColumn get syncAttempts => integer().withDefault(const Constant(0))();
  DateTimeColumn get lastSyncAttempt => dateTime().nullable()();
  TextColumn get errorMessage => text().nullable()();
  
  @override
  Set<Column> get primaryKey => {id};
}

// lib/database/tables/sync_queue.dart
class SyncQueue extends Table {
  TextColumn get id => text()();
  TextColumn get operationType => text()();
  TextColumn get data => text()();
  TextColumn get state => text()();
  TextColumn get dependencies => text()(); // JSON array
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get lastAttempt => dateTime().nullable()();
  IntColumn get retryCount => integer().withDefault(const Constant(0))();
  TextColumn get errorMessage => text().nullable()();
  
  @override
  Set<Column> get primaryKey => {id};
}
```

### 1.2 Connectivity Monitoring Service

```dart
// lib/core/services/connectivity_service.dart
class ConnectivityService extends GetxController {
  final Connectivity _connectivity = Connectivity();
  final Rx<ConnectivityStatus> _status = ConnectivityStatus.unknown.obs;
  
  ConnectivityStatus get status => _status.value;
  Stream<ConnectivityStatus> get statusStream => _status.stream;
  
  @override
  void onInit() {
    super.onInit();
    _initConnectivity();
    _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }
  
  Future<void> _initConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      log('Failed to get connectivity: $e');
      _status.value = ConnectivityStatus.unknown;
    }
  }
  
  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    final previousStatus = _status.value;
    
    switch (result) {
      case ConnectivityResult.wifi:
      case ConnectivityResult.mobile:
      case ConnectivityResult.ethernet:
        final isReachable = await _testInternetConnection();
        _status.value = isReachable 
          ? ConnectivityStatus.online 
          : ConnectivityStatus.offline;
        break;
      case ConnectivityResult.none:
        _status.value = ConnectivityStatus.offline;
        break;
    }
    
    // Trigger sync when coming back online
    if (previousStatus == ConnectivityStatus.offline && 
        _status.value == ConnectivityStatus.online) {
      Get.find<SyncService>().triggerSync();
    }
  }
  
  Future<bool> _testInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}

enum ConnectivityStatus { online, offline, unknown }
```

### 1.3 Offline State Management

```dart
// lib/core/services/offline_state_service.dart
class OfflineStateService extends GetxController {
  final OfflineDatabase _database = Get.find<OfflineDatabase>();
  final ConnectivityService _connectivity = Get.find<ConnectivityService>();
  
  final RxBool _isOfflineMode = false.obs;
  final RxInt _pendingOperations = 0.obs;
  final RxString _lastSyncTime = ''.obs;
  
  bool get isOfflineMode => _isOfflineMode.value;
  int get pendingOperations => _pendingOperations.value;
  String get lastSyncTime => _lastSyncTime.value;
  
  @override
  void onInit() {
    super.onInit();
    _connectivity.statusStream.listen(_handleConnectivityChange);
    _updatePendingOperationsCount();
  }
  
  void _handleConnectivityChange(ConnectivityStatus status) {
    _isOfflineMode.value = status == ConnectivityStatus.offline;
    
    if (status == ConnectivityStatus.online) {
      _triggerAutoSync();
    }
  }
  
  Future<void> _updatePendingOperationsCount() async {
    final count = await _database.select(_database.syncQueue)
        .where((tbl) => tbl.state.equals('pending'))
        .get()
        .then((rows) => rows.length);
    _pendingOperations.value = count;
  }
  
  Future<void> _triggerAutoSync() async {
    if (_pendingOperations.value > 0) {
      await Get.find<SyncService>().syncPendingOperations();
    }
  }
}
```

## 2. Offline Transaction Models

### 2.1 Enhanced Data Models

```dart
// lib/models/offline/offline_transaction_state.dart
enum OfflineTransactionState {
  pending('pending'),
  syncing('syncing'),
  synced('synced'),
  conflict('conflict'),
  failed('failed'),
  interrupted('interrupted');
  
  const OfflineTransactionState(this.value);
  final String value;
  
  static OfflineTransactionState fromString(String value) {
    return values.firstWhere((e) => e.value == value);
  }
}

// lib/models/offline/sync_queue_entry.dart
class SyncQueueEntry {
  final String id;
  final String operationType;
  final Map<String, dynamic> data;
  final OfflineTransactionState state;
  final DateTime createdAt;
  final DateTime? lastSyncAttempt;
  final int retryCount;
  final String? errorMessage;
  final List<String> dependencies;
  
  SyncQueueEntry({
    required this.id,
    required this.operationType,
    required this.data,
    required this.state,
    required this.createdAt,
    this.lastSyncAttempt,
    this.retryCount = 0,
    this.errorMessage,
    this.dependencies = const [],
  });
  
  factory SyncQueueEntry.fromJson(Map<String, dynamic> json) {
    return SyncQueueEntry(
      id: json['id'],
      operationType: json['operationType'],
      data: Map<String, dynamic>.from(json['data']),
      state: OfflineTransactionState.fromString(json['state']),
      createdAt: DateTime.parse(json['createdAt']),
      lastSyncAttempt: json['lastSyncAttempt'] != null 
          ? DateTime.parse(json['lastSyncAttempt']) 
          : null,
      retryCount: json['retryCount'] ?? 0,
      errorMessage: json['errorMessage'],
      dependencies: List<String>.from(json['dependencies'] ?? []),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'operationType': operationType,
      'data': data,
      'state': state.value,
      'createdAt': createdAt.toIso8601String(),
      'lastSyncAttempt': lastSyncAttempt?.toIso8601String(),
      'retryCount': retryCount,
      'errorMessage': errorMessage,
      'dependencies': dependencies,
    };
  }
  
  SyncQueueEntry copyWith({
    OfflineTransactionState? state,
    DateTime? lastSyncAttempt,
    int? retryCount,
    String? errorMessage,
  }) {
    return SyncQueueEntry(
      id: id,
      operationType: operationType,
      data: data,
      state: state ?? this.state,
      createdAt: createdAt,
      lastSyncAttempt: lastSyncAttempt ?? this.lastSyncAttempt,
      retryCount: retryCount ?? this.retryCount,
      errorMessage: errorMessage ?? this.errorMessage,
      dependencies: dependencies,
    );
  }
}
```

### 2.2 Offline Voucher Model Extension

```dart
// lib/models/offline/offline_voucher_model.dart
class OfflineVoucherModel extends VoucherModel {
  final String offlineId;
  final OfflineTransactionState syncState;
  final DateTime localCreatedAt;
  final List<String> localJournalEntryIds;
  final Map<String, double> localBalanceChanges;
  final bool hasConflicts;
  final String? conflictDetails;
  
  OfflineVoucherModel({
    required this.offlineId,
    required this.syncState,
    required this.localCreatedAt,
    this.localJournalEntryIds = const [],
    this.localBalanceChanges = const {},
    this.hasConflicts = false,
    this.conflictDetails,
    // All VoucherModel parameters
    required super.voucherStatus,
    required super.departureDate,
    required super.driverName,
    required super.invoiceTasNumberList,
    required super.invoiceBiltyNumberList,
    required super.weightInTons,
    required super.voucherNumber,
    required super.productName,
    required super.totalNumberOfBags,
    required super.brokerType,
    required super.brokerName,
    required super.brokerFees,
    required super.munshianaFees,
    required super.brokerAccount,
    required super.munshianaAccount,
    required super.driverPhoneNumber,
    required super.truckNumber,
    required super.conveyNoteNumber,
    required super.totalFreight,
    super.companyFreight = 0.0,
    super.settledFreight = 0.0,
    super.paymentTransactions = const [],
    super.dieselLiters,
    super.dieselCompany,
    super.chequeAmount,
    super.bankName,
    super.chequeNumber,
    super.belongsToDate,
    super.createdAt,
    super.brokerList = const [],
    super.calculatedProfit = 0.0,
    super.calculatedTax = 0.0,
    super.calculatedFreightTax = 0.0,
    super.brokerAccountId,
    super.munshianaAccountId,
    super.salesTaxAccountId,
    super.freightTaxAccountId,
    super.profitAccountId,
    super.truckFreightAccountId,
    super.taxAccountName,
    super.freightTaxAccountName,
    super.profitAccountName,
    super.companyFreightAccountId,
    super.companyFreightAccountName,
    super.brokerCompanyId,
    super.brokerCompanyName,
    super.selectedTaxAuthorities = const [],
  });
  
  // Convert to regular VoucherModel for sync
  VoucherModel toVoucherModel() {
    return VoucherModel(
      voucherStatus: voucherStatus,
      departureDate: departureDate,
      driverName: driverName,
      invoiceTasNumberList: invoiceTasNumberList,
      invoiceBiltyNumberList: invoiceBiltyNumberList,
      weightInTons: weightInTons,
      voucherNumber: voucherNumber,
      productName: productName,
      totalNumberOfBags: totalNumberOfBags,
      brokerType: brokerType,
      brokerName: brokerName,
      brokerFees: brokerFees,
      munshianaFees: munshianaFees,
      brokerAccount: brokerAccount,
      munshianaAccount: munshianaAccount,
      driverPhoneNumber: driverPhoneNumber,
      truckNumber: truckNumber,
      conveyNoteNumber: conveyNoteNumber,
      totalFreight: totalFreight,
      companyFreight: companyFreight,
      settledFreight: settledFreight,
      paymentTransactions: paymentTransactions,
      dieselLiters: dieselLiters,
      dieselCompany: dieselCompany,
      chequeAmount: chequeAmount,
      bankName: bankName,
      chequeNumber: chequeNumber,
      belongsToDate: belongsToDate,
      createdAt: createdAt,
      brokerList: brokerList,
      calculatedProfit: calculatedProfit,
      calculatedTax: calculatedTax,
      calculatedFreightTax: calculatedFreightTax,
      brokerAccountId: brokerAccountId,
      munshianaAccountId: munshianaAccountId,
      salesTaxAccountId: salesTaxAccountId,
      freightTaxAccountId: freightTaxAccountId,
      profitAccountId: profitAccountId,
      truckFreightAccountId: truckFreightAccountId,
      taxAccountName: taxAccountName,
      freightTaxAccountName: freightTaxAccountName,
      profitAccountName: profitAccountName,
      companyFreightAccountId: companyFreightAccountId,
      companyFreightAccountName: companyFreightAccountName,
      brokerCompanyId: brokerCompanyId,
      brokerCompanyName: brokerCompanyName,
      selectedTaxAuthorities: selectedTaxAuthorities,
    );
  }
}
```

## 3. Implementation Roadmap

### Phase 1: Foundation (Week 1-2)
1. Set up Drift database with offline tables
2. Implement connectivity monitoring service
3. Create offline state management service
4. Basic sync queue infrastructure

### Phase 2: Offline Voucher System (Week 3-4)
1. Offline voucher creation with local storage
2. Local journal entry generation
3. Local balance calculations
4. Atomic transaction recovery mechanisms

### Phase 3: Sync Engine (Week 5-6)
1. Robust synchronization engine
2. Conflict detection and resolution
3. Retry mechanisms with exponential backoff
4. Batch operation support

### Phase 4: UI Integration (Week 7)
1. Offline status indicators
2. Sync progress displays
3. Conflict resolution interfaces
4. User feedback systems

This specification provides the foundation for implementing comprehensive offline resilience while maintaining data integrity and user experience.
