import 'package:flutter/material.dart';
import '../../../../../models/finance/journal_entry_model.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../../../../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../../../../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../services/account_journal_transaction_service.dart';
import '../../../../../features/accounting/journal_entries/repositories/journal_entry_repository.dart';

/// Test widget to verify balance calculation system works correctly
/// for both current-dated and backdated entries
class BalanceCalculationTestWidget extends StatefulWidget {
  const BalanceCalculationTestWidget({super.key});

  @override
  State<BalanceCalculationTestWidget> createState() =>
      _BalanceCalculationTestWidgetState();
}

class _BalanceCalculationTestWidgetState
    extends State<BalanceCalculationTestWidget> {
  final _journalEntryService = JournalEntryFirebaseService();
  final _chartOfAccountsService = ChartOfAccountsFirebaseService();
  late final _transactionService = AccountJournalTransactionService(
    JournalEntryRepositoryImpl(_journalEntryService),
  );

  bool _isLoading = false;
  String _testResults = '';
  ChartOfAccountsModel? _testAccount;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Balance Calculation Test'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Balance Calculation System Test',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This test verifies that balance calculations work correctly '
                      'for both current-dated and backdated journal entries.',
                    ),
                    const SizedBox(height: 16),
                    if (_testAccount != null) ...[
                      Text('Test Account: ${_testAccount!.accountName}'),
                      Text('Account Number: ${_testAccount!.accountNumber}'),
                      Text('Account Type: ${_testAccount!.accountType.name}'),
                      const SizedBox(height: 8),
                    ],
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _isLoading ? null : _setupTestAccount,
                          child: const Text('Setup Test Account'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isLoading || _testAccount == null
                              ? null
                              : _runBalanceTest,
                          child: const Text('Run Balance Test'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _isLoading ? null : _clearTestResults,
                          child: const Text('Clear Results'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Test Results',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      if (_isLoading)
                        const Center(
                          child: CircularProgressIndicator(),
                        )
                      else
                        Expanded(
                          child: SingleChildScrollView(
                            child: Text(
                              _testResults.isEmpty
                                  ? 'No test results yet. Click "Run Balance Test" to start.'
                                  : _testResults,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _setupTestAccount() async {
    setState(() {
      _isLoading = true;
      _testResults = 'Setting up test account...\n';
    });

    try {
      // Get or create a test account
      final accounts = await _chartOfAccountsService.getAllAccounts();
      final assetAccounts = accounts
          .where((account) =>
              account.accountType == AccountType.cash && account.isActive)
          .toList();

      if (assetAccounts.isNotEmpty) {
        _testAccount = assetAccounts.first;
        setState(() {
          _testResults +=
              'Using existing account: ${_testAccount!.accountName}\n';
          _testResults += 'Account ID: ${_testAccount!.id}\n';
          _testResults += 'Current Balance: ${_testAccount!.balance}\n\n';
        });
      } else {
        setState(() {
          _testResults +=
              'No suitable test account found. Please create an Asset account first.\n';
        });
      }
    } catch (e) {
      setState(() {
        _testResults += 'Error setting up test account: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _runBalanceTest() async {
    if (_testAccount == null) return;

    setState(() {
      _isLoading = true;
      _testResults += '\n=== Starting Balance Calculation Test ===\n';
    });

    try {
      // Test 1: Create current-dated entry
      await _testCurrentDatedEntry();

      // Test 2: Create backdated entry
      await _testBackdatedEntry();

      // Test 3: Verify balance consistency
      await _testBalanceConsistency();

      setState(() {
        _testResults += '\n=== Test Completed Successfully ===\n';
      });
    } catch (e) {
      setState(() {
        _testResults += '\nError during test: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testCurrentDatedEntry() async {
    setState(() {
      _testResults += '\n--- Test 1: Entry dated 22nd (Latest) ---\n';
    });

    // Create entry dated 22nd (simulating current date)
    final date22nd = DateTime(2024, 1, 22, 10, 0, 0);
    final entry = _createTestJournalEntry(
      'TEST-22ND-${DateTime.now().millisecondsSinceEpoch}',
      'Test entry dated 22nd',
      date22nd,
      100.0,
      0.0,
    );

    await _journalEntryService.createJournalEntry(entry);
    await _journalEntryService.postJournalEntry(entry.id);

    setState(() {
      _testResults +=
          'Created and posted entry dated 22nd: ${entry.entryNumber}\n';
      _testResults += 'Amount: \$100.00 debit\n';
      _testResults += 'Date: ${date22nd.toString().substring(0, 19)}\n';
      _testResults += 'Expected running balance: Previous balance + \$100.00\n';
    });
  }

  Future<void> _testBackdatedEntry() async {
    setState(() {
      _testResults += '\n--- Test 2: Entry dated 16th (Backdated) ---\n';
    });

    // Create entry dated 16th (before the 22nd entry)
    final date16th = DateTime(2024, 1, 16, 14, 30, 0);
    final entry = _createTestJournalEntry(
      'TEST-16TH-${DateTime.now().millisecondsSinceEpoch}',
      'Test entry dated 16th (backdated)',
      date16th,
      50.0,
      0.0,
    );

    await _journalEntryService.createJournalEntry(entry);
    await _journalEntryService.postJournalEntry(entry.id);

    setState(() {
      _testResults +=
          'Created and posted entry dated 16th: ${entry.entryNumber}\n';
      _testResults += 'Amount: \$50.00 debit\n';
      _testResults += 'Date: ${date16th.toString().substring(0, 19)}\n';
      _testResults += 'This should trigger cascade recalculation:\n';
      _testResults += '  - 16th entry balance = Previous balance + \$50.00\n';
      _testResults += '  - 22nd entry balance = 16th balance + \$100.00\n';
    });
  }

  Future<void> _testBalanceConsistency() async {
    setState(() {
      _testResults += '\n--- Test 3: Balance Consistency Check ---\n';
    });

    // Get transaction history for the test account
    final result = await _transactionService.getAccountTransactionsPaginated(
      accountId: _testAccount!.id,
      uid: _testAccount!.uid,
      accountCategory: AccountCategory.assets,
      limit: 50,
    );

    setState(() {
      _testResults += 'Retrieved ${result.transactions.length} transactions\n';
      _testResults += '\nTransaction History (display order - newest first):\n';

      // Sort transactions by date for chronological analysis
      final chronologicalTransactions = List.from(result.transactions);
      chronologicalTransactions
          .sort((a, b) => a.entryDate.compareTo(b.entryDate));

      for (int i = 0; i < result.transactions.length && i < 10; i++) {
        final tx = result.transactions[i];
        _testResults += '${i + 1}. ${tx.entryNumber} - ${tx.description}\n';
        _testResults +=
            '   Date: ${tx.entryDate.toString().substring(0, 19)}\n';
        _testResults +=
            '   Amount: \$${tx.debitAmount > 0 ? tx.debitAmount : -tx.creditAmount}\n';
        _testResults += '   Running Balance: \$${tx.runningBalance}\n';
        _testResults +=
            '   Has Stored Balance: ${tx.runningBalance != 0 ? "Yes" : "No"}\n\n';
      }

      _testResults += '\n🔍 Chronological Order Analysis:\n';
      _testResults +=
          'This verifies that running balances are calculated correctly in chronological order:\n\n';

      for (int i = 0; i < chronologicalTransactions.length && i < 10; i++) {
        final tx = chronologicalTransactions[i];
        _testResults +=
            '${i + 1}. ${tx.entryDate.toString().substring(0, 10)} - ${tx.entryNumber}\n';
        _testResults +=
            '   Amount: \$${tx.debitAmount > 0 ? "+${tx.debitAmount}" : "-${tx.creditAmount}"}\n';
        _testResults +=
            '   Running Balance: \$${tx.runningBalance.toStringAsFixed(2)}\n';

        // Validate chronological balance progression
        if (i > 0) {
          final prevTx = chronologicalTransactions[i - 1];
          final expectedBalance =
              prevTx.runningBalance + (tx.debitAmount - tx.creditAmount);
          final isCorrect = (tx.runningBalance - expectedBalance).abs() < 0.01;
          _testResults +=
              '   Expected: \$${expectedBalance.toStringAsFixed(2)} | Actual: \$${tx.runningBalance.toStringAsFixed(2)} | ${isCorrect ? "✅ CORRECT" : "❌ INCORRECT"}\n';

          if (!isCorrect) {
            _testResults += '   ⚠️  BALANCE CALCULATION ERROR DETECTED!\n';
            _testResults +=
                '   Previous balance: \$${prevTx.runningBalance.toStringAsFixed(2)}\n';
            _testResults +=
                '   Current transaction: \$${(tx.debitAmount - tx.creditAmount).toStringAsFixed(2)}\n';
            _testResults +=
                '   Should equal: \$${expectedBalance.toStringAsFixed(2)}\n';
          }
        } else {
          _testResults +=
              '   (First transaction - no previous balance to compare)\n';
        }
        _testResults += '\n';
      }

      // Summary
      final hasErrors = chronologicalTransactions.length > 1 &&
          chronologicalTransactions.skip(1).any((tx) {
            final index = chronologicalTransactions.indexOf(tx);
            final prevTx = chronologicalTransactions[index - 1];
            final expectedBalance =
                prevTx.runningBalance + (tx.debitAmount - tx.creditAmount);
            return (tx.runningBalance - expectedBalance).abs() >= 0.01;
          });

      _testResults += hasErrors
          ? '❌ BALANCE CALCULATION TEST FAILED - Chronological order issues detected!\n'
          : '✅ BALANCE CALCULATION TEST PASSED - All balances are chronologically correct!\n';
    });
  }

  JournalEntryModel _createTestJournalEntry(
    String entryNumber,
    String description,
    DateTime entryDate,
    double debitAmount,
    double creditAmount,
  ) {
    final entryId = 'TEST_${DateTime.now().millisecondsSinceEpoch}';
    final lineId = 'LINE_${DateTime.now().millisecondsSinceEpoch}';

    return JournalEntryModel(
      id: entryId,
      entryNumber: entryNumber,
      entryDate: entryDate,
      description: description,
      entryType: JournalEntryType.manual,
      status: JournalEntryStatus.draft,
      lines: [
        JournalEntryLineModel(
          id: lineId,
          journalEntryId: entryId,
          accountId: _testAccount!.id,
          accountNumber: _testAccount!.accountNumber,
          accountName: _testAccount!.accountName,
          debitAmount: debitAmount,
          creditAmount: creditAmount,
          description: description,
          createdAt: DateTime.now(),
        ),
      ],
      totalDebits: debitAmount,
      totalCredits: creditAmount,
      createdAt: DateTime.now(),
      createdBy: 'test_user',
      uid: _testAccount!.uid,
    );
  }

  void _clearTestResults() {
    setState(() {
      _testResults = '';
      _testAccount = null;
    });
  }
}
