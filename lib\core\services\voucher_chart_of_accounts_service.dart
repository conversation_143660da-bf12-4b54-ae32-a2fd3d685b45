import 'dart:developer';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/voucher_model.dart';
import '../../models/payment_transaction_model.dart';

/// Service to handle Chart of Accounts integration with voucher system
/// Provides validation, mapping, and helper methods for voucher-account relationships
class VoucherChartOfAccountsService {
  /// Validate that selected accounts are appropriate for voucher transactions
  static ValidationResult validateVoucherAccounts({
    required ChartOfAccountsModel? brokerAccount,
    required ChartOfAccountsModel? munshianaAccount,
    required ChartOfAccountsModel? salesTaxAccount,
    required ChartOfAccountsModel? freightTaxAccount,
    required ChartOfAccountsModel? profitAccount,
    required List<PaymentTransactionModel> paymentTransactions,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    // Validate broker account (should be liability or expense account)
    final brokerValidation = _validateLiabilityOrExpenseAccount(
      account: brokerAccount,
      accountName: 'Broker',
      allowedTypes: [
        AccountType.accountsPayable,
        AccountType.operatingExpenses,
        AccountType.administrativeExpenses
      ],
    );
    errors.addAll(brokerValidation.errors);
    warnings.addAll(brokerValidation.warnings);

    // Validate munshiana account (should be expense account)
    final munshianaValidation = _validateExpenseAccount(
      account: munshianaAccount,
      accountName: 'Munshiana',
      allowedTypes: [
        AccountType.operatingExpenses,
        AccountType.administrativeExpenses
      ],
    );
    errors.addAll(munshianaValidation.errors);
    warnings.addAll(munshianaValidation.warnings);

    // Validate sales tax account (should be liability or expense account)
    final salesTaxValidation = _validateTaxAccount(
      account: salesTaxAccount,
      accountName: 'Sales Tax',
    );
    errors.addAll(salesTaxValidation.errors);
    warnings.addAll(salesTaxValidation.warnings);

    // Validate freight tax account (should be liability or expense account)
    final freightTaxValidation = _validateTaxAccount(
      account: freightTaxAccount,
      accountName: 'Freight Tax',
    );
    errors.addAll(freightTaxValidation.errors);
    warnings.addAll(freightTaxValidation.warnings);

    // Validate profit account (should be equity or revenue account)
    final profitValidation = _validateProfitAccount(profitAccount);
    errors.addAll(profitValidation.errors);
    warnings.addAll(profitValidation.warnings);

    // Validate payment transaction accounts
    final paymentValidation = _validatePaymentAccounts(paymentTransactions);
    errors.addAll(paymentValidation.errors);
    warnings.addAll(paymentValidation.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Enhanced validation for voucher accounts with Chart of Accounts objects
  static ValidationResult validateVoucherAccountsEnhanced({
    required ChartOfAccountsModel? brokerAccount,
    required ChartOfAccountsModel? munshianaAccount,
    required ChartOfAccountsModel? salesTaxAccount,
    required ChartOfAccountsModel? freightTaxAccount,
    required ChartOfAccountsModel? profitAccount,
    required List<PaymentTransactionModel> paymentTransactions,
    required List<ChartOfAccountsModel> allAccounts,
    required double totalVoucherAmount,
    bool strictValidation = false,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    // Basic validation first
    final basicValidation = validateVoucherAccounts(
      brokerAccount: brokerAccount,
      munshianaAccount: munshianaAccount,
      salesTaxAccount: salesTaxAccount,
      freightTaxAccount: freightTaxAccount,
      profitAccount: profitAccount,
      paymentTransactions: paymentTransactions,
    );
    errors.addAll(basicValidation.errors);
    warnings.addAll(basicValidation.warnings);

    // Enhanced payment account validation with balance checking
    final enhancedPaymentValidation = _validatePaymentAccountsEnhanced(
      paymentTransactions: paymentTransactions,
      allAccounts: allAccounts,
      strictValidation: strictValidation,
    );
    errors.addAll(enhancedPaymentValidation.errors);
    warnings.addAll(enhancedPaymentValidation.warnings);

    // Business rule validation
    final businessRuleValidation = _validateBusinessRules(
      brokerAccount: brokerAccount,
      munshianaAccount: munshianaAccount,
      salesTaxAccount: salesTaxAccount,
      freightTaxAccount: freightTaxAccount,
      profitAccount: profitAccount,
      totalVoucherAmount: totalVoucherAmount,
    );
    warnings.addAll(businessRuleValidation.warnings);

    // Account hierarchy validation
    final hierarchyValidation = _validateAccountHierarchy(
      accounts: [
        brokerAccount,
        munshianaAccount,
        salesTaxAccount,
        freightTaxAccount,
        profitAccount
      ]
          .where((account) => account != null)
          .cast<ChartOfAccountsModel>()
          .toList(),
      allAccounts: allAccounts,
    );
    warnings.addAll(hierarchyValidation.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Get recommended account types for different voucher components
  static List<AccountType> getRecommendedAccountTypes(
      VoucherAccountType voucherAccountType) {
    switch (voucherAccountType) {
      case VoucherAccountType.brokerFees:
        return [
          AccountType.accountsPayable,
          AccountType.operatingExpenses,
        ];

      case VoucherAccountType.munshianaFees:
        return [
          AccountType.equityServiceRevenue,
        ];

      case VoucherAccountType.salesTax:
      case VoucherAccountType.freightTax:
        return [
          AccountType.currentLiabilities,
          AccountType.taxExpense,
        ];

      case VoucherAccountType.profit:
        return [
          AccountType.ownersEquity,
          AccountType.retainedEarnings,
          AccountType.salesRevenue,
          AccountType.serviceRevenue,
        ];

      case VoucherAccountType.payment:
        return [
          AccountType.cash,
          AccountType.bank,
          AccountType.currentAssets,
        ];

      case VoucherAccountType.freightRevenue:
        return [
          AccountType.salesRevenue,
          AccountType.serviceRevenue,
        ];
    }
  }

  /// Get account categories allowed for different voucher components
  static List<AccountCategory> getAllowedCategories(
      VoucherAccountType voucherAccountType) {
    switch (voucherAccountType) {
      case VoucherAccountType.brokerFees:
        return [AccountCategory.liabilities, AccountCategory.expenses];

      case VoucherAccountType.munshianaFees:
        return [AccountCategory.equity];

      case VoucherAccountType.salesTax:
      case VoucherAccountType.freightTax:
        return [AccountCategory.liabilities, AccountCategory.expenses];

      case VoucherAccountType.profit:
        return [AccountCategory.equity, AccountCategory.revenue];

      case VoucherAccountType.payment:
        return [AccountCategory.assets];

      case VoucherAccountType.freightRevenue:
        return [AccountCategory.revenue];
    }
  }

  /// Create a voucher model with Chart of Accounts integration
  static VoucherModel createVoucherWithChartOfAccounts({
    required VoucherModel baseVoucher,
    ChartOfAccountsModel? brokerAccount,
    ChartOfAccountsModel? munshianaAccount,
    ChartOfAccountsModel? salesTaxAccount,
    ChartOfAccountsModel? freightTaxAccount,
    ChartOfAccountsModel? profitAccount,
  }) {
    // Create a copy of the voucher with Chart of Accounts IDs
    return VoucherModel(
      voucherStatus: baseVoucher.voucherStatus,
      departureDate: baseVoucher.departureDate,
      driverName: baseVoucher.driverName,
      invoiceTasNumberList: baseVoucher.invoiceTasNumberList,
      invoiceBiltyNumberList: baseVoucher.invoiceBiltyNumberList,
      weightInTons: baseVoucher.weightInTons,
      voucherNumber: baseVoucher.voucherNumber,
      productName: baseVoucher.productName,
      totalNumberOfBags: baseVoucher.totalNumberOfBags,
      brokerType: baseVoucher.brokerType,
      brokerName: baseVoucher.brokerName,
      selectedBroker: baseVoucher.selectedBroker,
      brokerFees: baseVoucher.brokerFees,
      munshianaFees: baseVoucher.munshianaFees,
      brokerAccount: baseVoucher
          .brokerAccount, // Keep legacy field for backward compatibility
      munshianaAccount: baseVoucher
          .munshianaAccount, // Keep legacy field for backward compatibility
      driverPhoneNumber: baseVoucher.driverPhoneNumber,
      truckNumber: baseVoucher.truckNumber,
      conveyNoteNumber: baseVoucher.conveyNoteNumber,
      totalFreight: baseVoucher.totalFreight,
      companyFreight: baseVoucher.companyFreight,
      settledFreight: baseVoucher.settledFreight,
      paymentTransactions: baseVoucher.paymentTransactions,
      dieselLiters: baseVoucher.dieselLiters,
      dieselCompany: baseVoucher.dieselCompany,
      chequeAmount: baseVoucher.chequeAmount,
      bankName: baseVoucher.bankName,
      chequeNumber: baseVoucher.chequeNumber,
      belongsToDate: baseVoucher.belongsToDate,
      createdAt: baseVoucher.createdAt,
      brokerList: baseVoucher.brokerList,
      calculatedProfit: baseVoucher.calculatedProfit,
      calculatedTax: baseVoucher.calculatedTax,
      calculatedFreightTax: baseVoucher.calculatedFreightTax,
      // Chart of Accounts fields
      brokerAccountId: brokerAccount?.id,
      munshianaAccountId: munshianaAccount?.id,
      salesTaxAccountId: salesTaxAccount?.id,
      freightTaxAccountId: freightTaxAccount?.id,
      profitAccountId: profitAccount?.id,
      // Legacy account name fields
      taxAccountName: salesTaxAccount?.displayName,
      freightTaxAccountName: freightTaxAccount?.displayName,
      profitAccountName: profitAccount?.displayName,
      companyFreightAccountId: baseVoucher.companyFreightAccountId,
      companyFreightAccountName: baseVoucher.companyFreightAccountName,
      brokerCompanyId: baseVoucher.brokerCompanyId,
      brokerCompanyName: baseVoucher.brokerCompanyName,
      selectedTaxAuthorities: baseVoucher.selectedTaxAuthorities,
    );
  }

  /// Log account selection for debugging
  static void logAccountSelection({
    required String voucherNumber,
    ChartOfAccountsModel? brokerAccount,
    ChartOfAccountsModel? munshianaAccount,
    ChartOfAccountsModel? companyFreightAccount,
    ChartOfAccountsModel? salesTaxAccount,
    ChartOfAccountsModel? freightTaxAccount,
    ChartOfAccountsModel? profitAccount,
    ChartOfAccountsModel? truckFreightAccount,
  }) {
    log('Chart of Accounts selection for voucher $voucherNumber:');
    log('  Broker Account: ${brokerAccount?.displayName ?? 'Not selected'}');
    log('  Munshiana Account: ${munshianaAccount?.displayName ?? 'Not selected'}');
    log('  Company Freight Account: ${companyFreightAccount?.displayName ?? 'Not selected'}');
    log('  Sales Tax Account: ${salesTaxAccount?.displayName ?? 'Not selected'}');
    log('  Freight Tax Account: ${freightTaxAccount?.displayName ?? 'Not selected'}');
    log('  Profit Account: ${profitAccount?.displayName ?? 'Not selected'}');
    log('  Truck Freight Account: ${truckFreightAccount?.displayName ?? 'Not selected'}');
  }

  // ========== PRIVATE VALIDATION HELPER METHODS ==========

  /// Validate expense account with specific allowed types
  static ValidationResult _validateExpenseAccount({
    required ChartOfAccountsModel? account,
    required String accountName,
    required List<AccountType> allowedTypes,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (account == null) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    // Check if account is active
    if (!account.isActive) {
      errors.add('$accountName account must be active');
    }

    // Check if account is in expenses category
    if (account.category != AccountCategory.expenses) {
      errors.add('$accountName account must be an expense account');
    }

    // Check if account type is appropriate
    if (!allowedTypes.contains(account.accountType)) {
      warnings.add(
          '$accountName account type "${account.accountType.displayName}" may not be optimal. '
          'Recommended types: ${allowedTypes.map((t) => t.displayName).join(', ')}');
    }

    // Check account balance for unusual patterns
    if (account.balance > 0) {
      warnings.add(
          '$accountName account has a positive balance (${account.balance.toStringAsFixed(2)}), '
          'which is unusual for expense accounts');
    }

    return ValidationResult(
        isValid: errors.isEmpty, errors: errors, warnings: warnings);
  }

  /// Validate liability or expense account with specific allowed types
  static ValidationResult _validateLiabilityOrExpenseAccount({
    required ChartOfAccountsModel? account,
    required String accountName,
    required List<AccountType> allowedTypes,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (account == null) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    // Check if account is active
    if (!account.isActive) {
      errors.add('$accountName account must be active');
    }

    // Check if account is in liabilities or expenses category
    if (account.category != AccountCategory.liabilities &&
        account.category != AccountCategory.expenses) {
      errors.add('$accountName account must be a liability or expense account');
    }

    // Check if account type is appropriate
    if (!allowedTypes.contains(account.accountType)) {
      warnings.add(
          '$accountName account type "${account.accountType.displayName}" may not be optimal. '
          'Recommended types: ${allowedTypes.map((t) => t.displayName).join(', ')}');
    }

    // Check account balance for unusual patterns
    if (account.category == AccountCategory.expenses && account.balance > 0) {
      warnings.add(
          '$accountName account has a positive balance (${account.balance.toStringAsFixed(2)}), '
          'which is unusual for expense accounts');
    } else if (account.category == AccountCategory.liabilities &&
        account.balance < 0) {
      warnings.add(
          '$accountName account has a negative balance (${account.balance.toStringAsFixed(2)}), '
          'which is unusual for liability accounts');
    }

    return ValidationResult(
        isValid: errors.isEmpty, errors: errors, warnings: warnings);
  }

  /// Validate tax account (liability or expense)
  static ValidationResult _validateTaxAccount({
    required ChartOfAccountsModel? account,
    required String accountName,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (account == null) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    // Check if account is active
    if (!account.isActive) {
      errors.add('$accountName account must be active');
    }

    // Check if account category is appropriate
    if (account.category != AccountCategory.liabilities &&
        account.category != AccountCategory.expenses) {
      warnings.add(
          '$accountName account should typically be a liability or expense account');
    }

    // Recommend specific account types
    final recommendedTypes = [
      AccountType.currentLiabilities,
      AccountType.taxExpense,
    ];

    if (!recommendedTypes.contains(account.accountType)) {
      warnings.add(
          '$accountName account type "${account.accountType.displayName}" may not be optimal. '
          'Recommended types: ${recommendedTypes.map((t) => t.displayName).join(', ')}');
    }

    return ValidationResult(
        isValid: errors.isEmpty, errors: errors, warnings: warnings);
  }

  /// Validate profit account (equity or revenue)
  static ValidationResult _validateProfitAccount(
      ChartOfAccountsModel? account) {
    final errors = <String>[];
    final warnings = <String>[];

    if (account == null) {
      return ValidationResult(isValid: true, errors: [], warnings: []);
    }

    // Check if account is active
    if (!account.isActive) {
      errors.add('Profit account must be active');
    }

    // Check if account category is appropriate
    if (account.category != AccountCategory.equity &&
        account.category != AccountCategory.revenue) {
      warnings.add('Profit account should be an equity or revenue account');
    }

    // Recommend specific account types
    final recommendedTypes = [
      AccountType.retainedEarnings,
      AccountType.ownersEquity,
      AccountType.salesRevenue,
      AccountType.serviceRevenue,
    ];

    if (!recommendedTypes.contains(account.accountType)) {
      warnings.add(
          'Profit account type "${account.accountType.displayName}" may not be optimal. '
          'Recommended types: ${recommendedTypes.map((t) => t.displayName).join(', ')}');
    }

    return ValidationResult(
        isValid: errors.isEmpty, errors: errors, warnings: warnings);
  }

  /// Validate payment accounts (basic validation without account objects)
  static ValidationResult _validatePaymentAccounts(
      List<PaymentTransactionModel> paymentTransactions) {
    final errors = <String>[];
    final warnings = <String>[];

    for (int i = 0; i < paymentTransactions.length; i++) {
      final payment = paymentTransactions[i];

      // Check if payment has account ID when required
      if (payment.method == PaymentMethod.cash ||
          payment.method == PaymentMethod.check ||
          payment.method == PaymentMethod.accountTransfer) {
        if (payment.accountId == null || payment.accountId!.isEmpty) {
          errors.add(
              'Payment ${i + 1} (${payment.method.name}) requires an account selection');
        }
      }

      // Validate payment amount
      if (payment.amount <= 0) {
        errors.add('Payment ${i + 1} amount must be greater than zero');
      }
    }

    return ValidationResult(
        isValid: errors.isEmpty, errors: errors, warnings: warnings);
  }

  /// Enhanced payment account validation with actual account objects
  static ValidationResult _validatePaymentAccountsEnhanced({
    required List<PaymentTransactionModel> paymentTransactions,
    required List<ChartOfAccountsModel> allAccounts,
    required bool strictValidation,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    for (int i = 0; i < paymentTransactions.length; i++) {
      final payment = paymentTransactions[i];

      if (payment.accountId == null || payment.accountId!.isEmpty) continue;

      // Find the actual account
      final account =
          allAccounts.where((a) => a.id == payment.accountId).firstOrNull;

      if (account == null) {
        errors.add('Payment ${i + 1}: Account not found');
        continue;
      }

      // Check if account is active
      if (!account.isActive) {
        errors.add(
            'Payment ${i + 1}: Account "${account.accountName}" is not active');
      }

      // Check if account category is appropriate for payments
      if (account.category != AccountCategory.assets) {
        if (strictValidation) {
          errors.add(
              'Payment ${i + 1}: Account "${account.accountName}" must be an asset account for payments');
        } else {
          warnings.add(
              'Payment ${i + 1}: Account "${account.accountName}" should typically be an asset account for payments');
        }
      }

      // Check account type appropriateness for payment method
      final appropriateTypes =
          _getAppropriateAccountTypesForPayment(payment.method);
      if (!appropriateTypes.contains(account.accountType)) {
        warnings.add(
            'Payment ${i + 1}: Account type "${account.accountType.displayName}" may not be optimal for ${payment.method.name} payments. '
            'Recommended types: ${appropriateTypes.map((t) => t.displayName).join(', ')}');
      }

      // Check account balance for insufficient funds (if applicable)
      if (payment.method == PaymentMethod.cash ||
          payment.method == PaymentMethod.check) {
        if (account.balance < payment.amount) {
          if (strictValidation) {
            errors.add(
                'Payment ${i + 1}: Insufficient balance in account "${account.accountName}" '
                '(Available: ${account.balance.toStringAsFixed(2)}, Required: ${payment.amount.toStringAsFixed(2)})');
          } else {
            warnings.add(
                'Payment ${i + 1}: Low balance in account "${account.accountName}" '
                '(Available: ${account.balance.toStringAsFixed(2)}, Required: ${payment.amount.toStringAsFixed(2)})');
          }
        }
      }
    }

    return ValidationResult(
        isValid: errors.isEmpty, errors: errors, warnings: warnings);
  }

  /// Get appropriate account types for different payment methods
  static List<AccountType> _getAppropriateAccountTypesForPayment(
      PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return [AccountType.cash];
      case PaymentMethod.check:
        return [AccountType.bank, AccountType.cash];
      case PaymentMethod.accountTransfer:
        return [AccountType.bank, AccountType.currentAssets];
      default:
        return [AccountType.cash, AccountType.bank, AccountType.currentAssets];
    }
  }

  /// Validate business rules for voucher accounts
  static ValidationResult _validateBusinessRules({
    required ChartOfAccountsModel? brokerAccount,
    required ChartOfAccountsModel? munshianaAccount,
    required ChartOfAccountsModel? salesTaxAccount,
    required ChartOfAccountsModel? freightTaxAccount,
    required ChartOfAccountsModel? profitAccount,
    required double totalVoucherAmount,
  }) {
    final warnings = <String>[];

    // Check for duplicate account usage
    final usedAccounts = <String, String>{};

    if (brokerAccount != null) usedAccounts[brokerAccount.id] = 'Broker';
    if (munshianaAccount != null) {
      usedAccounts[munshianaAccount.id] = 'Munshiana';
    }
    if (salesTaxAccount != null) usedAccounts[salesTaxAccount.id] = 'Sales Tax';
    if (freightTaxAccount != null) {
      usedAccounts[freightTaxAccount.id] = 'Freight Tax';
    }
    if (profitAccount != null) usedAccounts[profitAccount.id] = 'Profit';

    final duplicateAccounts = <String>[];
    final accountIds = usedAccounts.keys.toList();
    for (int i = 0; i < accountIds.length; i++) {
      for (int j = i + 1; j < accountIds.length; j++) {
        if (accountIds[i] == accountIds[j]) {
          duplicateAccounts.add(
              '${usedAccounts[accountIds[i]]} and ${usedAccounts[accountIds[j]]} use the same account');
        }
      }
    }
    warnings.addAll(duplicateAccounts);

    // Check for large transaction amounts
    if (totalVoucherAmount > 100000) {
      warnings.add(
          'Large voucher amount (${totalVoucherAmount.toStringAsFixed(2)}) - consider additional approval requirements');
    }

    // Check for account balance implications
    if (brokerAccount != null && brokerAccount.balance < 0) {
      warnings.add(
          'Broker account has negative balance - this will increase the negative balance');
    }

    return ValidationResult(isValid: true, errors: [], warnings: warnings);
  }

  /// Validate account hierarchy relationships
  static ValidationResult _validateAccountHierarchy({
    required List<ChartOfAccountsModel> accounts,
    required List<ChartOfAccountsModel> allAccounts,
  }) {
    final warnings = <String>[];

    for (final account in accounts) {
      // Check if account has parent and validate parent relationship
      if (account.parentAccountId != null &&
          account.parentAccountId!.isNotEmpty) {
        final parent = allAccounts
            .where((a) => a.id == account.parentAccountId)
            .firstOrNull;

        if (parent == null) {
          warnings.add(
              'Account "${account.accountName}" references non-existent parent account');
        } else if (parent.category != account.category) {
          warnings.add(
              'Account "${account.accountName}" has different category than parent "${parent.accountName}"');
        }
      }

      // Check for circular references in hierarchy
      if (_hasCircularReference(account, allAccounts)) {
        warnings.add(
            'Account "${account.accountName}" has circular reference in hierarchy');
      }
    }

    return ValidationResult(isValid: true, errors: [], warnings: warnings);
  }

  /// Check for circular references in account hierarchy
  static bool _hasCircularReference(
      ChartOfAccountsModel account, List<ChartOfAccountsModel> allAccounts) {
    final visited = <String>{};
    var currentAccountId = account.parentAccountId;

    while (currentAccountId != null && currentAccountId.isNotEmpty) {
      if (visited.contains(currentAccountId) ||
          currentAccountId == account.id) {
        return true; // Circular reference found
      }

      visited.add(currentAccountId);
      final parent =
          allAccounts.where((a) => a.id == currentAccountId).firstOrNull;
      currentAccountId = parent?.parentAccountId;
    }

    return false;
  }
}

/// Enum for different types of accounts used in vouchers
enum VoucherAccountType {
  brokerFees,
  munshianaFees,
  salesTax,
  freightTax,
  profit,
  payment,
  freightRevenue,
}

/// Validation result class
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasWarnings => warnings.isNotEmpty;
  String get errorsText => errors.join(', ');
  String get warningsText => warnings.join(', ');
}
