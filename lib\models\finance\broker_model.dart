import 'package:cloud_firestore/cloud_firestore.dart';

enum BrokerPaymentMethod { cash, check, accountTransfer }

enum BrokerPaymentStatus { paid, partial, pending }

enum BrokerTransactionType { fee, payment }

class BrokerPaymentModel {
  final String id;
  final String brokerId;
  final String brokerName;
  final double amount;
  final BrokerPaymentMethod method;
  final BrokerPaymentStatus status;
  final DateTime paymentDate;
  final DateTime createdAt;
  final String? notes;

  // Chart of Accounts integration
  final String? accountId; // Chart of Accounts ID
  final String? accountName; // Chart of Accounts display name

  // Check-specific fields
  final String? checkNumber;
  final String? bankName;
  final DateTime? checkIssueDate;
  final DateTime? checkExpiryDate;

  // Reference information
  final String? referenceNumber;
  final String? journalEntryId; // Reference to generated journal entry

  final String uid; // User ID who owns this payment
  final String createdBy; // User who created this payment

  BrokerPaymentModel({
    required this.id,
    required this.brokerId,
    required this.brokerName,
    required this.amount,
    required this.method,
    required this.status,
    required this.paymentDate,
    DateTime? createdAt,
    this.notes,
    this.accountId,
    this.accountName,
    this.checkNumber,
    this.bankName,
    this.checkIssueDate,
    this.checkExpiryDate,
    this.referenceNumber,
    this.journalEntryId,
    required this.uid,
    required this.createdBy,
  }) : createdAt = createdAt ?? DateTime.now();

  factory BrokerPaymentModel.fromJson(Map<String, dynamic> json) {
    return BrokerPaymentModel(
      id: json['id'] ?? '',
      brokerId: json['brokerId'] ?? '',
      brokerName: json['brokerName'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      method: BrokerPaymentMethod.values.firstWhere(
        (e) => e.toString().split('.').last == json['method'],
        orElse: () => BrokerPaymentMethod.cash,
      ),
      status: BrokerPaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => BrokerPaymentStatus.paid,
      ),
      paymentDate: json['paymentDate'] is Timestamp
          ? (json['paymentDate'] as Timestamp).toDate()
          : json['paymentDate'] is String
              ? DateTime.parse(json['paymentDate'])
              : DateTime.now(),
      createdAt: json['createdAt'] is Timestamp
          ? (json['createdAt'] as Timestamp).toDate()
          : json['createdAt'] is String
              ? DateTime.parse(json['createdAt'])
              : DateTime.now(),
      notes: json['notes'],
      accountId: json['accountId'],
      accountName: json['accountName'],
      checkNumber: json['checkNumber'],
      bankName: json['bankName'],
      checkIssueDate: json['checkIssueDate'] is Timestamp
          ? (json['checkIssueDate'] as Timestamp).toDate()
          : json['checkIssueDate'] is String
              ? DateTime.parse(json['checkIssueDate'])
              : null,
      checkExpiryDate: json['checkExpiryDate'] is Timestamp
          ? (json['checkExpiryDate'] as Timestamp).toDate()
          : json['checkExpiryDate'] is String
              ? DateTime.parse(json['checkExpiryDate'])
              : null,
      referenceNumber: json['referenceNumber'],
      journalEntryId: json['journalEntryId'],
      uid: json['uid'] ?? '',
      createdBy: json['createdBy'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'brokerId': brokerId,
      'brokerName': brokerName,
      'amount': amount,
      'method': method.toString().split('.').last,
      'status': status.toString().split('.').last,
      'paymentDate': paymentDate,
      'createdAt': createdAt,
      'notes': notes,
      'accountId': accountId,
      'accountName': accountName,
      'checkNumber': checkNumber,
      'bankName': bankName,
      'checkIssueDate': checkIssueDate,
      'checkExpiryDate': checkExpiryDate,
      'referenceNumber': referenceNumber,
      'journalEntryId': journalEntryId,
      'uid': uid,
      'createdBy': createdBy,
    };
  }

  BrokerPaymentModel copyWith({
    String? id,
    String? brokerId,
    String? brokerName,
    double? amount,
    BrokerPaymentMethod? method,
    BrokerPaymentStatus? status,
    DateTime? paymentDate,
    DateTime? createdAt,
    String? notes,
    String? accountId,
    String? accountName,
    String? checkNumber,
    String? bankName,
    DateTime? checkIssueDate,
    DateTime? checkExpiryDate,
    String? referenceNumber,
    String? journalEntryId,
    String? uid,
    String? createdBy,
  }) {
    return BrokerPaymentModel(
      id: id ?? this.id,
      brokerId: brokerId ?? this.brokerId,
      brokerName: brokerName ?? this.brokerName,
      amount: amount ?? this.amount,
      method: method ?? this.method,
      status: status ?? this.status,
      paymentDate: paymentDate ?? this.paymentDate,
      createdAt: createdAt ?? this.createdAt,
      notes: notes ?? this.notes,
      accountId: accountId ?? this.accountId,
      accountName: accountName ?? this.accountName,
      checkNumber: checkNumber ?? this.checkNumber,
      bankName: bankName ?? this.bankName,
      checkIssueDate: checkIssueDate ?? this.checkIssueDate,
      checkExpiryDate: checkExpiryDate ?? this.checkExpiryDate,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      uid: uid ?? this.uid,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  @override
  String toString() {
    return 'BrokerPaymentModel(id: $id, brokerId: $brokerId, brokerName: $brokerName, amount: $amount, method: $method, status: $status, paymentDate: $paymentDate, uid: $uid)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BrokerPaymentModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class BrokerTransactionModel {
  final String id;
  final String brokerId;
  final String brokerName;
  final BrokerTransactionType type;
  final double amount;
  final DateTime transactionDate;
  final DateTime createdAt;
  final String description;

  // Reference information
  final String? voucherId; // For fee transactions
  final String? voucherNumber; // For fee transactions
  final String? paymentId; // For payment transactions
  final String? journalEntryId; // Reference to journal entry

  final String uid; // User ID who owns this transaction

  BrokerTransactionModel({
    required this.id,
    required this.brokerId,
    required this.brokerName,
    required this.type,
    required this.amount,
    required this.transactionDate,
    DateTime? createdAt,
    required this.description,
    this.voucherId,
    this.voucherNumber,
    this.paymentId,
    this.journalEntryId,
    required this.uid,
  }) : createdAt = createdAt ?? DateTime.now();

  factory BrokerTransactionModel.fromJson(Map<String, dynamic> json) {
    return BrokerTransactionModel(
      id: json['id'] ?? '',
      brokerId: json['brokerId'] ?? '',
      brokerName: json['brokerName'] ?? '',
      type: BrokerTransactionType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => BrokerTransactionType.fee,
      ),
      amount: (json['amount'] ?? 0).toDouble(),
      transactionDate: json['transactionDate'] is Timestamp
          ? (json['transactionDate'] as Timestamp).toDate()
          : json['transactionDate'] is String
              ? DateTime.parse(json['transactionDate'])
              : DateTime.now(),
      createdAt: json['createdAt'] is Timestamp
          ? (json['createdAt'] as Timestamp).toDate()
          : json['createdAt'] is String
              ? DateTime.parse(json['createdAt'])
              : DateTime.now(),
      description: json['description'] ?? '',
      voucherId: json['voucherId'],
      voucherNumber: json['voucherNumber'],
      paymentId: json['paymentId'],
      journalEntryId: json['journalEntryId'],
      uid: json['uid'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'brokerId': brokerId,
      'brokerName': brokerName,
      'type': type.toString().split('.').last,
      'amount': amount,
      'transactionDate': transactionDate,
      'createdAt': createdAt,
      'description': description,
      'voucherId': voucherId,
      'voucherNumber': voucherNumber,
      'paymentId': paymentId,
      'journalEntryId': journalEntryId,
      'uid': uid,
    };
  }

  BrokerTransactionModel copyWith({
    String? id,
    String? brokerId,
    String? brokerName,
    BrokerTransactionType? type,
    double? amount,
    DateTime? transactionDate,
    DateTime? createdAt,
    String? description,
    String? voucherId,
    String? voucherNumber,
    String? paymentId,
    String? journalEntryId,
    String? uid,
  }) {
    return BrokerTransactionModel(
      id: id ?? this.id,
      brokerId: brokerId ?? this.brokerId,
      brokerName: brokerName ?? this.brokerName,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      transactionDate: transactionDate ?? this.transactionDate,
      createdAt: createdAt ?? this.createdAt,
      description: description ?? this.description,
      voucherId: voucherId ?? this.voucherId,
      voucherNumber: voucherNumber ?? this.voucherNumber,
      paymentId: paymentId ?? this.paymentId,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      uid: uid ?? this.uid,
    );
  }

  @override
  String toString() {
    return 'BrokerTransactionModel(id: $id, brokerId: $brokerId, type: $type, amount: $amount, transactionDate: $transactionDate, uid: $uid)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BrokerTransactionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class BrokerModel {
  final String id;
  final String name;
  final String? description;
  final String? phoneNumber;
  final String? email;
  final String? address;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String uid; // User ID who owns this broker

  BrokerModel({
    required this.id,
    required this.name,
    this.description,
    this.phoneNumber,
    this.email,
    this.address,
    required this.createdAt,
    required this.updatedAt,
    this.uid = '', // Default empty string for backward compatibility
  });

  factory BrokerModel.fromJson(Map<String, dynamic> json) {
    return BrokerModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] as String?,
      phoneNumber: json['phoneNumber'] ?? '',
      email: json['email'] as String?,
      address: json['address'] as String?,
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] is DateTime
              ? (json['createdAt'] as DateTime)
              : json['createdAt'] is String
                  ? DateTime.parse(json['createdAt'])
                  : json['createdAt'] is int
                      ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
                      : DateTime.now())
          : DateTime.now(),
      updatedAt: json['updatedAt'] is DateTime
          ? (json['updatedAt'] as DateTime)
          : json['updatedAt'] is String
              ? DateTime.parse(json['updatedAt'])
              : json['updatedAt'] is int
                  ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
                  : DateTime.now(),
      uid: json['uid'] ?? '', // Extract UID from JSON
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'phoneNumber': phoneNumber,
      'email': email,
      'address': address,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'uid': uid, // Include UID in JSON
    };
  }

  BrokerModel copyWith({
    String? id,
    String? name,
    String? description,
    String? phoneNumber,
    String? email,
    String? address,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uid,
  }) {
    return BrokerModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      address: address ?? this.address,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uid: uid ?? this.uid,
    );
  }

  @override
  String toString() {
    return 'BrokerModel(id: $id, name: $name, phoneNumber: $phoneNumber, email: $email, address: $address, createdAt: $createdAt, updatedAt: $updatedAt, uid: $uid)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BrokerModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
