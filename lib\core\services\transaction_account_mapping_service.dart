import 'dart:developer';
import 'package:get_storage/get_storage.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import 'accounting_validation_service.dart';

/// Service for managing configurable account mappings for different transaction types
class TransactionAccountMappingService {
  final ChartOfAccountsFirebaseService _chartOfAccountsService;

  TransactionAccountMappingService(this._chartOfAccountsService);

  /// Get default account mapping for expense transactions
  Future<ExpenseAccountMapping?> getExpenseAccountMapping(String uid) async {
    try {
      // Get expense account (operating expenses)
      final expenseAccount = await _getAccountByType(
        AccountType.operatingExpenses,
        'General Expenses',
      );

      // Get cash account
      final cashAccount = await _getAccountByType(
        AccountType.cash,
        'Cash',
      );

      if (expenseAccount == null || cashAccount == null) return null;

      return ExpenseAccountMapping(
        expenseAccount: expenseAccount,
        cashAccount: cashAccount,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get default account mapping for bill transactions
  Future<BillAccountMapping?> getBillAccountMapping(String uid) async {
    try {
      // Get accounts receivable account
      final accountsReceivableAccount = await _getAccountByType(
        AccountType.accountsReceivable,
        'Accounts Receivable',
      );

      // Get service revenue account
      final serviceRevenueAccount = await _getAccountByType(
        AccountType.serviceRevenue,
        'Service Revenue',
      );

      if (accountsReceivableAccount == null || serviceRevenueAccount == null) {
        return null;
      }

      return BillAccountMapping(
        accountsReceivableAccount: accountsReceivableAccount,
        serviceRevenueAccount: serviceRevenueAccount,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get default account mapping for voucher transactions
  Future<VoucherAccountMapping?> getVoucherAccountMapping(String uid) async {
    try {
      log('🔍 TransactionAccountMappingService: Getting voucher account mapping for uid: $uid');

      // Get broker fees payable account (liability)
      final brokerFeesAccount = await _getAccountByType(
        AccountType.accountsPayable,
        'Broker Fees Payable',
      );
      log('🏦 Broker Fees Account: ${brokerFeesAccount?.accountName ?? 'NOT FOUND'}');

      // Get munshiana service revenue account (equity)
      final munshianaAccount = await _getAccountByType(
        AccountType.equityServiceRevenue,
        'Munshiana Service Revenue',
      );
      log('🏦 Munshiana Account: ${munshianaAccount?.accountName ?? 'NOT FOUND'}');

      // Get freight revenue account
      final freightRevenueAccount = await _getAccountByType(
        AccountType.salesRevenue,
        'Freight Revenue',
      );
      log('🏦 Freight Revenue Account: ${freightRevenueAccount?.accountName ?? 'NOT FOUND'}');

      // Get cash account
      final cashAccount = await _getAccountByType(
        AccountType.cash,
        'Cash',
      );
      log('🏦 Cash Account: ${cashAccount?.accountName ?? 'NOT FOUND'}');

      // Get NLC receivable account (asset)
      final nlcReceivableAccount = await _getAccountByType(
        AccountType.accountsReceivable,
        'NLC Receivable',
      );
      log('🏦 NLC Receivable Account: ${nlcReceivableAccount?.accountName ?? 'NOT FOUND'}');

      // Get sales tax receivable account (asset)
      final salesTaxReceivableAccount = await _getAccountByType(
        AccountType.accountsReceivable,
        '15% Tax Receivable',
      );
      log('🏦 Sales Tax Receivable Account: ${salesTaxReceivableAccount?.accountName ?? 'NOT FOUND'}');

      // Get truck fare payable account (liability)
      final truckFarePayableAccount = await _getAccountByType(
        AccountType.accountsPayable,
        'Truck Fare Payable',
      );
      log('🏦 Truck Fare Payable Account: ${truckFarePayableAccount?.accountName ?? 'NOT FOUND'}');

      // Get freight tax payable account (liability)
      final freightTaxPayableAccount = await _getAccountByType(
        AccountType.currentLiabilities,
        '4.6% Tax Payable',
      );
      log('🏦 Freight Tax Payable Account: ${freightTaxPayableAccount?.accountName ?? 'NOT FOUND'}');

      // Get net profit account (equity)
      final netProfitAccount = await _getAccountByType(
        AccountType.retainedEarnings,
        'Retained Earnings',
      );
      log('🏦 Net Profit Account: ${netProfitAccount?.accountName ?? 'NOT FOUND'}');

      // Detailed validation with specific error messages
      final missingAccounts = <String>[];

      if (brokerFeesAccount == null) {
        missingAccounts.add('Broker Fees (Accounts Payable)');
      }
      if (munshianaAccount == null) {
        missingAccounts.add('Munshiana (Service Revenue)');
      }
      if (freightRevenueAccount == null) {
        missingAccounts.add('Freight Revenue (Sales Revenue)');
      }
      if (cashAccount == null) {
        missingAccounts.add('Cash Account');
      }
      if (nlcReceivableAccount == null) {
        missingAccounts.add('NLC Receivable (Accounts Receivable)');
      }
      if (salesTaxReceivableAccount == null) {
        missingAccounts.add('Sales Tax Receivable (Accounts Receivable)');
      }
      if (truckFarePayableAccount == null) {
        missingAccounts.add('Truck Fare Payable (Accounts Payable)');
      }
      if (freightTaxPayableAccount == null) {
        missingAccounts.add('Freight Tax Payable (Current Liabilities)');
      }
      if (netProfitAccount == null) {
        missingAccounts.add('Net Profit (Retained Earnings)');
      }

      if (missingAccounts.isNotEmpty) {
        log('❌ TransactionAccountMappingService: Missing required accounts for voucher mapping:');
        for (final missing in missingAccounts) {
          log('   - $missing');
        }
        log('❌ Total missing accounts: ${missingAccounts.length}');
        log('❌ Please create these accounts in Chart of Accounts or run the debug tool to auto-create them');
        return null;
      }

      log('✅ TransactionAccountMappingService: Successfully created voucher account mapping');
      return VoucherAccountMapping(
        brokerFeesAccount: brokerFeesAccount!,
        munshianaAccount: munshianaAccount!,
        freightRevenueAccount: freightRevenueAccount!,
        cashAccount: cashAccount!,
        // Use specific accounts for proper account type behavior
        nlcFareReceivableAccount: nlcReceivableAccount!,
        salesTaxReceivableAccount: salesTaxReceivableAccount!,
        truckFarePayableAccount: truckFarePayableAccount!,
        brokerFeePayableAccount:
            brokerFeesAccount, // Same as broker fees account
        freightTaxPayableAccount: freightTaxPayableAccount!,
        salesTaxPayableAccount:
            freightTaxPayableAccount, // Use same as freight tax for simplicity
        netProfitAccount: netProfitAccount!,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get configured account mapping for loan transactions
  Future<LoanAccountMapping?> getLoanAccountMapping(String uid) async {
    try {
      log('🔍 TransactionAccountMappingService: Getting loan account mapping for uid: $uid');

      final storage = GetStorage();
      ChartOfAccountsModel? loansReceivableAccount;
      ChartOfAccountsModel? loansPayableAccount;
      ChartOfAccountsModel? cashAccount;

      // Get configured loan receivable account from GetStorage
      try {
        final receivableData = storage.read('loan_receivable_account');
        if (receivableData != null) {
          loansReceivableAccount = ChartOfAccountsModel.fromJson(
              Map<String, dynamic>.from(receivableData));
          log('✅ Found configured loan receivable account: ${loansReceivableAccount.displayName}');
        } else {
          log('⚠️ No loan receivable account configured in GetStorage');
        }
      } catch (e) {
        log('❌ Error loading loan receivable account from GetStorage: $e');
      }

      // Get configured loan payable account from GetStorage
      try {
        final payableData = storage.read('loan_payable_account');
        if (payableData != null) {
          loansPayableAccount = ChartOfAccountsModel.fromJson(
              Map<String, dynamic>.from(payableData));
          log('✅ Found configured loan payable account: ${loansPayableAccount.displayName}');
        } else {
          log('⚠️ No loan payable account configured in GetStorage');
        }
      } catch (e) {
        log('❌ Error loading loan payable account from GetStorage: $e');
      }

      // If accounts are not configured, try to find them by type as fallback
      if (loansReceivableAccount == null) {
        log('🔍 Fallback: Searching for loan receivable account by type');
        loansReceivableAccount = await _getAccountByType(
          AccountType.currentAssets,
          'Loans Receivable',
        );
        log('🏦 Fallback Loan Receivable Account: ${loansReceivableAccount?.accountName ?? 'NOT FOUND'}');
      }

      if (loansPayableAccount == null) {
        log('🔍 Fallback: Searching for loan payable account by type');
        loansPayableAccount = await _getAccountByType(
          AccountType.longTermLiabilities,
          'Loans Payable',
        );
        log('🏦 Fallback Loan Payable Account: ${loansPayableAccount?.accountName ?? 'NOT FOUND'}');
      }

      // Get cash account (fallback to any asset account)
      cashAccount = await _getAccountByType(
        AccountType.cash,
        'Cash',
      );

      // If no cash account found, try any asset account
      if (cashAccount == null) {
        log('🔍 No cash account found, trying any asset account');
        cashAccount = await _getAccountByType(
          AccountType.currentAssets,
          'Asset',
        );
      }

      log('🏦 Cash Account: ${cashAccount?.accountName ?? 'NOT FOUND'}');

      // Check if we have all required accounts
      if (loansReceivableAccount == null ||
          loansPayableAccount == null ||
          cashAccount == null) {
        log('❌ Missing required loan accounts:');
        log('   - Loan Receivable: ${loansReceivableAccount?.accountName ?? 'MISSING'}');
        log('   - Loan Payable: ${loansPayableAccount?.accountName ?? 'MISSING'}');
        log('   - Cash/Asset: ${cashAccount?.accountName ?? 'MISSING'}');
        return null;
      }

      log('✅ Successfully found all required loan accounts');
      return LoanAccountMapping(
        loansReceivableAccount: loansReceivableAccount,
        loansPayableAccount: loansPayableAccount,
        cashAccount: cashAccount,
      );
    } catch (e) {
      log('❌ Error getting loan account mapping: $e');
      return null;
    }
  }

  /// Get default account mapping for general account transactions
  Future<AccountTransactionMapping?> getAccountTransactionMapping(
      String uid) async {
    try {
      // Get accounts receivable account
      final accountsReceivableAccount = await _getAccountByType(
        AccountType.accountsReceivable,
        'Accounts Receivable',
      );

      // Get general expenses account
      final generalExpensesAccount = await _getAccountByType(
        AccountType.operatingExpenses,
        'General Expenses',
      );

      // Get miscellaneous expenses account
      final miscExpensesAccount = await _getAccountByType(
        AccountType.operatingExpenses,
        'Miscellaneous Expenses',
      );

      if (accountsReceivableAccount == null ||
          generalExpensesAccount == null ||
          miscExpensesAccount == null) {
        return null;
      }

      return AccountTransactionMapping(
        accountsReceivableAccount: accountsReceivableAccount,
        generalExpensesAccount: generalExpensesAccount,
        miscellaneousExpensesAccount: miscExpensesAccount,
      );
    } catch (e) {
      return null;
    }
  }

  // Helper method
  Future<ChartOfAccountsModel?> _getAccountByType(
    AccountType accountType,
    String preferredName,
  ) async {
    try {
      final accounts =
          await _chartOfAccountsService.getAccountsByType(accountType);
      // First try to find by preferred name and type
      try {
        return accounts.firstWhere(
          (account) => account.accountName
              .toLowerCase()
              .contains(preferredName.toLowerCase()),
        );
      } catch (e) {
        // If not found, get any account of the specified type
        try {
          return accounts.first;
        } catch (e) {
          return null;
        }
      }
    } catch (e) {
      return null;
    }
  }
}

/// Account mapping classes for different transaction types
class ExpenseAccountMapping {
  final ChartOfAccountsModel expenseAccount;
  final ChartOfAccountsModel cashAccount;

  ExpenseAccountMapping({
    required this.expenseAccount,
    required this.cashAccount,
  });
}

class BillAccountMapping {
  final ChartOfAccountsModel accountsReceivableAccount;
  final ChartOfAccountsModel serviceRevenueAccount;

  BillAccountMapping({
    required this.accountsReceivableAccount,
    required this.serviceRevenueAccount,
  });
}

class VoucherAccountMapping {
  // Liability Accounts (Credit increases balance)
  final ChartOfAccountsModel brokerFeesAccount;
  final ChartOfAccountsModel truckFarePayableAccount;
  final ChartOfAccountsModel brokerFeePayableAccount;
  final ChartOfAccountsModel freightTaxPayableAccount;
  final ChartOfAccountsModel salesTaxPayableAccount;

  // Equity Accounts (Credit increases balance)
  final ChartOfAccountsModel munshianaAccount;
  final ChartOfAccountsModel netProfitAccount;

  // Revenue Accounts (Credit increases balance)
  final ChartOfAccountsModel freightRevenueAccount;

  // Asset Accounts (Debit increases balance)
  final ChartOfAccountsModel cashAccount;
  final ChartOfAccountsModel nlcFareReceivableAccount;
  final ChartOfAccountsModel salesTaxReceivableAccount;

  VoucherAccountMapping({
    // Required liability accounts (Credit increases balance)
    required this.brokerFeesAccount,
    required this.truckFarePayableAccount,
    required this.brokerFeePayableAccount,
    required this.freightTaxPayableAccount,
    required this.salesTaxPayableAccount,

    // Required equity accounts (Credit increases balance)
    required this.munshianaAccount,
    required this.netProfitAccount,

    // Required revenue accounts (Credit increases balance)
    required this.freightRevenueAccount,

    // Required asset accounts (Debit increases balance)
    required this.cashAccount,
    required this.nlcFareReceivableAccount,
    required this.salesTaxReceivableAccount,
  });

  /// Validate that all accounts have correct account types for voucher transactions
  ValidationResult validateAccountTypes() {
    final errors = <String>[];
    final warnings = <String>[];

    // Validate liability accounts (should be liability type)
    if (brokerFeesAccount.category != AccountCategory.liabilities) {
      errors.add('Broker Fees account must be a Liability account');
    }
    if (truckFarePayableAccount.category != AccountCategory.liabilities) {
      errors.add('Truck Fare Payable account must be a Liability account');
    }
    if (brokerFeePayableAccount.category != AccountCategory.liabilities) {
      errors.add('Broker Fee Payable account must be a Liability account');
    }
    if (freightTaxPayableAccount.category != AccountCategory.liabilities) {
      errors.add('Freight Tax Payable account must be a Liability account');
    }
    if (salesTaxPayableAccount.category != AccountCategory.liabilities) {
      errors.add('Sales Tax Payable account must be a Liability account');
    }

    // Validate equity accounts (should be equity type)
    if (munshianaAccount.category != AccountCategory.equity) {
      errors.add('Munshiana account must be an Equity account');
    }
    if (netProfitAccount.category != AccountCategory.equity) {
      errors.add('Net Profit account must be an Equity account');
    }

    // Validate revenue accounts (should be revenue type)
    if (freightRevenueAccount.category != AccountCategory.revenue) {
      errors.add('Freight Revenue account must be a Revenue account');
    }

    // Validate asset accounts (should be asset type)
    if (cashAccount.category != AccountCategory.assets) {
      errors.add('Cash account must be an Asset account');
    }
    if (nlcFareReceivableAccount.category != AccountCategory.assets) {
      errors.add('NLC Fare Receivable account must be an Asset account');
    }
    if (salesTaxReceivableAccount.category != AccountCategory.assets) {
      errors.add('Sales Tax Receivable account must be an Asset account');
    }
    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }
}

class LoanAccountMapping {
  final ChartOfAccountsModel loansReceivableAccount;
  final ChartOfAccountsModel loansPayableAccount;
  final ChartOfAccountsModel cashAccount;

  LoanAccountMapping({
    required this.loansReceivableAccount,
    required this.loansPayableAccount,
    required this.cashAccount,
  });
}

class AccountTransactionMapping {
  final ChartOfAccountsModel accountsReceivableAccount;
  final ChartOfAccountsModel generalExpensesAccount;
  final ChartOfAccountsModel miscellaneousExpensesAccount;

  AccountTransactionMapping({
    required this.accountsReceivableAccount,
    required this.generalExpensesAccount,
    required this.miscellaneousExpensesAccount,
  });
}
