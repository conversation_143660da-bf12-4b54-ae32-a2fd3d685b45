import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../../models/finance/fiscal_period_model.dart';

/// Widget displaying list of fiscal periods with management actions
class FiscalPeriodListWidget extends StatelessWidget {
  final RxList<FiscalPeriodModel> fiscalPeriods;
  final Function(FiscalPeriodModel) onClosePeriod;
  final Function(FiscalPeriodModel) onReopenPeriod;
  final RxBool isLoading;

  const FiscalPeriodListWidget({
    super.key,
    required this.fiscalPeriods,
    required this.onClosePeriod,
    required this.onReopenPeriod,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final periods = fiscalPeriods;

      if (periods.isEmpty) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              children: [
                Icon(Icons.schedule, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No fiscal periods found',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Generate periods for this fiscal year to get started',
                  style: TextStyle(
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        );
      }

      return Card(
        elevation: 2,
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  const Expanded(
                    flex: 3,
                    child: Text(
                      'Period Name',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  const Expanded(
                    flex: 2,
                    child: Text(
                      'Type',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  const Expanded(
                    flex: 3,
                    child: Text(
                      'Date Range',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  const Expanded(
                    flex: 2,
                    child: Text(
                      'Status',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  const Expanded(
                    flex: 2,
                    child: Text(
                      'Actions',
                      style: TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),

            // Period rows
            ...periods.asMap().entries.map((entry) {
              final period = entry.value;

              return Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.grey[200]!,
                      width: 1,
                    ),
                  ),
                  color: period.isCurrent ? Colors.blue[50] : null,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Period Name
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              period.periodName,
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color:
                                    period.isCurrent ? Colors.blue[700] : null,
                              ),
                            ),
                            if (period.isCurrent)
                              Text(
                                'CURRENT PERIOD',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.blue[600],
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Type
                      Expanded(
                        flex: 2,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getPeriodTypeColor(period.periodType),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            period.periodType.displayName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),

                      // Date Range
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              DateFormat('MMM dd, yyyy')
                                  .format(period.startDate),
                              style: const TextStyle(fontSize: 13),
                            ),
                            Text(
                              'to ${DateFormat('MMM dd, yyyy').format(period.endDate)}',
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              '(${period.durationInDays} days)',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Status
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: _getStatusColor(period.status),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                period.status.displayName.toUpperCase(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            if (period.closedDate != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  'Closed: ${DateFormat('MMM dd').format(period.closedDate!)}',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Actions
                      Expanded(
                        flex: 2,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (period.canBeClosed)
                              Obx(() => IconButton(
                                    onPressed: isLoading.value
                                        ? null
                                        : () => _showClosePeriodDialog(
                                            context, period),
                                    icon: isLoading.value
                                        ? const SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(
                                                strokeWidth: 2),
                                          )
                                        : const Icon(Icons.lock, size: 20),
                                    tooltip: 'Close Period',
                                    color: Colors.red[600],
                                  )),
                            if (period.canBeReopened)
                              Obx(() => IconButton(
                                    onPressed: isLoading.value
                                        ? null
                                        : () => _showReopenPeriodDialog(
                                            context, period),
                                    icon: isLoading.value
                                        ? const SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(
                                                strokeWidth: 2),
                                          )
                                        : const Icon(Icons.lock_open, size: 20),
                                    tooltip: 'Reopen Period',
                                    color: Colors.green[600],
                                  )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
          ],
        ),
      );
    });
  }

  /// Get color for period type
  Color _getPeriodTypeColor(PeriodType type) {
    switch (type) {
      case PeriodType.monthly:
        return Colors.blue;
      case PeriodType.quarterly:
        return Colors.orange;
      case PeriodType.yearly:
        return Colors.purple;
    }
  }

  /// Get color for fiscal period status
  Color _getStatusColor(FiscalPeriodStatus status) {
    switch (status) {
      case FiscalPeriodStatus.open:
        return Colors.green;
      case FiscalPeriodStatus.closed:
        return Colors.red;
      case FiscalPeriodStatus.locked:
        return Colors.orange;
    }
  }

  /// Show close period confirmation dialog
  void _showClosePeriodDialog(BuildContext context, FiscalPeriodModel period) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Close Fiscal Period'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                'Are you sure you want to close the period "${period.periodName}"?'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Warning:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 4),
                  Text('• No new transactions can be posted to this period'),
                  Text('• Existing transactions cannot be modified'),
                  Text('• This action can be reversed by reopening the period'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onClosePeriod(period);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Close Period'),
          ),
        ],
      ),
    );
  }

  /// Show reopen period confirmation dialog
  void _showReopenPeriodDialog(BuildContext context, FiscalPeriodModel period) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reopen Fiscal Period'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                'Are you sure you want to reopen the period "${period.periodName}"?'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Note:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 4),
                  Text('• Transactions can be posted to this period again'),
                  Text('• Existing transactions can be modified'),
                  Text('• Financial reports may change'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onReopenPeriod(period);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reopen Period'),
          ),
        ],
      ),
    );
  }
}
