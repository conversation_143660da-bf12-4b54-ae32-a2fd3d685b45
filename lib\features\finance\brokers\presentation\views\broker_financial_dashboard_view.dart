import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/features/finance/brokers/presentation/controllers/broker_financial_dashboard_controller.dart';
import 'package:logestics/features/finance/brokers/presentation/views/broker_detail_view.dart';
import 'package:logestics/features/finance/brokers/presentation/widgets/broker_financial_summary_widget.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';

class BrokerFinancialDashboardView extends StatelessWidget {
  const BrokerFinancialDashboardView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<BrokerFinancialDashboardController>();
    notifier = Provider.of(context, listen: true);

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      body: Column(
        children: [
          _buildHeader(controller),
          _buildSummary<PERSON><PERSON>(controller),
          _buildFiltersAndSearch(controller),
          Expanded(
            child: _buildBrokers<PERSON>ist(controller),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BrokerFinancialDashboardController controller) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: notifier.getcardColor,
        border: Border(
          bottom: BorderSide(color: notifier.getfillborder),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Broker Financial Dashboard',
                  style: AppTextStyles.titleStyle.copyWith(
                    color: notifier.text,
                    fontSize: 24,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Track broker fees, payments, and outstanding balances',
                  style: AppTextStyles.subtitleStyle.copyWith(
                    color: notifier.text.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => controller.refreshData(),
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(BrokerFinancialDashboardController controller) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Obx(() {
        if (controller.isLoadingSummaries.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return Row(
          children: [
            Expanded(
              child: _buildOverviewCard(
                'Total Outstanding',
                controller.formatCurrency(controller.totalOutstandingBalance),
                Icons.account_balance_wallet,
                Colors.red,
                '${controller.brokersWithBalanceCount} brokers',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildOverviewCard(
                'Total Fees',
                controller.formatCurrency(controller.totalFeesAllBrokers),
                Icons.add_circle_outline,
                Colors.orange,
                'All time',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildOverviewCard(
                'Total Payments',
                controller.formatCurrency(controller.totalPaymentsAllBrokers),
                Icons.remove_circle_outline,
                Colors.green,
                'All time',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildOverviewCard(
                'Active Brokers',
                controller.brokers.length.toString(),
                Icons.people,
                Colors.blue,
                'Total registered',
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildOverviewCard(String title, String value, IconData icon, Color color, String subtitle) {
    return Card(
      color: notifier.getcardColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: AppTextStyles.subtitleStyle.copyWith(
                      color: notifier.text.withOpacity(0.7),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: AppTextStyles.titleStyle.copyWith(
                color: color,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              subtitle,
              style: AppTextStyles.subtitleStyle.copyWith(
                color: notifier.text.withOpacity(0.5),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersAndSearch(BrokerFinancialDashboardController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: notifier.getcardColor,
        border: Border(
          bottom: BorderSide(color: notifier.getfillborder),
        ),
      ),
      child: Row(
        children: [
          // Search field
          Expanded(
            flex: 2,
            child: TextField(
              controller: controller.searchController,
              decoration: InputDecoration(
                hintText: 'Search brokers...',
                hintStyle: TextStyle(color: notifier.text.withOpacity(0.5)),
                prefixIcon: Icon(Icons.search, color: notifier.text.withOpacity(0.5)),
                suffixIcon: Obx(() => controller.searchQuery.value.isNotEmpty
                    ? IconButton(
                        onPressed: () => controller.clearSearch(),
                        icon: Icon(Icons.clear, color: notifier.text.withOpacity(0.5)),
                      )
                    : const SizedBox.shrink()),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: notifier.getfillborder),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: notifier.getfillborder),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.blue),
                ),
                filled: true,
                fillColor: notifier.getHoverColor,
              ),
              style: TextStyle(color: notifier.text),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Filter dropdown
          Obx(() => DropdownButton<String>(
            value: controller.filterBy.value,
            dropdownColor: notifier.getcardColor,
            style: TextStyle(color: notifier.text),
            items: [
              DropdownMenuItem(value: 'all', child: Text('All Brokers')),
              DropdownMenuItem(value: 'withBalance', child: Text('With Balance')),
              DropdownMenuItem(value: 'withoutBalance', child: Text('Balanced')),
            ],
            onChanged: (value) {
              if (value != null) controller.setFilterBy(value);
            },
          )),
          
          const SizedBox(width: 16),
          
          // Sort dropdown
          Obx(() => DropdownButton<String>(
            value: controller.sortBy.value,
            dropdownColor: notifier.getcardColor,
            style: TextStyle(color: notifier.text),
            items: [
              DropdownMenuItem(value: 'name', child: Text('Name')),
              DropdownMenuItem(value: 'balance', child: Text('Balance')),
              DropdownMenuItem(value: 'totalFees', child: Text('Total Fees')),
              DropdownMenuItem(value: 'lastTransaction', child: Text('Last Transaction')),
            ],
            onChanged: (value) {
              if (value != null) controller.setSortBy(value);
            },
          )),
          
          // Sort direction
          Obx(() => IconButton(
            onPressed: () => controller.setSortBy(controller.sortBy.value),
            icon: Icon(
              controller.sortAscending.value ? Icons.arrow_upward : Icons.arrow_downward,
              color: notifier.text,
            ),
            tooltip: controller.sortAscending.value ? 'Ascending' : 'Descending',
          )),
        ],
      ),
    );
  }

  Widget _buildBrokersList(BrokerFinancialDashboardController controller) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      final filteredBrokers = controller.filteredBrokers;

      if (filteredBrokers.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.people_outline,
                size: 64,
                color: notifier.text.withOpacity(0.3),
              ),
              const SizedBox(height: 16),
              Text(
                controller.searchQuery.value.isNotEmpty
                    ? 'No brokers found matching your search'
                    : 'No brokers found',
                style: AppTextStyles.titleStyle.copyWith(
                  color: notifier.text.withOpacity(0.5),
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(20),
        itemCount: filteredBrokers.length,
        itemBuilder: (context, index) {
          final broker = filteredBrokers[index];
          final financialSummary = controller.getBrokerFinancialSummary(broker.id);

          return BrokerFinancialSummaryWidget(
            broker: broker,
            financialSummary: financialSummary,
            onViewDetails: () => _showBrokerDetails(broker),
            onRecordPayment: () => _showBrokerDetails(broker), // Will open payment tab
          );
        },
      );
    });
  }

  void _showBrokerDetails(broker) {
    showDialog(
      context: Get.context!,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          width: Get.width * 0.9,
          height: Get.height * 0.9,
          decoration: BoxDecoration(
            color: notifier.getBgColor,
            borderRadius: BorderRadius.circular(16),
          ),
          child: BrokerDetailView(broker: broker),
        ),
      ),
    );
  }
}
