import 'dart:developer' as dev;
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/finance/expense_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import 'automatic_journal_entry_service.dart';

/// Service for integrating expense transactions with automatic journal entry generation
/// Updated to support Chart of Accounts integration
class ExpenseJournalIntegrationService {
  final AutomaticJournalEntryService _automaticJournalService;
  final GeneralLedgerFirebaseService _generalLedgerService;
  final JournalEntryFirebaseService _journalEntryService;
  final ChartOfAccountsFirebaseService _chartOfAccountsService;

  ExpenseJournalIntegrationService(
    this._automaticJournalService,
    this._generalLedgerService,
    this._journalEntryService,
    this._chartOfAccountsService,
  );

  /// Get current user's display name for audit trail
  String get _currentUserName =>
      FirebaseAuth.instance.currentUser?.displayName ??
      FirebaseAuth.instance.currentUser?.email ??
      'System User';

  /// Generate and create journal entry for an expense transaction
  Future<bool> processExpenseTransaction(
      ExpenseModel expense, String uid) async {
    try {
      dev.log(
          'Processing expense transaction for journal entry: ${expense.title}');

      // Validate expense for journal entry generation
      final validation = await validateExpenseForJournalEntry(expense);
      if (!validation.isValid) {
        dev.log('Expense validation failed: ${validation.issuesText}');
        throw Exception('Expense validation failed: ${validation.issuesText}');
      }

      // Generate journal entry for the expense
      final journalEntry =
          await _automaticJournalService.generateExpenseJournalEntry(
        expense: expense,
        uid: uid,
        createdBy: _currentUserName, // Use actual authenticated user
      );

      if (journalEntry == null) {
        final errorMsg =
            'No journal entry generated for expense: ${expense.title}. This indicates a configuration issue with Chart of Accounts.';
        dev.log(errorMsg);
        throw Exception(errorMsg);
      }

      // Create the journal entry using the proper service
      // Note: JournalEntryFirebaseService automatically handles account balance updates
      // and ledger entries when creating journal entries, so we don't need to do it manually
      await _journalEntryService.createJournalEntry(journalEntry);
      dev.log(
          'Successfully created journal entry for expense: ${expense.title}');

      dev.log('Successfully processed expense transaction: ${expense.title}');
      return true;
    } catch (e) {
      dev.log('Error processing expense transaction: $e');
      return false;
    }
  }

  /// Batch process multiple expense transactions
  Future<BatchProcessResult> batchProcessExpenseTransactions(
    List<ExpenseModel> expenses,
    String uid,
  ) async {
    int successCount = 0;
    int failureCount = 0;
    final List<String> failedExpenseIds = [];

    dev.log('Batch processing ${expenses.length} expense transactions');

    for (final expense in expenses) {
      final success = await processExpenseTransaction(expense, uid);
      if (success) {
        successCount++;
      } else {
        failureCount++;
        failedExpenseIds.add(expense.id);
      }
    }

    dev.log(
        'Batch processing completed: $successCount successful, $failureCount failed');

    return BatchProcessResult(
      totalProcessed: expenses.length,
      successCount: successCount,
      failureCount: failureCount,
      failedTransactionIds: failedExpenseIds,
    );
  }

  /// Validate expense transaction for journal entry generation
  Future<ExpenseValidationResult> validateExpenseForJournalEntry(
    ExpenseModel expense,
  ) async {
    try {
      final errors = <String>[];
      final warnings = <String>[];

      // Check if expense uses Chart of Accounts
      if (!expense.usesChartOfAccounts) {
        errors.add(
            'Expense must use Chart of Accounts (source and destination accounts required)');
        return ExpenseValidationResult(
          isValid: false,
          issues: errors,
        );
      }

      // Validate source account exists and is accessible
      final sourceAccount = await _chartOfAccountsService
          .getAccountById(expense.sourceAccountId!);
      if (sourceAccount == null) {
        errors.add('Source account not found: ${expense.sourceAccountId}');
      } else if (!sourceAccount.isActive) {
        errors.add('Source account is inactive: ${sourceAccount.accountName}');
      }

      // Validate destination account exists and is accessible
      final destinationAccount = await _chartOfAccountsService
          .getAccountById(expense.destinationAccountId!);
      if (destinationAccount == null) {
        errors.add(
            'Destination account not found: ${expense.destinationAccountId}');
      } else if (!destinationAccount.isActive) {
        errors.add(
            'Destination account is inactive: ${destinationAccount.accountName}');
      }

      // Validate amount
      if (expense.amount <= 0) {
        errors.add('Expense amount must be greater than zero');
      }

      // Add warnings for account type recommendations
      if (sourceAccount != null && destinationAccount != null) {
        _addAccountTypeWarnings(sourceAccount, destinationAccount, warnings);
      }

      return ExpenseValidationResult(
        isValid: errors.isEmpty,
        issues: errors,
      );
    } catch (e) {
      dev.log('Error validating expense for journal entry: $e');
      return ExpenseValidationResult(
        isValid: false,
        issues: ['Validation error: $e'],
      );
    }
  }

  /// Add account type warnings for expense transactions
  void _addAccountTypeWarnings(
    ChartOfAccountsModel sourceAccount,
    ChartOfAccountsModel destinationAccount,
    List<String> warnings,
  ) {
    // Typical expense flow: Asset account (cash/bank) decreases, Expense account increases
    if (sourceAccount.category != AccountCategory.assets) {
      warnings.add(
          'Source account "${sourceAccount.accountName}" is not an Asset account. '
          'Typical expense transactions decrease Asset accounts (Cash, Bank, etc.)');
    }

    if (destinationAccount.category != AccountCategory.expenses) {
      warnings.add(
          'Destination account "${destinationAccount.accountName}" is not an Expense account. '
          'Typical expense transactions increase Expense accounts (Fuel, Office Supplies, etc.)');
    }
  }

  /// Get journal entries associated with an expense
  Future<List<JournalEntryModel>> getJournalEntriesForExpense(
    String expenseId,
    String uid,
  ) async {
    try {
      final allEntries =
          await _generalLedgerService.getJournalEntries(uid, null, null);

      return allEntries.fold(
        (failure) {
          dev.log('Failed to fetch journal entries: ${failure.message}');
          return <JournalEntryModel>[];
        },
        (entries) {
          return entries
              .where((entry) =>
                  entry.sourceTransactionId == expenseId &&
                  entry.sourceTransactionType == 'expense' &&
                  entry.uid == uid)
              .toList();
        },
      );
    } catch (e) {
      dev.log('Error fetching journal entries for expense: $e');
      return <JournalEntryModel>[];
    }
  }

  /// Check if an expense already has associated journal entries
  Future<bool> hasExistingJournalEntries(String expenseId, String uid) async {
    final entries = await getJournalEntriesForExpense(expenseId, uid);
    return entries.isNotEmpty;
  }

  /// Reverse journal entries for an expense (when expense is deleted/modified)
  Future<bool> reverseExpenseJournalEntries(
      String expenseId, String uid) async {
    try {
      final entries = await getJournalEntriesForExpense(expenseId, uid);

      if (entries.isEmpty) {
        dev.log('No journal entries found for expense: $expenseId');
        return true; // Nothing to reverse
      }

      bool allReversed = true;
      for (final entry in entries) {
        final result = await _generalLedgerService.reverseJournalEntry(
          entry.id,
          'Expense transaction reversed',
          _currentUserName, // Use actual authenticated user
        );

        result.fold(
          (failure) {
            dev.log(
                'Failed to reverse journal entry ${entry.id}: ${failure.message}');
            allReversed = false;
          },
          (success) {
            dev.log('Successfully reversed journal entry: ${entry.id}');
          },
        );
      }

      return allReversed;
    } catch (e) {
      dev.log('Error reversing expense journal entries: $e');
      return false;
    }
  }
}

/// Result class for batch processing operations
class BatchProcessResult {
  final int totalProcessed;
  final int successCount;
  final int failureCount;
  final List<String> failedTransactionIds;

  BatchProcessResult({
    required this.totalProcessed,
    required this.successCount,
    required this.failureCount,
    required this.failedTransactionIds,
  });

  double get successRate =>
      totalProcessed > 0 ? successCount / totalProcessed : 0.0;
  bool get hasFailures => failureCount > 0;
  bool get allSuccessful => failureCount == 0;
}

/// Result class for expense validation
class ExpenseValidationResult {
  final bool isValid;
  final List<String> issues;

  ExpenseValidationResult({
    required this.isValid,
    required this.issues,
  });

  String get issuesText => issues.join(', ');
}
