import 'dart:async';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:dartz/dartz.dart';
import '../../models/voucher_model.dart';
import '../../models/offline/offline_voucher_model.dart';
import '../../models/offline/sync_queue_entry.dart';
import '../../models/offline/offline_transaction_state.dart';
import '../../core/shared_services/failure_obj.dart';
import '../../core/shared_services/success_obj.dart';
import 'connectivity_service.dart';
import 'offline_state_service.dart';

/// Service for managing offline voucher operations
class OfflineVoucherService extends GetxController {
  static OfflineVoucherService get instance =>
      Get.find<OfflineVoucherService>();

  // Hive boxes for local storage
  Box<Map>? _offlineVouchersBox;
  Box<Map>? _localJournalEntriesBox;
  Box<Map>? _localBalanceChangesBox;

  // Services
  late final ConnectivityService _connectivityService;
  late final OfflineStateService _offlineStateService;

  // Reactive state
  final RxList<OfflineVoucherModel> _offlineVouchers =
      <OfflineVoucherModel>[].obs;
  final RxBool _isInitialized = false.obs;

  // Public getters
  List<OfflineVoucherModel> get offlineVouchers => _offlineVouchers;
  bool get isInitialized => _isInitialized.value;
  Stream<List<OfflineVoucherModel>> get offlineVouchersStream =>
      _offlineVouchers.stream;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    _initializeOfflineStorage();
  }

  @override
  void onClose() {
    _offlineVouchersBox?.close();
    _localJournalEntriesBox?.close();
    _localBalanceChangesBox?.close();
    super.onClose();
  }

  /// Initialize required services
  void _initializeServices() {
    _connectivityService = Get.find<ConnectivityService>();
    _offlineStateService = Get.find<OfflineStateService>();
  }

  /// Initialize offline storage
  Future<void> _initializeOfflineStorage() async {
    try {
      _offlineVouchersBox = await Hive.openBox<Map>('offline_vouchers');
      _localJournalEntriesBox =
          await Hive.openBox<Map>('local_journal_entries');
      _localBalanceChangesBox =
          await Hive.openBox<Map>('local_balance_changes');

      await _loadOfflineVouchers();
      _isInitialized.value = true;

      log('OfflineVoucherService: Initialized successfully');
    } catch (e) {
      log('OfflineVoucherService: Failed to initialize: $e');
    }
  }

  /// Load offline vouchers from local storage
  Future<void> _loadOfflineVouchers() async {
    try {
      final vouchers = <OfflineVoucherModel>[];

      final values = _offlineVouchersBox?.values ?? <Map>[];
      for (final entry in values) {
        try {
          final voucher =
              OfflineVoucherModel.fromJson(Map<String, dynamic>.from(entry));
          vouchers.add(voucher);
        } catch (e) {
          log('OfflineVoucherService: Failed to load voucher from storage: $e');
        }
      }

      _offlineVouchers.value = vouchers;
      log('OfflineVoucherService: Loaded ${vouchers.length} offline vouchers');
    } catch (e) {
      log('OfflineVoucherService: Failed to load offline vouchers: $e');
    }
  }

  /// Create voucher offline with atomic transaction recovery
  Future<Either<FailureObj, SuccessObj>> createVoucherOffline({
    required String uid,
    required Map<String, dynamic> voucherData,
  }) async {
    try {
      log('OfflineVoucherService: Starting offline voucher creation for ${voucherData['voucherNumber']}');

      // Create offline voucher model
      final voucherModel = VoucherModel.fromJson(voucherData);
      final offlineVoucher = OfflineVoucherModel.fromVoucherModel(voucherModel);

      // Start atomic transaction
      final transactionId = DateTime.now().millisecondsSinceEpoch.toString();
      await _startAtomicTransaction(transactionId, offlineVoucher, uid);

      try {
        // Step 1: Save voucher locally
        await _saveVoucherLocally(offlineVoucher);

        // Step 2: Generate local journal entries
        final journalEntries =
            await _generateLocalJournalEntries(offlineVoucher, uid);

        // Step 3: Calculate local balance changes
        final balanceChanges =
            await _calculateLocalBalanceChanges(journalEntries);

        // Step 4: Update offline voucher with generated data
        final updatedVoucher = offlineVoucher.copyWithOffline(
          localJournalEntryIds:
              journalEntries.map((e) => e['id'] as String).toList(),
          localBalanceChanges: balanceChanges,
        );

        // Step 5: Save updated voucher and complete transaction
        await _saveVoucherLocally(updatedVoucher);
        await _completeAtomicTransaction(transactionId);

        // Step 6: Add to sync queue
        await _addToSyncQueue(updatedVoucher, uid);

        // Update reactive list
        _offlineVouchers.add(updatedVoucher);

        log('OfflineVoucherService: Offline voucher creation completed successfully');
        return Right(SuccessObj(
            message:
                'Voucher created offline successfully. Will sync when online.'));
      } catch (e) {
        // Rollback atomic transaction
        await _rollbackAtomicTransaction(transactionId);
        rethrow;
      }
    } catch (e) {
      log('OfflineVoucherService: Failed to create voucher offline: $e');
      return Left(FailureObj(
        code: 'offline-voucher-creation-failed',
        message: 'Failed to create voucher offline: $e',
      ));
    }
  }

  /// Start atomic transaction for voucher creation
  Future<void> _startAtomicTransaction(
      String transactionId, OfflineVoucherModel voucher, String uid) async {
    try {
      final transactionData = {
        'id': transactionId,
        'type': 'voucher_create',
        'voucherNumber': voucher.voucherNumber,
        'uid': uid,
        'startedAt': DateTime.now().toIso8601String(),
        'status': 'started',
      };

      final transactionBox = await Hive.openBox<Map>('atomic_transactions');
      await transactionBox.put(transactionId, transactionData);

      log('OfflineVoucherService: Started atomic transaction: $transactionId');
    } catch (e) {
      log('OfflineVoucherService: Failed to start atomic transaction: $e');
      rethrow;
    }
  }

  /// Complete atomic transaction
  Future<void> _completeAtomicTransaction(String transactionId) async {
    try {
      final transactionBox = await Hive.openBox<Map>('atomic_transactions');
      final transactionData = transactionBox.get(transactionId);

      if (transactionData != null) {
        transactionData['status'] = 'completed';
        transactionData['completedAt'] = DateTime.now().toIso8601String();
        await transactionBox.put(transactionId, transactionData);
      }

      log('OfflineVoucherService: Completed atomic transaction: $transactionId');
    } catch (e) {
      log('OfflineVoucherService: Failed to complete atomic transaction: $e');
    }
  }

  /// Rollback atomic transaction
  Future<void> _rollbackAtomicTransaction(String transactionId) async {
    try {
      log('OfflineVoucherService: Rolling back atomic transaction: $transactionId');

      final transactionBox = await Hive.openBox<Map>('atomic_transactions');
      final transactionData = transactionBox.get(transactionId);

      if (transactionData != null) {
        final voucherNumber = transactionData['voucherNumber'] as String?;

        // Remove voucher from local storage
        if (voucherNumber != null) {
          await _offlineVouchersBox?.delete(voucherNumber);
        }

        // Mark transaction as rolled back
        transactionData['status'] = 'rolled_back';
        transactionData['rolledBackAt'] = DateTime.now().toIso8601String();
        await transactionBox.put(transactionId, transactionData);
      }

      log('OfflineVoucherService: Rollback completed for transaction: $transactionId');
    } catch (e) {
      log('OfflineVoucherService: Failed to rollback atomic transaction: $e');
    }
  }

  /// Save voucher locally
  Future<void> _saveVoucherLocally(OfflineVoucherModel voucher) async {
    try {
      await _offlineVouchersBox?.put(voucher.voucherNumber, voucher.toJson());
      log('OfflineVoucherService: Saved voucher locally: ${voucher.voucherNumber}');
    } catch (e) {
      log('OfflineVoucherService: Failed to save voucher locally: $e');
      rethrow;
    }
  }

  /// Generate local journal entries for offline voucher
  Future<List<Map<String, dynamic>>> _generateLocalJournalEntries(
      OfflineVoucherModel voucher, String uid) async {
    try {
      // This would integrate with the existing voucher accounting hook service
      // For now, we'll create a simplified version
      final journalEntries = <Map<String, dynamic>>[];

      // Generate journal entry for voucher creation
      final entryId = 'local_${DateTime.now().millisecondsSinceEpoch}';
      final journalEntry = {
        'id': entryId,
        'entryNumber': 'LOCAL-${voucher.voucherNumber}',
        'entryDate': DateTime.now().millisecondsSinceEpoch,
        'description': 'Voucher Creation - ${voucher.voucherNumber}',
        'entryType': 'voucher',
        'status': 'draft',
        'sourceTransactionId': voucher.voucherNumber,
        'sourceTransactionType': 'voucher',
        'uid': uid,
        'isLocal': true,
        'lines': _generateJournalLines(voucher),
      };

      journalEntries.add(journalEntry);

      // Save journal entry locally
      await _localJournalEntriesBox?.put(entryId, journalEntry);

      log('OfflineVoucherService: Generated ${journalEntries.length} local journal entries');
      return journalEntries;
    } catch (e) {
      log('OfflineVoucherService: Failed to generate local journal entries: $e');
      rethrow;
    }
  }

  /// Generate journal lines for voucher
  List<Map<String, dynamic>> _generateJournalLines(
      OfflineVoucherModel voucher) {
    final lines = <Map<String, dynamic>>[];

    // Add broker fees line if applicable
    if (voucher.brokerFees > 0 && voucher.brokerAccountId != null) {
      lines.add({
        'id': 'line_${DateTime.now().millisecondsSinceEpoch}_1',
        'accountId': voucher.brokerAccountId,
        'accountName': voucher.brokerAccount,
        'debitAmount': voucher.brokerFees,
        'creditAmount': 0.0,
        'description': 'Broker fees for voucher ${voucher.voucherNumber}',
      });
    }

    // Add munshiana fees line if applicable
    if (voucher.munshianaFees > 0 && voucher.munshianaAccountId != null) {
      lines.add({
        'id': 'line_${DateTime.now().millisecondsSinceEpoch}_2',
        'accountId': voucher.munshianaAccountId,
        'accountName': voucher.munshianaAccount,
        'debitAmount': voucher.munshianaFees,
        'creditAmount': 0.0,
        'description': 'Munshiana fees for voucher ${voucher.voucherNumber}',
      });
    }

    return lines;
  }

  /// Calculate local balance changes
  Future<Map<String, double>> _calculateLocalBalanceChanges(
      List<Map<String, dynamic>> journalEntries) async {
    try {
      final balanceChanges = <String, double>{};

      for (final entry in journalEntries) {
        final lines = entry['lines'] as List<dynamic>? ?? [];

        for (final line in lines) {
          final accountId = line['accountId'] as String?;
          final debitAmount = (line['debitAmount'] as num?)?.toDouble() ?? 0.0;
          final creditAmount =
              (line['creditAmount'] as num?)?.toDouble() ?? 0.0;

          if (accountId != null) {
            final netChange = debitAmount - creditAmount;
            balanceChanges[accountId] =
                (balanceChanges[accountId] ?? 0.0) + netChange;
          }
        }
      }

      // Save balance changes locally
      for (final entry in balanceChanges.entries) {
        await _localBalanceChangesBox?.put(entry.key, {
          'accountId': entry.key,
          'balanceChange': entry.value,
          'timestamp': DateTime.now().toIso8601String(),
        });
      }

      log('OfflineVoucherService: Calculated balance changes for ${balanceChanges.length} accounts');
      return balanceChanges;
    } catch (e) {
      log('OfflineVoucherService: Failed to calculate local balance changes: $e');
      rethrow;
    }
  }

  /// Add voucher to sync queue
  Future<void> _addToSyncQueue(OfflineVoucherModel voucher, String uid) async {
    try {
      final syncEntry = SyncQueueEntry.voucherCreate(
        voucherData: voucher.toJson(),
        userId: uid,
        companyId: uid,
      );

      await _offlineStateService.addToSyncQueue(
        operationType: syncEntry.operationType.value,
        data: syncEntry.data,
        dependencies: syncEntry.dependencies,
      );

      log('OfflineVoucherService: Added voucher to sync queue: ${voucher.voucherNumber}');
    } catch (e) {
      log('OfflineVoucherService: Failed to add voucher to sync queue: $e');
    }
  }

  /// Get offline voucher by number
  OfflineVoucherModel? getOfflineVoucher(String voucherNumber) {
    try {
      return _offlineVouchers.firstWhereOrNull(
        (voucher) => voucher.voucherNumber == voucherNumber,
      );
    } catch (e) {
      log('OfflineVoucherService: Failed to get offline voucher: $e');
      return null;
    }
  }

  /// Get pending vouchers for sync
  List<OfflineVoucherModel> getPendingVouchers() {
    return _offlineVouchers
        .where(
            (voucher) => voucher.syncState == OfflineTransactionState.pending)
        .toList();
  }

  /// Update voucher sync state
  Future<void> updateVoucherSyncState(
      String voucherNumber, OfflineTransactionState newState) async {
    try {
      final voucherIndex = _offlineVouchers.indexWhere(
        (voucher) => voucher.voucherNumber == voucherNumber,
      );

      if (voucherIndex != -1) {
        final updatedVoucher = _offlineVouchers[voucherIndex].copyWithOffline(
          syncState: newState,
          lastSyncAttempt: DateTime.now(),
        );

        _offlineVouchers[voucherIndex] = updatedVoucher;
        await _saveVoucherLocally(updatedVoucher);

        log('OfflineVoucherService: Updated voucher sync state: $voucherNumber -> ${newState.value}');
      }
    } catch (e) {
      log('OfflineVoucherService: Failed to update voucher sync state: $e');
    }
  }

  /// Clear synced vouchers from local storage
  Future<void> clearSyncedVouchers() async {
    try {
      final syncedVouchers = _offlineVouchers
          .where(
              (voucher) => voucher.syncState == OfflineTransactionState.synced)
          .toList();

      for (final voucher in syncedVouchers) {
        await _offlineVouchersBox?.delete(voucher.voucherNumber);
        _offlineVouchers.remove(voucher);
      }

      log('OfflineVoucherService: Cleared ${syncedVouchers.length} synced vouchers');
    } catch (e) {
      log('OfflineVoucherService: Failed to clear synced vouchers: $e');
    }
  }
}
