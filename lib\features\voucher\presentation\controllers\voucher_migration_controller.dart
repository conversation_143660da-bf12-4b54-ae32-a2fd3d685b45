import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/services/voucher_chart_of_accounts_migration_service.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';

/// Controller for managing voucher migration to Chart of Accounts
class VoucherMigrationController extends GetxController {
  final VoucherChartOfAccountsMigrationService _migrationService =
      VoucherChartOfAccountsMigrationService();

  // Reactive variables
  final isMigrationInProgress = false.obs;
  final migrationProgress = ''.obs;
  final migrationCompleted = false.obs;
  final migrationResult = Rxn<dynamic>();
  final showMigrationReport = false.obs;

  // Get company UID - using a default for now
  String get companyUid {
    // TODO: Replace with proper auth controller when available
    return 'default-company';
  }

  /// Start the migration process
  Future<void> startMigration({bool dryRun = false}) async {
    try {
      if (isMigrationInProgress.value) {
        SnackbarUtils.showWarning(
            'Migration In Progress', 'A migration is already running');
        return;
      }

      // Reset state
      isMigrationInProgress.value = true;
      migrationCompleted.value = false;
      migrationResult.value = null;
      migrationProgress.value = 'Initializing migration...';

      log('Starting voucher migration${dryRun ? ' (DRY RUN)' : ''}');

      final result = await _migrationService.migrateVouchersToChartOfAccounts(
        uid: companyUid,
        dryRun: dryRun,
        onProgress: (progress) {
          migrationProgress.value = progress;
        },
      );

      result.fold(
        (failure) {
          final errorMessage = failure.toString();
          log('Migration failed: $errorMessage');
          SnackbarUtils.showError(
            'Migration Failed',
            errorMessage,
          );
          migrationResult.value = null;
        },
        (result) {
          log('Migration completed successfully');
          migrationResult.value = result;

          // Check if result has errors (assuming it's a migration result object)
          final hasErrors = result.toString().contains('error');

          if (!hasErrors) {
            SnackbarUtils.showSuccess(
              '${dryRun ? 'Dry Run' : 'Migration'} Completed',
              'Migration processed successfully',
            );
          } else {
            SnackbarUtils.showWarning(
              '${dryRun ? 'Dry Run' : 'Migration'} Completed with Issues',
              'Some errors occurred during migration',
            );
          }
        },
      );
    } catch (e) {
      log('Error during migration: $e');
      SnackbarUtils.showError(
        'Migration Error',
        'An unexpected error occurred: $e',
      );
    } finally {
      isMigrationInProgress.value = false;
      migrationCompleted.value = true;
      migrationProgress.value = 'Migration completed';
    }
  }

  /// Run a dry run migration to preview changes
  Future<void> runDryRun() async {
    await startMigration(dryRun: true);
  }

  /// Run the actual migration
  Future<void> runMigration() async {
    // Show confirmation dialog
    final confirmed = await _showMigrationConfirmationDialog();
    if (!confirmed) return;

    await startMigration(dryRun: false);
  }

  /// Show confirmation dialog for migration
  Future<bool> _showMigrationConfirmationDialog() async {
    return await Get.dialog<bool>(
          AlertDialog(
            title: const Text('Confirm Migration'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'This will migrate all vouchers to use Chart of Accounts.',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 16),
                Text('This action will:'),
                Text('• Update voucher data with Chart of Accounts IDs'),
                Text('• Preserve existing legacy account names'),
                Text('• Add migration metadata to vouchers'),
                SizedBox(height: 16),
                Text(
                  'It is recommended to run a dry run first to preview changes.',
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Get.back(result: true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                ),
                child: const Text('Proceed with Migration'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// Toggle migration report visibility
  void toggleMigrationReport() {
    showMigrationReport.value = !showMigrationReport.value;
  }

  /// Get migration report text
  String get migrationReportText {
    if (migrationResult.value == null) return '';
    return _migrationService.generateMigrationReport(migrationResult.value!);
  }

  /// Check if migration is needed
  Future<bool> isMigrationNeeded() async {
    try {
      // Run a quick dry run to check if any vouchers need migration
      final result = await _migrationService.migrateVouchersToChartOfAccounts(
        uid: companyUid,
        dryRun: true,
      );

      return result.fold(
        (failure) => false,
        (migrationResult) => true, // Assume migration is needed if no error
      );
    } catch (e) {
      log('Error checking migration status: $e');
      return false;
    }
  }

  /// Get migration status summary
  String get migrationStatusSummary {
    final result = migrationResult.value;
    if (result == null) return 'No migration data available';

    return 'Migration completed: ${result.toString()}';
  }

  /// Get migration status color
  Color get migrationStatusColor {
    final result = migrationResult.value;
    if (result == null) return Colors.grey;

    // Simple check based on result string
    final resultStr = result.toString().toLowerCase();
    if (resultStr.contains('error')) return Colors.red;
    if (resultStr.contains('warning')) return Colors.orange;
    return Colors.green;
  }

  /// Reset migration state
  void resetMigrationState() {
    isMigrationInProgress.value = false;
    migrationProgress.value = '';
    migrationCompleted.value = false;
    migrationResult.value = null;
    showMigrationReport.value = false;
  }

  @override
  void onClose() {
    resetMigrationState();
    super.onClose();
  }
}
