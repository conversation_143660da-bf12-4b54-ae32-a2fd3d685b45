import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/slab/domain/usecases/create_slab_use_case.dart';
import 'package:logestics/features/slab/domain/usecases/update_slab_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/region_use_case/get_region_use_case.dart';
import 'package:logestics/features/locations/domain/usecases/district_use_case/get_district_use_case.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/models/locations/region_model.dart';
import 'package:logestics/models/locations/district_model.dart';

class SlabFormController extends GetxController {
  final CreateSlabUseCase createSlabUseCase;
  final UpdateSlabUseCase updateSlabUseCase;
  final GetRegionsUseCase getRegionsUseCase;
  final GetDistrictsUseCase getDistrictsUseCase;

  SlabFormController({
    required this.createSlabUseCase,
    required this.updateSlabUseCase,
    required this.getRegionsUseCase,
    required this.getDistrictsUseCase,
  });

  // Form controllers
  final slabNameController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // Observable variables
  final isLoading = false.obs;
  final isSaving = false.obs;
  final isEditing = false.obs;

  // Date selection
  final startDate = Rx<DateTime?>(null);
  final expiryDate = Rx<DateTime?>(null);

  // Location data
  final regions = <RegionModel>[].obs;
  final districts = <DistrictModel>[].obs;
  final isLoadingRegions = false.obs;
  final isLoadingDistricts = false.obs;

  // Slab rates data
  final slabRates = <SlabRateModel>[].obs;

  // Dynamic columns
  final customColumns = <String>[].obs;
  final customColumnControllers = <String, TextEditingController>{}.obs;

  // Rate field controllers - Map<districtId_fieldName, TextEditingController>
  final rateControllers = <String, TextEditingController>{}.obs;

  // Current slab being edited
  SlabModel? currentSlab;

  // Formula management
  final calculationFormula = Rx<CalculationFormulaModel?>(null);

  @override
  void onInit() {
    super.onInit();
    loadRegions();
    loadDistricts();

    // Start with zero pre-configured columns - users must create all columns manually
    // customColumns starts empty
    _initializeCustomColumnControllers();
  }

  @override
  void onClose() {
    slabNameController.dispose();
    _disposeCustomColumnControllers();
    _disposeRateControllers();
    super.onClose();
  }

  /// Initialize controllers for custom columns
  void _initializeCustomColumnControllers() {
    for (String column in customColumns) {
      customColumnControllers[column] = TextEditingController();
    }
  }

  /// Dispose custom column controllers
  void _disposeCustomColumnControllers() {
    for (var controller in customColumnControllers.values) {
      controller.dispose();
    }
    customColumnControllers.clear();
  }

  /// Initialize rate controllers for all districts and fields
  void _initializeRateControllers() {
    // Dispose existing controllers
    for (var controller in rateControllers.values) {
      controller.dispose();
    }
    rateControllers.clear();

    log('Initializing rate controllers for ${slabRates.length} rates');

    // Create controllers for each district and rate field
    for (var rate in slabRates) {
      final hmtKey = '${rate.districtId}_hmtRate';
      final nonFuelKey = '${rate.districtId}_nonFuelRate';

      rateControllers[hmtKey] =
          TextEditingController(text: rate.hmtRate.toString());
      rateControllers[nonFuelKey] =
          TextEditingController(text: rate.nonFuelRate.toString());

      log('Created controllers for ${rate.districtName}: HMT=${rate.hmtRate}, NonFuel=${rate.nonFuelRate}');

      // Create controllers for custom columns
      for (var column in customColumns) {
        final customKey = '${rate.districtId}_$column';
        final value = rate.getCustomValue(column) ?? 0.0;
        rateControllers[customKey] =
            TextEditingController(text: value.toString());
        log('Created custom controller for ${rate.districtName}.$column = $value');
      }
    }

    log('Total rate controllers created: ${rateControllers.length}');
  }

  /// Get controller for a specific district and field
  TextEditingController? getRateController(String districtId, String field) {
    final key = '${districtId}_$field';
    return rateControllers[key];
  }

  /// Dispose rate controllers
  void _disposeRateControllers() {
    for (var controller in rateControllers.values) {
      controller.dispose();
    }
    rateControllers.clear();
  }

  /// Load regions from Firebase
  Future<void> loadRegions() async {
    try {
      isLoadingRegions.value = true;

      final result = await getRegionsUseCase.call();

      result.fold(
        (failure) {
          log('Error loading regions: ${failure.message}');
          SnackbarUtils.showError('Error', 'Failed to load regions');
        },
        (regionsList) {
          regions.value = regionsList;
          log('Successfully loaded ${regionsList.length} regions');
        },
      );
    } catch (e) {
      log('Unexpected error loading regions: $e');
      SnackbarUtils.showError('Error', 'Failed to load regions');
    } finally {
      isLoadingRegions.value = false;
    }
  }

  /// Load districts from Firebase
  Future<void> loadDistricts() async {
    try {
      isLoadingDistricts.value = true;

      final result = await getDistrictsUseCase.call();

      result.fold(
        (failure) {
          log('Error loading districts: ${failure.message}');
          SnackbarUtils.showError('Error', 'Failed to load districts');
        },
        (districtsList) {
          districts.value = districtsList;
          log('Successfully loaded ${districtsList.length} districts');
          _initializeSlabRates();
        },
      );
    } catch (e) {
      log('Unexpected error loading districts: $e');
      SnackbarUtils.showError('Error', 'Failed to load districts');
    } finally {
      isLoadingDistricts.value = false;
    }
  }

  /// Initialize slab rates for all districts
  void _initializeSlabRates() {
    // If we're editing and already have rates, preserve them
    if (isEditing.value && slabRates.isNotEmpty) {
      // Create a map of existing rates by district ID for quick lookup
      final existingRatesMap = <String, SlabRateModel>{};
      for (var rate in slabRates) {
        existingRatesMap[rate.districtId] = rate;
      }

      // Clear and rebuild rates, preserving existing values where available
      slabRates.clear();

      for (var district in districts) {
        final existingRate = existingRatesMap[district.districtId];

        final rate = SlabRateModel(
          regionId: district.regionId,
          regionName: district.regionName,
          districtId: district.districtId,
          districtName: district.districtName,
          hmtRate: existingRate?.hmtRate ?? 0.0,
          nonFuelRate: existingRate?.nonFuelRate ?? 0.0,
          customColumns: existingRate?.customColumns ?? {},
        );
        slabRates.add(rate);
      }
    } else {
      // For new slabs, initialize with zeros
      slabRates.clear();

      for (var district in districts) {
        final rate = SlabRateModel(
          regionId: district.regionId,
          regionName: district.regionName,
          districtId: district.districtId,
          districtName: district.districtName,
          hmtRate: 0.0,
          nonFuelRate: 0.0,
          customColumns: {},
        );
        slabRates.add(rate);
      }
    }

    // Initialize rate controllers after rates are set
    _initializeRateControllers();
  }

  /// Set current slab for editing
  void setCurrentSlab(SlabModel slab) {
    currentSlab = slab;
    isEditing.value = true;

    // Populate form fields
    slabNameController.text = slab.slabName;
    startDate.value = slab.startDate;
    expiryDate.value = slab.expiryDate;

    // Populate rates
    slabRates.value = slab.rates;

    // Populate calculation formula
    calculationFormula.value = slab.calculationFormula;

    // Extract custom columns from rates
    final allCustomColumns = <String>{};
    for (var rate in slab.rates) {
      allCustomColumns.addAll(rate.customColumns.keys);
    }

    customColumns.value = allCustomColumns.toList();
    _initializeCustomColumnControllers();

    // Initialize rate controllers after setting rates and custom columns
    _initializeRateControllers();

    log('Set current slab: ${slab.slabName} with ${slab.rates.length} rates');
    for (var rate in slab.rates) {
      log('Rate for ${rate.districtName}: HMT=${rate.hmtRate}, NonFuel=${rate.nonFuelRate}');
    }
  }

  /// Add a new custom column
  void addCustomColumn(String columnName) {
    if (columnName.isNotEmpty && !customColumns.contains(columnName)) {
      customColumns.add(columnName);
      customColumnControllers[columnName] = TextEditingController();

      // Update all existing rates with the new column
      for (int i = 0; i < slabRates.length; i++) {
        final rate = slabRates[i];
        final updatedCustomColumns =
            Map<String, dynamic>.from(rate.customColumns);
        updatedCustomColumns[columnName] = 0.0;

        slabRates[i] = rate.copyWithCustomColumns(updatedCustomColumns);
      }
    }
  }

  /// Remove a custom column
  void removeCustomColumn(String columnName) {
    customColumns.remove(columnName);
    customColumnControllers[columnName]?.dispose();
    customColumnControllers.remove(columnName);

    // Remove from all rates
    for (int i = 0; i < slabRates.length; i++) {
      final rate = slabRates[i];
      final updatedCustomColumns =
          Map<String, dynamic>.from(rate.customColumns);
      updatedCustomColumns.remove(columnName);

      slabRates[i] = rate.copyWithCustomColumns(updatedCustomColumns);
    }
  }

  /// Update rate value for a specific district and field
  void updateRateValue(String districtId, String field, dynamic value) {
    final rateIndex =
        slabRates.indexWhere((rate) => rate.districtId == districtId);
    if (rateIndex == -1) return;

    final rate = slabRates[rateIndex];
    SlabRateModel updatedRate;

    switch (field) {
      case 'hmtRate':
        updatedRate = SlabRateModel(
          regionId: rate.regionId,
          regionName: rate.regionName,
          districtId: rate.districtId,
          districtName: rate.districtName,
          hmtRate: double.tryParse(value.toString()) ?? 0.0,
          nonFuelRate: rate.nonFuelRate,
          customColumns: rate.customColumns,
        );
        break;
      case 'nonFuelRate':
        updatedRate = SlabRateModel(
          regionId: rate.regionId,
          regionName: rate.regionName,
          districtId: rate.districtId,
          districtName: rate.districtName,
          hmtRate: rate.hmtRate,
          nonFuelRate: double.tryParse(value.toString()) ?? 0.0,
          customColumns: rate.customColumns,
        );
        break;
      default:
        // Custom column
        final updatedCustomColumns =
            Map<String, dynamic>.from(rate.customColumns);
        updatedCustomColumns[field] = double.tryParse(value.toString()) ?? 0.0;
        updatedRate = rate.copyWithCustomColumns(updatedCustomColumns);
        break;
    }

    slabRates[rateIndex] = updatedRate;
  }

  /// Sync rate values from text controllers to slabRates before saving
  /// This ensures that any values entered in text fields but not yet synced via onChanged are captured
  void _syncRateValuesFromControllers() {
    log('Syncing rate values from controllers to slabRates before saving');

    for (int i = 0; i < slabRates.length; i++) {
      final rate = slabRates[i];
      bool rateUpdated = false;

      // Create a copy of the rate to update
      double hmtRate = rate.hmtRate;
      double nonFuelRate = rate.nonFuelRate;
      final updatedCustomColumns =
          Map<String, dynamic>.from(rate.customColumns);

      // Sync HMT rate
      final hmtController = getRateController(rate.districtId, 'hmtRate');
      if (hmtController != null && hmtController.text.isNotEmpty) {
        final newHmtRate = double.tryParse(hmtController.text) ?? 0.0;
        if (newHmtRate != rate.hmtRate) {
          hmtRate = newHmtRate;
          rateUpdated = true;
          log('Synced HMT rate for ${rate.districtName}: ${rate.hmtRate} → $newHmtRate');
        }
      }

      // Sync Non-Fuel rate
      final nonFuelController =
          getRateController(rate.districtId, 'nonFuelRate');
      if (nonFuelController != null && nonFuelController.text.isNotEmpty) {
        final newNonFuelRate = double.tryParse(nonFuelController.text) ?? 0.0;
        if (newNonFuelRate != rate.nonFuelRate) {
          nonFuelRate = newNonFuelRate;
          rateUpdated = true;
          log('Synced Non-Fuel rate for ${rate.districtName}: ${rate.nonFuelRate} → $newNonFuelRate');
        }
      }

      // Sync custom columns
      for (final column in customColumns) {
        final customController = getRateController(rate.districtId, column);
        if (customController != null && customController.text.isNotEmpty) {
          final newValue = double.tryParse(customController.text) ?? 0.0;
          final currentValue = rate.getCustomValue(column) ?? 0.0;
          if (newValue != currentValue) {
            updatedCustomColumns[column] = newValue;
            rateUpdated = true;
            log('Synced custom column $column for ${rate.districtName}: $currentValue → $newValue');
          }
        }
      }

      // Update the rate if any values changed
      if (rateUpdated) {
        slabRates[i] = SlabRateModel(
          regionId: rate.regionId,
          regionName: rate.regionName,
          districtId: rate.districtId,
          districtName: rate.districtName,
          hmtRate: hmtRate,
          nonFuelRate: nonFuelRate,
          customColumns: updatedCustomColumns,
        );
      }
    }

    log('Rate sync complete. Total rates: ${slabRates.length}');
  }

  /// Handle calculation formula changes
  void onFormulaChanged(CalculationFormulaModel? formula) {
    calculationFormula.value = formula;
    log('Calculation formula updated: ${formula?.formulaName ?? 'None'}');
  }

  /// Select start date
  Future<void> selectStartDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: startDate.value ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );

    if (date != null) {
      startDate.value = date;

      // If expiry date is before start date, clear it
      if (expiryDate.value != null && expiryDate.value!.isBefore(date)) {
        expiryDate.value = null;
      }
    }
  }

  /// Select expiry date
  Future<void> selectExpiryDate(BuildContext context) async {
    final minDate = startDate.value ?? DateTime.now();

    final date = await showDatePicker(
      context: context,
      initialDate: expiryDate.value ?? minDate.add(const Duration(days: 30)),
      firstDate: minDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );

    if (date != null) {
      expiryDate.value = date;
    }
  }

  /// Validate form
  bool validateForm() {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    if (startDate.value == null) {
      SnackbarUtils.showError('Error', 'Please select start date');
      return false;
    }

    if (expiryDate.value == null) {
      SnackbarUtils.showError('Error', 'Please select expiry date');
      return false;
    }

    if (startDate.value!.isAfter(expiryDate.value!)) {
      SnackbarUtils.showError('Error', 'Start date must be before expiry date');
      return false;
    }

    return true;
  }

  /// Save slab
  Future<void> saveSlab() async {
    if (!validateForm()) return;

    try {
      isSaving.value = true;

      // Sync rate values from controllers to slabRates before saving
      _syncRateValuesFromControllers();

      final slab = SlabModel(
        slabId: currentSlab?.slabId ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        slabName: slabNameController.text.trim(),
        startDate: startDate.value!,
        expiryDate: expiryDate.value!,
        createdAt: currentSlab?.createdAt ?? DateTime.now(),
        isActive: true,
        rates: slabRates.toList(),
        calculationFormula: calculationFormula.value,
      );

      final result = isEditing.value
          ? await updateSlabUseCase.call(slab: slab)
          : await createSlabUseCase.call(slab: slab);

      result.fold(
        (failure) {
          log('Error saving slab: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (_) {
          SnackbarUtils.showSuccess(
              'Success',
              isEditing.value
                  ? 'Slab updated successfully'
                  : 'Slab created successfully');
          Get.back(); // Return to slab list
        },
      );
    } catch (e) {
      log('Unexpected error saving slab: $e');
      SnackbarUtils.showError('Error', 'Failed to save slab');
    } finally {
      isSaving.value = false;
    }
  }

  /// Clear form
  void clearForm() {
    slabNameController.clear();
    startDate.value = null;
    expiryDate.value = null;
    currentSlab = null;
    isEditing.value = false;
    _initializeSlabRates();
  }

  /// Validation methods
  String? validateSlabName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Slab name is required';
    }
    if (value.trim().length < 3) {
      return 'Slab name must be at least 3 characters';
    }
    return null;
  }
}
