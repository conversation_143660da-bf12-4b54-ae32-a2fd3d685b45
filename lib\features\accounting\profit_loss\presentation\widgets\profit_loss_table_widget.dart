import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profit_loss_controller.dart';

/// Table widget for displaying detailed Profit & Loss data
class ProfitLossTableWidget extends StatelessWidget {
  const ProfitLossTableWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfitLossController>();

    return Obx(() {
      if (!controller.hasReport) {
        return const SizedBox.shrink();
      }

      final report = controller.currentReport!;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Revenue Section
          if (controller.hasRevenueData) ...[
            _buildSectionHeader(context, 'REVENUE', Colors.green),
            const SizedBox(height: 8),
            ...report.revenueGroups
                .map((group) => _buildGroupSection(context, group)),
            _buildTotalRow(
                context, 'Total Revenue', report.totalRevenue, Colors.green),
            const SizedBox(height: 24),
          ],

          // Expenses Section
          if (controller.hasExpenseData) ...[
            _buildSectionHeader(context, 'EXPENSES', Colors.orange),
            const SizedBox(height: 8),
            ...report.expenseGroups
                .map((group) => _buildGroupSection(context, group)),
            _buildTotalRow(
                context, 'Total Expenses', report.totalExpenses, Colors.orange),
            const SizedBox(height: 24),
          ],

          // Net Income Section
          const Divider(thickness: 2),
          const SizedBox(height: 16),
          _buildNetIncomeRow(context, report.netIncome, report.isProfitable),
        ],
      );
    });
  }

  /// Build section header
  Widget _buildSectionHeader(BuildContext context, String title, Color color) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
      ),
    );
  }

  /// Build group section (e.g., Sales Revenue, Operating Expenses)
  Widget _buildGroupSection(BuildContext context, dynamic group) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Group Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  group.groupName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Text(
                  _formatCurrency(group.groupTotal),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: group.groupType == 'revenue'
                            ? Colors.green
                            : Colors.orange,
                      ),
                ),
              ],
            ),
          ),

          // Account Items
          ...group.accounts
              .map<Widget>((account) => _buildAccountRow(context, account)),
        ],
      ),
    );
  }

  /// Build individual account row
  Widget _buildAccountRow(BuildContext context, dynamic account) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey, width: 0.2),
        ),
      ),
      child: Row(
        children: [
          // Account Number
          SizedBox(
            width: 80,
            child: Text(
              account.accountNumber,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'monospace',
                    color: Colors.grey[600],
                  ),
            ),
          ),
          const SizedBox(width: 16),
          // Account Name
          Expanded(
            child: Text(
              account.accountName,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          // Amount
          SizedBox(
            width: 120,
            child: Text(
              _formatCurrency(account.amount),
              textAlign: TextAlign.right,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'monospace',
                  ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build total row for sections
  Widget _buildTotalRow(
      BuildContext context, String label, double amount, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          Text(
            _formatCurrency(amount),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontFamily: 'monospace',
                ),
          ),
        ],
      ),
    );
  }

  /// Build net income row
  Widget _buildNetIncomeRow(
      BuildContext context, double netIncome, bool isProfitable) {
    final color = isProfitable ? Colors.green : Colors.red;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color, width: 2),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(
                isProfitable ? Icons.trending_up : Icons.trending_down,
                color: color,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'NET INCOME',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
              ),
            ],
          ),
          Text(
            _formatCurrency(netIncome),
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontFamily: 'monospace',
                ),
          ),
        ],
      ),
    );
  }

  /// Format currency amount
  String _formatCurrency(double amount) {
    final isNegative = amount < 0;
    final absAmount = amount.abs();

    String formatted;
    if (absAmount >= 1000000) {
      formatted = 'PKR ${(absAmount / 1000000).toStringAsFixed(2)}M';
    } else if (absAmount >= 1000) {
      formatted = 'PKR ${(absAmount / 1000).toStringAsFixed(1)}K';
    } else {
      formatted = 'PKR ${absAmount.toStringAsFixed(2)}';
    }

    return isNegative ? '($formatted)' : formatted;
  }
}
